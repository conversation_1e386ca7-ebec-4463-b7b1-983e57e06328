// IE兼容性polyfills
import 'core-js/stable';
import 'regenerator-runtime/runtime';

// 确保IE支持Promise
if (!window.Promise) {
    window.Promise = require('es6-promise').Promise;
}

// 确保IE支持Object.assign
if (!Object.assign) {
    Object.assign = require('object-assign');
}

// 确保IE支持Array.from
if (!Array.from) {
    Array.from = function(arrayLike, mapFn, thisArg) {
        var C = this;
        var items = Object(arrayLike);
        if (arrayLike == null) {
            throw new TypeError('Array.from requires an array-like object - not null or undefined');
        }
        var mapFunction = mapFn === undefined ? undefined : mapFn;
        var T;
        if (typeof mapFunction !== 'undefined') {
            if (typeof mapFunction !== 'function') {
                throw new TypeError('Array.from: when provided, the second argument must be a function');
            }
            if (arguments.length > 2) {
                T = thisArg;
            }
        }
        var len = parseInt(items.length);
        var A = typeof C === 'function' ? Object(new C(len)) : new Array(len);
        var k = 0;
        var kValue;
        while (k < len) {
            kValue = items[k];
            if (mapFunction) {
                A[k] = typeof T === 'undefined' ? mapFunction(kValue, k) : mapFunction.call(T, kValue, k);
            } else {
                A[k] = kValue;
            }
            k += 1;
        }
        A.length = len;
        return A;
    };
}

// 确保IE支持Array.includes
if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement, fromIndex) {
        'use strict';
        var O = Object(this);
        var len = parseInt(O.length) || 0;
        if (len === 0) {
            return false;
        }
        var n = parseInt(fromIndex) || 0;
        var k;
        if (n >= 0) {
            k = n;
        } else {
            k = len + n;
            if (k < 0) {
                k = 0;
            }
        }
        function sameValueZero(x, y) {
            return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
        }
        for (; k < len; k++) {
            if (sameValueZero(O[k], searchElement)) {
                return true;
            }
        }
        return false;
    };
}

// 确保IE支持String.includes
if (!String.prototype.includes) {
    String.prototype.includes = function(search, start) {
        'use strict';
        if (typeof start !== 'number') {
            start = 0;
        }
        if (start + search.length > this.length) {
            return false;
        } else {
            return this.indexOf(search, start) !== -1;
        }
    };
}

// 确保IE支持String.startsWith
if (!String.prototype.startsWith) {
    String.prototype.startsWith = function(searchString, position) {
        position = position || 0;
        return this.substr(position, searchString.length) === searchString;
    };
}

// 确保IE支持String.endsWith
if (!String.prototype.endsWith) {
    String.prototype.endsWith = function(searchString, length) {
        if (length === undefined || length > this.length) {
            length = this.length;
        }
        return this.substring(length - searchString.length, length) === searchString;
    };
}

// 确保IE支持Number.isNaN
if (!Number.isNaN) {
    Number.isNaN = function(value) {
        return typeof value === 'number' && isNaN(value);
    };
}

// 确保IE支持Number.isInteger
if (!Number.isInteger) {
    Number.isInteger = function(value) {
        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;
    };
}

// 确保IE支持Object.keys
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// 确保IE支持Object.values
if (!Object.values) {
    Object.values = function(obj) {
        var values = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                values.push(obj[key]);
            }
        }
        return values;
    };
}

// 确保IE支持Object.entries
if (!Object.entries) {
    Object.entries = function(obj) {
        var entries = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                entries.push([key, obj[key]]);
            }
        }
        return entries;
    };
}

// 确保IE支持Element.closest
if (!Element.prototype.closest) {
    Element.prototype.closest = function(s) {
        var el = this;
        do {
            if (el.matches(s)) return el;
            el = el.parentElement || el.parentNode;
        } while (el !== null && el.nodeType === 1);
        return null;
    };
}

// 确保IE支持Element.matches
if (!Element.prototype.matches) {
    Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
}

// 确保IE支持CustomEvent
if (typeof window.CustomEvent !== 'function') {
    function CustomEvent(event, params) {
        params = params || { bubbles: false, cancelable: false, detail: undefined };
        var evt = document.createEvent('CustomEvent');
        evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
        return evt;
    }
    CustomEvent.prototype = window.Event.prototype;
    window.CustomEvent = CustomEvent;
}

// 确保IE支持console对象
if (!window.console) {
    window.console = {
        log: function() {},
        warn: function() {},
        error: function() {},
        info: function() {},
        debug: function() {}
    };
}
