:global {

    .m-table-four-col2,
    .m-table-four-col-popover2 {
        margin: 10px 0;

        table {
            width: 100%;

            tr {
                td {
                    padding: 14px;
                    border: #fff solid 1px;
                    font-size : 12px;
                    word-break: break-all;
                    white-space: pre;

                    .s1 {
                        display: inline-block;
                        // width  : 138px;
                    }

                    .s2 {
                        display: inline-block;
                        // width  : 188px;
                    }

                    .s3 {
                        display: inline-block;
                        width  : 100%;
                    }
                }

                td:nth-child(2n-1) {
                    text-align: left;
                    background: #EEF3FA;
                }

                td:nth-child(2n) {
                    background: #F8F9FA;
                }

                .td1 {
                    background: #f3f7ff;
                }
            }
        }

        .u-more {
            text-align: center;
            border    : #dfdfdf solid 1px;
            padding   : 5px;
            cursor    : pointer;
            border-top: none;
            font-size : 12px;

            &:hover {
                color: #4594f9;
            }

            span {
                margin-right: 5px;
            }
        }
    }

    .m-table-four-col-popover2 {
        table {
            tr:first-child {
                td {
                    border-top: none;
                }
            }

            tr:last-child {
                td {
                    border-bottom: none;
                }
            }
        }
    }
}