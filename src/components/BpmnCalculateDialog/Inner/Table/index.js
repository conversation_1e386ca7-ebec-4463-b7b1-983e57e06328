/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-03-01 11:30:17
 * @Describe: 表格组件
 */

import { PureComponent } from 'react';
import { Ellipsis } from 'tntd';
import './index.less';

class Table extends PureComponent {
    state = {
        // data: [
        //     {
        //         displayName: "",
        //         value: ""
        //     }
        // ]
    };

    render() {
        const { data: initData, popover } = this.props;

        let data = initData;
        data.forEach((item, i) => {
            if (!item.displayName) data.splice(i, 1);
        });
        // 组装
        let turnTableData = [];
        let leftArr = [];
        for (let i = 0; i < data.length; i = i + 2) {
            if (data[i].value && data[i].value.length > 50) {
                turnTableData.push({
                    dName: data[i].displayName,
                    value: data[i].value,
                    dName2: '',
                    value2: ''
                });
                if (data[i + 1] && data[i + 1].value && data[i + 1].value.length > 50) {
                    turnTableData.push({
                        dName: data[i + 1].displayName,
                        value: data[i + 1].value,
                        dName2: '',
                        value2: ''
                    });
                } else if (data[i + 1]) {
                    leftArr.push(data[i + 1]);
                }
            } else {
                if (data[i + 1]) {
                    if (data[i + 1].value && data[i + 1].value.length > 50) {
                        turnTableData.push({
                            dName: data[i + 1].displayName,
                            value: data[i + 1].value,
                            dName2: '',
                            value2: ''
                        });
                        leftArr.push(data[i]);
                    } else {
                        turnTableData.push({
                            dName: data[i].displayName,
                            value: data[i].value,
                            dName2: data[i + 1] ? data[i + 1].displayName : '',
                            value2: data[i + 1] ? data[i + 1].value : ''
                        });
                    }
                } else {
                    leftArr.push(data[i]);
                }
            }
        }
        let leftTableData = [];
        for (let i = 0; i < leftArr.length; i = i + 2) {
            leftTableData.push({
                dName: leftArr[i].displayName,
                value: leftArr[i].value,
                dName2: leftArr[i + 1] ? leftArr[i + 1].displayName : '',
                value2: leftArr[i + 1] ? leftArr[i + 1].value : ''
            });
        }
        turnTableData = turnTableData.concat(leftTableData);

        return (
            <div className={popover ? 'm-table-four-col-popover2' : 'm-table-four-col2'}>
                <table>
                    <tbody>
                        {turnTableData &&
                            turnTableData.map((item, index) => {
                                let type = 1;
                                if (!item.dName2 && !item.value2) type = 0;
                                return (
                                    <tr key={index}>
                                        <td className="td1" width={popover ? 100 : 150}>
                                            <span className="s1" style={popover ? { width: '88px' } : {}}>
                                                <Ellipsis title={item.dName} widthLimit={88} />
                                            </span>
                                        </td>
                                        <td colSpan={type === 0 ? '3' : null} width={popover ? 150 : 200}>
                                            <span
                                                className={type === 0 ? 's3' : 's2'}
                                                style={popover ? (type === 0 ? { width: '388px' } : { width: '138px' }) : {}}>
                                                <Ellipsis
                                                    widthLimit={type === 0 ? 388 : 138}
                                                    title={typeof item.value === 'boolean' ? `${item.value}` : item.value}
                                                />
                                            </span>
                                        </td>
                                        {type === 1 && (
                                            <td className="td1" width={popover ? 100 : 150}>
                                                <span className="s1" style={popover ? { width: '88px' } : {}}>
                                                    <Ellipsis widthLimit={88} title={item.dName2} />
                                                </span>
                                            </td>
                                        )}
                                        {type === 1 && (
                                            <td width={popover ? 150 : 200}>
                                                <span className="s2" style={popover ? { width: '138px' } : {}}>
                                                    <Ellipsis
                                                        widthLimit={138}
                                                        title={typeof item.value2 === 'boolean' ? `${item.value2}` : item.value2}
                                                    />
                                                </span>
                                            </td>
                                        )}
                                    </tr>
                                );
                            })}
                    </tbody>
                </table>
            </div>
        );
    }
}

export default Table;
