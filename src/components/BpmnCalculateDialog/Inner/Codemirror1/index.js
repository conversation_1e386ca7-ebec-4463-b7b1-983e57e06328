/*
 * @Author: liubo
 * @CreatDate: 2019-02-19 18:24:32
 * @Describe: 编辑器组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Tag, Popover, Icon } from 'tntd';
import * as CodeMirror from 'codemirror/lib/codemirror';
// import "codemirror/mode/javascript/javascript";
// import "./JavaScript";
import './defineScript';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/3024-day.css';
import './index.less';

class Codemirror extends PureComponent {
    state = {
        posLeft: 0,
        posTop: 0,
        tipShow: false,
        inputValue: '',
        copyFieldList: [],
        blurFlag: false, // 移除焦点flag
        ctrlPosLeft: 0,
        ctrlPosTop: 0,
        ctrlTipShow: false,
        ctrlInputValue: '',
        copyCtrlList: [],
        name: I18N.codemirror1.index.gengDuo, // 更多
        allFunDesList: [] // 公式大全说明
    };

    componentDidMount() {
        const { code, fieldList, id, ctrlList, readOnly, tagList, height } = this.props;
        let allFunDesList = [];
        tagList &&
            tagList.forEach((item) => {
                if (item.value.includes('#')) {
                    allFunDesList.push(item);
                }
            });
        this.setState({
            copyFieldList: Object.assign([], fieldList),
            copyCtrlList: Object.assign([], ctrlList),
            allFunDesList
        });
        let myTextarea = document.getElementById(id);
        this.CodeMirrorEditor = CodeMirror.fromTextArea(myTextarea, {
            mode: 'defineScript',
            theme: '3024-day',
            lineNumbers: true,
            readOnly: readOnly ? 'nocursor' : false
        });
        this.CodeMirrorEditor.setValue(code);
        this.CodeMirrorEditor.setSize('auto', `${height ? height : 400}px`);
        this.CodeMirrorEditor.on('cursorActivity', (cm) => {
            this.cursorActivity(cm);
        });
        this.CodeMirrorEditor.on('changes', (cm) => {
            if (this.props.onChange) {
                this.props.onChange(cm.getValue());
            }
        });
        // 获取焦点
        this.CodeMirrorEditor.on('focus', (cm) => {
            this.cursorActivity(cm);
            this.setState({ blurFlag: true });
        });
        // 监听鼠标点击，除了编辑器和tag
        document.body.addEventListener('click', this.listenner);
        // 修改codemirror默认键盘事件
        this.CodeMirrorEditor.addKeyMap({
            Up: (cm) => {
                const { tipShow, ctrlTipShow } = this.state;
                if (tipShow) {
                    this.enterFuc('up', '@');
                } else if (ctrlTipShow) {
                    this.enterFuc('up', '#');
                } else {
                    cm.execCommand('goLineUp');
                }
            },
            Down: (cm) => {
                const { tipShow, ctrlTipShow } = this.state;
                if (tipShow) {
                    this.enterFuc('down', '@');
                } else if (ctrlTipShow) {
                    this.enterFuc('down', '#');
                } else {
                    cm.execCommand('goLineDown');
                }
            },
            Enter: (cm) => {
                const { tipShow, ctrlTipShow } = this.state;
                if (tipShow) {
                    this.enterFuc('enter', '@');
                } else if (ctrlTipShow) {
                    this.enterFuc('enter', '#');
                } else {
                    cm.execCommand('newlineAndIndent');
                }
            }
        });
    }

    componentDidUpdate(prevProps) {
        const code = prevProps.code;
        const nextCode = this.props.code;
        if (code !== nextCode) {
            this.CodeMirrorEditor.setValue(nextCode);
        }
        const preHeight = prevProps.height;
        const nextHeight = this.props.height;
        if (preHeight !== nextHeight) {
            const { name } = this.state;
            let dHeight;
            if (name === I18N.codemirror1.index.shouQi && nextHeight !== 400) {
                const quickHeight = document.querySelector('.codemirror-quick-select').clientHeight;
                dHeight = nextHeight - quickHeight + 45;
            } else {
                dHeight = nextHeight;
            }
            this.CodeMirrorEditor.setSize('auto', `${dHeight}px`);
        }
        const preLang = prevProps.globalStore.personalMode.lang;
        const nextLang = this.props.globalStore.personalMode.lang;
        if (preLang !== nextLang) {
            this.setState({
                name: I18N.codemirror1.index.gengDuo // 更多
            });
        }
    }

    componentWillUnmount() {
        document.body.removeEventListener('click', this.listenner);
        delete this.CodeMirrorEditor;
    }

    listenner = (e) => {
        const targetClassName = e.target.className;
        if (typeof targetClassName !== 'string') return;
        const ctrlList = [
            'codemirror-ctrl-tipbox',
            'codemirror-ctrl-tipbox-search',
            'codemirror-ctrl-tipbox-search-input',
            'codemirror-ctrl-tipbox-nodata'
        ];
        const returnCtrlFalse = ctrlList.find((item) => targetClassName.includes(item));
        if (returnCtrlFalse) return false;
        const list = [
            'codemirror-tag',
            'codemirror-tipbox-search-input',
            'codemirror-tipbox-search',
            'codemirror-tipbox',
            'codemirror-tipbox-nodata'
        ];
        const returnFalse = list.find((item) => targetClassName.includes(item));
        if (returnFalse) return false;
        const targetPath = e.path;
        let flag = false;
        targetPath?.forEach((item) => {
            if (item.className) {
                if (typeof item.className !== 'string') return;
                if (item.className.includes('CodeMirror-line') || item.className.includes('CodeMirror-linenumber')) {
                    flag = true;
                }
            }
        });
        if (flag) {
            this.setState({ blurFlag: true });
        } else {
            this.setState({
                blurFlag: false,
                tipShow: false,
                ctrlTipShow: false
            });
        }
        if (targetClassName === 'CodeMirror-scroll') {
            this.setState({ blurFlag: true });
        }
    };

    cursorActivity = (cm) => {
        const { readOnly } = this.props;
        if (readOnly) return;
        const getCursor = cm.getCursor(); // 获取光标位置,返回{line,char}
        const pos = cm.cursorCoords(getCursor); // 光标坐标
        const getLineInfo = cm.getLine(getCursor.line); // 获取当前行的内容
        const cursorBeforeOneChar = getLineInfo.substring(0, getCursor.ch); // 获取光标前的数据
        if (cursorBeforeOneChar.includes('@')) {
            const lastIndex = cursorBeforeOneChar.lastIndexOf('@', getCursor.ch); // 光标前最近一个@位置
            const content = cursorBeforeOneChar.substring(lastIndex + 1, getCursor.ch);
            const { fieldList } = this.props;
            const findObj = fieldList.find((item) => item.name.includes(content));
            if (findObj) {
                this.setState({
                    posLeft: pos.left,
                    posTop: pos.top + 20,
                    tipShow: true,
                    ctrlTipShow: false
                });
                const e = { target: { value: content } };
                this.search(e);
            } else {
                this.setState({
                    tipShow: false,
                    ctrlTipShow: false
                });
            }
        }
        if (cursorBeforeOneChar.includes('#')) {
            const lastIndex = cursorBeforeOneChar.lastIndexOf('#', getCursor.ch); // 光标前最近一个@位置
            const content = cursorBeforeOneChar.substring(lastIndex + 1, getCursor.ch);
            const { ctrlList } = this.props;
            const findObj = ctrlList.find((item) => item.name.includes(content));
            if (findObj) {
                this.setState({
                    ctrlPosLeft: pos.left,
                    ctrlPosTop: pos.top + 20,
                    ctrlTipShow: true,
                    tipShow: false
                });
                const e = { target: { value: content } };
                this.ctrlSearch(e);
            } else {
                this.setState({ ctrlTipShow: false });
            }
        }
        if (!cursorBeforeOneChar.includes('@') && !cursorBeforeOneChar.includes('#')) {
            this.setState({
                tipShow: false,
                ctrlTipShow: false
            });
        }
    };

    // 弹框搜索
    search(e) {
        const { fieldList } = this.props;
        const inputValue = e.target.value;
        let list = [];
        fieldList.forEach((item) => {
            if (item.name.includes(inputValue)) {
                list.push(item);
            }
        });
        this.setState({
            copyFieldList: list
        });
        this.defaultFirst('@');
    }

    // 弹框数据选中
    handleClick(item) {
        const getCursor = this.CodeMirrorEditor.getCursor();
        const getLineInfo = this.CodeMirrorEditor.getLine(getCursor.line); // 获取当前行的内容
        const cursorBeforeOneChar = getLineInfo.substring(0, getCursor.ch); // 获取光标前的数据
        const lastIndex = cursorBeforeOneChar.lastIndexOf('@', getCursor.ch); // 光标前最近一个@位置
        this.CodeMirrorEditor.setSelection({ line: getCursor.line, ch: lastIndex + 1 }, { line: getCursor.line, ch: getCursor.ch }); // 设置替换区域
        this.CodeMirrorEditor.replaceSelection(item.name);
        this.CodeMirrorEditor.setCursor(getCursor.line, lastIndex + 1 + item.name.length);
        this.CodeMirrorEditor.focus();
        this.setState({
            tipShow: false,
            inputValue: ''
        });
    }

    // #弹框搜索
    ctrlSearch(e) {
        const { ctrlList } = this.props;
        const inputValue = e.target.value;
        let list = [];
        ctrlList.forEach((item) => {
            if (item.name.includes(inputValue)) {
                list.push(item);
            }
        });
        this.setState({
            copyCtrlList: list
        });
        this.defaultFirst('#');
    }

    // #弹框选中数据
    handleCtrlClick(item) {
        const getCursor = this.CodeMirrorEditor.getCursor();
        const getLineInfo = this.CodeMirrorEditor.getLine(getCursor.line); // 获取当前行的内容
        const cursorBeforeOneChar = getLineInfo.substring(0, getCursor.ch); // 获取光标前的数据
        const lastIndex = cursorBeforeOneChar.lastIndexOf('#', getCursor.ch); // 光标前最近一个#位置
        this.CodeMirrorEditor.setSelection({ line: getCursor.line, ch: lastIndex + 1 }, { line: getCursor.line, ch: getCursor.ch }); // 设置替换区域
        this.CodeMirrorEditor.replaceSelection(item.value);
        this.CodeMirrorEditor.setCursor(getCursor.line, lastIndex + 1 + item.value.length);
        this.CodeMirrorEditor.focus();
        this.setState({
            ctrlTipShow: false,
            ctrlInputValue: ''
        });
    }

    // 弹框键盘up,down,enter事件
    enterFuc = (type, status) => {
        let findLi = 'cm-field-li';
        let active = 'cm-active';
        if (status === '#') {
            findLi = 'cm-func-li';
            active = 'cm-func-active';
        }
        const nodeList = document.querySelectorAll(`.${findLi}`);
        const length = nodeList.length;
        let index = 0;
        for (let i = 0; i < length; i++) {
            if (nodeList[i].className.includes(active)) {
                index = i;
            }
        }
        if (type === 'up') {
            nodeList[index].className = findLi;
            if (index === 0) {
                nodeList[index].className = `${active} ${findLi}`;
            } else {
                nodeList[index - 1].className = `${active} ${findLi}`;
            }
        } else if (type === 'down') {
            nodeList[index].className = findLi;
            if (index === length - 1) {
                nodeList[index].className = `${active} ${findLi}`;
            } else {
                nodeList[index + 1].className = `${active} ${findLi}`;
            }
        } else if (type === 'enter') {
            const node = document.querySelector(`.${active}`);
            if (status === '#') {
                this.handleCtrlClick({ value: node.attributes.data.value });
                setTimeout(() => {
                    this.setState({
                        ctrlTipShow: false,
                        ctrlInputValue: ''
                    });
                }, 100);
            } else {
                this.handleClick({ name: node.innerText });
                setTimeout(() => {
                    this.setState({
                        tipShow: false,
                        inputValue: ''
                    });
                }, 100);
            }
        }
        document.querySelector(`.${active}`).scrollIntoViewIfNeeded();
    };

    // 弹框默认选择第一个
    defaultFirst = (status) => {
        let findLi = 'cm-field-li';
        let active = 'cm-active';
        if (status === '#') {
            findLi = 'cm-func-li';
            active = 'cm-func-active';
        }
        const nodeList = document.querySelectorAll(`.${findLi}`);
        if (nodeList.length > 0) {
            for (let i = 0; i < nodeList.length; i++) {
                nodeList[i].className = findLi;
            }
            nodeList[0].className = `${active} ${findLi}`;
        }
    };

    // 点击tag
    handleTagClick(item) {
        const { readOnly } = this.props;
        if (readOnly) return;
        const { blurFlag } = this.state;
        const getCursor = this.CodeMirrorEditor.getCursor();
        if (blurFlag) {
            this.CodeMirrorEditor.replaceSelection(item.value);
            this.CodeMirrorEditor.setCursor(getCursor.line, getCursor.ch + item.value.length);
            this.CodeMirrorEditor.focus();
            this.setState({ ctrlTipShow: false });
        }
    }

    // 点击更多
    getMore(name) {
        const { height } = this.props;
        let dHeight;
        if (height !== 400 && name === I18N.codemirror1.index.gengDuo) {
            setTimeout(() => {
                const quickHeight = document.querySelector('.codemirror-quick-select').clientHeight;
                dHeight = height - quickHeight + 45;
                this.CodeMirrorEditor.setSize('auto', `${dHeight}px`);
            }, 100);
        } else {
            dHeight = height;
            this.CodeMirrorEditor.setSize('auto', `${dHeight}px`);
        }

        let turnName;
        if (name === I18N.codemirror1.index.gengDuo) {
            // 更多
            turnName = I18N.codemirror1.index.shouQi; // 收起
        } else {
            turnName = I18N.codemirror1.index.gengDuo; // 更多
        }
        this.setState({
            name: turnName
        });
    }

    render() {
        const { id, globalStore, tagList } = this.props;
        const { lang } = globalStore.personalMode;
        const {
            posLeft,
            posTop,
            tipShow,
            copyFieldList,
            inputValue,
            name,
            ctrlPosLeft,
            ctrlPosTop,
            ctrlTipShow,
            ctrlInputValue,
            copyCtrlList,
            allFunDesList
        } = this.state;

        const desContent = (
            <div className="cm-question-box">
                <div className="title">
                    {/* 函数说明 */}
                    {I18N.codemirror1.index.hanShuShuoMing}
                </div>
                <div className="wrap">
                    <table>
                        <tbody>
                            {allFunDesList &&
                                allFunDesList.map((item, index) => {
                                    return (
                                        <tr key={index}>
                                            <td className="td1">{item.name}</td>
                                            <td className="td2">{item.content}</td>
                                        </tr>
                                    );
                                })}
                        </tbody>
                    </table>
                </div>
            </div>
        );

        return (
            <div className="m-codemirror">
                <div className="codemirror-quick-select" style={{ height: name === I18N.codemirror1.index.gengDuo ? '45px' : 'auto' }}>
                    {tagList &&
                        tagList.length > 0 &&
                        tagList.map((item, index) => {
                            const content = (
                                <div className="codemirror-tip">
                                    <span>{item.content}</span>
                                </div>
                            );
                            return (
                                <Popover key={index} content={content} placement="topRight" overlayClassName="codemirror-math-pop">
                                    <Tag className="codemirror-tag" onClick={() => this.handleTagClick(item)}>
                                        {item.name}
                                    </Tag>
                                </Popover>
                            );
                        })}
                    <div className="cm-more" onClick={() => this.getMore(name)}>
                        {name} <Icon type={name === I18N.codemirror1.index.gengDuo ? 'down' : 'up'} />
                    </div>
                    {name === I18N.codemirror1.index.shouQi && (
                        <Popover content={desContent} placement="left" className="cm-question">
                            <Icon type="question-circle" />
                        </Popover>
                    )}
                </div>
                <textarea id={id} />
                {/* @弹框 */}
                <div
                    className="codemirror-tipbox"
                    style={{
                        left: `${posLeft}px`,
                        top: `${posTop}px`,
                        display: tipShow ? 'inline-block' : 'none'
                    }}>
                    {/* <div className="codemirror-tipbox-search">
                        <input
                            className="codemirror-tipbox-search-input"
                            placeholder="请输入关键词搜索"
                            onChange={(e) => {
                                this.setState({ inputValue: e.target.value });
                                this.search(e);
                            }}
                            value={inputValue}
                        />
                    </div> */}
                    <ul className="cm-field-ul">
                        {copyFieldList &&
                            copyFieldList.length > 0 &&
                            copyFieldList.map((item, index) => {
                                return (
                                    <li
                                        key={index}
                                        className={index === 0 ? 'cm-active cm-field-li' : 'cm-field-li'}
                                        onClick={() => this.handleClick(item)}>
                                        {item.name}
                                    </li>
                                );
                            })}
                    </ul>
                    {!copyFieldList || (copyFieldList.length === 0 && <div className="codemirror-tipbox-nodata">{I18N.codemirror1.index.wuShuJu}</div>)}
                </div>
                {/* ctrl弹框 */}
                <div
                    className="codemirror-tipbox codemirror-ctrl-tipbox"
                    style={{
                        left: `${ctrlPosLeft}px`,
                        top: `${ctrlPosTop}px`,
                        display: ctrlTipShow ? 'inline-block' : 'none'
                    }}>
                    {/* <div className="codemirror-tipbox-search codemirror-ctrl-tipbox-search">
                        <input
                            className="codemirror-tipbox-search-input codemirror-ctrl-tipbox-search-input"
                            placeholder="请输入关键词搜索"
                            onChange={(e) => {
                                this.setState({
                                    ctrlInputValue: e.target.value
                                });
                                this.ctrlSearch(e);
                            }}
                            value={ctrlInputValue}
                        />
                    </div> */}
                    <ul>
                        {copyCtrlList &&
                            copyCtrlList.length > 0 &&
                            copyCtrlList.map((item, index) => {
                                return (
                                    <li
                                        key={index}
                                        className={index === 0 ? 'cm-func-active cm-func-li' : 'cm-func-li'}
                                        onClick={() => this.handleCtrlClick(item)}
                                        data={item.value}
                                        style={item.noShow ? { display: 'none' } : {}}>
                                        {item.name}
                                    </li>
                                );
                            })}
                    </ul>
                    {!copyCtrlList ||
                        (copyCtrlList.length === 0 && (
                            <div className="codemirror-tipbox-nodata codemirror-ctrl-tipbox-nodata">{I18N.codemirror1.index.wuShuJu}</div>
                        ))}
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(Codemirror);
