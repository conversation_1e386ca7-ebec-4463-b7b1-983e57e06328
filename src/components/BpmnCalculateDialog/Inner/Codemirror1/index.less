:global{
    .m-codemirror{
        position: relative;
        overflow: hidden;
        border: 1px solid #C9D2DD;
        .codemirror-tipbox{
            position: fixed;
            left: 0;
            top: 0;
            z-index: 999;
            background: #fff;
            width: 200px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid #ccc;
            font-size: 12px;
            padding: 2px 0;
            .codemirror-tipbox-search{
                padding: 0 10px;
                border-bottom: dashed #ccc 1px;
                input{
                    width: 100%;
                    outline: none;
                    border: none;
                    height: 28px;
                }
            }
            .codemirror-tipbox-nodata{
                text-align: center;
                height: 30px;
                line-height: 30px;
                color: #ccc;
            }
            ul{
                margin: 0;
                padding: 0;
                max-height: 226px;
                overflow: auto;
                li{
                    list-style: none;
                    padding: 5px 10px;
                    cursor: pointer;
                    &:hover{
                        background: #eee;
                    }
                }
                .cm-active{
                    background: #eee;
                }
                .cm-func-active{
                    background: #eee;
                }
            }
        }
        .codemirror-quick-select{
            position: relative;
            background: #fdfdfd;
            padding: 8px 50px 25px 10px;
            border-bottom: #C9D2DD solid 1px;
            .cm-more{
                position: absolute;
                right: 8px;
                top: 12px;
                color: #2196F3;
                cursor: pointer;
                &:hover{
                    color: #3987f8;
                }
            }
            .cm-question{
                position: absolute;
                right: 10px;
                bottom: 12px;
                font-size: 20px;
                cursor: pointer;
            }
        }
        // 重置codemirror主题颜色
        .cm-s-3024-day span.cm-function-keyword{
            color: #03A9F4;
        }
        .cm-s-3024-day span.cm-field-keyword{
            color: #FF9800;
        }
        .cm-s-3024-day span.cm-nomal-keyword{
            color: #F44336;
            border-bottom: #F44336 1px dotted;
        }
        .cm-s-3024-day span.cm-mark-keyword{
            color: #3a3432;
        }
        .cm-s-3024-day span.cm-boolean-keyword{
            color: #673AB7;
        }
        .cm-s-3024-day span.cm-string{
            color: #3a3432;
        }
        // 重置antd样式
        .ant-tag{
            margin: 5px 8px 5px 0;
        }
        .CodeMirror{
            height: 400px;
            transition: all 0.3s;
        }
    }
    // 重置antd样式
    .ant-popover-arrow{
        background-color: #fff;
    }
    .ant-popover-inner{
        .ant-popover-inner-content{
            .codemirror-tip{
                text-align: center;
                min-width: 200px;
                span{
                    display: inline-block;
                    text-align: left;
                }
            }
        }
    }
    .cm-question-box{
        .title{
            font-size: 18px;
            margin-bottom: 5px;
        }
        .wrap{
            max-height: 500px;
            overflow: auto;
            border-top: #ccc solid 1px;
            border-bottom: #ccc solid 1px;
        }
        table{
            td{
                border: #ccc solid 1px;
                font-size: 12px;
                padding: 0;
            }
            tr:first-child{
                td{
                    border-top: none;
                }
            }
            tr:last-child{
                td{
                    border-bottom: none;
                }
            }
            .td1,.td2{
                padding: 7px 12px;
            }
            .td1{
                background: #f4f8ff;
            }
            .td2{
                width: 300px;
            }
        }
    }
    .codemirror-math-pop{
        max-width: 800px;
    }
}