/*
 * @Author: liubo
 * @CreatDate: 2019-02-19 18:24:32
 * @Describe: 编辑器组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Tag, Popover, Icon } from 'tntd';
import './defineScript';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/3024-day.css';
import './index.less';

class Codemirror extends PureComponent {
    state = {
        posLeft: 0,
        posTop: 0,
        tipShow: false,
        inputValue: '',
        copyFieldList: [],
        blurFlag: false, // 移除焦点flag
        ctrlPosLeft: 0,
        ctrlPosTop: 0,
        ctrlTipShow: false,
        ctrlInputValue: '',
        copyCtrlList: [],
        name: I18N.codemirror.index.gengDuo, // 更多
        allFunDesList: [] // 公式大全说明
    };

    componentDidMount() {
        const { tagList } = this.props;
        let allFunDesList = [];
        tagList &&
            tagList.forEach((item) => {
                if (item.value.includes('#')) {
                    allFunDesList.push(item);
                }
            });
        this.setState({
            allFunDesList
        });
    }

    componentDidUpdate(prevProps) {
        const preHeight = prevProps.height;
        const nextHeight = this.props.height;
        if (preHeight !== nextHeight) {
            const { name } = this.state;
            let dHeight;
            if (name === I18N.codemirror.index.shouQi && nextHeight !== 400) {
                const quickHeight = document.querySelector('.codemirror-quick-select').clientHeight;
                dHeight = nextHeight - quickHeight + 45;
            } else {
                dHeight = nextHeight;
            }
            this.CodeMirrorEditor.setSize('auto', `${dHeight}px`);
        }
        const preLang = prevProps.globalStore.personalMode.lang;
        const nextLang = this.props.globalStore.personalMode.lang;
        if (preLang !== nextLang) {
            this.setState({
                name: I18N.codemirror.index.gengDuo // 更多
            });
        }
    }

    // #弹框搜索
    ctrlSearch(e) {
        const { ctrlList } = this.props;
        const inputValue = e.target.value;
        let list = [];
        ctrlList.forEach((item) => {
            if (item.name.includes(inputValue)) {
                list.push(item);
            }
        });
        this.setState({
            copyCtrlList: list
        });
        this.defaultFirst('#');
    }

    // 点击tag
    handleTagClick(item) {
        const { readOnly } = this.props;
        if (readOnly) return;
        let CodeMirrorEditor = this.props.formulaEditor.codeEditor;
        const getCursor = CodeMirrorEditor.getCursor();
        CodeMirrorEditor.replaceSelection(item.value);
        CodeMirrorEditor.setCursor(getCursor.line, getCursor.ch + item.value.length);
        CodeMirrorEditor.focus();
    }

    // 点击更多
    getMore(name) {
        const { height } = this.props;
        let dHeight;
        if (height !== 400 && name === I18N.codemirror.index.gengDuo) {
            setTimeout(() => {
                const quickHeight = document.querySelector('.codemirror-quick-select').clientHeight;
                dHeight = height - quickHeight + 45;
            }, 100);
        } else {
            dHeight = height;
        }

        let turnName;
        if (name === I18N.codemirror.index.gengDuo) {
            // 更多
            turnName = I18N.codemirror.index.shouQi; // 收起
        } else {
            turnName = I18N.codemirror.index.gengDuo; // 更多
        }
        this.setState({
            name: turnName
        });
    }

    render() {
        const { tagList } = this.props;
        const { posLeft, posTop, tipShow, copyFieldList, name, ctrlPosLeft, ctrlPosTop, ctrlTipShow, copyCtrlList, allFunDesList } =
            this.state;

        const desContent = (
            <div className="cm-question-box">
                <div className="title">
                    {/* 函数说明 */}
                    {I18N.codemirror.index.hanShuShuoMing}
                </div>
                <div className="wrap">
                    <table>
                        <tbody>
                            {allFunDesList &&
                                allFunDesList.map((item, index) => {
                                    return (
                                        <tr key={index}>
                                            <td className="td1">{item.name}</td>
                                            <td className="td2">{item.content}</td>
                                        </tr>
                                    );
                                })}
                        </tbody>
                    </table>
                </div>
            </div>
        );

        return (
            <div className="m-codemirror">
                <div className="codemirror-quick-select" style={{ height: name === I18N.codemirror.index.gengDuo ? '45px' : 'auto' }}>
                    {tagList &&
                        tagList.length > 0 &&
                        tagList.map((item, index) => {
                            const content = (
                                <div className="codemirror-tip">
                                    <span>{item.content}</span>
                                </div>
                            );
                            return (
                                <Popover key={index} content={content} placement="topRight" overlayClassName="codemirror-math-pop">
                                    <Tag className="codemirror-tag" onClick={() => this.handleTagClick(item)}>
                                        {item.name}
                                    </Tag>
                                </Popover>
                            );
                        })}
                    <div className="cm-more" onClick={() => this.getMore(name)}>
                        {name} <Icon type={name === I18N.codemirror.index.gengDuo ? 'down' : 'up'} />
                    </div>
                    {name === I18N.codemirror.index.shouQi && (
                        <Popover content={desContent} placement="left" className="cm-question">
                            <Icon type="question-circle" />
                        </Popover>
                    )}
                </div>
                {/* <textarea id={id} /> */}
                {/* @弹框 */}
                <div
                    className="codemirror-tipbox"
                    style={{
                        left: `${posLeft}px`,
                        top: `${posTop}px`,
                        display: tipShow ? 'inline-block' : 'none'
                    }}>
                    <ul className="cm-field-ul">
                        {copyFieldList &&
                            copyFieldList.length > 0 &&
                            copyFieldList.map((item, index) => {
                                return (
                                    <li
                                        key={index}
                                        className={index === 0 ? 'cm-active cm-field-li' : 'cm-field-li'}
                                        onClick={() => this.handleClick(item)}>
                                        {item.name}
                                    </li>
                                );
                            })}
                    </ul>
                    {!copyFieldList || (copyFieldList.length === 0 && <div className="codemirror-tipbox-nodata">{I18N.codemirror.index.wuShuJu}</div>)}
                </div>
                {/* ctrl弹框 */}
                <div
                    className="codemirror-tipbox codemirror-ctrl-tipbox"
                    style={{
                        left: `${ctrlPosLeft}px`,
                        top: `${ctrlPosTop}px`,
                        display: ctrlTipShow ? 'inline-block' : 'none'
                    }}>
                    <ul>
                        {copyCtrlList &&
                            copyCtrlList.length > 0 &&
                            copyCtrlList.map((item, index) => {
                                return (
                                    <li
                                        key={index}
                                        className={index === 0 ? 'cm-func-active cm-func-li' : 'cm-func-li'}
                                        onClick={() => this.handleCtrlClick(item)}
                                        data={item.value}
                                        style={item.noShow ? { display: 'none' } : {}}>
                                        {item.name}
                                    </li>
                                );
                            })}
                    </ul>
                    {!copyCtrlList ||
                        (copyCtrlList.length === 0 && (
                            <div className="codemirror-tipbox-nodata codemirror-ctrl-tipbox-nodata">{I18N.codemirror.index.wuShuJu}</div>
                        ))}
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(Codemirror);
