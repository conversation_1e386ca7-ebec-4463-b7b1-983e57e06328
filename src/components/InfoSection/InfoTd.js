import { Ellipsis } from 'tntd';

export default (props) => {
    const { className, label, value, rate, vStyle = {} } = props;
    let labelStyle = {};
    let valueStyle = { ...vStyle };
    if (rate) {
        labelStyle = { width: rate.labelWidth };
        valueStyle = { width: rate.valueWidth };
    }
    return (
        <div className={`item ${className || ''}`}>
            <div className="label" style={labelStyle}>
                <Ellipsis title={label} widthLimit={labelStyle.width || 110} />
            </div>
            <div className="value" style={valueStyle}>
                <Ellipsis title={value} widthLimit={valueStyle.width || 170} />
            </div>
        </div>
    );
};
