.info-container {
    padding-left: 16px;
    padding-right: 16px;

    .title {
        margin-bottom: 10px;
        padding-left: 12px;
        font-size: 14px;
        color: #17233d;
        font-weight: bold;
        position: relative;

        &::after {
            content: "";
            position: absolute;
            background: #126bfb;
            left: 0;
            // top: 4px;
            height: 14px;
            width: 4px;
        }
    }

    .cols {
        display: flex;
        flex-wrap: wrap;
        border-left: 1px solid #e1e6ee;
        border-right: 1px solid #e1e6ee;
        &.cols-1{
            .item{
                width: 100%;
                .label{
                    width: 200px;
                    .tnt-ellipsis {
                        max-width: 95% !important;
                    }
                }
                .value{
                    width: calc(100% - 200px)
                }
            }
        }
        &.cols-2 {
            .item {
                width: 50%;
                &.item-full {
                    width: 100%;
                    .tnt-ellipsis {
                        max-width: 90% !important;
                    }
                }
            }
        }
        &.cols-3 {
            .item {
                width: 33.3333%;
                &.item-full {
                    width: 100%;
                }
            }
            .last-one {
                width: 100%;
            }
            .last-two {
                width: 66.6666%;
            }
        }
        .item {
            min-height: 40px;
            display: flex;
            align-items: stretch;
            background: #fff;
            font-size: 14px;
            color: #17233d;
            border: 1px solid #e1e6ee;
            margin-top: -1px;
            border-left: 0 solid #e1e6ee;
            border-right: 0 solid #e1e6ee;

            .label {
                width: 120px;
                background: rgba(225, 230, 238, 0.4);
                text-align: right;
                min-height: 40px;
                padding-right: 10px;
                padding-top: 4px;
                padding-bottom: 4px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }

            .value {
                width: calc(100% - 120px);
                white-space: pre-wrap;
                word-break: break-all;
                padding: 10px 20px;
                display: flex;
                align-items: center;
            }
            &.rate {
                .label {
                    width: 36%;
                }

                .value {
                    // width: 97%%;
                }
            }
        }
    }
}
