import I18N from '@/utils/I18N';
import { Tooltip, Icon } from 'tntd';
import './index.less';

import { TYPE_MAP } from '@/constants';

let params = {
    field: I18N.tdtag.index.ziDuan,
    realtime: I18N.tdtag.index.shiShi,
    offline: I18N.tdtag.index.liXian,
    ruleSet: I18N.tdtag.index.guiZeJi
};

const Tag = (props) => {
    let { data = {}, showSourceName = true, type = 'field', ...rest } = props;
    let showImg = false;
    let filedType;
    let dataTypeObj = {};
    let status = data?.status || data?.ruleSetStatus;
    data?.metricArea && data.metricArea === 'EDIT' && (showImg = true); // 指标显示导入待提交
    status === 'import_wait_commit' && (showImg = true); // 规则集显示导入待提交

    let sourceName = `[${params[data?.sourceName]}]`; // 字段 和 指标显示类型

    if (type === 'field') {
        filedType = data.dataType || data.datatype || data.type || '';
        dataTypeObj = TYPE_MAP[filedType.toUpperCase()] ? TYPE_MAP[filedType.toUpperCase()] : {}; // 字段和指标的类型和颜色
    }

    return (
        <div className="td-tag" {...rest}>
            {type === 'field' && <Icon type={dataTypeObj?.icon} style={{ color: dataTypeObj?.color }} />}
            {showImg && (
                <Tooltip title={I18N.tdtag.index.daoRuDaiTiJiao} placement="top">
                    <img src={require('./imgs/import_export.svg')} className="tag-img" />
                </Tooltip>
            )}
            {showSourceName && type === 'field' && sourceName}
        </div>
    );
};

export default Tag;
