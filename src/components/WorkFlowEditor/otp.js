import { createOtp } from '@/utils/I18N';
export default createOtp({
    cn: {
        StartFlowNodeName: '开始',
        EndFlowNodeName: '结束',
        ExclusiveGatewayStartName: '判断开始',
        ExclusiveGatewayEndName: '判断结束',
        ParallelGatewayStartName: '并行开始',
        ParallelGatewayEndName: '并行结束'
    },

    en: {
        StartFlowNodeName: 'Start',
        EndFlowNodeName: 'End',
        ExclusiveGatewayStartName: 'Start Judgment',
        ExclusiveGatewayEndName: 'Judgment End',
        ParallelGatewayStartName: 'Start Parallel',
        ParallelGatewayEndName: 'End of parallelism'
    }
});
