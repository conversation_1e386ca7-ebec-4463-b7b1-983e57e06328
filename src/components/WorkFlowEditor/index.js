import I18N from '@/utils/I18N';
import React, { useRef, useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { message, Tooltip, Row, Handle } from 'tntd';
import MMEditor from './MMEditor/MMEditor';
import TopBar from './Content/TopBar';
import LeftBar from './Content/LeftBar';
import initShapes, { sliceName } from './MMShapes/initShapes';
import DefaultDataConvert from './DefaultDataConvert';
import DialogHandle from './DialogHandle';
import './index.less';

export { sliceName };
export default forwardRef((props, ref) => {
    const editorWrapRef = useRef();
    const editorDomRef = useRef();
    const editorRef = useRef();
    const dialogHandleRef = useRef();
    const operaHandleRef = useRef();
    const [toolTipVisible, setToolTipVisible] = useState();
    const [toolTipInfo, setToolTipInfo] = useState();
    const [dialogShowInfo, setDialogShowInfo] = useState(null);
    const [initReady, setInitReady] = useState(false);
    const [nodeOpera, setNodeOpera] = useState(false);
    const [nodeOperaInfo, setNodeOperaInfo] = useState();
    const {
        type,
        graphData,
        flowNodesDict = [],
        auditedNodes = [],
        className,
        showMiniMap = true,
        DataConvert,
        toolTipNameHandle,
        dialogHide,
        showType,
        dialogDom = [],
        editorStyle,
        onRef,
        checkLineExtendFn,
        showLengend,
        LengendDom,
        autoDiffAuditNodes = true
    } = props;
    const previewMode = type === 'view';

    const auditedNodesPre = useRef(auditedNodes);

    useImperativeHandle(ref, () => ({
        updateGraph: setGraphData,
        editor: editorRef?.current
    }));

    const checkNewLine = (data, editor) => {
        const {
            graph: {
                node: { nodes }
            },
            schema
        } = editor;
        const { from, to } = data;
        // 通组件输入输出不能连接
        if (from === to) return false;
        const fromNode = nodes[from];
        const toNode = nodes[to];
        const {
            data: { type: fromType, name: fromName, nodeType: fromNodeType },
            fromLines: sourceFromLines,
            toLines: sourceToLines
        } = fromNode || {};
        const {
            data: { type: toType, name: toName },
            toLines: targetToLines,
            fromLines: targetFromLines
        } = toNode || {};

        const fromData = schema.data.nodesMap[from];

        if (['StartFlowNode'].includes(toType) && targetToLines && targetToLines.size) {
            message.error(toName + I18N.workfloweditor.index.buNengSheZhiShu2);
            return false;
        }
        // 不能设置输出流
        if (['EndFlowNode'].includes(fromType) && sourceFromLines && sourceFromLines.size) {
            message.error(fromName + I18N.workfloweditor.index.buNengSheZhiShu);
            return false;
        }
        let [oneCome, oneOut] = [false, false];
        if (fromType === 'ExclusiveGateway') {
            if (fromNodeType === 'start') {
                oneCome = true;
            }
            if (fromNodeType === 'end') {
                oneOut = true;
            }
        }

        if (fromType === 'ParallelGateway') {
            if (fromNodeType === 'start') {
                oneCome = true;
            }
            if (fromNodeType === 'end') {
                oneOut = true;
            }
        }
        if (['EndFlowNode'].includes(toType)) {
            oneCome = true;
        }

        if (['StartFlowNode'].includes(fromType)) {
            oneOut = true;
        }

        if (['ParallelGateway'].includes(fromType) && fromData?.data?.type === 'end') {
            oneOut = true;
        }

        // // 只能一个输入
        // if (oneCome && sourceFromLines.size === 1 && !sourceFromLines.has(data.uuid)) {
        //     message.error(fromName + '只能有一个输入'); // 输出流只能有一个
        //     return false;
        // }

        // 开始 只能有一个输出
        if (oneOut && sourceToLines.size === 1 && !sourceToLines.has(data.uuid)) {
            message.error(fromName + I18N.workfloweditor.index.zhiNengYouYiGe); // 输出流只能有一个
            return false;
        }

        data.data = {};
        // 如果是组件判断和冠军，则标红
        if (['ExclusiveGateway', 'RouteServiceNode'].includes(fromData.type)) {
            if (fromData.type === 'ExclusiveGateway' && fromData?.data?.type === 'end') {
                return true;
            }
            data.required = true;
            data.className = 'red-line';
            data.data.type = fromData.type;
        }
        checkLineExtendFn && checkLineExtendFn({ data, editor });
        return true;
    };

    useEffect(() => {
        const resizeBound = () => {
            const { height: jobEditorHei, width: jobEditorWid } =
                (editorWrapRef && editorWrapRef.current && editorWrapRef.current.getBoundingClientRect()) || {};
            if (jobEditorHei && editorDomRef) {
                editorDomRef.current.style.height = jobEditorHei - (!previewMode ? 48 : 0) + 'px';
                editorDomRef.current.style.width = jobEditorWid - (!previewMode ? 160 : 0) + 'px';
            }
            if (editorRef.current) {
                editorRef.current.controller.autoFit();
            }
        };
        const init = async () => {
            dialogHandleRef.current = new DialogHandle(showType);
            resizeBound();
            editorRef.current = new MMEditor({
                dom: editorDomRef.current,
                showMiniMap,
                mode: previewMode ? 'view' : 'edit' // 只读模式设置 mode:"view"
            });
            // 注册节点
            initShapes(editorRef.current, flowNodesDict);
            if (graphData) {
                await setGraphData(graphData);
            }

            // 连线时校验
            if (editorRef.current?.graph?.line?.shapes['default']) {
                editorRef.current.graph.line.shapes['default'].checkNewLine = checkNewLine;
            } else {
                return;
            }

            // 注册节点⌚️
            addEditorEvent();

            onRef && onRef(this);

            setInitReady(true);
        };
        init();
        window.addEventListener('resize', resizeBound);
        operaHandleRef.current.addEventListener('mouseenter', showOperaEvent);
        operaHandleRef.current.addEventListener('mouseleave', hideOperaEvent);
        return () => {
            if (editorRef.current) {
                editorRef.current.graph.clearGraph();
                editorRef.current.destroy();
                editorRef.current = null;
            }
            setInitReady(false);
            window.removeEventListener('resize', resizeBound);
            operaHandleRef.current.removeEventListener('mouseenter', showOperaEvent);
            operaHandleRef.current.removeEventListener('mouseleave', hideOperaEvent);
        };
    }, []);

    const showOperaEvent = () => {
        setNodeOpera(true);
    };
    const hideOperaEvent = () => {
        setNodeOpera(false);
    };

    const setGraphData = async (data) => {
        try {
            const dataFormatted = typeof data === 'object' ? data : JSON.parse(data || '{}');
            let convertFun = DefaultDataConvert;
            if (DataConvert) {
                convertFun = DataConvert;
            }
            await editorRef.current.schema.setInitData(convertFun?.convert(dataFormatted, editorRef.current));
            await editorRef.current.controller.autoFit();
            runFlow();
        } catch (e) {
            message.error(I18N.workfloweditor.index.jieXiShuJuCuo + e?.message);
        }
    };

    useEffect(() => {
        if (editorRef.current && initReady) {
            setGraphData(graphData);
        }
    }, [graphData, initReady]);

    useEffect(() => {
        if (
            autoDiffAuditNodes &&
            editorRef.current &&
            initReady &&
            JSON.stringify(auditedNodes) !== JSON.stringify(auditedNodesPre?.current)
        ) {
            setGraphData(graphData);
            auditedNodesPre.current = auditedNodes;
        }
    }, [graphData, auditedNodes, autoDiffAuditNodes]);

    // 初始化编辑器事件
    const addEditorEvent = () => {
        let timeStamp;
        // 选中
        editorRef?.current?.graph.on('node:click', ({ node }) => {
            document.getElementsByClassName('lb-workflow-header')[0]?.children[1]?.blur();
            const now = new Date().getTime();
            if (now - timeStamp < 300) {
                // 产品说这个情况下就不用弹窗
                !dialogHide &&
                    dialogHandleRef.current.show(node, editorRef?.current, (data) => {
                        setDialogShowInfo(data);
                    });
            }
            timeStamp = now;
            props.onNodeClick && props.onNodeClick(node.data);
        });

        // 没有选中
        editorRef?.current.graph.on('node:mouseenter', ({ node }) => {
            const bbox = node.node.getBoundingClientRect();
            const handleNums = operaHandleRef.current?.querySelector('.editor-node-opera-wrap');
            setToolTipVisible(true);
            setToolTipInfo({
                nowNode: node,
                nowTextNode: toolTipNameHandle ? toolTipNameHandle(node.data) : node.data,
                textVisible: true,
                textX: bbox.x + bbox.width / 2,
                textY: bbox.y - (handleNums?.children?.length ? 30 / 2 : -5)
            });

            showOperaEvent();
            setNodeOperaInfo({
                x: bbox.x + bbox.width / 2 - operaHandleRef?.current?.clientWidth / 2,
                y: bbox.y - operaHandleRef?.current?.clientHeight + 6
            });
        });

        editorRef?.current.graph.on('node:mouseleave', () => {
            setToolTipVisible(false);
            hideOperaEvent();
        });

        // 节点删除事件
        editorRef?.current.graph.on('node:remove', () => {
            setToolTipVisible(false);
            hideOperaEvent();
        });

        // 选中
        editorRef.current.graph.on('line:click', ({ line, event }) => {
            document.getElementsByClassName('lb-workflow-header')[0]?.children[1]?.blur();
            const now = new Date().getTime();
            if (now - timeStamp < 300) {
                const { data } = line;
                const fromNode = editorRef.current.graph.node.nodes[data.from];
                if (fromNode.data.data.type === 'end') {
                    return;
                }
                data.fromType = fromNode.data.type + 'Line';
                !dialogHide &&
                    dialogHandleRef.current.show(line, editorRef?.current, (data) => {
                        setDialogShowInfo(data);
                    });
            }
            timeStamp = now;

            if (['mm-line-opera-edit', 'mm-line-opera-view'].includes(event.target.className)) {
                document.getElementsByClassName('lb-workflow-header')[0]?.children[1]?.blur();
                const { data } = line;
                const fromNode = editorRef.current.graph.node.nodes[data.from];
                if (fromNode.data.data.type === 'end') {
                    return;
                }
                data.fromType = fromNode.data.type + 'Line';
                !dialogHide &&
                    dialogHandleRef.current.show(line, editorRef?.current, (data) => {
                        setDialogShowInfo(data);
                    });
            }
            if (event.target.className === 'mm-line-opera-del') {
                editorRef.current.graph.line.deleteLine(line, true);
            }
        });
    };

    // 动画效果
    const runFlow = async () => {
        if (!auditedNodes?.length) {
            return;
        }
        const { graph } = editorRef?.current || {};
        const {
            node: { nodes },
            line: { lines }
        } = graph || {};
        const [hasAuditedNodeUuids, auditedLine] = [[], []];
        auditedNodes.forEach((node) => {
            hasAuditedNodeUuids.push(node?.uuid);
        });
        auditedNodes.forEach((hasAudited) => {
            const { uuid } = hasAudited || {};
            const status = `instance ${hasAudited.status || ''}`;
            Object.values(nodes).forEach((node) => {
                if (node.data.uuid === uuid) {
                    node.data.className = `${node.data.className || ''} ${status}`;
                    node.addClass(status);
                }
            });

            for (let key in lines) {
                const line = lines[key];
                if (uuid === line.data.to && hasAuditedNodeUuids.indexOf(line.data.from) > -1) {
                    line.data.className = `${line.data.className || ''}  ${status}`;
                    line.addClass(status);
                    auditedLine.push(line.data.uuid);
                }
            }
        });

        for (let key in nodes) {
            if (!hasAuditedNodeUuids.includes(key)) {
                const node = nodes[key];
                node.data.className = `${node.data.className || ''} unrun`;
                node.addClass('unrun');
            }
        }

        for (let key in lines) {
            if (!auditedLine.includes(key)) {
                const line = lines[key];
                line.data.className = `${line.data.className || ''} unrun`;
                line.addClass('unrun');
            }
        }
    };

    const editNode = () => {
        const node = toolTipInfo?.nowNode;
        document.getElementsByClassName('lb-workflow-header')[0]?.children[1]?.blur();
        // 产品说这个情况下就不用弹窗
        !dialogHide &&
            dialogHandleRef.current.show(node, editorRef?.current, (data) => {
                setDialogShowInfo(data);
            });
        props.onNodeClick && props.onNodeClick(node.data);
    };

    const delNode = () => {
        const node = toolTipInfo?.nowNode;
        editorRef.current.graph.node.deleteNode(node);
        const { fromLines, toLines } = node || {};
        for (let line of fromLines) {
            editorRef.current.graph.line.deleteLine(line);
        }
        for (let line of toLines) {
            editorRef.current.graph.line.deleteLine(line);
        }
        editorRef.current.graph.fire('delete', { deleteKeys: node.data.uuid });
    };

    // 目标放置
    const onDrop = (item, e) => {
        // 增加节点
        const dom = editorRef?.current?.dom.node;
        const name = item?.initName || item?.name;
        const { size = [] } = item || {};
        const transform = editorRef?.current.paper.transform();
        const info = transform.globalMatrix.split();
        const bbox = dom.getBoundingClientRect();
        if (e.clientX - bbox.x < 0 || e.clientY - bbox.y < 0) return;
        const x = (e.clientX - bbox.x - info.dx) / info.scalex - (size[0] / 2) * info.scalex;
        const y = (e.clientY - bbox.y - info.dy) / info.scalex - (size[1] / 2) * info.scalex;
        if (['RouteServiceNode'].includes(item?.type)) {
            if (item.data) {
                item.data.name = I18N.workfloweditor.index.zhiNengLuYou;
            } else {
                item.data = {
                    name: I18N.workfloweditor.index.zhiNengLuYou
                };
            }
        }
        editorRef?.current?.graph.node.addNode(
            Object.assign({}, item, {
                type: item?.type,
                data: item?.data || {},
                name,
                x,
                y
            })
        );
    };

    return (
        <div ref={editorWrapRef} className={`job-editor ${className || ''}`} {...editorStyle}>
            {!previewMode && initReady && editorRef?.current && <LeftBar {...props} editor={editorRef.current} onDrop={onDrop} />}
            <div className="job-content flow-editor-content">
                {initReady && !!editorRef?.current && <TopBar {...props} previewMode={previewMode} editor={editorRef.current} />}
                <div className="job-mm-editor" ref={editorDomRef} />
                {!!showLengend &&
                    (LengendDom || (
                        <Row type="flex" className="mm-lengend">
                            <span className="success">
                                <i />
                                {I18N.workfloweditor.index.yunXingWanCheng}
                            </span>
                            <span className="running">
                                <i />
                                {I18N.workfloweditor.index.yunXingZhong}
                            </span>
                            <span className="fail">
                                <i />
                                {I18N.workfloweditor.index.yunXingShiBai}
                            </span>
                        </Row>
                    ))}
            </div>

            {/* 节点hover展示 */}
            <div
                style={{
                    position: 'fixed',
                    left: toolTipVisible && toolTipInfo?.textX,
                    top: toolTipVisible && toolTipInfo?.textY - 10,
                    display: toolTipVisible && toolTipInfo?.textVisible ? 'block' : 'none'
                }}>
                <Tooltip visible={true} title={`${toolTipInfo?.nowTextNode?.name}`} />
            </div>

            <div
                ref={operaHandleRef}
                style={{
                    visibility: nodeOpera ? 'visible' : 'hidden',
                    position: 'fixed',
                    height: '28px',
                    left: nodeOpera && nodeOperaInfo?.x,
                    top: nodeOpera && nodeOperaInfo?.y - 6.5,
                    border: 'none'
                }}>
                <Handle className="editor-node-opera-wrap" lang="cn">
                    {!['StartFlowNode', 'EndFlowNode', 'RouteServiceNode'].includes(toolTipInfo?.nowTextNode?.type) && !dialogHide && (
                        <span
                            key="opera"
                            className={`editor-node-opera ${!previewMode ? 'node-opera-edit' : 'node-opera-view'}`}
                            onClick={editNode}
                        />
                    )}
                    {!previewMode && <span key="del" className="editor-node-opera node-opera-del" onClick={delNode} />}
                </Handle>
            </div>

            {dialogDom?.map((dialog) => {
                return React.cloneElement(dialog, {
                    ...props,
                    dialogShowInfo,
                    disabled: previewMode,
                    editor: editorRef?.current,
                    onCancel: () => {
                        setDialogShowInfo(null);
                    }
                });
            })}
        </div>
    );
});
