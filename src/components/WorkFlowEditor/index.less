@import url(./Fonts/iconfont.css);
@hover-back: #e9eefa;
@back-color: #f7f8fa;
@font-2-color: rgba(23, 35, 61, 0.8);
@font-2-color-hover: rgba(23, 35, 61, 0.5);
@font-3-color: #546470;

.job-editor {
  display: flex;
  width: 100%;
  height: 600px;
  overflow: hidden;
  background: #fff;
  user-select: none;
  svg text {
    font-size: 12px;
    font-family: Robot;
    text-anchor: middle;
    dominant-baseline: middle;
  }
  .job-left-bar {
    z-index: 2;
    width: 140px;
    overflow-y: auto;
    background: #fff;
    border-left: 1px solid #f0f0f0;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
    .item-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      padding-top: 55px;
      overflow: auto;
    }
    .drag-item {
      position: fixed;
      z-index: 10;
      display: none;
      font-size: 12px;
      cursor: move;
      pointer-events: none;
    }
  }
  .job-content {
    position: relative;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .flow-btn-wrap {
      position: absolute;
      top: 10px;
      right: 16px;
      z-index: 9;
    }
    .job-top-bar {
      width: 100%;
      height: 48x;
      .flow-editor-hd {
        width: 100%;
        height: 48px;
        padding: 0 16px;
        font-size: 12px;
        line-height: 48px;
        line-height: 48px;
        border-bottom: 1px solid #e8e8e8;
        .command-item {
          position: relative;
          float: left;
          margin-right: 8px;
          cursor: pointer;
          user-select: none;
          i {
            margin-right: 2px;
            font-size: 12px;
          }
          &:hover {
            color: #2f80f7;
          }
          &.disable {
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
          }
        }
      }
    }
    .job-mm-editor {
      flex-grow: 1;
      width: 100%;
      height: calc(100% - 48px);
      .mm-minimap {
        top: auto;
        bottom: 10px;
        height: 127px;
        border: 1px solid #e9edf3;
        border-radius: 0;
      }
      .mm-node {
        position: relative;
        &.error,
        &.fail {
          .flow-icon-node {
            // fill: #fff;
            stroke: red;
          }
        }
        &.success {
          .flow-icon-node {
            stroke: #4ee192;
          }
        }
        &.unrun {
          .mm-node-shape {
            .flow-txt-node {
              fill: #999;
            }
            circle {
              fill: #babdc5;
              & + .flow-txt-node {
                fill: #fff;
              }
            }
          }
        }

        .task-status {
          display: block;
          width: 14px;
          height: 14px;
          border-radius: 100%;
        }
        &.success {
          .task-status {
            background-image: url(./Images/success.svg);
          }
        }
        &.fail {
          .task-status {
            background-image: url(./Images/fail.svg);
          }
        }
        &.running {
          .task-status {
            background-image: url(./Images/running.svg);
          }
        }
      }
      .mm-line {
            path{
                stroke: #B2BECD;
                pointer-events:visibleStroke !important;
            }
            .mm-line-arrow{
                fill:#B2BECD;
            }
            &.success path {
                stroke: green !important;
            }
            &.error path {
                stroke: red !important;
            }
            &.running {
                path {
                    stroke: #4c79ff !important;
                }
                .mm-line-arrow {
                    animation: shink 1s ease-in-out infinite alternate;
                }
            }

            // 修改样式
            &.red-line{
                .mm-line-opera{
                    display: block !important;
                }
                .mm-line-shape{
                    path{
                        stroke-dasharray: 4px !important;
                    }
                }
            }
            &:hover,&.active{
                opacity: 1 !important;
                .mm-line-opera{
                    display: block !important;
                }
                .mm-line-shape{
                    opacity: 1 !important;
                    stroke-width: 2px !important;
                    >path, text{
                        opacity: 0.5;
                    }
                }
            }
            &.red-line:not(:hover){
                &:not(.active) {
                    .mm-line-shape > path {
                        stroke: #B1BDCC;
                    }
                }
            }
            g.mm-line-oper{
                position: relative;
            }
            .mm-line-opera{
                white-space: nowrap;
                padding: 2px 3px;
                width: 100%;
                height: 100%;
                display: none;

                &.opera-handle{
                    >.mm-line-opera-content{
                        > i:not(:last-of-type){
                            &::after{
                                content:"";
                                position: absolute;
                                height: 12px;
                                width: 1px;
                                left: 19px;
                                top: 2px;
                                background-color: #E1E6EE;
                            }
                        }
                    }
                }
                .mm-line-opera-content{
                    height: 18px;
                    display: block;
                    background-color: #fff;
                    i{
                        display: inline-block;
                        background-size: cover;
                        background-repeat: no-repeat;
                        width: 16px;
                        height:16px;
                        position: relative;
                        &.mm-line-opera-edit{
                            background-image: url(./Images/edit.svg);
                            margin-right:6px;
                        }
                        &.mm-line-opera-del{
                            background-image: url(./Images/del.svg);
                        }
                        &.mm-line-opera-view{
                            background-image: url(./Images/view.svg);
                        }
                    }
                }

            }
        }
    }
    .mm-lengend {
      position: absolute;
      right: 16px;
      bottom: 20px;
      span {
        display: flex;
        align-items: center;
        margin-left: 16px;
        color: #454f64;
        font-size: 12px;
        i {
          display: inline-block;
          width: 14px;
          height: 14px;
          margin-right: 4px;
          background-repeat: no-repeat;
        }
      }
      .success {
        i {
          background-image: url(./Images/success.svg);
        }
      }
      .running {
        i {
          background-image: url(./Images/running.svg);
        }
      }
      .fail {
        i {
          background-image: url(./Images/fail.svg);
        }
      }
    }
  }
}
.editor-node-opera-wrap{
    white-space: nowrap;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding:2px 4px;
    .editor-node-opera{
        width:16px;
        height: 16px;
        background-repeat: no-repeat;
        background-size: cover;
        display: inline-block;
        cursor: pointer;
        &.node-opera-edit{
            background-image: url(./Images/edit.svg);
        }
        &.node-opera-view{
            background-image: url(./Images/view.svg);
        }
        &.node-opera-del{
            background-image: url(./Images/del.svg);
        }
    }
}

@keyframes dashing {
  from {
    stroke-dashoffset: 200;
  }
  to {
    stroke-dashoffset: 0;
  }
}
