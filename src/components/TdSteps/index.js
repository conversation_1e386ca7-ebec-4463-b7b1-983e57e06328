import React, { Component } from 'react';
import classNames from 'classnames';

import './index.less';
import Step from './Step';

class TdSteps extends Component {
    static Step = Step;
    static defaultProps = {
        current: 0,
        prefixCls: 'td-steps'
    };

    render() {
        const {
            current = 0,
            children,
            prefixCls,
            style = {},
            className
        } = this.props;

        const classStr = classNames(prefixCls, className);
        const filteredChildren = React.Children.toArray(children).filter(child => !!child);
        const childrenList = React.Children.map(filteredChildren, (child, index) => {
            if (!child) return null;
            const childProps = {
                prefixCls,
                ...child.props
            };
            if (index === current) {
                childProps.status = 'process';
            } else {
                childProps.status = 'wait';
            }
            // } else if (index > current) {
            //     childProps.status = 'wait';
            // } else {
            //     childProps.status = 'finish';
            // }

            return React.cloneElement(child, childProps);
        });

        return (
            <div
                className={classStr}
                style={style}
            >
                {childrenList}
            </div>
        );
    }
}

export default TdSteps;
