// .td-steps {
//     padding-top: 0;
//     &.ant-steps-small.ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item {
//         margin-right: 20px;
//     }
//     .ant-steps-item {
//         height: 36px;
//         line-height: 36px;
//         &-process {
//             background-color: #126BFB;
//             color: #fff;
//         }
//         &-wait {
//             background-color: #E9EBEF;
//             color: #17233D ;
//         }
//         &-finish {
//             background-color: #C6DBFC ;
//             color: #17233D ;
//         }
//         .ant-steps-item-icon {
//             display: none;
//         }
//     }
// }
@stepsPrefixClass: ~"td-steps";
@processColor: #fff;
@normalColor: #17233D;
@processBgColor: #126BFB;
@waitBgColor: #E9EBEF;
@finishBgColor: #C6DBFC;

.@{stepsPrefixClass} {
    display: flex;
    width: 100%;

    &-item {
        position: relative;
        flex: 1;
        overflow: hidden;
        cursor: pointer;
        &:first-child {
            &::before {
                display: none;
            }
        }
        &:before {
            content: "";
            position: absolute;
            left: 0;
            width: 0;
            height: 0;
            display: block;
            border-left: 20px solid @processBgColor;
            border-top: 18px solid transparent;
            border-bottom: 18px solid transparent;
        }
    }

    &-item-content {
        font-size: 14px;
        color: @normalColor;
        line-height: 36px;
        text-align: center;
    }

    &-item-finish {
        background: @finishBgColor;
    }
    &-item-process {
        background: @processBgColor;
        .@{stepsPrefixClass}-item-content {
            color: @processColor;
        }
    }

    &-item-wait {
        background: @waitBgColor;
    }

    &-item-process + &-item-wait {
        &:before {
            border-left-color: @processBgColor;
        }
    }
    &-item-finish + &-item-process {
        &::before {
            border-left-color: @finishBgColor;
        }
    }
    &-item-wait + &-item-wait {
        &:before {
            border-left-color: @waitBgColor;
        }
    }
    &-item-finish + &-item-finish {
        &::before {
            border-left-color: @finishBgColor;
        }
    }
    &-item-wait + &-item-process {
        &:before {
            border-left-color: @waitBgColor;
        }
    }
}