/*
 * @CreatDate: 2020-12-24 10:20:30
 * @Describe: 简单封装table页面，模块配置化，集成权限校验
 */

import './index.less';
import { useEffect, useState, useRef } from 'react';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { QueryListScene, Handle } from 'tntd';
import NoPermission from './components/NoPermission';
import Status from './components/Status';
import JSONTip from './components/JSONTip';
import Ellipsis from './components/Ellipsis';
const { QueryForm, Field, Toolbar, QueryList, createActions } = QueryListScene;

export default (props) => {
    const {
        title,
        query,
        queryList,
        queryForm = [],
        queryFormProps = {},
        queryListSceneProps = {},
        toolbar = [],
        extralActions = [],
        hasPermission = false,
        actionKey,
        actions,
        noMemory,
        initSearch
    } = props;
    // const actionsRef = useRef(createActions());
    // const actionsRef = actions;
    const toolbarHasPermission = toolbar.find((k) => k.hasPermission);

    const [columns, setColumns] = useState([]);
    const changeCol = () => {
        let col = cloneDeep(queryList?.columns);
        col?.forEach((item, i) => {
            if (item.deleteCol) {
                col.splice(i, 1);
            }
        });
        col?.forEach((item, i) => {
            item.ellipsis = true;
            if (!item.render) {
                item.render = (text) => {
                    let str = text;
                    if (item.formatterDate) {
                        // "YYYY-MM-DD HH:mm:ss" 时间戳格式化
                        str = text ? moment(text).format(item.formatterDate) : '--';
                    }

                    // 默认引入Ellipsis组件
                    let dom = <Ellipsis copyable={item.copyable} text={str} placement="topLeft" />;

                    // 引入状态Status组件
                    if (item.statusMap) {
                        dom = <Status text={item.statusMap[text]?.text} color={item.statusMap[text]?.color} type={item.statusType} />;
                    }

                    // 引入JSONTip组件
                    if (item.jsonTip) {
                        dom = <JSONTip text={text} />;
                    }

                    return text || text === 0 ? dom : '--';
                };
            }
        });
        setColumns(col);
    };
    useEffect(() => {
        changeCol();
    }, [queryList, queryForm]);

    const ExtralActions =
        extralActions.length > 0 &&
        extralActions.map((item) => {
            if (item.hasPermission) {
                return item.render;
            }
        });
    return (
        <div className="g-table-page">
            {hasPermission && (
                <QueryListScene initSearch={initSearch} memory title={title} query={query} actions={actions} {...queryListSceneProps}>
                    {queryForm && queryForm.length > 0 && (
                        <QueryForm extraActions={ExtralActions} {...queryFormProps}>
                            {queryForm.map((item) => (
                                <Field {...item} />
                            ))}
                        </QueryForm>
                    )}
                    {toolbar.length > 0 && toolbarHasPermission && (
                        <Toolbar>
                            {toolbar.map((item) => {
                                if (item.hasPermission) {
                                    return item.render;
                                }
                            })}
                        </Toolbar>
                    )}
                    <QueryList bordered={false} {...queryList} columns={columns} />
                </QueryListScene>
            )}
            {!hasPermission && (
                <>
                    {title ? (
                        <>
                            <div className="page-global-header">
                                <div className="left-info">
                                    <h2>{title}</h2>
                                </div>
                            </div>
                            <NoPermission />
                        </>
                    ) : (
                        <NoPermission />
                    )}
                </>
            )}
        </div>
    );
};
