/*
 * @CreatDate: 2018-10-09 11:51:30
 * @Describe: 无权限公共组件
 */

import I18N from '@/utils/I18N';
import './index.less';
import { PureComponent } from 'react';
import { Empty } from 'tntd';

export default class NoPermission extends PureComponent {
    render() {
        const { title } = this.props;

        return (
            <div className="m-no-permission">
                {/* <div className="no-permisson" />
                <p className="txt">{title ? title : '暂无权限'}</p> */}
                <Empty type="no-permission" description={title ? title : I18N.nopermission.index.zanWuQuanXian} size="ultra" />
            </div>
        );
    }
}
