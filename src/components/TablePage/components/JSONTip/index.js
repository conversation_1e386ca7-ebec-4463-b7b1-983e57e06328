/*
 * @CreatDate: 2021-03-31 17:10:51
 * @Describe: JSON展示框
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useState } from 'react';
import { Popover, Icon, message } from 'tntd';
import ReactJson from 'react-json-view';
import copy from 'copy-to-clipboard';

export default props => {
	const { text } = props;

	const copyHandle = () => {
		if (copy(text)) {
			message.success(I18N.jsontip.index.fuZhiChengGong);
		}
	};

	const content = (
		<div className="lb-json-tip">
			<p>{I18N.jsontip.index.xiangQing}</p>
			<div className="content">
				<ReactJson
					src={text ? JSON.parse(text) : {}}
					name={null}
					displayObjectSize={false}
					displayDataTypes={false}
					enableClipboard={false}
				/>
			</div>
			<Icon type="copy" className="u-copy" onClick={copyHandle} />
		</div>
	);

	return text ? (
		<Popover
			content={content}
			placement="leftTop"
		>
			{text}
		</Popover>
	) : '--';
};
