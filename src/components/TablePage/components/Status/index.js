/*
 * @CreatDate: 2021-03-31 17:10:51
 * @Describe: 状态组件
 */

import "./index.less";
import { useState } from "react";

export default props => {
	const { text, color = "#5D7092", type = "dot" } = props;

	let dom = (
		<span className="lb-status" style={{ color: color }}>
			<b style={{ background: color }}></b>
			{text}
		</span>
	);

	if (type === "ring") { // 环
		dom = (
			<span className="lb-status2">
				<span style={{ borderColor: color }}><i style={{ background: color }}></i></span>
				{text}
			</span>
		);
	}

	if (type === "normal") { // 默认模式
		dom = text;
	}

	return text ? dom : "--";
};
