## 简单封装 table 页面，模块配置化，集成权限校验

## 用法

```jsx
import { useState, useRef } from "react";
import TablePage from "@/components/TablePage";
import service from "./service";

const Demo = () => {
    const actionsRef = useRef();

    const query = ({ current = 1, pageSize = 10, ...rest }) => {
        let params = {
            curPage: current,
            pageSize,
            ...rest,
        };

        return service.getList(params).then((res) => {
            if (!res) return;
            if (res.success) {
                let data = res?.data;
                return {
                    pageSize: Number(data?.pageSize),
                    current: Number(data?.curPage),
                    total: Number(data?.total),
                    data: data?.contents || [],
                };
            } else {
                //
                return;
            }
        });
    };

    const queryForm = [
        {
            type: "input",
            name: "ruleCode",
            props: {
                placeholder: "规则标识",
                style: { width: "200px" },
            },
        },
        {
            type: "select",
            name: "workflowCode",
            props: {
                placeholder: "关联策略",
                style: { width: "200px" },
                options: workflowList,
            },
        },
    ];

    const toolbar = [
        {
            hasPermission: window.auth("HD0201", "add"),
            render: (
                <Button type="primary" onClick={() => addModifyHandle("", 1)}>
                    新增
                </Button>
            ),
        },
    ];

    const extralActions = [
        {
            hasPermission: window.auth("HD0201", "add"),
            render: (
                <Button type="primary" onClick={() => addModifyHandle("", 1)}>
                    新增
                </Button>
            ),
        },
    ];

    const columns = [
        {
            // 正常情况下，只是渲染数据的，这样就可以了，组件内部会自动识别  显示省略号及Tooltip,以及数据不存在时 展示 “--”
            title: "规则标识",
            dataIndex: "ruleCode",
        },
        {
            title: "规则标识",
            dataIndex: "ruleCode",
            deleteCol: true, // 不展示此列，主要用于权限控制
        },
        {
            title: "状态",
            dataIndex: "status",
            width: 100,
            statusType: "dot", // 默认dot,可不写，ring环
            statusMap: {
                1: { text: "待处理", color: "#5D7092" },
                2: { text: "挂起中", color: "#FF9845" },
                3: { text: "结束", color: "#b2becd" },
            },
        },
        {
            title: "字典详情",
            dataIndex: "configValue",
            jsonTip: true, // 内置JSONTip组件，展示json数据
        },
        {
            title: "修改时间",
            dataIndex: "gmtModify",
            width: 190,
            formatterDate: "YYYY-MM-DD HH:mm:ss", // 后端日期传时间戳时，设置这个属性自动格式化
        },
        {
            title: "操作",
            dataIndex: "operate",
            width: 210,
            fixed: "right",
            operate: true, // 填了此项，组件会调用 <Handle /> 组件
            render: (text, record) => (
                <>
                    <a>查看</a>
                    <a>修改</a>
                    <a>删除</a>
                </>
            ),
        },
    ];

    return (
        <TablePage
            actionKey="ruleConfig" // 必填 组件唯一key
            title="告警规则配置" // 同QueryListScene
            query={query} // 同QueryListScene
            actions={(obj) => {
                actionsRef.current = obj;
            }} // 同QueryListScene
            queryForm={queryForm} // 搜索项
            queryFormProps={{
                showFieldCount: 3,
            }} // 同QueryForm props
            toolbar={toolbar} // 表头操作栏
            extralActions={extralActions} // QueryForm的extralActions
            queryList={{
                // 同antd table
                rowKey: "id",
                columns: columns,
                scroll: { x: 1600 },
            }}
            hasPermission={window.auth("HD0201", "query")} // 页面权限
        />
    );
};

ReactDOM.render(<Demo />, document.getElementById("root"));
```
