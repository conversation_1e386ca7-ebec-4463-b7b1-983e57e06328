export default (props) => {
	const { children, href, onUnMount, ...rest } = props;
	if (window.__isMultiTab__) {
		return (
			<a onClick={evt => {
				evt.preventDefault();
                if (onUnMount && typeof onUnMount === 'function') {
                    onUnMount();
                }
				window.push(href);
			}}>{children}</a>
		);
	}
	return (
		<a href={href} {...rest}>{children}</a>
	);
};
