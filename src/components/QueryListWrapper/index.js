import { useEffect } from 'react';
import { withRouter } from 'dva/router';
import eventEmitter from '@/eventEmitter';

export default (WrappedComponent, actions, actionsMap) => {
    // 加载页面组件时，对应的路由
    const path = location.pathname;
    if (window.__lightBoxActions__) {
        return withRouter((props) => {
            useEffect(() => {
                const { actionKey } = props;
                if (actionKey && actionsMap) {
                    actions = actionsMap[actionKey];
                }
                const { EVENTS_ENUM, on } = window.__lightBoxActions__;
                const reset = (tab) => {
                    if (path.startsWith(tab.path)) {
                        actions?.resetMemoryData();
                    }
                };
                // 关闭页签时，重置数据
                on(EVENTS_ENUM.ON_TAB_CLOSE, reset);

                // 当渠道不一致时才需要reset
                if (EVENTS_ENUM.CURRENT_ORG_APP_LISTENER) {
                    on(EVENTS_ENUM.CURRENT_ORG_APP_LISTENER, actions?.resetMemoryData);
                } else {
                    on(EVENTS_ENUM.CURRENT_APP_CHANGE, actions?.resetMemoryData);
                }

                on(EVENTS_ENUM.ON_TABS_CLOSE, ({ closeTabs }) => {
                    const closeTab = closeTabs.find((t) => path.startsWith(t.path));
                    if (closeTab) {
                        actions?.resetMemoryData();
                    }
                });

                on('menuClick', () => {
                    actions?.resetMemoryData();
                });
            }, []);

            return <WrappedComponent {...props} />;
        });
    }

    return withRouter((props) => {
        useEffect(() => {
            const { actionKey } = props;
            if (actionKey && actionsMap) {
                actions = actionsMap[actionKey];
            }
            eventEmitter?.on('menuClick', () => {
                actions?.resetMemoryData();
            });
            eventEmitter?.on('appChange', actions?.resetMemoryData);
        }, []);

        return <WrappedComponent {...props} />;
    });
};
