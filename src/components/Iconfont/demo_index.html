<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2937683" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c7;</span>
                <div class="name">lesson</div>
                <div class="code-name">&amp;#xe7c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">enlarge</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">full-screen</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">half-screen</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f4;</span>
                <div class="name">relation</div>
                <div class="code-name">&amp;#xe7f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">body</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ee;</span>
                <div class="name">detailed</div>
                <div class="code-name">&amp;#xe7ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">appoint</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">contents</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">to-top</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">diagram</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">simple</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ec;</span>
                <div class="name">to-be-submitted</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7eb;</span>
                <div class="name">jump-off</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">delete2</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ea;</span>
                <div class="name">view2</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e3;</span>
                <div class="name">locate2</div>
                <div class="code-name">&amp;#xe7e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">locate1</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">thin-up</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">thin-left</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">thin-down</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">thin-right</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">copy3</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e0;</span>
                <div class="name">effective</div>
                <div class="code-name">&amp;#xe7e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e1;</span>
                <div class="name">ineffective</div>
                <div class="code-name">&amp;#xe7e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7df;</span>
                <div class="name">batch</div>
                <div class="code-name">&amp;#xe7df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">icon-风险评级-黑色</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7de;</span>
                <div class="name">icon-风险评级-蓝色</div>
                <div class="code-name">&amp;#xe7de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ca;</span>
                <div class="name">account-rating</div>
                <div class="code-name">&amp;#xe7ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b3;</span>
                <div class="name">full-screen</div>
                <div class="code-name">&amp;#xe7b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b4;</span>
                <div class="name">contrast</div>
                <div class="code-name">&amp;#xe7b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b5;</span>
                <div class="name">import</div>
                <div class="code-name">&amp;#xe7b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b6;</span>
                <div class="name">edit-square</div>
                <div class="code-name">&amp;#xe7b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b7;</span>
                <div class="name">history2</div>
                <div class="code-name">&amp;#xe7b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b8;</span>
                <div class="name">copy2</div>
                <div class="code-name">&amp;#xe7b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b9;</span>
                <div class="name">link</div>
                <div class="code-name">&amp;#xe7b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ba;</span>
                <div class="name">exclamation-circle</div>
                <div class="code-name">&amp;#xe7ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bb;</span>
                <div class="name">history</div>
                <div class="code-name">&amp;#xe7bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bc;</span>
                <div class="name">minus-square</div>
                <div class="code-name">&amp;#xe7bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">plus-circle</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7be;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe7be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bf;</span>
                <div class="name">offline</div>
                <div class="code-name">&amp;#xe7bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c0;</span>
                <div class="name">next</div>
                <div class="code-name">&amp;#xe7c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c1;</span>
                <div class="name">right</div>
                <div class="code-name">&amp;#xe7c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c2;</span>
                <div class="name">save</div>
                <div class="code-name">&amp;#xe7c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c3;</span>
                <div class="name">index-backtracking</div>
                <div class="code-name">&amp;#xe7c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">switch</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c5;</span>
                <div class="name">plus-square</div>
                <div class="code-name">&amp;#xe7c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c6;</span>
                <div class="name">shrink</div>
                <div class="code-name">&amp;#xe7c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c8;</span>
                <div class="name">pop-data</div>
                <div class="code-name">&amp;#xe7c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c9;</span>
                <div class="name">plus</div>
                <div class="code-name">&amp;#xe7c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cb;</span>
                <div class="name">reset</div>
                <div class="code-name">&amp;#xe7cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cc;</span>
                <div class="name">permission</div>
                <div class="code-name">&amp;#xe7cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">tree</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">refresh</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">publish</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">view</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d1;</span>
                <div class="name">upload</div>
                <div class="code-name">&amp;#xe7d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d2;</span>
                <div class="name">up</div>
                <div class="code-name">&amp;#xe7d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d3;</span>
                <div class="name">dubble-down</div>
                <div class="code-name">&amp;#xe7d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d4;</span>
                <div class="name">offl-screen</div>
                <div class="code-name">&amp;#xe7d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d5;</span>
                <div class="name">setting</div>
                <div class="code-name">&amp;#xe7d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d6;</span>
                <div class="name">user-add</div>
                <div class="code-name">&amp;#xe7d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">user-delete</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d8;</span>
                <div class="name">sort</div>
                <div class="code-name">&amp;#xe7d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">test</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7da;</span>
                <div class="name">question-circle</div>
                <div class="code-name">&amp;#xe7da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7db;</span>
                <div class="name">upload2</div>
                <div class="code-name">&amp;#xe7db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dc;</span>
                <div class="name">left</div>
                <div class="code-name">&amp;#xe7dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">arrow-left</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a2;</span>
                <div class="name">arrow-down</div>
                <div class="code-name">&amp;#xe7a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">all-screen</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a4;</span>
                <div class="name">arrow-up</div>
                <div class="code-name">&amp;#xe7a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">back</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">clear-format</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a7;</span>
                <div class="name">add</div>
                <div class="code-name">&amp;#xe7a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7aa;</span>
                <div class="name">dubble-up</div>
                <div class="code-name">&amp;#xe7aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">cloud-upload</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ac;</span>
                <div class="name">minus-circle</div>
                <div class="code-name">&amp;#xe7ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ad;</span>
                <div class="name">down</div>
                <div class="code-name">&amp;#xe7ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">export</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">cloud-download</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">arrow-right</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a0;</span>
                <div class="name">history</div>
                <div class="code-name">&amp;#xe7a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">sort</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">rule_indicator</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79e;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe79e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">submit</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">quote</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78d;</span>
                <div class="name">export_all</div>
                <div class="code-name">&amp;#xe78d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">help</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78b;</span>
                <div class="name">warning</div>
                <div class="code-name">&amp;#xe78b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe773;</span>
                <div class="name">empower</div>
                <div class="code-name">&amp;#xe773;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe774;</span>
                <div class="name">offline_all</div>
                <div class="code-name">&amp;#xe774;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe775;</span>
                <div class="name">running</div>
                <div class="code-name">&amp;#xe775;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe776;</span>
                <div class="name">online_all</div>
                <div class="code-name">&amp;#xe776;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe777;</span>
                <div class="name">view</div>
                <div class="code-name">&amp;#xe777;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe778;</span>
                <div class="name">setting</div>
                <div class="code-name">&amp;#xe778;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe77e;</span>
                <div class="name">offline</div>
                <div class="code-name">&amp;#xe77e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe77f;</span>
                <div class="name">online</div>
                <div class="code-name">&amp;#xe77f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe780;</span>
                <div class="name">progress</div>
                <div class="code-name">&amp;#xe780;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe781;</span>
                <div class="name">confirm</div>
                <div class="code-name">&amp;#xe781;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe782;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe782;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe783;</span>
                <div class="name">test</div>
                <div class="code-name">&amp;#xe783;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe784;</span>
                <div class="name">complement</div>
                <div class="code-name">&amp;#xe784;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe785;</span>
                <div class="name">history</div>
                <div class="code-name">&amp;#xe785;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe788;</span>
                <div class="name">empower</div>
                <div class="code-name">&amp;#xe788;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe789;</span>
                <div class="name">analysis</div>
                <div class="code-name">&amp;#xe789;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1672737213319'); /* IE9 */
  src: url('iconfont.eot?t=1672737213319#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1672737213319') format('woff'),
       url('iconfont.ttf?t=1672737213319') format('truetype'),
       url('iconfont.svg?t=1672737213319#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-lesson"></span>
            <div class="name">
              lesson
            </div>
            <div class="code-name">.icon-lesson
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-enlarge"></span>
            <div class="name">
              enlarge
            </div>
            <div class="code-name">.icon-enlarge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-full-screen1"></span>
            <div class="name">
              full-screen
            </div>
            <div class="code-name">.icon-full-screen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-half-screen"></span>
            <div class="name">
              half-screen
            </div>
            <div class="code-name">.icon-half-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-relation"></span>
            <div class="name">
              relation
            </div>
            <div class="code-name">.icon-relation
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-body"></span>
            <div class="name">
              body
            </div>
            <div class="code-name">.icon-body
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-detailed"></span>
            <div class="name">
              detailed
            </div>
            <div class="code-name">.icon-detailed
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-appoint"></span>
            <div class="name">
              appoint
            </div>
            <div class="code-name">.icon-appoint
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-contents"></span>
            <div class="name">
              contents
            </div>
            <div class="code-name">.icon-contents
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-to-top"></span>
            <div class="name">
              to-top
            </div>
            <div class="code-name">.icon-to-top
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diagram"></span>
            <div class="name">
              diagram
            </div>
            <div class="code-name">.icon-diagram
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-simple"></span>
            <div class="name">
              simple
            </div>
            <div class="code-name">.icon-simple
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-to-be-submitted"></span>
            <div class="name">
              to-be-submitted
            </div>
            <div class="code-name">.icon-to-be-submitted
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jump-off"></span>
            <div class="name">
              jump-off
            </div>
            <div class="code-name">.icon-jump-off
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete2"></span>
            <div class="name">
              delete2
            </div>
            <div class="code-name">.icon-delete2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-view2"></span>
            <div class="name">
              view2
            </div>
            <div class="code-name">.icon-view2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-locate2"></span>
            <div class="name">
              locate2
            </div>
            <div class="code-name">.icon-locate2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-locate1"></span>
            <div class="name">
              locate1
            </div>
            <div class="code-name">.icon-locate1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-thin-up"></span>
            <div class="name">
              thin-up
            </div>
            <div class="code-name">.icon-thin-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-thin-left"></span>
            <div class="name">
              thin-left
            </div>
            <div class="code-name">.icon-thin-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-thin-down"></span>
            <div class="name">
              thin-down
            </div>
            <div class="code-name">.icon-thin-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-thin-right"></span>
            <div class="name">
              thin-right
            </div>
            <div class="code-name">.icon-thin-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy3"></span>
            <div class="name">
              copy3
            </div>
            <div class="code-name">.icon-copy3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-effective"></span>
            <div class="name">
              effective
            </div>
            <div class="code-name">.icon-effective
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ineffective"></span>
            <div class="name">
              ineffective
            </div>
            <div class="code-name">.icon-ineffective
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-batch"></span>
            <div class="name">
              batch
            </div>
            <div class="code-name">.icon-batch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-fengxianpingji-heise"></span>
            <div class="name">
              icon-风险评级-黑色
            </div>
            <div class="code-name">.icon-icon-fengxianpingji-heise
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-fengxianpingji-lanse"></span>
            <div class="name">
              icon-风险评级-蓝色
            </div>
            <div class="code-name">.icon-icon-fengxianpingji-lanse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-zhanghufengxian"></span>
            <div class="name">
              account-rating
            </div>
            <div class="code-name">.icon-icon-zhanghufengxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-full-screen"></span>
            <div class="name">
              full-screen
            </div>
            <div class="code-name">.icon-full-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-contrast"></span>
            <div class="name">
              contrast
            </div>
            <div class="code-name">.icon-contrast
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-import"></span>
            <div class="name">
              import
            </div>
            <div class="code-name">.icon-import
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit-square"></span>
            <div class="name">
              edit-square
            </div>
            <div class="code-name">.icon-edit-square
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-history2"></span>
            <div class="name">
              history2
            </div>
            <div class="code-name">.icon-history2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy21"></span>
            <div class="name">
              copy2
            </div>
            <div class="code-name">.icon-copy21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-link"></span>
            <div class="name">
              link
            </div>
            <div class="code-name">.icon-link
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-exclamation-circle"></span>
            <div class="name">
              exclamation-circle
            </div>
            <div class="code-name">.icon-exclamation-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-history3"></span>
            <div class="name">
              history
            </div>
            <div class="code-name">.icon-history3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-minus-square"></span>
            <div class="name">
              minus-square
            </div>
            <div class="code-name">.icon-minus-square
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-plus-circle"></span>
            <div class="name">
              plus-circle
            </div>
            <div class="code-name">.icon-plus-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search1"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-offline1"></span>
            <div class="name">
              offline
            </div>
            <div class="code-name">.icon-offline1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-next"></span>
            <div class="name">
              next
            </div>
            <div class="code-name">.icon-next
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-right"></span>
            <div class="name">
              right
            </div>
            <div class="code-name">.icon-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-save"></span>
            <div class="name">
              save
            </div>
            <div class="code-name">.icon-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-index-backtracking"></span>
            <div class="name">
              index-backtracking
            </div>
            <div class="code-name">.icon-index-backtracking
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-switch"></span>
            <div class="name">
              switch
            </div>
            <div class="code-name">.icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-plus-square"></span>
            <div class="name">
              plus-square
            </div>
            <div class="code-name">.icon-plus-square
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shrink"></span>
            <div class="name">
              shrink
            </div>
            <div class="code-name">.icon-shrink
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pop-data"></span>
            <div class="name">
              pop-data
            </div>
            <div class="code-name">.icon-pop-data
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-plus"></span>
            <div class="name">
              plus
            </div>
            <div class="code-name">.icon-plus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-reset"></span>
            <div class="name">
              reset
            </div>
            <div class="code-name">.icon-reset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-permission"></span>
            <div class="name">
              permission
            </div>
            <div class="code-name">.icon-permission
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tree"></span>
            <div class="name">
              tree
            </div>
            <div class="code-name">.icon-tree
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-refresh"></span>
            <div class="name">
              refresh
            </div>
            <div class="code-name">.icon-refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-publish"></span>
            <div class="name">
              publish
            </div>
            <div class="code-name">.icon-publish
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-view1"></span>
            <div class="name">
              view
            </div>
            <div class="code-name">.icon-view1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-upload1"></span>
            <div class="name">
              upload
            </div>
            <div class="code-name">.icon-upload1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-up1"></span>
            <div class="name">
              up
            </div>
            <div class="code-name">.icon-up1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dubble-down"></span>
            <div class="name">
              dubble-down
            </div>
            <div class="code-name">.icon-dubble-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-offl-screen"></span>
            <div class="name">
              offl-screen
            </div>
            <div class="code-name">.icon-offl-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-setting1"></span>
            <div class="name">
              setting
            </div>
            <div class="code-name">.icon-setting1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-user-add"></span>
            <div class="name">
              user-add
            </div>
            <div class="code-name">.icon-user-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-user-delete"></span>
            <div class="name">
              user-delete
            </div>
            <div class="code-name">.icon-user-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sort1"></span>
            <div class="name">
              sort
            </div>
            <div class="code-name">.icon-sort1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-test1"></span>
            <div class="name">
              test
            </div>
            <div class="code-name">.icon-test1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-question-circle"></span>
            <div class="name">
              question-circle
            </div>
            <div class="code-name">.icon-question-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-upload2"></span>
            <div class="name">
              upload2
            </div>
            <div class="code-name">.icon-upload2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-left"></span>
            <div class="name">
              left
            </div>
            <div class="code-name">.icon-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-left"></span>
            <div class="name">
              arrow-left
            </div>
            <div class="code-name">.icon-arrow-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-down"></span>
            <div class="name">
              arrow-down
            </div>
            <div class="code-name">.icon-arrow-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-all-screen"></span>
            <div class="name">
              all-screen
            </div>
            <div class="code-name">.icon-all-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-up"></span>
            <div class="name">
              arrow-up
            </div>
            <div class="code-name">.icon-arrow-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-back"></span>
            <div class="name">
              back
            </div>
            <div class="code-name">.icon-back
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-clear-format"></span>
            <div class="name">
              clear-format
            </div>
            <div class="code-name">.icon-clear-format
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-add1"></span>
            <div class="name">
              add
            </div>
            <div class="code-name">.icon-add1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy2"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.icon-copy2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-download1"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.icon-download1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dubble-up"></span>
            <div class="name">
              dubble-up
            </div>
            <div class="code-name">.icon-dubble-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cloud-upload"></span>
            <div class="name">
              cloud-upload
            </div>
            <div class="code-name">.icon-cloud-upload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-minus-circle"></span>
            <div class="name">
              minus-circle
            </div>
            <div class="code-name">.icon-minus-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-down1"></span>
            <div class="name">
              down
            </div>
            <div class="code-name">.icon-down1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-export"></span>
            <div class="name">
              export
            </div>
            <div class="code-name">.icon-export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cloud-download"></span>
            <div class="name">
              cloud-download
            </div>
            <div class="code-name">.icon-cloud-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete1"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-right"></span>
            <div class="name">
              arrow-right
            </div>
            <div class="code-name">.icon-arrow-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-history1"></span>
            <div class="name">
              history
            </div>
            <div class="code-name">.icon-history1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy1"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.icon-copy1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sort"></span>
            <div class="name">
              sort
            </div>
            <div class="code-name">.icon-sort
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rule_indicator"></span>
            <div class="name">
              rule_indicator
            </div>
            <div class="code-name">.icon-rule_indicator
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-submit"></span>
            <div class="name">
              submit
            </div>
            <div class="code-name">.icon-submit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quote"></span>
            <div class="name">
              quote
            </div>
            <div class="code-name">.icon-quote
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-export_all"></span>
            <div class="name">
              export_all
            </div>
            <div class="code-name">.icon-export_all
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-help"></span>
            <div class="name">
              help
            </div>
            <div class="code-name">.icon-help
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-warning"></span>
            <div class="name">
              warning
            </div>
            <div class="code-name">.icon-warning
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-empower"></span>
            <div class="name">
              empower
            </div>
            <div class="code-name">.icon-empower
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-offline_all"></span>
            <div class="name">
              offline_all
            </div>
            <div class="code-name">.icon-offline_all
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-running"></span>
            <div class="name">
              running
            </div>
            <div class="code-name">.icon-running
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-online_all"></span>
            <div class="name">
              online_all
            </div>
            <div class="code-name">.icon-online_all
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-view"></span>
            <div class="name">
              view
            </div>
            <div class="code-name">.icon-view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-setting"></span>
            <div class="name">
              setting
            </div>
            <div class="code-name">.icon-setting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-offline"></span>
            <div class="name">
              offline
            </div>
            <div class="code-name">.icon-offline
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-online"></span>
            <div class="name">
              online
            </div>
            <div class="code-name">.icon-online
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-progress"></span>
            <div class="name">
              progress
            </div>
            <div class="code-name">.icon-progress
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-confirm"></span>
            <div class="name">
              confirm
            </div>
            <div class="code-name">.icon-confirm
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.icon-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-test"></span>
            <div class="name">
              test
            </div>
            <div class="code-name">.icon-test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-complement"></span>
            <div class="name">
              complement
            </div>
            <div class="code-name">.icon-complement
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-history"></span>
            <div class="name">
              history
            </div>
            <div class="code-name">.icon-history
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-empower1"></span>
            <div class="name">
              empower
            </div>
            <div class="code-name">.icon-empower1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-analysis"></span>
            <div class="name">
              analysis
            </div>
            <div class="code-name">.icon-analysis
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lesson"></use>
                </svg>
                <div class="name">lesson</div>
                <div class="code-name">#icon-lesson</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-enlarge"></use>
                </svg>
                <div class="name">enlarge</div>
                <div class="code-name">#icon-enlarge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-full-screen1"></use>
                </svg>
                <div class="name">full-screen</div>
                <div class="code-name">#icon-full-screen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-half-screen"></use>
                </svg>
                <div class="name">half-screen</div>
                <div class="code-name">#icon-half-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-relation"></use>
                </svg>
                <div class="name">relation</div>
                <div class="code-name">#icon-relation</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-body"></use>
                </svg>
                <div class="name">body</div>
                <div class="code-name">#icon-body</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-detailed"></use>
                </svg>
                <div class="name">detailed</div>
                <div class="code-name">#icon-detailed</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-appoint"></use>
                </svg>
                <div class="name">appoint</div>
                <div class="code-name">#icon-appoint</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-contents"></use>
                </svg>
                <div class="name">contents</div>
                <div class="code-name">#icon-contents</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-to-top"></use>
                </svg>
                <div class="name">to-top</div>
                <div class="code-name">#icon-to-top</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diagram"></use>
                </svg>
                <div class="name">diagram</div>
                <div class="code-name">#icon-diagram</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-simple"></use>
                </svg>
                <div class="name">simple</div>
                <div class="code-name">#icon-simple</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-to-be-submitted"></use>
                </svg>
                <div class="name">to-be-submitted</div>
                <div class="code-name">#icon-to-be-submitted</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jump-off"></use>
                </svg>
                <div class="name">jump-off</div>
                <div class="code-name">#icon-jump-off</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete2"></use>
                </svg>
                <div class="name">delete2</div>
                <div class="code-name">#icon-delete2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-view2"></use>
                </svg>
                <div class="name">view2</div>
                <div class="code-name">#icon-view2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-locate2"></use>
                </svg>
                <div class="name">locate2</div>
                <div class="code-name">#icon-locate2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-locate1"></use>
                </svg>
                <div class="name">locate1</div>
                <div class="code-name">#icon-locate1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-thin-up"></use>
                </svg>
                <div class="name">thin-up</div>
                <div class="code-name">#icon-thin-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-thin-left"></use>
                </svg>
                <div class="name">thin-left</div>
                <div class="code-name">#icon-thin-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-thin-down"></use>
                </svg>
                <div class="name">thin-down</div>
                <div class="code-name">#icon-thin-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-thin-right"></use>
                </svg>
                <div class="name">thin-right</div>
                <div class="code-name">#icon-thin-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy3"></use>
                </svg>
                <div class="name">copy3</div>
                <div class="code-name">#icon-copy3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-effective"></use>
                </svg>
                <div class="name">effective</div>
                <div class="code-name">#icon-effective</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ineffective"></use>
                </svg>
                <div class="name">ineffective</div>
                <div class="code-name">#icon-ineffective</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-batch"></use>
                </svg>
                <div class="name">batch</div>
                <div class="code-name">#icon-batch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-fengxianpingji-heise"></use>
                </svg>
                <div class="name">icon-风险评级-黑色</div>
                <div class="code-name">#icon-icon-fengxianpingji-heise</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-fengxianpingji-lanse"></use>
                </svg>
                <div class="name">icon-风险评级-蓝色</div>
                <div class="code-name">#icon-icon-fengxianpingji-lanse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-zhanghufengxian"></use>
                </svg>
                <div class="name">account-rating</div>
                <div class="code-name">#icon-icon-zhanghufengxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-full-screen"></use>
                </svg>
                <div class="name">full-screen</div>
                <div class="code-name">#icon-full-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-contrast"></use>
                </svg>
                <div class="name">contrast</div>
                <div class="code-name">#icon-contrast</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-import"></use>
                </svg>
                <div class="name">import</div>
                <div class="code-name">#icon-import</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit-square"></use>
                </svg>
                <div class="name">edit-square</div>
                <div class="code-name">#icon-edit-square</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-history2"></use>
                </svg>
                <div class="name">history2</div>
                <div class="code-name">#icon-history2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy21"></use>
                </svg>
                <div class="name">copy2</div>
                <div class="code-name">#icon-copy21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-link"></use>
                </svg>
                <div class="name">link</div>
                <div class="code-name">#icon-link</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-exclamation-circle"></use>
                </svg>
                <div class="name">exclamation-circle</div>
                <div class="code-name">#icon-exclamation-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-history3"></use>
                </svg>
                <div class="name">history</div>
                <div class="code-name">#icon-history3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-minus-square"></use>
                </svg>
                <div class="name">minus-square</div>
                <div class="code-name">#icon-minus-square</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-plus-circle"></use>
                </svg>
                <div class="name">plus-circle</div>
                <div class="code-name">#icon-plus-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search1"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-offline1"></use>
                </svg>
                <div class="name">offline</div>
                <div class="code-name">#icon-offline1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-next"></use>
                </svg>
                <div class="name">next</div>
                <div class="code-name">#icon-next</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-right"></use>
                </svg>
                <div class="name">right</div>
                <div class="code-name">#icon-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-save"></use>
                </svg>
                <div class="name">save</div>
                <div class="code-name">#icon-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-index-backtracking"></use>
                </svg>
                <div class="name">index-backtracking</div>
                <div class="code-name">#icon-index-backtracking</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-switch"></use>
                </svg>
                <div class="name">switch</div>
                <div class="code-name">#icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-plus-square"></use>
                </svg>
                <div class="name">plus-square</div>
                <div class="code-name">#icon-plus-square</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shrink"></use>
                </svg>
                <div class="name">shrink</div>
                <div class="code-name">#icon-shrink</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pop-data"></use>
                </svg>
                <div class="name">pop-data</div>
                <div class="code-name">#icon-pop-data</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-plus"></use>
                </svg>
                <div class="name">plus</div>
                <div class="code-name">#icon-plus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-reset"></use>
                </svg>
                <div class="name">reset</div>
                <div class="code-name">#icon-reset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-permission"></use>
                </svg>
                <div class="name">permission</div>
                <div class="code-name">#icon-permission</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tree"></use>
                </svg>
                <div class="name">tree</div>
                <div class="code-name">#icon-tree</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-refresh"></use>
                </svg>
                <div class="name">refresh</div>
                <div class="code-name">#icon-refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-publish"></use>
                </svg>
                <div class="name">publish</div>
                <div class="code-name">#icon-publish</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-view1"></use>
                </svg>
                <div class="name">view</div>
                <div class="code-name">#icon-view1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-upload1"></use>
                </svg>
                <div class="name">upload</div>
                <div class="code-name">#icon-upload1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-up1"></use>
                </svg>
                <div class="name">up</div>
                <div class="code-name">#icon-up1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dubble-down"></use>
                </svg>
                <div class="name">dubble-down</div>
                <div class="code-name">#icon-dubble-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-offl-screen"></use>
                </svg>
                <div class="name">offl-screen</div>
                <div class="code-name">#icon-offl-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-setting1"></use>
                </svg>
                <div class="name">setting</div>
                <div class="code-name">#icon-setting1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user-add"></use>
                </svg>
                <div class="name">user-add</div>
                <div class="code-name">#icon-user-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user-delete"></use>
                </svg>
                <div class="name">user-delete</div>
                <div class="code-name">#icon-user-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sort1"></use>
                </svg>
                <div class="name">sort</div>
                <div class="code-name">#icon-sort1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-test1"></use>
                </svg>
                <div class="name">test</div>
                <div class="code-name">#icon-test1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-question-circle"></use>
                </svg>
                <div class="name">question-circle</div>
                <div class="code-name">#icon-question-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-upload2"></use>
                </svg>
                <div class="name">upload2</div>
                <div class="code-name">#icon-upload2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-left"></use>
                </svg>
                <div class="name">left</div>
                <div class="code-name">#icon-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-left"></use>
                </svg>
                <div class="name">arrow-left</div>
                <div class="code-name">#icon-arrow-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-down"></use>
                </svg>
                <div class="name">arrow-down</div>
                <div class="code-name">#icon-arrow-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-all-screen"></use>
                </svg>
                <div class="name">all-screen</div>
                <div class="code-name">#icon-all-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-up"></use>
                </svg>
                <div class="name">arrow-up</div>
                <div class="code-name">#icon-arrow-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-back"></use>
                </svg>
                <div class="name">back</div>
                <div class="code-name">#icon-back</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-clear-format"></use>
                </svg>
                <div class="name">clear-format</div>
                <div class="code-name">#icon-clear-format</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-add1"></use>
                </svg>
                <div class="name">add</div>
                <div class="code-name">#icon-add1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy2"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#icon-copy2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-download1"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#icon-download1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dubble-up"></use>
                </svg>
                <div class="name">dubble-up</div>
                <div class="code-name">#icon-dubble-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cloud-upload"></use>
                </svg>
                <div class="name">cloud-upload</div>
                <div class="code-name">#icon-cloud-upload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-minus-circle"></use>
                </svg>
                <div class="name">minus-circle</div>
                <div class="code-name">#icon-minus-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-down1"></use>
                </svg>
                <div class="name">down</div>
                <div class="code-name">#icon-down1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-export"></use>
                </svg>
                <div class="name">export</div>
                <div class="code-name">#icon-export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cloud-download"></use>
                </svg>
                <div class="name">cloud-download</div>
                <div class="code-name">#icon-cloud-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete1"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-right"></use>
                </svg>
                <div class="name">arrow-right</div>
                <div class="code-name">#icon-arrow-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-history1"></use>
                </svg>
                <div class="name">history</div>
                <div class="code-name">#icon-history1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy1"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#icon-copy1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sort"></use>
                </svg>
                <div class="name">sort</div>
                <div class="code-name">#icon-sort</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rule_indicator"></use>
                </svg>
                <div class="name">rule_indicator</div>
                <div class="code-name">#icon-rule_indicator</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-submit"></use>
                </svg>
                <div class="name">submit</div>
                <div class="code-name">#icon-submit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quote"></use>
                </svg>
                <div class="name">quote</div>
                <div class="code-name">#icon-quote</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-export_all"></use>
                </svg>
                <div class="name">export_all</div>
                <div class="code-name">#icon-export_all</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-help"></use>
                </svg>
                <div class="name">help</div>
                <div class="code-name">#icon-help</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-warning"></use>
                </svg>
                <div class="name">warning</div>
                <div class="code-name">#icon-warning</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-empower"></use>
                </svg>
                <div class="name">empower</div>
                <div class="code-name">#icon-empower</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-offline_all"></use>
                </svg>
                <div class="name">offline_all</div>
                <div class="code-name">#icon-offline_all</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-running"></use>
                </svg>
                <div class="name">running</div>
                <div class="code-name">#icon-running</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-online_all"></use>
                </svg>
                <div class="name">online_all</div>
                <div class="code-name">#icon-online_all</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-view"></use>
                </svg>
                <div class="name">view</div>
                <div class="code-name">#icon-view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-setting"></use>
                </svg>
                <div class="name">setting</div>
                <div class="code-name">#icon-setting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-offline"></use>
                </svg>
                <div class="name">offline</div>
                <div class="code-name">#icon-offline</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-online"></use>
                </svg>
                <div class="name">online</div>
                <div class="code-name">#icon-online</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-progress"></use>
                </svg>
                <div class="name">progress</div>
                <div class="code-name">#icon-progress</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-confirm"></use>
                </svg>
                <div class="name">confirm</div>
                <div class="code-name">#icon-confirm</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#icon-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-test"></use>
                </svg>
                <div class="name">test</div>
                <div class="code-name">#icon-test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-complement"></use>
                </svg>
                <div class="name">complement</div>
                <div class="code-name">#icon-complement</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-history"></use>
                </svg>
                <div class="name">history</div>
                <div class="code-name">#icon-history</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-empower1"></use>
                </svg>
                <div class="name">empower</div>
                <div class="code-name">#icon-empower1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-analysis"></use>
                </svg>
                <div class="name">analysis</div>
                <div class="code-name">#icon-analysis</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
