import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Modal, Input } from 'tntd';
const DetailModal = (props) => {
    const { record } = props;
    return (
        <Modal
            // visible={modalVisible}
            title={!record ? I18N.detailmodal.index.xinZeng : I18N.detailmodal.index.xiuGai}
            okText={!record ? I18N.detailmodal.index.xinZeng : I18N.detailmodal.index.xiuGai}
            // onCancel={onCancel}
            // onOk={onSubmit}
            maskClosable={false}
        />
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(DetailModal);
