import I18N from '@/utils/I18N';
import { message, Select, Upload, Form, TreeSelect, Tooltip, Icon } from 'tntd';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { userAPI } from '@/services';
import { traverseTree } from '@/utils/utils';
import { exportMap } from '../constant.js';

const Option = Select.Option;
const Zero = (props) => {
    const { form, globalStore, setFileName, setMultipartfile, type, style, modelType } = props;
    const { getFieldDecorator, getFieldValue } = form;

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 9 }
        },
        wrapperCol: {
            xs: { span: 10 },
            sm: { span: 7 }
        }
    };

    useEffect(() => {
        props.onRef({
            form
        });
    }, []);

    const beforeUploadHandle = (file) => {
        let fileName = file.name;
        // 获取前缀名
        setFileName(fileName.substring(0, fileName.lastIndexOf('.')));

        // 清除

        // setFileLists(fileList);
        // form.setFieldsValue({ file: fileList });
        setMultipartfile(file);
        return false;
    };

    const removeHanlde = () => {
        setMultipartfile([]);
        setFileName();
        console.log(form.getFieldsValue());
        form.setFieldsValue({ file: undefined });

        // console.log(form.getFieldsValue());
    };

    const normFile = (e) => {
        let { file, fileList } = e;
        let fileName = file.name;
        let fileExt = fileName.replace(/.+\./, '').toLowerCase(); // 后缀名
        let fileList1 = [e.file];

        if (fileList.length === 0) {
            fileList1 = [];
        }
        // setFileLists([]);
        //决策工具
        let curFileExt = exportMap[type];
        let flag = curFileExt.split(',').includes(fileExt);

        if (!flag) {
            // 先判断文件类型
            message.error(I18N.steps.zero.wenJianLeiXingCuo);
            fileList1 = [];
        }
        if (file.size / 1024 / 1024 > 50) {
            // 再判断文件大小
            message.error(I18N.steps.zero.wenJianDaXiaoBu);
            fileList1 = [];
        }
        // return e && fileList1;
        return e && fileList1;
    };
    return (
        <div style={style}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.steps.zero.daoRuMoShi}>
                    {getFieldDecorator('mode', {
                        initialValue: 2,
                        rules: [
                            {
                                required: true,
                                message: I18N.steps.zero.qingXuanZeDaoRu
                            }
                        ]
                    })(
                        <Select placeholder={I18N.steps.zero.qingXuanZeDaoRu} showSearch optionFilterProp="children" dropdownMatchSelectWidth={false}>
                            <Option value={1}>{I18N.steps.zero.genJuZhiDingJi}</Option>
                            <Option value={2}>{I18N.steps.zero.genJuYuanWenJian}</Option>
                        </Select>
                    )}
                </Form.Item>

                {type === 'FUNCTION' && (
                    <>
                        <Form.Item label={I18N.steps.zero.yiLaiZuJianDao}>
                            {getFieldDecorator('dependentFlag', {
                                initialValue: 1,
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.steps.zero.qingTianXieJiGou
                                    }
                                ]
                            })(
                                <Select>
                                    <Option value={1}>{I18N.steps.zero.shi}</Option>
                                    <Option value={0}>{I18N.steps.zero.fou}</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </>
                )}
                <Form.Item label={I18N.steps.zero.shangChuanWenJian}>
                    {getFieldDecorator('file', {
                        valuePropName: 'fileList',
                        getValueFromEvent: normFile,
                        rules: [
                            {
                                required: true,
                                message: I18N.steps.zero.qingXuanZeShangChuan
                            }
                        ]
                    })(
                        <Upload
                            onChange={() => { }}
                            multiple={false}
                            beforeUpload={(file, fileList) => beforeUploadHandle(file, fileList)}
                            onRemove={() => {
                                removeHanlde();
                            }}>
                            <>
                                <a>{I18N.steps.zero.dianJiXuanZeWen}</a>
                                <Tooltip title={I18N.steps.zero.wenJianZhongRuoYin}>
                                    <Icon className="ml10 mr10" type="question-circle" />
                                </Tooltip>
                            </>
                        </Upload>
                    )}
                </Form.Item>
            </Form>
        </div>
    );
};

export default Form.create()(
    connect((state) => {
        return {
            globalStore: state.global
        };
    })(Zero)
);
