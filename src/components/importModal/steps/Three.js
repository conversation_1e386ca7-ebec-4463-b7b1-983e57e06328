import I18N from '@/utils/I18N';
import { Badge, Collapse, Result, Table, TntdIcon, Ellipsis } from 'tntd';
import { useEffect, useState } from 'react';
import { baseAPI } from '@/services';
import history from '@/utils/history';
import { titleMap } from '../constant';

const { Panel } = Collapse;

const Three = (props) => {
    // 联调
    const { record, taskId, fileName, details, style, type } = props;
    const [errSumList, setErrSumList] = useState({});
    const [isAccept, setIsAccept] = useState(true);

    useEffect(() => {
        if (details.length > 0) {
            let obj = {};
            details.map((item) => {
                let errSum = 0;

                //获取每种校验内有几条为通过的数据
                if (item.errorNum > 0) {
                    setIsAccept(false);
                    errSum += 1;
                }
                // 不展示名单集校验结果
                obj[item.title] = errSum;
            });
            setErrSumList(obj);
        } else {
            setIsAccept(true);
            setErrSumList({});
        }
        return () => {
            setErrSumList();
            setIsAccept(true);
        };
    }, [details]);

    const resultColumns = (columnName) => {
        if (columnName.length === 3) {
            return [
                {
                    dataIndex: 'column1',
                    keys: 'column1',
                    title: columnName[0],
                    width: 354,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={350} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column2',
                    keys: 'column2',
                    title: columnName[1],
                    width: 354,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={350} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column3',
                    keys: 'column3',
                    title: columnName[2],
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={300} title={text} />
                            </span>
                        );
                    }
                }
            ];
        } else if (columnName.length === 4) {
            return [
                {
                    dataIndex: 'column1',
                    keys: 'column1',
                    title: columnName[0],
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column2',
                    keys: 'column2',
                    title: columnName[1],
                    width: 100,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={90} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column3',
                    keys: 'column3',
                    title: columnName[2],
                    filters: [
                        { text: I18N.steps.three.chengGong, value: I18N.steps.three.chengGong },
                        { text: I18N.steps.three.shiBai, value: I18N.steps.three.shiBai }
                    ],
                    onFilter: (value, record) => record.column3.indexOf(value) === 0,
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column4',
                    keys: 'column4',
                    title: columnName[3],
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                }
            ];
        } else if (columnName.length === 5) {
            return [
                {
                    dataIndex: 'column1',
                    keys: 'column1',
                    title: columnName[0],
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column2',
                    keys: 'column2',
                    title: columnName[1],
                    width: 100,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={90} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column3',
                    keys: 'column3',
                    title: columnName[2],
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column4',
                    keys: 'column4',
                    title: columnName[3],
                    filters: [
                        { text: I18N.steps.three.chengGong, value: I18N.steps.three.chengGong },
                        { text: I18N.steps.three.shiBai, value: I18N.steps.three.shiBai }
                    ],
                    onFilter: (value, record) => record.column4.indexOf(value) === 0,
                    width: 150,
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={140} title={text} />
                            </span>
                        );
                    }
                },
                {
                    dataIndex: 'column5',
                    keys: 'column5',
                    title: columnName[4],
                    render: (text) => {
                        return (
                            <span style={{ display: 'inline-block' }}>
                                <Ellipsis widthLimit={150} title={text} />
                            </span>
                        );
                    }
                }
            ];
        }
    };
    return (
        <div className="import-result" style={style}>
            <div
                className="resultTop-icon"
                onClick={() => {
                    if (type === 'RESULT_VIEW' || type === 'ROLLBACK') {
                        let params = {
                            importTaskId: record?.importTaskId || taskId,
                            componentShowType: 'xls'
                        };
                        let curName;
                        if (fileName) {
                            curName = fileName;
                        } else {
                            curName = record?.importFileName.substring(0, record?.importFileName.lastIndexOf('.'));
                        }
                        baseAPI.rollbackDwresult(params, I18N.template(I18N.steps.three.cURNA2, { val1: curName || record }));
                    } else {
                        let params = {
                            type: 'importInfo',
                            taskId: record?.importTaskId || taskId,
                            componentShowType: 'xls'
                        };
                        let curName;
                        if (fileName) {
                            curName = fileName;
                        } else {
                            curName = record?.importFileName.substring(0, record?.importFileName.lastIndexOf('.'));
                        }
                        baseAPI.listDownload(params, I18N.template(I18N.steps.three.cURNA, { val1: curName || record }));
                    }
                }}>
                <span>
                    {window.auth('ImportHistory', 'RollbackDownload') && type === 'RESULT_VIEW' && (
                        <>
                            <span>{I18N.steps.three.xiaZaiXiangXiHui}</span>
                            <TntdIcon type="download" />
                        </>
                    )}
                </span>
                <span>
                    {((window.auth('ImportHistory', 'DownLoadResultFile') && type === 'ROLLBACK') || type === 'RESULT') && (
                        <>
                            <span>
                                <TntdIcon type="download" />
                                {I18N.steps.three.xiaZaiXiangXiDao}
                            </span>
                        </>
                    )}
                </span>
            </div>
            <div className="result-header">
                {isAccept ? (
                    <Result
                        status="success"
                        title={type === 'RESULT_VIEW' || type === 'ROLLBACK' ? I18N.steps.three.huiGunCaoZuoQuan : I18N.steps.three.daoRuCaoZuoQuan}
                    />
                ) : (
                    <Result
                        status="error"
                        title={type === 'RESULT_VIEW' || type === 'ROLLBACK' ? I18N.steps.three.huiGunCaoZuoWei : I18N.steps.three.daoRuCaoZuoWei}
                        subTitle={Object.keys(errSumList)?.map((item, index) => {
                            if (errSumList[item] > 0) {
                                return (
                                    <div key={index}>
                                        <span>{titleMap[item]}：</span>
                                        <span>
                                            {I18N.steps.three.you}
                                            {Object.values(errSumList)[index]}
                                            {I18N.steps.three.geZuJianShiLi}
                                            {type === 'RESULT_VIEW' || type === 'ROLLBACK' ? I18N.steps.three.huiGun : I18N.steps.three.daoRu}
                                            {I18N.steps.three.weiTongGuo}
                                        </span>
                                    </div>
                                );
                            }
                        })}
                    />
                )}
            </div>
            <div className="result-bottom">
                {details?.map((item, index) => {
                    return (
                        <Collapse key={index} expandIconPosition="left">
                            <Panel
                                disabled={item?.rows?.length <= 0}
                                extra={
                                    <div>
                                        {
                                            <>
                                                <Badge style={{ top: '-23px', left: '64px' }} count={item.errorNum} />
                                                <span>
                                                    {I18N.steps.three.gong}
                                                    {item?.rows?.length}
                                                    {I18N.steps.three.tiao}
                                                </span>
                                            </>
                                        }
                                    </div>
                                }
                                header={
                                    <div>
                                        <span className="active-button">
                                            {titleMap[item?.title]}
                                            {I18N.steps.three.daoRuJieGuo}
                                        </span>
                                        {(item.title === 'POLICY' ||
                                            item.title === 'RULE_SET' ||
                                            item.title === 'INDEX_REALTIME' ||
                                            item.title === 'INDEX_OFFLINE' ||
                                            item.title === 'DECISION_TOOL' ||
                                            item.title === 'SCORE_CARD') &&
                                            type === 'RESULT' &&
                                            record && (
                                                <span
                                                    className="history-button"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        let arr = [];
                                                        item?.rows?.map((item) => {
                                                            arr.push(item.column2);
                                                        });
                                                        let str = arr.join(',');

                                                        switch (item.title) {
                                                            case 'POLICY':
                                                                history.push(`/noah/policyManage?currentTab=2&code=${str}`);
                                                                break;
                                                            case 'RULE_SET':
                                                                history.push(`/noah/ruleSet?currentTab=2&code=${str}`);
                                                                break;
                                                            case 'INDEX_REALTIME':
                                                                history.push(`/index/realtime?currentTab=2&metricCodes=${str}`);
                                                                break;
                                                            case 'INDEX_OFFLINE':
                                                                history.push(`/index/offIndexManage?currentTab=2&featureSetCode=${str}`);
                                                                break;
                                                            case 'DECISION_TOOL':
                                                                history.push(`/noah/bodyguard/modelTool?currentTab=2&code=${str}`);
                                                                break;
                                                            case 'SCORE_CARD':
                                                                history.push(`/noah/bodyguard/scoreCard?currentTab=2&code=${str}`);
                                                                break;
                                                        }
                                                    }}>
                                                    <a>{I18N.steps.three.kuaiSuTiaoZhuan}</a>
                                                </span>
                                            )}
                                    </div>
                                }
                                key={index}
                                style={{ background: '#FBFCFF' }}>
                                <div className="content-row">
                                    <Table className="result-table" columns={resultColumns(item?.columnName)} dataSource={item?.rows} />
                                </div>
                            </Panel>
                        </Collapse>
                    );
                })}
            </div>
        </div>
    );
};

export default Three;
