import I18N from '@/utils/I18N';
import { Radio, Tooltip, Icon, Row, Result } from 'tntd';
import { cloneDeep } from 'lodash';
import { titleMap, crossProjectMap, sameCrossEnv, CrossProjectEnv } from '../constant';

const Two = (props) => {
    const { importMode, setImportMode, style, mode } = props;
    return (
        <div style={style}>
            {importMode?.typeModeList?.length === 0 && (
                <Result style={{ padding: '48px 48px 0px 32px' }} status="success" title={I18N.steps.two.dangQianWuXuShou} />
            )}

            <div style={{ color: '#8B919E', margin: '40px 283px 30px 297px ' }}>
                <Row>{I18N.steps.two.zhuQueShiZuJian}</Row>
                <Row>{I18N.steps.two.zhuRuoZiDongChuang}</Row>
                <Row>{I18N.steps.two.zhuXiangTongZuJian}</Row>
            </div>

            {importMode?.typeModeList?.map((item, index) => {
                return (
                    <div key={index} className="columns-container">
                        <div className="title-text">
                            {I18N.steps.two.xiangTong}
                            {titleMap[item.category]}
                            {I18N.steps.two.daoRuFangShi}
                            <Tooltip title={mode === 1 ? sameCrossEnv[item.category] : CrossProjectEnv[item.category]}>
                                <Icon className="ml10" type="question-circle" />
                            </Tooltip>
                        </div>
                        <div className="content-text">
                            <Radio.Group
                                value={item?.subModeList.find((item) => item.selected).code}
                                onChange={(e) => {
                                    let tempMap = cloneDeep(importMode);
                                    tempMap.typeModeList[index].subModeList?.map((item3) => {
                                        if (item3.code === e.target.value) {
                                            item3.selected = true;
                                        } else {
                                            item3.selected = false;
                                        }
                                    });
                                    setImportMode(tempMap);
                                }}>
                                {item?.subModeList?.map((item1, index1) => {
                                    return (
                                        <Radio key={index1} value={item1.code}>
                                            {crossProjectMap[item1.code]}
                                        </Radio>
                                    );
                                })}
                            </Radio.Group>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default Two;
