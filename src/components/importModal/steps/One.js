import I18N from '@/utils/I18N';
import { Tabs, Collapse, Table, Icon as AIcon, Tooltip, Result, Button, Badge, TntdIcon, Ellipsis } from 'tntd';
import { useEffect, useState } from 'react';
import { baseAPI } from '@/services';
import { FourColumns, ThreeColumns } from '@/components/RowSpanTable/index.js';
import { tabMap, titleMap, tabTooltipMap } from '../constant.js';
const { TabPane } = Tabs;
const { Panel } = Collapse;

const One = (props) => {
    const { record, taskId, fileName, checkTable = {}, style, step, isNext } = props;
    const [errSumList, setErrSumList] = useState({});
    const [isAccept, setIsAccept] = useState(true);

    // 缺失项校验 两列
    // 用户权限校验、导入环境校验 三列
    // 组件配置校验 四列

    useEffect(() => {
        if (Object.keys(checkTable).length > 0) {
            let obj = {};
            Object.keys(checkTable)?.map((item) => {
                let errSum = 0;
                checkTable[item]?.map((item1) => {
                    //获取每种校验内有几条为通过的数据
                    if (item1.errorNum > 0) {
                        setIsAccept(false);
                        errSum += 1;
                    }
                    // 不展示名单集校验结果
                });
                obj[item] = errSum;
            });
            setErrSumList(obj);
        } else {
            setIsAccept(true);
            setErrSumList({});
        }
    }, [checkTable]);

    const missingCheckColumns = (columnName) => {
        return [
            {
                dataIndex: 'column1',
                keys: 'column1',
                title: columnName[0],
                width: 480,
                render: (text) => {
                    return (
                        <span style={{ display: 'inline-block' }}>
                            <Ellipsis widthLimit={400} title={text} />
                        </span>
                    );
                }
            },
            {
                dataIndex: 'column2',
                keys: 'column2',
                title: columnName[1],
                render: (text) => {
                    return (
                        <span style={{ display: 'inline-block' }}>
                            <Ellipsis widthLimit={400} title={text} />
                        </span>
                    );
                }
            }
        ];
    };

    return (
        Object.keys(checkTable).length > 0 &&
        Object.keys(errSumList).length > 0 &&
        Object.keys(checkTable)?.length === Object.keys(errSumList).length && (
            <div style={style}>
                <div className="container-header">
                    {isAccept ? (
                        <Result status="success" title={I18N.steps.one.quanBuJiaoYanTong} />
                    ) : (
                        <Result
                            status="error"
                            title={I18N.steps.one.jiaoYanWeiTongGuo}
                            subTitle={Object.keys(errSumList)?.map((item, index) => {
                                if (errSumList[item] > 0) {
                                    return (
                                        <div>
                                            <span>{tabMap[item]}：</span>
                                            <span>
                                                {I18N.steps.one.you}
                                                {errSumList[item]}
                                                {I18N.steps.one.geZuJianJiaoYan}
                                            </span>
                                        </div>
                                    );
                                }
                            })}
                        />
                    )}
                </div>
                <div className="container-bottom">
                    {window.auth('ImportHistory', 'DownLoadCheckedFile') && (
                        <div
                            className="checkTop-icon"
                            onClick={() => {
                                let params = {
                                    type: 'checkInfo',
                                    taskId: record?.importTaskId || taskId,
                                    componentShowType: 'xls'
                                };
                                let curName;
                                if (fileName) {
                                    curName = fileName;
                                } else {
                                    curName = record?.importFileName.substring(0, record?.importFileName.lastIndexOf('.'));
                                }
                                baseAPI.listDownload(params, I18N.template(I18N.steps.one.cURNA, { val1: curName || record }));
                            }}>
                            <TntdIcon type="download" />
                            <span>{I18N.steps.one.xiaZaiXiangXiXiao}</span>
                        </div>
                    )}
                    {step === 1 && (
                        <>
                            <Tabs defaultActiveKey="0">
                                {Object.keys(checkTable)?.map((item, index) => {
                                    return (
                                        <TabPane
                                            tab={
                                                <>
                                                    <Badge style={{ top: '-4px', right: '103%' }} count={errSumList[item]}>
                                                        {tabMap[item]}
                                                    </Badge>

                                                    <Tooltip title={tabTooltipMap[item]}>
                                                        <AIcon className="ml10" type="question-circle" />
                                                    </Tooltip>
                                                </>
                                            }
                                            key={index}>
                                            <Collapse expandIconPosition="left">
                                                {checkTable[item]?.map((item1, index1) => {
                                                    // 不展示名单集校验结果
                                                    return (
                                                        <Panel
                                                            disabled={item1?.rows?.length <= 0}
                                                            extra={
                                                                <div>
                                                                    {item1?.rows?.length <= 0 ? (
                                                                        <>
                                                                            <span style={{ margin: '0px 4px' }}>
                                                                                <AIcon
                                                                                    type="check-circle"
                                                                                    theme="twoTone"
                                                                                    twoToneColor="#52c41a"
                                                                                />
                                                                            </span>
                                                                            <span style={{ margin: '0px 4px' }}>
                                                                                {I18N.steps.one.gongJiaoYan}
                                                                                {item1?.totalNum}
                                                                                {I18N.steps.one.tiao}
                                                                            </span>
                                                                            <span>{I18N.steps.one.jiaoYanQuanBuTong}</span>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            {item === 'lackCheck' ? (
                                                                                <span>
                                                                                    {I18N.steps.one.gong}
                                                                                    {item1?.totalNum}
                                                                                    {I18N.steps.one.tiao}
                                                                                </span>
                                                                            ) : (
                                                                                <span>
                                                                                    {I18N.steps.one.gongJiaoYan}
                                                                                    {item1?.totalNum}
                                                                                    {I18N.steps.one.tiao}
                                                                                </span>
                                                                            )}

                                                                            {item !== 'lackCheck' && <span>,</span>}
                                                                            {item !== 'lackCheck' && (
                                                                                <>
                                                                                    <span style={{ color: '#ee6722', margin: '0px 4px' }}>
                                                                                        {item1?.errorNum || 0}
                                                                                    </span>
                                                                                    <span>{I18N.steps.one.tiaoJiaoYanWeiTong}</span>
                                                                                </>
                                                                            )}
                                                                        </>
                                                                    )}
                                                                </div>
                                                            }
                                                            header={
                                                                <div>
                                                                    <span className="active-button">{titleMap[item1?.title]}</span>
                                                                </div>
                                                            }
                                                            key={index1}
                                                            style={{ background: '#FBFCFF' }}>
                                                            <div className="content-row">
                                                                {item === 'configCheck' && (
                                                                    <FourColumns
                                                                        item={item}
                                                                        data={item1?.rows}
                                                                        size={3}
                                                                        columnName={item1.columnName}
                                                                    />
                                                                )}
                                                                {(item === 'userAuthCheck' || item === 'envAuthCheck') && (
                                                                    <ThreeColumns
                                                                        item={item}
                                                                        data={item1?.rows}
                                                                        columnName={item1.columnName}
                                                                        size={3}
                                                                    />
                                                                )}
                                                                {item === 'lackCheck' && (
                                                                    <Table
                                                                        columns={missingCheckColumns(item1?.columnName)}
                                                                        dataSource={item1?.rows}
                                                                    />
                                                                )}
                                                            </div>
                                                        </Panel>
                                                    );
                                                })}
                                            </Collapse>
                                        </TabPane>
                                    );
                                })}
                            </Tabs>
                            <div style={{ margin: '0px 0px 20px 20px' }}>
                                {I18N.steps.one.zhuJiaoYanTongGuo}
                            </div>
                        </>
                    )}
                </div>
            </div>
        )
    );
};
export default One;
