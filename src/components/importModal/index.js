import I18N from '@/utils/I18N';
import { useState, useEffect, useRef } from 'react';
import { Modal, message, Button } from 'tntd';
import { connect } from 'dva';
import TdSteps from '@/components/TdSteps';
import service from './service';
import './index.less';
import Zero from './steps/Zero.js';
import One from './steps/One.js';
import Two from './steps/Two.js';
import Three from './steps/Three.js';
import { modalTitleMap } from './constant';

const { Step } = TdSteps;

const ImportModal = (props) => {
    const { visible, onCancel, onRefresh, type, curStep, record, className, dispatch, modelType, setVisible, setRecord } = props;
    const [fileName, setFileName] = useState();
    const [fileLists, setFileLists] = useState([]);
    const [taskId, setTaskId] = useState();
    const [checkTable, setCheckTable] = useState([]);
    const [importMode, setImportMode] = useState({});
    const [isNext, setIsNext] = useState(true);
    const [multipartfile, setMultipartfile] = useState([]);
    const [details, setDetails] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isAccept, setIsAccept] = useState(true);

    const [mode, setMode] = useState();
    const [disabled, setDisabled] = useState(false);

    // mode:1 根据指定机构渠道导入  mode:2 根据源文件机构渠道导入
    // step
    // 0 导入文件
    // 1 系统校验
    // 2 导入模型选择
    // 3 导入结果

    const [step, setStep] = useState(0);

    const ZeroRef = useRef();

    useEffect(() => {
        if (curStep && record) {
            if (curStep === 1) {
                service.checkInfo({ taskId: record?.importTaskId }).then((res) => {
                    if (res?.success) {
                        const { checkTable = {}, importMode = {}, taskId, isNext } = res.data;
                        setTaskId(taskId);
                        setCheckTable(checkTable);
                        setImportMode(importMode);
                        setIsNext(isNext);
                    }
                });
            } else if (curStep === 3) {
                if (type === 'RESULT') {
                    //导入结果
                    service.importInfo({ taskId: record?.importTaskId }).then((res) => {
                        if (res?.success) {
                            const { details = [] } = res.data;
                            setDetails(details);
                        }
                    });
                } else if (type === 'ROLLBACK') {
                    //回滚
                    service.rollbackHandle({ importTaskId: record?.importTaskId }).then((res) => {
                        if (res?.success) {
                            const { details = [] } = res.data;
                            setDetails(details);
                            setVisible(true);
                            onRefresh();
                        } else {
                            setRecord();
                        }
                    });
                } else if (type === 'RESULT_VIEW') {
                    //回滚结果查看
                    service.rollbackResult({ importTaskId: record?.importTaskId }).then((res) => {
                        if (res?.success) {
                            const { details = [] } = res.data;
                            setDetails(details);
                        }
                    });
                }
            }

            setStep(curStep);
        }
    }, [curStep, record]);

    useEffect(() => {
        if (details?.length > 0) {
            let errSum = 0;
            for (let i in details) {
                if (details[i].errorNum > 0) {
                    errSum++;
                    setIsAccept(false);
                    break;
                }
            }
            errSum === 0 && setIsAccept(true);
        }
    }, [details]);
    const getTitle = () => {
        // record表示是通过导入历史进入的组件
        if (record) {
            if (curStep === 1) {
                return record.importTaskId + I18N.importmodal.index.daoRuPiCiXiao;
            } else if (curStep === 3) {
                if (type === 'ROLLBACK' || type === 'RESULT_VIEW') {
                    return record.importTaskId + I18N.importmodal.index.daoRuPiCiHui;
                }
                return record.importTaskId + I18N.importmodal.index.daoRuPiCiDao;
            }
        } else {
            return I18N.template(I18N.importmodal.index.mODAL, { val1: modalTitleMap[type] });
        }
    };
    const goNext = () => {
        if (step === 3 || record) {
            onCancel();
        } else {
            ZeroRef.current.form.validateFields(async (errors, data) => {
                if (!errors) {
                    if (step === 0) {
                        // 接口校验
                        let params = {
                            componentCategory: type,
                            env: data?.mode,
                            modelType,
                            file: multipartfile
                        };
                        setMode(data?.mode);
                        setLoading(true);
                        service
                            .importComponent(params)
                            .then((res) => {
                                if (res?.success) {
                                    const { checkTable = {}, importMode = {}, taskId, isNext } = JSON.parse(res?.data);
                                    setStep(step + 1);
                                    setTaskId(taskId);
                                    setCheckTable(checkTable);
                                    setImportMode(importMode);
                                    setIsNext(isNext);
                                } else {
                                    message.error(res?.message);
                                }
                            })
                            .finally(() => {
                                setLoading(false);
                            });
                    } else if (step === 1) {
                        setStep(step + 1);
                    } else if (step === 2) {
                        let map = {};
                        let flag = true;
                        importMode?.typeModeList?.map((item) => {
                            let count = 0;
                            item?.subModeList?.map((item1) => {
                                if (item1.selected) {
                                    map[item.category] = item1.code;
                                    count++;
                                }
                            });
                            if (count !== 1) {
                                flag = false;
                            }
                        });
                        if (!flag) {
                            return message.warning(I18N.importmodal.index.meiGeZuJianYou);
                        }
                        // 导入模型选择
                        let params = {
                            taskId,
                            componentCategory: type,
                            modeMap: JSON.stringify(map),
                            dependentFlag: ZeroRef?.current?.form?.getFieldValue('dependentFlag')
                        };
                        setLoading(true);
                        service
                            .importConfirm(params)
                            .then(async (res) => {
                                if (res?.success) {
                                    const { details = [] } = JSON.parse(res?.data);
                                    setDetails(details);
                                    setStep(step + 1);
                                } else {
                                    //导入最后一步如果报系统错误就把下一步按钮置灰，直到下一次重新打开导入弹窗才变成正常
                                    setDisabled(true);
                                }
                                await dispatch({
                                    type: 'global/getAllMap'
                                });
                            })
                            .finally(() => {
                                setLoading(false);
                            });
                        // 导入成功后刷新字段
                    }
                }
            });
        }
    };

    return (
        <>
            <Modal
                width={1000}
                maskClosable={false}
                title={getTitle()}
                className={'m-import-modal ' + className}
                visible={visible}
                onCancel={() => {
                    onCancel();
                    setDisabled(false);
                }}
                afterClose={() => {
                    setMultipartfile();
                    setFileLists([]);
                    setCheckTable({});
                    setImportMode({});
                    setTaskId();
                    setIsNext(true);
                    !record && setStep(0);
                }}
                footer={
                    <>
                        {!record && step !== 3 && (
                            <Button
                                onClick={() => {
                                    onCancel();
                                }}>
                                {I18N.importmodal.index.quXiao}
                            </Button>
                        )}
                        {step !== 0 && step !== 3 && !record && (
                            <Button
                                disabled={disabled}
                                type="primary"
                                onClick={() => {
                                    //回到第一步清除系统校验列表数据 防止渲染闪屏
                                    if (step === 1) {
                                        setCheckTable({});
                                    }
                                    setStep(step - 1);
                                    setIsNext(true);
                                }}>
                                {I18N.importmodal.index.shangYiBu}
                            </Button>
                        )}
                        {step === 3 && type !== 'RESULT' && type === 'RESULT_VIEW' && !isAccept && (
                            <Button
                                loading={loading}
                                onClick={() => {
                                    setLoading(true);
                                    service
                                        .rollbackHandle({ importTaskId: taskId || record?.importTaskId })
                                        .then((res) => {
                                            if (res?.success) {
                                                message.success(I18N.importmodal.index.caoZuoChengGong);
                                                onCancel();
                                            }
                                        })
                                        .finally(() => {
                                            setLoading(false);
                                        });
                                }}>
                                {I18N.importmodal.index.huiGun}
                            </Button>
                        )}
                        <Button
                            type="primary"
                            disabled={!isNext || disabled}
                            onClick={() => {
                                goNext();
                            }}
                            loading={loading}>
                            {step === 3 || record ? I18N.importmodal.index.guanBi : I18N.importmodal.index.xiaYiBu}
                        </Button>
                    </>
                }
                destroyOnClose>
                {!record && (
                    <TdSteps progressDot current={step}>
                        <Step className={step > 0 && 'item-processed'} title={I18N.importmodal.index.diYiBuDaoRu} />
                        <Step className={step > 1 && 'item-processed'} title={I18N.importmodal.index.diErBuXiTong} />
                        <Step className={step > 2 && 'item-processed'} title={I18N.importmodal.index.diSanBuDaoRu} />
                        <Step className={step > 3 && 'item-processed'} title={I18N.importmodal.index.diSiBuDaoRu} />
                    </TdSteps>
                )}
                {
                    <Zero
                        className="step-zero"
                        style={{ display: step === 0 ? 'block' : 'none' }}
                        {...props}
                        onRef={(ref) => {
                            ZeroRef.current = ref;
                        }}
                        fileLists={fileLists}
                        setFileLists={setFileLists}
                        setFileName={setFileName}
                        fileName={fileName}
                        setMultipartfile={setMultipartfile}
                        modelType={modelType}
                    />
                }
                {
                    <One
                        isNext={isNext}
                        step={step}
                        className="step-one"
                        style={{ display: step === 1 ? 'block' : 'none' }}
                        {...props}
                        taskId={taskId}
                        record={record}
                        checkTable={checkTable}
                        fileName={fileName}
                    />
                }
                {
                    <Two
                        mode={mode}
                        className="step-two"
                        style={{ display: step === 2 ? 'block' : 'none' }}
                        {...props}
                        importMode={importMode}
                        setImportMode={setImportMode}
                    />
                }
                {
                    <Three
                        className="step-three"
                        style={{ display: step === 3 ? 'block' : 'none' }}
                        {...props}
                        taskId={taskId}
                        record={record}
                        details={details}
                        fileName={fileName}
                    />
                }
            </Modal>
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ImportModal);
