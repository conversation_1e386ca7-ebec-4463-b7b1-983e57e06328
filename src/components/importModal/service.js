import request, { PostForm } from '@/utils/request';
import { getHeader, getUrl, deleteEmptyObjItem } from './common';

// 导入校验接口
const importComponent = async (param) => {
    return PostForm(`/component/import/check/${param.componentCategory}`, 'POST', deleteEmptyObjItem(param));
};

// 导入确认通用接口
const importConfirm = async (params) => {
    return request(`/component/import/confirm/${params.componentCategory}`, {
        method: 'POST',
        body: params
    });
};

// 导入历史列表
const getList = async (params) => {
    return request(getUrl('/component/import/history/list', params), {
        method: 'GET'
    });
};

// 导入历史校验信息
const checkInfo = async (params) => {
    return request(getUrl('/component/import/history/checkInfo', params), {
        method: 'GET'
    });
};

// 导入历史导入信息
const importInfo = async (params) => {
    return request(getUrl('/component/import/history/importInfo', params), {
        method: 'GET'
    });
};
// 导入历史查询条件
const historyCondition = async (params) => {
    return request(getUrl('/component/import/history/condition', params), {
        method: 'GET'
    });
};
// 授权
const rollbackHandle = (params) => {
    return request('/component/rollback/handle ', {
        method: 'POST',
        headers: {
            ...getHeader()
            // 'Content-Type': 'application/json'
        },
        body: { ...params }
    });
};
// 导入历史查询条件
const rollbackResult = async (params) => {
    return request(getUrl('/component/rollback/result', params), {
        method: 'GET'
    });
};
export default { importComponent, importConfirm, getList, checkInfo, importInfo, historyCondition, rollbackHandle, rollbackResult };
