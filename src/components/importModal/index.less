.m-import-modal {
    .container-bottom {
        .ant-tabs {
            margin: 0px 20px 20px 20px !important;
        }
    }
    .result-table {
        .ant-table-column-title {
            color: #17233d;
            font-weight: 500;
        }
        th,
        td {
            padding: 9px 40px !important;
            color: #454f64;
        }
    }
    .ant-table-thead {
        th {
            background: white;
        }
        th {
            border-right: 0px !important;
        }
    }
    .ant-table-row-level-0 {
        td {
            border-right: 0px !important;
        }
    }

    .ant-collapse {
        border: none !important;
        background: white !important;
    }
    .ant-collapse-item {
        border: 2px solid #f0f2f6;
        margin-bottom: 20px;
    }
    .ant-collapse-item-disabled {
        .ant-collapse-header {
            color: #17233d !important;
            i {
                color: rgba(23, 35, 61, 0.3) !important;
            }
        }
    }
    .content-row {
        padding: 0px !important;
    }
    .ant-collapse-content-box {
        padding: 0px !important;
    }
    .ant-modal-title {
        font-size: 14px !important;
    }
    .ant-modal-close-x {
        line-height: 40px !important;
    }
    .ant-modal-body {
        position: relative;
        padding: 0px !important;
        .ant-form {
            margin: 45px 0px 50px 0px;
            .ant-form-item {
                margin-bottom: 8px !important;
            }
            .ant-form-item-label {
                width: 40% !important;
                display: block;
                box-sizing: border-box;
            }
            .ant-form-item-control-wrapper {
                width: 29.5% !important;
                display: block;
                box-sizing: border-box;
            }
        }
        .ant-tabs {
            margin: 20px 20px 30px 20px;
        }
        .columns-container:last-child {
            margin-bottom: 40px;
        }
    }
    .ant-modal-header {
        padding: 10px 20px !important;
    }
    .content-row {
        padding: 12px 12px 6px 16px;
        span {
            .ant-checkbox-wrapper {
                margin-right: 18px;
                display: inline-block;
                padding-bottom: 10px;
            }
        }

        .content-row {
            max-width: 276px;
            height: 20px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }
    .active-button {
        position: absolute;
        display: flex;
        align-items: center;
        width: 100%;
        padding: 10px;
        top: 0px;
        left: 30px;
        height: 48px;
    }
    .history-button {
        position: absolute;
        display: flex;
        align-items: center;
        padding: 10px;
        top: 0px;
        left: 170px;
        height: 48px;
    }
    .ant-collapse-header {
        background: #f1f4fa;
        height: 48px !important;
        padding: 21px 0px !important;
        .ant-collapse-arrow {
            font-size: 14px !important;
            left: 16px !important;
        }
    }
    .ant-collapse-extra {
        div {
            position: relative;
            right: 20px;
            top: -8px;
        }
    }
    .ant-collapse-content {
        width: 100%;
    }
    .ant-steps {
        margin-bottom: 20px !important;
    }
    .table-title {
        margin-top: 20px !important;
        // display: flex;
        align-items: center;
        .title-icon {
            display: inline-block;
            border-left: 5px solid #126bfb;
            height: 14px;
            margin-right: 10px;
        }
        .title-header {
            // width: 56px;
            font-size: 14px;
            color: #17233d;
            letter-spacing: 0;
            font-weight: 500 !important;
        }
        .info-header {
            font-family: PingFangSC-Medium;
            font-size: 12px;
            color: #17233d;
            letter-spacing: 0;
            font-weight: 500;
        }
        .title-content {
            width: 400px;
            height: 20px;
            font-size: 14px;
            color: #8b919e;
            letter-spacing: 0;
            .content-page {
            }
        }
    }
    .title-text {
        width: 420px;
        text-align: right;
        display: inline-block;
        margin-bottom: 20px;
    }
    .content-text {
        width: 500px;
        text-align: left;
        display: inline-block;
        margin-left: 27px;
        .ant-checkbox-inner {
            border: 1px solid rgba(184, 195, 210, 1);
            border-radius: 2px;
        }
    }
    .item-processed {
        background: #126bfb !important;
        opacity: 0.6;
        .td-steps-item-content {
            color: white !important;
        }
        &::before {
            border-left-color: #126bfb;
        }
        &::after {
            border-left-color: red;
        }
    }

    .td-steps-item-process:before {
        border-left-color: #7aa6fe;
        z-index: 50;
    }
    .td-steps {
        .td-steps-item:nth-of-type(1):after {
            border: none;
        }
    }
    .td-steps-item:after {
        content: "";
        height: 0;
        width: 0;
        position: absolute;
        display: block;
        border-left: 20px solid #fff;
        border-top: 18px solid transparent;
        border-bottom: 18px solid transparent;
        left: 2px;
        top: 0;
        z-index: 30;
    }

    .td-steps-item:before {
        z-index: 50 !important;
    }
    .checkTop-icon {
        z-index: 10;
        cursor: pointer;
        display: flex;
        position: absolute;
        left: 835px;
        top: 65px;
        span {
            color: #126bfb;
        }
        i {
            margin: 0px 7px;
            font-size: 16px;
            color: #126bfb;
        }
    }
    .import-result {
        margin: 70px 20px 20px 20px;
        .ant-collapse {
            margin-bottom: 10px;
        }
        .resultTop-icon {
            z-index: 10;
            cursor: pointer;
            display: flex;
            position: absolute;
            left: 835px;

            top: 65px;
            span {
                color: #126bfb;
            }
            i {
                margin: 0px 7px;
                font-size: 16px;
                color: #126bfb;
            }
        }
    }
    .step-two {
        .ant-row {
            margin-bottom: 5px;
        }
    }
}
