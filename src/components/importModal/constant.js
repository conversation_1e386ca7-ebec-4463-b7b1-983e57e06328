import I18N from '@/utils/I18N';
const tabMap = {
    configCheck: I18N.importmodal.constant.xiangTongZuJian<PERSON>ei,
    envAuthCheck: I18N.importmodal.constant.huanJingXinXiQue,
    lackCheck: I18N.importmodal.constant.zu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    userAuthCheck: I18N.importmodal.constant.yongHuQuanXianXiao
};
const tabTooltipMap = {
    configCheck: I18N.importmodal.constant.xiangTongZuJian<PERSON>ao,
    envAuthCheck:
        I18N.importmodal.constant.dang<PERSON>ian<PERSON><PERSON><PERSON>u<PERSON><PERSON>,
    lackCheck: I18N.importmodal.constant.zu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    userAuthCheck: I18N.importmodal.constant.yongHuCaoZuoQuan
};
const titleMap = {
    SCORE_CARD: I18N.importmodal.constant.pingFenKa,
    DICTIONARY: I18N.importmodal.constant.zi<PERSON>ian,
    SYSTEM_FIELD: I18N.importmodal.constant.xiTongZiDuan,
    SCRIPT_FIELD: I18N.importmodal.constant.dongTaiZiDuan,

    // 基础组件 2000
    THIRD_SERVICE: I18N.importmodal.constant.sanFang,
    HOLMES: I18N.importmodal.constant.moXing,
    NAME_LIST: I18N.importmodal.constant.mingDan,
    FUNCTION: I18N.importmodal.constant.hanShuKu,
    FUNCTION_VERSION: I18N.importmodal.constant.hanShuKu,
    DECISION_TOOL: I18N.importmodal.constant.jueCeGongJu,
    SERVICE: I18N.importmodal.constant.jiGouDuiJieFu,

    // 业务组件 3000
    INDEX_REALTIME: I18N.importmodal.constant.shiShiZhiBiao,
    INDEX_OFFLINE: I18N.importmodal.constant.liXianZhiBiao,
    RULE: I18N.importmodal.constant.guiZe,
    RULE_SET: I18N.importmodal.constant.guiZeJi,
    POLICY: I18N.importmodal.constant.ceLue
};

const crossProjectMap = {
    CREATE: I18N.importmodal.constant.ziDongXinJian,
    AUTH: I18N.importmodal.constant.shouQuanGuiZeJi,
    COVER: I18N.importmodal.constant.fuGai,
    SKIP: I18N.importmodal.constant.tiaoGuo,
    CREATE_VERSION: I18N.importmodal.constant.chuangJianBanBen
};
const exportMap = {
    FUNCTION: 'fun',
    POLICY: 'pls',
    SCORE_CARD: 'card',
    RULE_SET: 'rss',
    DECISION_TOOL: 'dstt,dste,dstd',
    TREE: 'dstt',
    TABLE: 'dste',
    METRIC: 'dstd'
};

const modalTitleMap = {
    SCORE_CARD: I18N.importmodal.constant.pingFenKa,
    POLICY: I18N.importmodal.constant.ceLue,
    RULE_SET: I18N.importmodal.constant.guiZeJi,
    FUNCTION: I18N.importmodal.constant.hanShuKu,
    DECISION_TOOL: I18N.importmodal.constant.jueCeGongJu,
    subflow: I18N.importmodal.constant.liuChengMuBan
};
const sameCrossEnv = {
    SCORE_CARD: I18N.importmodal.constant.pingFenKa,
    SYSTEM_FIELD: I18N.importmodal.constant.xiTongZiDuanBiao,
    SCRIPT_FIELD: I18N.importmodal.constant.dongTaiZiDuanBiao,

    // 基础组件 2000
    THIRD_SERVICE: I18N.importmodal.constant.sanFangShuJuBiao,
    HOLMES: I18N.importmodal.constant.moXingBiaoZhiYi,
    NAME_LIST: I18N.importmodal.constant.mingDanBiaoZhiYi,
    FUNCTION: I18N.importmodal.constant.hanShuKuBiaoZhi,
    FUNCTION_VERSION: I18N.importmodal.constant.hanShuKu,
    DECISION_TOOL: I18N.importmodal.constant.jueCeGongJuBiao,
    SERVICE: I18N.importmodal.constant.jiGouDuiJieFu,

    // 业务组件 3000
    INDEX_REALTIME: I18N.importmodal.constant.shiShiZhiBiaoBiao,
    INDEX_OFFLINE: I18N.importmodal.constant.liXianZhiBiaoBiao,
    RULE_SET: I18N.importmodal.constant.guiZeJiUU,
    POLICY: I18N.importmodal.constant.ceLueBiaoZhiYi
};
const CrossProjectEnv = {
    SCORE_CARD: I18N.importmodal.constant.pingFenKa,
    SYSTEM_FIELD: I18N.importmodal.constant.xiTongZiDuanBiao,
    SCRIPT_FIELD: I18N.importmodal.constant.dongTaiZiDuanBiao,

    // 基础组件 2000
    THIRD_SERVICE: I18N.importmodal.constant.sanFangShuJuBiao,
    HOLMES: I18N.importmodal.constant.moXingBiaoZhiYi,
    NAME_LIST: I18N.importmodal.constant.mingDanBiaoZhiYi,
    FUNCTION: I18N.importmodal.constant.hanShuKuBiaoZhi,
    FUNCTION_VERSION: I18N.importmodal.constant.hanShuKu,
    DECISION_TOOL: I18N.importmodal.constant.zhiDingJiGouQu,
    SERVICE: I18N.importmodal.constant.jiGouDuiJieFu,

    // 业务组件 3000
    INDEX_REALTIME: I18N.importmodal.constant.shiShiZhiBiaoBiao,
    INDEX_OFFLINE: I18N.importmodal.constant.liXianZhiBiaoBiao,
    RULE_SET: I18N.importmodal.constant.guiZeJiUU,
    POLICY: I18N.importmodal.constant.ceLueBiaoZhiYi
};

export { CrossProjectEnv, tabMap, titleMap, crossProjectMap, exportMap, modalTitleMap, sameCrossEnv, tabTooltipMap };
