/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-10-09 11:51:30
 * @Describe: 报文展示组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import PropTypes from 'prop-types';
import { Popover, Tooltip, Icon, message } from 'tntd';
import Clipboard from 'react-clipboard.js';
import './index.less';
import { isJSON } from '@/utils/isJSON';

class MessageBox extends PureComponent {

	clipboardHandle() {
		message.success(I18N.messagebox.index.fuZhiChengGong); // 复制成功
	}

	render() {
		const { content, placement } = this.props;

		const contents = (
			<div className="m-report-message">
				<p>
					{/* 参数详情 */}
					{I18N.messagebox.index.canShuXiangQing}
				</p>
				<div className="content">
					<pre>
						{
							isJSON(content) ? JSON.stringify(JSON.parse(content), null, 4) : content
						}
					</pre>
				</div>
				<Tooltip
					title={I18N.messagebox.index.fuZhi} // 复制
				>
					<div
						style={{
							position: 'absolute',
							right: '32px',
							top: '16px',
							color: '#03A9F4'
						}}
					>
						<Clipboard
							data-clipboard-text={content}
							onSuccess={() => this.clipboardHandle()}
							style={{
								border: 'none',
								outline: 'none',
								cursor: 'pointer',
								padding: 0,
								backgroundColor: '#ffffff'
							}}
						>
							<Icon type="copy" />
						</Clipboard>
					</div>
				</Tooltip>
			</div>
		);

		return (
			<Popover content={contents} placement={placement}>
				{this.props.children}
			</Popover>
		);
	}
}

MessageBox.propTypes = {
	content: PropTypes.string,
	placement: PropTypes.string
};

MessageBox.defaultProps = {
	content: '',
	placement: 'top'
};

export default connect(state => ({
	globalStore: state.global
}))(MessageBox);

