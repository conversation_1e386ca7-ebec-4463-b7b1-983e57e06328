/*
 * @CreatDate: 2018-10-09 11:51:30
 * @Describe: 无权限公共组件
 */
import I18N from '@/utils/I18N';
import { Empty } from 'tntd';
import { PureComponent } from 'react';
import { connect } from 'dva';
import './index.less';

class NoPermission extends PureComponent {
    render() {
        const { title } = this.props;

        return (
            <div className="m-no-permission">
                <Empty type="no-permission" description={title ? title : I18N.nopermission.index.zanWuQuanXian} size="ultra" />

                {/* <div className="no-permisson" /> */}
                {/* 暂无权限 */}
                {/* <p className="txt">{title ? title : '暂无权限'}</p> */}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(NoPermission);
