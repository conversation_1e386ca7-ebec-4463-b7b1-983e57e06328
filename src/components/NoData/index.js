import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Empty } from 'tntd';
import { otherImages } from '@/constants/images';
class index extends PureComponent {
    render() {
        const { content = I18N.nodata.index.zanWuShuJu, top, type } = this.props;
        return (
            <div className="m-no-data" style={top ? { paddingTop: top } : {}}>
                {/* <img src={otherImages.noData} alt="" />
				<p>{content}</p> */}
                <Empty type={type || 'no-result'} description={content} />
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(index);
