.dragm-title{
    .icon{
        float: right;
        margin-right: 30px;
        font-size: 12px;
        color: #2196F3;
        i{
            font-size: 16px;
            vertical-align: text-bottom;
            margin-left: 10px;
            transform: rotate(45deg) scale(0.8);
            animation-name: changeSize;
            /* 动画指定需要多少秒或毫秒完成 */
            animation-duration: 3s;
            /* 动画将如何完成一个周期 */
            animation-timing-function: linear;
            /* 定义动画应该播放多少次 */
            animation-iteration-count: infinite;
        }
    }
}
@keyframes changeSize {
    0% {
        transform: rotate(45deg) scale(0.8);
    }
    25% {
        transform: rotate(45deg) scale(0.9);
    }
    50% {
        transform: rotate(45deg) scale(1.1);
    }
    75% {
        transform: rotate(45deg) scale(0.9);
    }
    100% {
        transform: rotate(45deg) scale(0.8);
    }
}
.ant-modal-wrap-test{
    bottom: auto;
    .m-formula-test-modal{
        top: 0;
    }
}