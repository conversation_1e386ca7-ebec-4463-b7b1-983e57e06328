import I18N from '@/utils/I18N';
import './index.less';
import { PureComponent } from 'react';
import { Icon } from 'tntd';
import DragM from 'dragm';

export default class DragModalTitle extends PureComponent {
    updateTransform = (transformStr) => {
        this.modalDom.style.transform = transformStr;
    };
    componentDidMount() {
        const { modalClass } = this.props;
        this.modalDom = document.querySelector(`.${modalClass}`).parentNode;
        if (document.querySelector('.m-formula-test-modal')) {
            document.querySelector('.m-formula-test-modal').parentElement.className = 'ant-modal-wrap ant-modal-wrap-test';
        }
    }
    render() {
        const { title } = this.props;
        return (
            <DragM updateTransform={this.updateTransform}>
                <div className="dragm-title">
                    {title}
                    <span className="icon">
                        {I18N.dragmodaltitle.index.zhiChiTuoDong}
                        <Icon type="fullscreen" />
                    </span>
                </div>
            </DragM>
        );
    }
}
