import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Modal, Button } from 'tntd';
import { connect } from 'dva';

import '../NoOperate/index.less';

class MultiUser extends PureComponent {

	goToLogin() {
		const { dispatch } = this.props;
		dispatch({
			type: 'global/setAttrValue',
			payload: {
				multiUserModal: false
			}
		});
		dispatch({
			type: 'login/goLogin'
		});
	}

	render() {
		const { globalStore } = this.props;
		const { multiUserModal } = globalStore || {};
		return (
			<div className="m-no-operate">
				<Modal
					className="m-no-operate-modal"
					visible={multiUserModal}
					closable={false}
					footer={null}
					width={450}
				>
					<div className="u-pic"></div>
					<p>{I18N.multiuser.index.dangQianZhangHaoZai}</p>
					<p>{I18N.multiuser.index.dianJiQueDingZhong}</p>
					<div className="btn">
						<Button
							type="primary"
							onClick={this.goToLogin.bind(this)}
						>{I18N.multiuser.index.queDing}</Button></div>
				</Modal>
			</div>
		);
	}
}

export default connect(state => ({
	loginStore: state.login,
	globalStore: state.global
}))(MultiUser);
