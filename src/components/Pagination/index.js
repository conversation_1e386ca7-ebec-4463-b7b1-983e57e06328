import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent } from 'react';
import { Pagination, Select, Input } from 'tntd';
import { connect } from 'dva';

const Option = Select.Option;

class index extends PureComponent {

	state = {
		inputValue: null
	}

	changeField = (e) => {
		this.setState({
			inputValue: e.target.value
		});
	}

	pressEnter = (e) => {
		const { pageSize = 10 } = this.props;
		const val = e.target.value;
		// /^\d+$/
		var re = /^\d+$/;
		if (re.test(val)) {
			this.props.onChange(parseInt(val, 10), pageSize);
		}
		this.setState({ inputValue: null });
	}

	blur = () => {
		const { pageSize = 10 } = this.props;
		const { inputValue } = this.state;
		// /^\d+$/
		var re = /^\d+$/;
		if (re.test(inputValue)) {
			this.props.onChange(parseInt(inputValue, 10), pageSize);
		}
		this.setState({ inputValue: null });
	}

	render() {
		const { total = 500, pageSize = 10, curPage = 1 } = this.props;
		const { inputValue } = this.state;

		return (
			<div className="tnt-pagination">
				<span>
					{/* 每页 */}
					{I18N.pagination.index.meiYe}
					<Select
						defaultValue={10}
						onChange={(e) => this.props.onShowSizeChange(curPage, e)}
					>
						<Option value={10}>10</Option>
						<Option value={20}>20</Option>
						<Option value={30}>30</Option>
						<Option value={40}>40</Option>
					</Select>
					{/* 条 */}
					{I18N.pagination.index.tiao}
				</span>
				<Pagination
					size="small"
					total={total}
					current={curPage}
					pageSize={pageSize}
					onChange={(page, pageSize) => this.props.onChange(page, pageSize)}
				/>
				<span>
					{/* 前往 */}
					{I18N.pagination.index.qianWang}
					<Input
						value={inputValue}
						onChange={this.changeField}
						onPressEnter={this.pressEnter}
						onBlur={this.blur}
					/>
					{/* 页 */}
					{I18N.pagination.index.ye}
				</span>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(index);
