/*
 * @CreatDate: 2020-12-24 10:20:30
 * @Describe: 简单封装table页面，模块配置化，集成权限校验
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useEffect } from 'react';
import NoPermission from '@/components/NoPermission';
import { Button, QueryListScene } from 'tntd';

const { QueryForm, Field, Toolbar, QueryList, createActions } = QueryListScene;
let actionsMap = {};

export default (props) => {
    const { title, query, queryList, queryForm = [], queryFormProps, toolbar = [], hasPermission = false, actionKey } = props;

    actionsMap[actionKey] = actionsMap[actionKey] || createActions();

    const toolbarHasPermission = toolbar.find((k) => k.hasPermission);

    useEffect(() => {
        props.actions(actionsMap[actionKey]);
        window.eventEmitter.on('menuClick', (flag) => {
            if (flag) {
                actionsMap[actionKey] && actionsMap[actionKey].resetMemoryData();
            }
        });
    }, []);

    return (
        <div className="g-tablePage">
            {hasPermission && (
                <QueryListScene memory title={title} query={query} actions={actionsMap[actionKey]}>
                    {queryForm && queryForm.length > 0 && (
                        <QueryForm
                            {...queryFormProps}
                            renderActions={() =>
                                toolbar &&
                                toolbar.length > 0 &&
                                toolbarHasPermission &&
                                toolbar.map((item) => {
                                    if (item.hasPermission) {
                                        return item.render;
                                    }
                                })
                            }>
                            {queryForm.map((item) => (
                                <Field {...item} />
                            ))}
                            <Button type="primary" onClick={() => actionsMap[actionKey].search()}>
                                {I18N.pretablepage.index.chaXun}</Button>
                        </QueryForm>
                    )}

                    <QueryList bordered={false} {...queryList} />
                </QueryListScene>
            )}
            {!hasPermission && (
                <>
                    {title ? (
                        <>
                            <div className="page-global-header">
                                <div className="left-info">
                                    <h2>{title}</h2>
                                </div>
                            </div>
                            <NoPermission />
                        </>
                    ) : (
                        <NoPermission />
                    )}
                </>
            )}
        </div>
    );
};
