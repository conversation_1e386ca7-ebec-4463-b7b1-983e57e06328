import request from '@/utils/request';
import { getUrl } from '@/services/common';

// 获取画布节点配置
const getFlowConfig = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/node/config', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取api接口下拉列表
const apiServiceList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/dataSourceService/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取api接口出入参配置
const apiMapping = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/dataSourceService/mapping/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取继续补充服务列表和入参配置
const supplementList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/supplement/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 流程模板下拉接口
const workflowTemplateList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/list', param),
        {
            method: 'GET'
        },
        true
    );
};

// 查看流程模板
const workflowTemplateDetail = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/detail', param),
        {
            method: 'GET'
        },
        true
    );
};

// 流程模板预编译
const preBuild = async (params) => {
    return request(
        '/bridgeApi/workflowTemplate/preBuild',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

export default {
    getFlowConfig,
    apiServiceList,
    apiMapping,
    supplementList,
    workflowTemplateList,
    workflowTemplateDetail,
    preBuild
};
