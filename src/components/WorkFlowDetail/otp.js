import { createOtp } from '@/utils/I18N';
export default createOtp({
    cn: {
        suspendFlowNode: {
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 5 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 19 }
                }
            }
        }
    },

    en: {
        suspendFlowNode: {
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 11 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 13 }
                }
            }
        }
    }
});
