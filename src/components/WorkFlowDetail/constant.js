import I18N from '@/utils/I18N';
export const conditionOperator = {
    STRING: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: 'include',
            dName: I18N.workflowdetail.constant.baoHan,
            enDName: 'include'
        },
        {
            name: 'exclude',
            dName: I18N.workflowdetail.constant.buBaoHan,
            enDName: 'exclude'
        },
        {
            name: 'prefix',
            dName: I18N.workflowdetail.constant.qianZhui,
            enDName: 'prefix'
        },
        {
            name: 'suffix',
            dName: I18N.workflowdetail.constant.houZhui,
            enDName: 'suffix'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.bu<PERSON><PERSON><PERSON><PERSON>,
            enDName: 'notnull'
        },
        {
            name: 'in',
            dName: I18N.workflowdetail.constant.cunZaiYu,
            enDName: 'in'
        },
        {
            name: 'notin',
            dName: I18N.workflowdetail.constant.buCunZaiYu,
            enDName: 'notin'
        }
        // {
        // 	name: "belong",
        // 	dName: "属于",
        // 	enDName: "belong"
        // }, {
        // 	name: "notbelong",
        // 	dName: "不属于",
        // 	enDName: "notbelong"
        // }
    ],
    DOUBLE: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        }
        // {
        // 	name: "in",
        // 	dName: "存在于",
        // 	enDName: "in"
        // },
        // {
        // 	name: "notin",
        // 	dName: "不存在于",
        // 	enDName: "notin"
        // }
        // {
        // 	name: "belong",
        // 	dName: "属于",
        // 	enDName: "belong"
        // }, {
        // 	name: "notbelong",
        // 	dName: "不属于",
        // 	enDName: "notbelong"
        // }
    ],
    INT: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        },
        {
            name: 'in',
            dName: I18N.workflowdetail.constant.cunZaiYu,
            enDName: 'in'
        },
        {
            name: 'notin',
            dName: I18N.workflowdetail.constant.buCunZaiYu,
            enDName: 'notin'
        }
        // {
        // 	name: "belong",
        // 	dName: "属于",
        // 	enDName: "belong"
        // }, {
        // 	name: "notbelong",
        // 	dName: "不属于",
        // 	enDName: "notbelong"
        // }
    ],
    INTEGER: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        },
        {
            name: 'in',
            dName: I18N.workflowdetail.constant.cunZaiYu,
            enDName: 'in'
        },
        {
            name: 'notin',
            dName: I18N.workflowdetail.constant.buCunZaiYu,
            enDName: 'notin'
        }
        // {
        // 	name: "belong",
        // 	dName: "属于",
        // 	enDName: "belong"
        // }, {
        // 	name: "notbelong",
        // 	dName: "不属于",
        // 	enDName: "notbelong"
        // }
    ],
    LONG: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        },
        {
            name: 'in',
            dName: I18N.workflowdetail.constant.cunZaiYu,
            enDName: 'in'
        },
        {
            name: 'notin',
            dName: I18N.workflowdetail.constant.buCunZaiYu,
            enDName: 'notin'
        }
        // {
        // 	name: "belong",
        // 	dName: "属于",
        // 	enDName: "belong"
        // }, {
        // 	name: "notbelong",
        // 	dName: "不属于",
        // 	enDName: "notbelong"
        // }
    ],
    DATE: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        }
        // {
        // 	name: "in",
        // 	dName: "存在于",
        // 	enDName: "in"
        // },
        // {
        // 	name: "notin",
        // 	dName: "不存在于",
        // 	enDName: "notin"
        // }
    ],
    DATETIME: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: '>',
            dName: I18N.workflowdetail.constant.daYu,
            enDName: 'greater than'
        },
        {
            name: '>=',
            dName: I18N.workflowdetail.constant.daYuDengYu,
            enDName: 'equal or greater than'
        },
        {
            name: '<',
            dName: I18N.workflowdetail.constant.xiaoYu,
            enDName: 'less than'
        },
        {
            name: '<=',
            dName: I18N.workflowdetail.constant.xiaoYuDengYu,
            enDName: 'equal or less than'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        }
        // {
        // 	name: "in",
        // 	dName: "存在于",
        // 	enDName: "in"
        // },
        // {
        // 	name: "notin",
        // 	dName: "不存在于",
        // 	enDName: "notin"
        // }
    ],
    BOOLEAN: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        }
        // {
        // 	name: "in",
        // 	dName: "存在于",
        // 	enDName: "in"
        // },
        // {
        // 	name: "notin",
        // 	dName: "不存在于",
        // 	enDName: "notin"
        // }
    ],
    ENUM: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        },
        {
            name: 'isnull',
            dName: I18N.workflowdetail.constant.weiKong,
            enDName: 'isnull'
        },
        {
            name: 'notnull',
            dName: I18N.workflowdetail.constant.buWeiKong,
            enDName: 'notnull'
        },
        {
            name: 'in',
            dName: I18N.workflowdetail.constant.cunZaiYu,
            enDName: 'in'
        },
        {
            name: 'notin',
            dName: I18N.workflowdetail.constant.buCunZaiYu,
            enDName: 'notin'
        }
    ],
    POLICY: [
        {
            name: '==',
            dName: I18N.workflowdetail.constant.dengYu,
            enDName: 'equal'
        },
        {
            name: '!=',
            dName: I18N.workflowdetail.constant.buDengYu,
            enDName: 'unequal'
        }
    ],
    ARRAY: [
        {
            name: 'include',
            dName: I18N.workflowdetail.constant.baoHan,
            enDName: 'include'
        },
        {
            name: 'exclude',
            dName: I18N.workflowdetail.constant.buBaoHan,
            enDName: 'exclude'
        }
    ]
};
