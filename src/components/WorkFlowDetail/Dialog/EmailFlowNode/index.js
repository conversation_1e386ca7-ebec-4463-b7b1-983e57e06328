import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { Modal, Button, Input, Form, Alert } from 'tntd';
import { sliceName } from '@/components/WorkFlowEditor';

const {TextArea} = Input

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
    }
};
export default Form.create({ name: 'email-gateway' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled } = props;
    const { getFieldDecorator, setFieldsValue, resetFields, validateFields } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'MailServiceNode';

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [type]);

    const initData = () => {
        resetFields();
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        setFieldsValue(data || {});
    };

    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const { mailSubject } = data || {};
                editor.schema.data.nodesMap[nodeId].name = mailSubject;
                editor.schema.data.nodesMap[nodeId].data = {...data, name: mailSubject};

                const node = editor.graph.node.nodes[nodeId];
                node.shape.select('text.flow-txt-node').node.innerHTML = sliceName(mailSubject, 6);
                editor.graph.node.updateNode(node.data);
                node.toLines.forEach((line) => {
                    editor.schema.data.linesMap[line].data = {};
                    editor.graph.line.lines[line].data.label = undefined;
                    editor.graph.line.lines[line].data.className = type === 'start' ? 'red-line' : '';
                });
                editor.graph.line.updateByNode(node);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.emailflownode.index.quXiao}</Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.emailflownode.index.queDing}</Button>
    ];

    return (
        <Modal
            title={I18N.emailflownode.index.dianYouYuJing}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={1000}
            onCancel={onCancel}
            footer={disabled ? null : footerDom}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.emailflownode.index.youJianBiaoTi}>
                    {getFieldDecorator('mailSubject', {
                        rules: [
                            {
                                required: true,
                                message: I18N.emailflownode.index.qingShuRuYouJian2
                            }
                        ]
                    })(
                       <Input maxLength={100} />
                    )}
                </Form.Item>
                <Form.Item label={I18N.emailflownode.index.youJianShouJianRen}>
                    {getFieldDecorator('mailToAddress', {
                        rules: [
                            {
                                required: true,
                                message: I18N.emailflownode.index.qingShuRuShouJian
                            }
                        ]
                    })(
                       <Input />
                    )}
                </Form.Item>
                <Form.Item label={I18N.emailflownode.index.youJianNeiRong}>
                    {getFieldDecorator('mailContent', {
                        rules: [
                            {
                                required: true,
                                message: I18N.emailflownode.index.qingShuRuYouJian
                            }
                        ]
                    })(
                       <TextArea rows={12} maxLength={500} />
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
});
