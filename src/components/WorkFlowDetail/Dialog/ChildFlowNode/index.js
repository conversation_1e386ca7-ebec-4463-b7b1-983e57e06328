/*
 * @Describe: 决策补充弹框
 */
import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { Form, Select, Modal, Button, Row, Input, Spin, message } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import WorkFlowDetail from '@/components/WorkFlowDetail';
import AddModify from '@/routes/WorkFlowSub/List/Modal/AddModify';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { sliceName } from '@/components/WorkFlowEditor';
import './index.less';
import service from '../../service';

const Option = Select.Option;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 }
    }
};

export default Form.create({ name: 'child-node' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled, type: viewType } = props;
    const { getFieldDecorator, validateFields, setFieldsValue, resetFields, getFieldValue } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'SubDecisionFlowNode';
    const timeRef = useRef();
    const editorRef = useRef();
    const [addModifyData, setAddModifyData] = useState(); // 生成模版
    const [workflowEditorVisible, setWorkflowEditorVisible] = useState(false); //因为modal存在动画，导致画布宽度获取有问题，需要在动画结束之后
    const [workFlowTempList, setWorkFlowTempList] = useState([]);
    const [workFlowGraph, setWorkFlowGraph] = useState();

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                setWorkflowEditorVisible(false);
                resetFields();
                setWorkFlowGraph();
                timeRef.current && clearTimeout(timeRef.current);
            };
        }
    }, [visible]);

    const initData = async () => {
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        const { name, ...rest } = data || {};
        const workflowTemplateListRes = await service.workflowTemplateList();
        setWorkFlowTempList(workflowTemplateListRes?.data || []);
        timeRef.current = setTimeout(() => {
            setWorkflowEditorVisible(visible);
        }, 300);
        resetFields();
        setWorkFlowGraph(rest);
        setFieldsValue({ name } || {});
    };

    // 提交
    const commitHandle = () => {
        validateFields(async (err, data) => {
            if (!err) {
                const currentGraphData = editorRef?.current?.getGraphData();
                if (currentGraphData) {
                    const preBuildRes = await service.preBuild({ graphJson: JSON.stringify(currentGraphData) });
                    if (preBuildRes?.success) {
                        const name = data?.name;
                        editor.schema.data.nodesMap[nodeId].data = {
                            name,
                            ...(currentGraphData || {})
                        };
                        editor.schema.data.nodesMap[nodeId].name = name;
                        editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(name);
                        DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);
                        onCancel();
                    } else {
                        message.error(preBuildRes?.msg || preBuildRes?.message || I18N.childflownode.index.liuChengMuBanPei);
                    }
                }
            }
        });
    };

    // 获取流程模板详情
    const getSubFlowDetail = (v) => {
        service
            .workflowTemplateDetail({
                uuid: v
            })
            .then((res) => {
                const { data = {} } = res || {};
                if (!getFieldValue('name')) {
                    setFieldsValue({ name: data?.displayName });
                }
                setWorkFlowGraph(data?.graphJson || {});
            });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.childflownode.index.quXiao}</Button>,
        <Button type="primary" onClick={commitHandle} key="ok">
            {I18N.childflownode.index.queDing}</Button>
    ];

    const footerCancelDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.childflownode.index.guanBi}</Button>
    ];

    return (
        <>
            <Modal
                title={I18N.childflownode.index.liuChengMuBan}
                width={'86vw'}
                height={'86vh'}
                centered
                visible={visible}
                maskClosable={false}
                zIndex={1002}
                onCancel={onCancel}
                className="child-flow-modal"
                footer={disabled ? footerCancelDom : footerDom}
                destroyOnClose>
                <Row type="flex" align="top" justify="space-between" style={{ marginBottom: '10px' }}>
                    <Form layout="inline">
                        <Form.Item label={I18N.childflownode.index.jieDianMingCheng}>
                            {getFieldDecorator('name', {
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.childflownode.index.qingShuRuJieDian
                                    },
                                    {
                                        max: 200,
                                        message: I18N.childflownode.index.mingChengZuiChangBu
                                    },
                                    // 添加正则校验 /^[\w\s\u4E00-\u9FA5\-.]+$/，支持中文、英文、数字、空格、下划线、-、.
                                    {
                                        pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                        message: I18N.childflownode.index.mingChengZhiNengShu
                                    }
                                ]
                            })(<Input style={{ width: '240px' }} placeholder={I18N.childflownode.index.qingShuRuJieDian} disabled={disabled} allowClear />)}
                        </Form.Item>
                    </Form>
                    {!disabled && (
                        <Row>
                            <TooltipSelect
                                placeholder={I18N.childflownode.index.qingXuanZeLiuCheng}
                                isVirtual
                                optionFilterProp="children"
                                showSearch
                                style={{ width: '240px' }}
                                disabled={disabled}
                                onChange={getSubFlowDetail}>
                                {workFlowTempList?.map((item) => {
                                    return (
                                        <Option key={item.uuid} value={item.uuid}>
                                            {item.displayName}
                                        </Option>
                                    );
                                })}
                            </TooltipSelect>
                            <Button
                                type="primary"
                                onClick={() => {
                                    const currentGraphData = editorRef?.current?.getGraphData();
                                    if (currentGraphData) {
                                        setAddModifyData({
                                            titleType: 'add',
                                            title: I18N.childflownode.index.xinZeng,
                                            graphJson: editorRef?.current?.getGraphData() || {}
                                        });
                                    }
                                }}
                                style={{ marginLeft: '10px' }}>
                                {I18N.childflownode.index.shengChengMoBan}</Button>
                        </Row>
                    )}
                </Row>
                {workflowEditorVisible ? (
                    <WorkFlowDetail
                        ref={editorRef}
                        className="work-flow-child-node"
                        graphData={workFlowGraph || {}}
                        type={viewType}
                        flowType="child"
                    />
                ) : (
                    <Spin className="globalSpin" />
                )}
            </Modal>
            <AddModify
                visible={!!addModifyData && viewType !== 'view'}
                data={addModifyData}
                onCancel={() => {
                    setAddModifyData();
                }}
            />
        </>
    );
});
