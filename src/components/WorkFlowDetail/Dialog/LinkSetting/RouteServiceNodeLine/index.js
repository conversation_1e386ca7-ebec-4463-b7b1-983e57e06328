import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { Modal, Button, Input, Form, InputNumber } from 'tntd';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import './index.less';
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 }
    }
};
const LinkSetting = Form.create({ name: 'route-service-node-line' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled } = props;
    const { resetFields, setFieldsValue, validateFields, getFieldDecorator } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'RouteServiceNodeLine';
    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [visible]);

    const initData = () => {
        const { data } = editor.schema.data.linesMap[nodeId];
        setFieldsValue(data || {});
    };

    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const newData = {
                    ...data
                };
                editor.schema.data.linesMap[nodeId].data = newData;
                editor.graph.line.lines[nodeId].data.label = data.conditionName;
                editor.graph.line.lines[nodeId].data.className = '';
                editor.graph.line.updateLine(nodeId);
                editor.graph.line.unActiveLine();
                const line = editor.schema.data.linesMap[nodeId];
                DataConvert.rmNodeError(editor.graph.node.nodes[line.from]);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.routeservicenodeline.index.quXiao}</Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.routeservicenodeline.index.queDing}</Button>
    ];

    return (
        <Modal
            title={I18N.routeservicenodeline.index.sheZhiFenZhiTiao}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={700}
            onCancel={onCancel}
            footer={disabled ? null : footerDom}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.routeservicenodeline.index.fenZhiMingCheng}>
                    {getFieldDecorator('conditionName', {
                        rules: [
                            {
                                required: true,
                                message: I18N.routeservicenodeline.index.qingShuRuFenZhi
                            },
                            {
                                max: 200,
                                message: I18N.routeservicenodeline.index.fenZhiMingChengZuiDa
                            }
                        ]
                    })(<Input placeholder={I18N.routeservicenodeline.index.qingShuRuFenZhi} disabled={disabled} />)}
                </Form.Item>
                <Form.Item label={I18N.routeservicenodeline.index.fenLiuBiLi}>
                    <div className="ant-input-group form-percent">
                        {getFieldDecorator('ratio', {
                            rules: [
                                {
                                    required: true,
                                    message: I18N.routeservicenodeline.index.qingShuRuFenLiu
                                }
                            ]
                        })(
                            <InputNumber
                                precision={0}
                                placeholder={I18N.routeservicenodeline.index.qingShuRuFenLiu}
                                disabled={disabled}
                                style={{ width: 'calc(100% - 50px)' }}
                                min={0}
                                max={100}
                            />
                        )}
                        <span className="ant-input-group-addon">%</span>
                    </div>
                </Form.Item>
            </Form>
        </Modal>
    );
});

export default LinkSetting;
