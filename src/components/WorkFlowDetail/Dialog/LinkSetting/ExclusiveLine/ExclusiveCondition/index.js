import I18N from '@/utils/I18N';
import React from 'react';
import { Radio, Row } from 'tntd';
import OneGroup from './OneGroup';
import OneCondition from './OneCondition';
import './index.less';
const RadioGroup = Radio.Group;
const conditionData = {
    logicOperator: '&&',
    type: 'context',
    children: []
};
const conditionTemp = () => ({
    property: null,
    op: '==',
    value: null,
    propertyDataType: null,
    type: 'context',
    rightValueType: 'context'
});
const groupConditionTemp = () => ({
    logicOperator: '&&',
    type: 'context',
    children: [conditionTemp()]
});

export default (props) => {
    const { value = conditionData, onChange, disabled, ruleField, errRow } = props;
    const { children = [], logicOperator } = value || {};
    // 添加条件
    const addCondition = (type = {}) => {
        const newChildren = (children || []).slice();
        newChildren.push(type?.isGroup ? groupConditionTemp() : conditionTemp());
        onChange({
            ...value,
            children: newChildren
        });
    };

    // 修改值
    const conditionChange = (info, i) => {
        const newChildren = (children || []).slice();
        newChildren[i] = info;
        onChange({
            ...value,
            children: newChildren
        });
    };

    const delCondition = (index) => {
        const newChildren = (children || []).slice();
        newChildren.splice(index, 1);
        onChange({
            ...value,
            children: newChildren
        });
    };

    return (
        <div className="rule-condition">
            <RadioGroup
                value={logicOperator}
                disabled={disabled}
                onChange={(e) => {
                    onChange({ ...value, logicOperator: e.target.value });
                }}>
                <Radio value="&&">{I18N.exclusivecondition.index.manZuYiXiaSuo}</Radio>
                <Radio value="||">{I18N.exclusivecondition.index.manZuYiXiaRen}</Radio>
            </RadioGroup>
            <Row gutter={8}>
                <div className="custom-condition">
                    {children?.map((item, index) => {
                        let conditionDom = null;
                        let isGroup = item?.children?.length;
                        if (isGroup) {
                            conditionDom = (
                                <OneGroup
                                    parentValue={value}
                                    ruleField={ruleField}
                                    conditionTemp={conditionTemp}
                                    disabled={disabled}
                                    value={item}
                                    onChange={(info) => conditionChange(info, index)}
                                    delCondition={() => {
                                        delCondition(index);
                                    }}
                                />
                            );
                        } else {
                            conditionDom = (
                                <OneCondition
                                    parentValue={value}
                                    ruleField={ruleField}
                                    index={index}
                                    disabled={disabled}
                                    value={item}
                                    onChange={(info) => conditionChange(info, index)}
                                    delCondition={() => {
                                        delCondition(index);
                                    }}
                                />
                            );
                        }
                        return (
                            <Row type="flex" className={`condition-wrap ${errRow - 1 === index ? 'error' : ''}`} key={index} align="middle">
                                <div className="condition-index">{index + 1}</div>
                                <div className="condition-content">{conditionDom}</div>
                            </Row>
                        );
                    })}
                    {!disabled && (
                        <div className="add-condition-handle">
                            <a onClick={addCondition}>{I18N.exclusivecondition.index.tianJiaDanTiaoTiao}</a>
                            <a
                                onClick={() => {
                                    addCondition({ isGroup: true });
                                }}>
                                {I18N.exclusivecondition.index.tianJiaTiaoJianZu}</a>
                        </div>
                    )}
                </div>
            </Row>
        </div>
    );
};
