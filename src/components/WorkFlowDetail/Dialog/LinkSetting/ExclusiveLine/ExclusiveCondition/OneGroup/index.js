import I18N from '@/utils/I18N';
import { Row, Col, Select } from 'tntd';
import OneCondition from '../OneCondition';
import './index.less';
const { Option } = Select;
export default (props) => {
    const { value = {}, onChange, disabled, delCondition, conditionTemp, ruleField, parentValue } = props;
    const { children = [], logicOperator } = value || {};
    const groupChange = (info, i) => {
        const newChildren = (children || []).slice();
        newChildren[i] = info;
        onChange({
            ...value,
            children: newChildren
        });
    };
    const addCondition = (index) => {
        const newChildren = (children || []).slice();
        newChildren.splice((index || 0) + 1, 0, conditionTemp());
        onChange({
            ...value,
            children: newChildren
        });
    };
    const delHandle = (index) => {
        if (children?.length === 1) {
            delCondition();
            return;
        }
        const newChildren = (children || []).slice();
        newChildren.splice(index, 1);
        onChange({
            ...value,
            children: newChildren
        });
    };
    return (
        <div>
            <Row type="flex" gutter={20} align="middle">
                <Col span={3} className="group-logic">
                    <Select
                        value={logicOperator || undefined}
                        onChange={(v) => {
                            onChange({ ...value, logicOperator: v });
                        }}
                        dropdownMatchSelectWidth={false}
                        disabled={disabled}>
                        <Option value="&&" title={I18N.onegroup.index.yu}>
                            {I18N.onegroup.index.yu}</Option>
                        <Option value="||" title={I18N.onegroup.index.huo}>
                            {I18N.onegroup.index.huo}</Option>
                    </Select>
                    <i className="group-logic-line" />
                </Col>
                <Col span={21} className="condition-group">
                    {children?.map((item, i) => {
                        return (
                            <div className="condition-group-item" key={i}>
                                <OneCondition
                                    parentValue={parentValue}
                                    ruleField={ruleField}
                                    isGroup={true}
                                    disabled={disabled}
                                    value={item}
                                    onChange={(info) => {
                                        groupChange(info, i);
                                    }}
                                    addCondition={() => addCondition(i)}
                                    delCondition={() => delHandle(i)}
                                />
                                <i className="condition-group-item-line" />
                            </div>
                        );
                    })}
                    <i className="condition-group-line" />
                </Col>
            </Row>
        </div>
    );
};
