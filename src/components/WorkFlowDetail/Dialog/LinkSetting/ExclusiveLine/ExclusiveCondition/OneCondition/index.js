import I18N from '@/utils/I18N';
import React from 'react';
import { Input, Icon, Tooltip, Row, Col, Select } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import IndicatorsCascader from '@/components/IndicatorsCascader';
import { conditionOperator } from '@/components/WorkFlowDetail/constant';
import './index.less';
const InputGroup = Input.Group;

const Option = Select.Option;
export default (props) => {
    const { value: data, onChange, disabled, isGroup, addCondition, delCondition, ruleField, parentValue } = props;
    const { ruleFieldList = [] } = ruleField || {};
    const { property, propertyDataType, op, rightValueType, value, enumTypeValues } = data || {};
    const operaTypeInOrNot = ['in', 'notin'].includes(op);
    let enumValue = value;
    // op为存在于，select为多选
    if (operaTypeInOrNot && !(enumValue instanceof Array)) {
        enumValue = enumValue?.split(',') || [];
    }

    return (
        <Row gutter={10}>
            <Col span={8}>
                <IndicatorsCascader
                    parentValue={parentValue}
                    options={ruleFieldList}
                    fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                    placeholder={I18N.onecondition.index.qingXuanZe}
                    showSearch
                    disabled={disabled}
                    value={property}
                    style={{ width: '100%', maxWidth: 233 }}
                    onChange={(value, option) => {
                        const { enumTypeValues = [], type } = option || {};
                        const v = {
                            property: value,
                            propertyDataType: type,
                            rightValueType: 'context',
                            value: undefined
                        };
                        if (propertyDataType !== type) {
                            v.op = undefined;
                        }
                        if (enumTypeValues?.length) {
                            v.enumTypeValues = enumTypeValues || value;
                        }
                        if (['ENUM', 'BOOLEAN'].includes(type)) {
                            v.rightValueType = 'input';
                        }
                        onChange({
                            ...data,
                            ...v
                        });
                    }}
                />
            </Col>
            <Col span={3}>
                <Select
                    placeholder={I18N.onecondition.index.qingXuanZe}
                    value={op}
                    onChange={(v) => {
                        let tempData = { ...data, op: v };
                        if (['notnull', 'isnull'].includes(op)) {
                            tempData = {
                                ...tempData,
                                value: undefined,
                                enumTypeValues: undefined
                            };
                        }
                        onChange(tempData);
                    }}
                    dropdownMatchSelectWidth={false}
                    disabled={disabled}>
                    {conditionOperator[propertyDataType?.toUpperCase() || 'STRING'].map((item, index) => {
                        return (
                            <Option value={item.name} key={index} title={item.dName}>
                                {item.dName}
                            </Option>
                        );
                    })}
                </Select>
            </Col>
            {op && !['notnull', 'isnull'].includes(op) && (
                <Col className="gutter-row" span={10}>
                    <InputGroup compact>
                        <Select
                            disabled={disabled}
                            value={rightValueType}
                            style={{ width: '30%' }}
                            onChange={(v) => {
                                onChange({
                                    ...data,
                                    rightValueType: v,
                                    value: undefined
                                });
                            }}
                            dropdownMatchSelectWidth={false}>
                            <Option value="input" title={I18N.onecondition.index.changLiang}>
                                {I18N.onecondition.index.changLiang}</Option>
                            <Option value="context" title={I18N.onecondition.index.bianLiang}>
                                {I18N.onecondition.index.bianLiang}</Option>
                        </Select>
                        {rightValueType === 'input' && (
                            <>
                                {propertyDataType === 'BOOLEAN' && (
                                    <Select
                                        disabled={disabled}
                                        style={{ width: '70%', maxWidth: 180 }}
                                        value={value}
                                        placeholder={I18N.onecondition.index.qingXuanZe}
                                        onChange={(v) => {
                                            onChange({
                                                ...data,
                                                value: v
                                            });
                                        }}
                                        showSearch
                                        optionFilterProp="children"
                                        dropdownMatchSelectWidth={false}>
                                        <Option value="true">{I18N.onecondition.index.shiTRUE}</Option>
                                        <Option value="false">{I18N.onecondition.index.fouFALS}</Option>
                                    </Select>
                                )}
                                {propertyDataType === 'ENUM' && (
                                    <TooltipSelect
                                        disabled={disabled}
                                        value={enumValue}
                                        style={{ width: '70%', maxWidth: 180 }}
                                        placeholder={I18N.onecondition.index.qingXuanZe}
                                        onChange={(v) => {
                                            if (Array.isArray(v)) {
                                                v = v.join(',');
                                            }
                                            onChange({
                                                ...data,
                                                value: v || undefined
                                            });
                                        }}
                                        showSearch
                                        optionFilterProp="children"
                                        dropdownMatchSelectWidth={false}
                                        mode={operaTypeInOrNot ? 'multiple' : null}
                                        maxTagCount={1}
                                        maxTagTextLength={3}
                                        className="custom-condition-select">
                                        {enumTypeValues?.map((item, index) => {
                                            return (
                                                <Option value={item.value} key={index} title={`${item.description} [${item.value}]`}>
                                                    {item.description} [{item.value}]
                                                </Option>
                                            );
                                        })}
                                    </TooltipSelect>
                                )}
                                {!['BOOLEAN', 'ENUM'].includes(propertyDataType) && (
                                    <Input
                                        style={{ width: '70%', maxWidth: 180 }}
                                        value={value}
                                        placeholder={operaTypeInOrNot ? I18N.onecondition.index.yingWenDouHaoFen : I18N.onecondition.index.qingShuRuChangLiang} // 请输入常量内容
                                        onChange={(e) => {
                                            onChange({
                                                ...data,
                                                value: e.target.value
                                            });
                                        }}
                                    />
                                )}
                            </>
                        )}
                        {rightValueType === 'context' && (
                            <IndicatorsCascader
                                parentValue={parentValue}
                                options={ruleFieldList.filter((fItem) => {
                                    return (
                                        fItem.type === propertyDataType ||
                                        (['DOUBLE', 'INT'].includes(fItem.type) && ['DOUBLE', 'INT'].includes(propertyDataType))
                                    );
                                })}
                                fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                                style={{ width: '70%', maxWidth: 180 }}
                                value={value}
                                placeholder={I18N.onecondition.index.qingXuanZe}
                                onChange={(e) => {
                                    onChange({
                                        ...data,
                                        value: e
                                    });
                                }}
                                showSearch
                                optionFilterProp="children"
                            />
                        )}
                    </InputGroup>
                </Col>
            )}
            <Col span={2}>
                {isGroup && (
                    <Tooltip title={I18N.onecondition.index.tianJiaYiXiang} placement="left">
                        <Icon className="condition-btn add" type="plus-circle-o" onClick={addCondition} />
                    </Tooltip>
                )}
                <Tooltip title={I18N.onecondition.index.shanChuDangQianXing} placement="right">
                    <Icon className="condition-btn delete" type="delete" onClick={delCondition} />
                </Tooltip>
            </Col>
        </Row>
    );
};
