.condition-wrap{
    .condition-index {
        display: inline-block;
        width: 26px;
        height: 26px;
        background: #5B8FF9;
        border-radius: 2px;
        text-align: center;
        line-height: 26px;
        font-size: 14px;
        color: #ffffff;
        font-weight: 500;
    }
    .condition-content{
        flex: 1;
        margin-left: 10px;
    }
}
.add-condition-handle {
    &.disabled a {
        color: #c8c8c8; //#c8c8c8;
        cursor: not-allowed;
    }
    a {
        margin-right: 16px;
    }
    a:hover {
        text-decoration: underline;
    }
}


.has-error {
    .tntd-select-focused:not(.tntd-select-disabled).tntd-select:not(.tntd-select-customize-input) .tntd-select-selector{
        border:1px solid #F06555;
    }
    .condition-wrap:not(.error){
        .tntd-select:not(.tntd-select-customize-input) .tntd-select-selector,
        .tntd-select:not(.tntd-select-disabled):hover .tntd-select-selector,
        .ant-select-open .ant-select-selection,
        .ant-select-focused .ant-select-selection,
        .ant-select-selection,
        .ant-input,
        .ant-input-number,
        .ant-time-picker-input,
        .ant-input:hover{
            border: 1px solid #C9D2DD;
        }
        .ant-calendar-picker-icon::after,
        .ant-time-picker-icon::after,
        .ant-picker-icon::after,
        .ant-select-arrow,
        .ant-cascader-picker-arrow{
            color:rgba(23, 35, 61, 0.3);
        }

        .ant-select-open .ant-select-selection,
        .ant-select-focused .ant-select-selection,
        .ant-input-number-focused,
        .ant-input:focus{
            border-color: #126BFB;
            box-shadow: 0 0 0 2px rgba(18, 107, 251, 0.2);
        }
    }
}

