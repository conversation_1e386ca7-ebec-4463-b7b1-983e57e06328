import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Modal, Button, Input, Checkbox, Form, InputNumber, message } from 'tntd';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import ExclusiveCondition from './ExclusiveCondition';

import otp from './otp';
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: otp.labelCol }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: otp.wrapperCol }
    }
};
const LinkSetting = Form.create({ name: 'exclusive-gate-way' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled, ruleField } = props;
    const { resetFields, setFieldsValue, validateFields, getFieldDecorator, getFieldValue } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'ExclusiveGatewayLine';
    const [errRow, setErrRow] = useState(undefined);
    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [visible]);

    const initData = () => {
        const { data } = editor.schema.data.linesMap[nodeId];
        setFieldsValue(data || {});
    };

    const addConditionPriority = (childParams) => {
        let tempValue = { ...childParams };
        let priority = 1;
        tempValue?.children?.map((item) => {
            item.priority = priority++;
            item?.children?.map((item1) => {
                item1.priority = priority++;
            });
        });
        return tempValue;
    };

    const commitModal = () => {
        if (!getFieldValue('condition') && !getFieldValue('isDefault')) {
            message.error(I18N.exclusiveline.index.qingTianJiaZhiXing);
            return;
        }
        validateFields((errors, data) => {
            if (!errors) {
                const newData = {
                    ...data
                };
                if (!data?.isDefault) {
                    const newCondition = addConditionPriority({ ...(data?.condition || {}) });
                    newData.condition = newCondition;
                }
                editor.schema.data.linesMap[nodeId].data = newData;

                editor.graph.line.lines[nodeId].data.label = data.conditionName;
                editor.graph.line.lines[nodeId].data.className = '';
                editor.graph.line.updateLine(nodeId);
                editor.graph.line.unActiveLine();
                const line = editor.schema.data.linesMap[nodeId];
                DataConvert.rmNodeError(editor.graph.node.nodes[line.from]);
                onCancel();
            }
        });
    };
    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.exclusiveline.index.quXiao}</Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.exclusiveline.index.queDing}</Button>
    ];

    const check = (v, i) => {
        let hasEmptyDataRow = '';
        const { property, op, value } = v || {};
        if (!hasEmptyDataRow) {
            const notNull = !['notnull', 'isnull'].includes(op);

            if (!property || !op) {
                hasEmptyDataRow = i + 1;
            }
            if (!hasEmptyDataRow) {
                if (notNull && !value && value !== 0) {
                    hasEmptyDataRow = i + 1;
                }
            }
        }
        return hasEmptyDataRow;
    };
    return (
        <Modal
            title={I18N.exclusiveline.index.panDuan}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={1000}
            onCancel={onCancel}
            footer={disabled ? null : footerDom}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.exclusiveline.index.panDingTiaoJianMing}>
                    {getFieldDecorator('conditionName', {
                        rules: [
                            {
                                required: true,
                                message: I18N.exclusiveline.index.qingShuRuPanDing
                            },
                             // 添加正则校验 /^[\w\s\u4E00-\u9FA5\-.]+$/，支持中文、英文、数字、空格、下划线、-、.
                             {
                                pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                message: I18N.exclusiveline.index.mingChengZhiNengShu
                            },
                            {
                                max: 200,
                                message: I18N.exclusiveline.index.mingChengZuiChangBu
                            }
                        ]
                    })(<Input placeholder={I18N.exclusiveline.index.qingShuRuPanDing} disabled={disabled} />)}
                </Form.Item>
                <Form.Item label={I18N.exclusiveline.index.moRenFenZhi}>
                    {getFieldDecorator('isDefault')(
                        <Checkbox checked={!!getFieldValue('isDefault')} disabled={disabled}>
                            {I18N.exclusiveline.index.moRenFenZhi}</Checkbox>
                    )}
                    <div style={{ color: '#EF6555', lineHeight: '16px', marginBottom: '6px' }}>
                        {I18N.exclusiveline.index.yiGePanDingTiao}</div>
                </Form.Item>
                {!getFieldValue('isDefault') && (
                    <>
                        <Form.Item label={I18N.exclusiveline.index.youXianJi}>
                            {getFieldDecorator('priority')(
                                <InputNumber placeholder={I18N.exclusiveline.index.qingShuRuYouXian} disabled={disabled} style={{ width: '100%' }} />
                            )}
                        </Form.Item>
                        <Form.Item label={I18N.exclusiveline.index.zhiXingTiaoJian}>
                            {getFieldDecorator('condition', {
                                rules: [
                                    {
                                        validator: (rule, value, callback) => {
                                            let hasEmptyDataRow;
                                            let hasEmptyDataSubRow;
                                            if (!value.logicOperator) {
                                                callback(I18N.exclusiveline.index.qingSheZhiZhiXing2);
                                            }
                                            if (!value?.children?.length) {
                                                callback(I18N.exclusiveline.index.qingSheZhiZhiXing);
                                            }
                                            value?.children?.forEach((v, i) => {
                                                if (!hasEmptyDataRow) {
                                                    if (v?.children?.length) {
                                                        if (!hasEmptyDataSubRow) {
                                                            v?.children?.forEach((subv, si) => {
                                                                if (!hasEmptyDataSubRow) {
                                                                    hasEmptyDataSubRow = check(subv, si);
                                                                    if (hasEmptyDataSubRow) {
                                                                        hasEmptyDataRow = i + 1;
                                                                    }
                                                                }
                                                            });
                                                        }
                                                    } else {
                                                        hasEmptyDataRow = check(v, i);
                                                    }
                                                }
                                            });
                                            setErrRow(hasEmptyDataRow);
                                            if (hasEmptyDataRow) {
                                                if (hasEmptyDataSubRow) {
                                                    callback(I18N.template(I18N.exclusiveline.index.diHASE2, {
                                                        val1: hasEmptyDataRow,
                                                        val2: hasEmptyDataSubRow
                                                    }));
                                                } else {
                                                    callback(I18N.template(I18N.exclusiveline.index.diHASE, { val1: hasEmptyDataRow }));
                                                }
                                            }
                                            callback();
                                        }
                                    }
                                ]
                            })(<ExclusiveCondition disabled={disabled} ruleField={ruleField || {}} errRow={errRow} />)}
                        </Form.Item>
                    </>
                )}
            </Form>
        </Modal>
    );
});

export default LinkSetting;
