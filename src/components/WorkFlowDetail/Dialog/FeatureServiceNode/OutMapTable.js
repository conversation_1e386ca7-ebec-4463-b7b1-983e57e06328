import I18N from '@/utils/I18N';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Tag, Table, Row, Input, Select, Ellipsis, Icon } from 'tntd';

import TooltipSelect from '@tntd/tooltip-select';
import { useGetGlobalStore } from '@/utils/utils';
import { DATA_TYPE_MAP } from '@/constants';

const Option = Select.Option;

export default forwardRef((props, ref) => {
    const { visible, initOutput, value = [], onChange, ruleField, disabled } = props;
    const [outputMap, setOutputMap] = useState({});
    const [outPageInfo, setOutPageInfo] = useState({ page: 1, pageSize: 10 });

    let { allFieldList = [] } = useGetGlobalStore();

    const checkMapHandle = (curV) => {
        if (curV?.length) {
            let emptyMsg = '';
            curV.forEach((v, i) => {
                if (!emptyMsg && !v.field) {
                    const curPage = Math.ceil((i + 1) / outPageInfo?.pageSize);
                    emptyMsg = I18N.template(I18N.featureservicenode.outmaptable.chuCanZiDuanDi, {
                        val1: curPage,
                        val2: i + 1 - (curPage - 1) * outPageInfo?.pageSize
                    });
                }
            });
            return emptyMsg;
        }
        return '';
    };

    useImperativeHandle(ref, () => ({
        checkMapHandle
    }));

    const getMapItem = (record) => {
        return initOutput?.find((item) => item.serviceParam === record.serviceParam) || {};
    };

    const handleChange = (v, i) => {
        const newValue = value?.slice();
        i = ((outPageInfo?.page || 1) - 1) * (outPageInfo.pageSize || 10) + i;

        newValue[i] = {
            ...newValue[i],
            field: v
        };
        onChange && onChange(newValue);
    };

    useEffect(() => {
        if (visible) {
            if (value?.length) {
                const outputMapTemp = {};
                value?.forEach((v) => {
                    outputMapTemp[v.field] = v;
                });
                setOutputMap(outputMapTemp);
            }
        }
    }, [value, visible]);

    return (
        <div className="feature-service-table">
            <Table
                title={() => (
                    <Row>
                        {I18N.featureservicenode.outmaptable.shuChuZiDuan}
                        <Tag color="orange" style={{ marginLeft: '10px' }}>
                            {I18N.featureservicenode.outmaptable.shuChu}
                        </Tag>
                    </Row>
                )}
                size="small"
                dataSource={value}
                columns={[
                    {
                        title: I18N.featureservicenode.outmaptable.aPIJieKou2,
                        dataIndex: 'displayName',
                        width: 360,
                        ellipsis: true,
                        render: (text, record) => {
                            let str;
                            if (text) {
                                str = text;
                            } else if (initOutput?.length && record?.serviceParam) {
                                str = getMapItem(record)?.displayName;
                            }
                            return <Ellipsis title={str} />;
                        }
                    },
                    {
                        title: I18N.featureservicenode.outmaptable.aPIJieKou,
                        dataIndex: 'serviceParam',
                        key: 'serviceParam',
                        width: 240,
                        ellipsis: true,
                        render: (text) => {
                            return <Ellipsis title={text} />;
                        }
                    },
                    {
                        title: I18N.featureservicenode.outmaptable.chuCanZiDuanMing,
                        dataIndex: 'field',
                        key: 'field',
                        width: 250,
                        render: (text, record, i) => {
                            const initItem = getMapItem(record);
                            return initItem?.type === 'constant' ? (
                                <Input value={initItem.value} disabled={true} />
                            ) : (
                                // <IndicatorsCascader
                                //     className={!text ? 'hasError' : ''}
                                //     pageinfo={outPageInfo}
                                //     parentValue={value}
                                //     style={{ width: '234px' }}
                                //     options={ruleField?.ruleFieldList?.map((i) => {
                                //         const disabled = outputMap?.[i.name] || i.disabled;
                                //         return { ...i, disabled };
                                //     })}
                                //     showSearch
                                //     disabled={disabled}
                                //     fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                                //     value={text}
                                //     placeholder="请选择出参字段名"
                                //     onChange={(e) => {
                                //         handleChange(e, i);
                                //     }}
                                // />
                                <TooltipSelect
                                    isVirtual
                                    className={!text ? 'hasError' : ''}
                                    style={{ width: '234px' }}
                                    showSearch
                                    disabled={disabled}
                                    filterOption={(input, option) => option.props.dName.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                    value={text}
                                    placeholder={I18N.featureservicenode.outmaptable.qingXuanZeChuCan}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    onChange={(e) => {
                                        handleChange(e, i);
                                    }}>
                                    {(allFieldList || []).map((i) => {
                                        const disabled = outputMap?.[i.name] || i.disabled;
                                        // return { ...i, disabled };
                                        let obj = DATA_TYPE_MAP[i.dataType];
                                        return (
                                            <Option key={i.name} value={i.name} disabled={disabled} dName={i.displayName}>
                                                <Icon type={obj?.icon} style={{ color: obj?.color }} />
                                                {i.displayName}
                                            </Option>
                                        );
                                    })}
                                </TooltipSelect>
                            );
                        }
                    }
                ]}
                rowKey="serviceParam"
                pagination={{
                    onChange: (page, pageSize) => {
                        setOutPageInfo({ page, pageSize });
                    }
                }}
            />
        </div>
    );
});
