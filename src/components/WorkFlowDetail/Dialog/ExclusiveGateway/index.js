import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { Modal, Button, Radio, Form, Alert } from 'tntd';
import { sliceName } from '@/components/WorkFlowEditor';
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
    }
};
export default Form.create({ name: 'exclusive-gateway' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled } = props;
    const { getFieldDecorator, setFieldsValue, resetFields, validateFields } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'ExclusiveGateway';

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [type]);

    const initData = () => {
        resetFields();
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        setFieldsValue(data || {});
    };

    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const { type } = data || {};
                const nodeName = type === 'start' ? I18N.exclusivegateway.index.panDuanKaiShi : I18N.exclusivegateway.index.panDuanJieShu;
                editor.schema.data.nodesMap[nodeId].name = nodeName;
                editor.schema.data.nodesMap[nodeId].data = data;

                const node = editor.graph.node.nodes[nodeId];
                node.shape.select('text.flow-txt-node').node.innerHTML = nodeName;
                editor.graph.node.updateNode(node.data);
                node.toLines.forEach((line) => {
                    editor.schema.data.linesMap[line].data = {};
                    editor.graph.line.lines[line].data.label = undefined;
                    editor.graph.line.lines[line].data.className = type === 'start' ? 'red-line' : '';
                });
                editor.graph.line.updateByNode(node);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.exclusivegateway.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.exclusivegateway.index.queDing}
        </Button>
    ];

    return (
        <Modal
            title={I18N.exclusivegateway.index.panDuan}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={600}
            onCancel={onCancel}
            footer={disabled ? null : footerDom}>
            <Form {...formItemLayout}>
                <Alert message={I18N.exclusivegateway.index.zhuZuiShaoPeiZhi} type="warning" style={{ marginBottom: '8px' }} />
                <Form.Item label={I18N.exclusivegateway.index.panDuanLeiXing}>
                    {getFieldDecorator('type', {
                        rules: [
                            {
                                required: true,
                                message: I18N.exclusivegateway.index.qingXuanZeWangGuan
                            }
                        ]
                    })(
                        <Radio.Group disabled={disabled}>
                            <Radio value="start">{I18N.exclusivegateway.index.panDuanKaiShi}</Radio>
                            <Radio value="end">{I18N.exclusivegateway.index.panDuanJieShu}</Radio>
                        </Radio.Group>
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
});
