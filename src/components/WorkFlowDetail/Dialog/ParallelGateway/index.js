import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { Alert, Form, Modal, Button, Radio } from 'tntd';
import { sliceName } from '@/components/WorkFlowEditor';
import './index.less';

const maxParallelNodes = 5;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
    }
};

export default Form.create({ name: 'parallel-gateway' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled } = props;
    const { getFieldDecorator, setFieldsValue, resetFields, validateFields } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'ParallelGateway';

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [type]);

    const initData = () => {
        resetFields();
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        setFieldsValue(data || {});
    };

    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const { type } = data;
                const nodeName = type === 'start' ? I18N.parallelgateway.index.bingXingKaiShi : I18N.parallelgateway.index.bingXingJieShu;
                editor.schema.data.nodesMap[nodeId].name = nodeName;
                editor.schema.data.nodesMap[nodeId].data = data;
                const node = editor.graph.node.nodes[nodeId];
                node.shape.select('text.flow-txt-node').node.innerHTML = nodeName;
                editor.graph.node.updateNode(editor.graph.node.nodes[nodeId].data);
                // 关闭弹窗并清空数据
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.parallelgateway.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.parallelgateway.index.queDing}
        </Button>
    ];

    const footerCancelDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.parallelgateway.index.guanBi}
        </Button>
    ];

    return (
        <Modal
            title={I18N.parallelgateway.index.bingXing}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={600}
            onCancel={onCancel}
            footer={disabled ? footerCancelDom : footerDom}>
            <Form {...formItemLayout}>
                <Alert
                    type="warning"
                    message={I18N.template(I18N.parallelgateway.index.zhuZuiDuoZhiChi, { val1: maxParallelNodes })}
                    style={{ marginBottom: '8px' }}
                />
                <Form.Item label={I18N.parallelgateway.index.bingXingLeiXing}>
                    {getFieldDecorator('type', {
                        rules: [
                            {
                                required: true,
                                message: I18N.parallelgateway.index.qingXuanZeBingXing
                            }
                        ]
                    })(
                        <Radio.Group disabled={disabled}>
                            <Radio value="start">{I18N.parallelgateway.index.bingXingKaiShi}</Radio>
                            <Radio value="end">{I18N.parallelgateway.index.bingXingJieShu}</Radio>
                        </Radio.Group>
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
});
