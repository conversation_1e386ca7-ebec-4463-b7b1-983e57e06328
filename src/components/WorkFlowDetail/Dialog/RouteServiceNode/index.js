import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { Modal, Button, Input, Form, Alert } from 'tntd';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { sliceName } from '@/components/WorkFlowEditor';
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 3 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
    }
};
export default Form.create({ name: 'route-service' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled } = props;
    const { getFieldDecorator, setFieldsValue, resetFields, validateFields } = form;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'RouteServiceNode';

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
            };
        }
    }, [type]);

    const initData = () => {
        resetFields();
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        setFieldsValue(data || {});
    };

    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const name = data?.name;
                editor.schema.data.nodesMap[nodeId].data = data;
                editor.schema.data.nodesMap[nodeId].name = name;
                editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(name);
                DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.routeservicenode.index.quXiao}</Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.routeservicenode.index.queDing}</Button>
    ];

    return (
        <Modal
            title={I18N.routeservicenode.index.zhiNengLuYou}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={600}
            onCancel={onCancel}
            footer={disabled ? null : footerDom}>
            <Form {...formItemLayout}>
                <Alert message={I18N.routeservicenode.index.zhuZuiShaoPeiZhi} type="warning" style={{ marginBottom: '8px' }} />
                <Form.Item label={I18N.routeservicenode.index.mingCheng}>
                    {getFieldDecorator('name', {
                        rules: [
                            {
                                required: true,
                                message: I18N.routeservicenode.index.qingShuRuMingCheng
                            },
                            {
                                max: 200,
                                message: I18N.routeservicenode.index.mingChengZuiChangBu
                            },
                            {
                                pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                message: I18N.routeservicenode.index.mingChengZhiNengShu
                            }
                        ]
                    })(<Input placeholder={I18N.routeservicenode.index.qingShuRuMingCheng} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
});
