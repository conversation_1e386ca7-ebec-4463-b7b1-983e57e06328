import I18N from '@/utils/I18N';
import { Table, Ellipsis } from 'tntd';
import { useEffect, useState } from 'react';
import '../index.less';
export default (props) => {
    const { data, item, size, columnName } = props;
    const [dataSource, setDataSource] = useState([]);
    const [columns, setColumns] = useState([]);
    const [current, setCurrent] = useState(1);

    useEffect(() => {
        if (data) {
            getPageSize(data);
        }
    }, [data]);

    useEffect(() => {
        if (item && dataSource.length > 0 && columnName.length > 0) {
            getColumns(columnName);
        }
    }, [item, dataSource, columnName]);

    const getColumns = (columnName) => {
        setColumns([
            {
                dataIndex: 'uniqueKey',
                keys: 'uniqueKey',
                title: I18N.threecolumns.index.xuHao,
                width: 120,
                render: (text) => {
                    return <div className="first-number">{dataSource.findIndex((item) => item.uniqueKey === text) + 1}</div>;
                }
            },
            {
                dataIndex: 'column1',
                keys: 'column1',
                title: columnName[0],
                width: 182,
                render: (text, record) => {
                    var root = (
                        <div className="td-content">
                            {record.children1.map((item, index) => {
                                return (
                                    <div key={index} className="row-content">
                                        <div style={{ width: '177px' }}>
                                            <Ellipsis widthLimit={177} title={item.column1} />
                                        </div>
                                        <div style={{ width: '270px' }}>
                                            <Ellipsis widthLimit={270} title={item.column2} />
                                        </div>
                                        <div
                                            className="last-row"
                                            style={{
                                                color:
                                                    item.column3 !== I18N.threecolumns.index.jiaoYanTongGuo &&
                                                    item.column3 !== I18N.threecolumns.index.buYiZhi &&
                                                    item.column3 !== I18N.threecolumns.index.yiZhi &&
                                                    '#D96156',
                                                fontWeight: item.column4 !== I18N.threecolumns.index.jiaoYanTongGuo && '500'
                                            }}>
                                            <Ellipsis widthLimit={360} title={item.column3} />
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    );
                    var obj = {
                        children: root,
                        props: {
                            colSpan: 4
                        }
                    };

                    return obj;
                }
            },
            {
                dataIndex: 'column2',
                keys: 'column2',
                title: columnName[1],
                width: 270,
                render: () => {
                    var obj = {
                        props: {
                            colSpan: 0
                        }
                    };
                    return obj;
                }
            },
            {
                dataIndex: 'column3',
                keys: 'column3',
                title: columnName[2],
                render: () => {
                    var obj = {
                        props: {
                            colSpan: 0
                        }
                    };
                    return obj;
                }
            }
        ]);
    };

    const getPageSize = (data) => {
        var map = new Set();
        var arr = [];
        var obj;
        data.map((item) => {
            if (!map.has(item.uniqueKey)) {
                if (obj && obj?.children1?.length > 0) {
                    arr.push(obj);
                }
                map.add(item.uniqueKey);
                obj = {
                    uniqueKey: item.uniqueKey,
                    children1: []
                };
                obj.children1.push(item);
            } else {
                obj.children1.push(item);
            }
        });
        if (obj?.children1?.length > 0) {
            arr.push(obj);
        }
        setDataSource(arr);
    };
    return (
        columns.length > 0 && (
            <Table
                className="rowSpan-table"
                dataSource={dataSource}
                columns={columns}
                pagination={{
                    pageSize: size,
                    onChange: (current) => {
                        setCurrent(current);
                    },
                    current
                }}
            />
        )
    );
};
