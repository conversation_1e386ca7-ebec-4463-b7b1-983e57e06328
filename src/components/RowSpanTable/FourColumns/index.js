import I18N from '@/utils/I18N';
import { Table, Ellipsis } from 'tntd';
import { useEffect, useState } from 'react';
import '../index.less';
export default (props) => {
    const { data, item, size, columnName } = props;
    const [dataSource, setDataSource] = useState([]);
    const [columns, setColumns] = useState([]);
    const [current, setCurrent] = useState(1);

    useEffect(() => {
        if (data) {
            getPageSize(data);
        }
    }, [data]);

    useEffect(() => {
        if (item && dataSource.length > 0 && columnName.length > 0) {
            getColumns(columnName);
        }
    }, [item, dataSource, columnName]);

    const getColumns = (columnName) => {
        setColumns([
            {
                dataIndex: 'uniqueKey',
                keys: 'uniqueKey',
                title: I18N.fourcolumns.index.xuHao,
                width: 75,
                render: (text) => {
                    return <div className="first-number">{dataSource.findIndex((item) => item.uniqueKey === text) + 1}</div>;
                }
            },
            {
                dataIndex: 'column1',
                keys: 'column1',
                title: columnName[0],
                width: 130,
                render: (text, record) => {
                    var root = (
                        <div className="td-content">
                            {record.children1.map((item, index) => {
                                return (
                                    <div className="row-content" key={index}>
                                        <div style={{ width: '125px', display: 'inline-block' }}>
                                            <Ellipsis widthLimit={125} title={item.column1} />
                                        </div>
                                        <div style={{ width: '270px', display: 'inline-block' }}>
                                            <Ellipsis widthLimit={270} title={item.column2} />
                                        </div>
                                        <div style={{ width: '285px', display: 'inline-block' }}>
                                            <Ellipsis widthLimit={285} title={item.column3} />
                                        </div>
                                        <div
                                            className="last-row"
                                            style={{
                                                color:
                                                    item.column4 !== I18N.fourcolumns.index.jiaoYanTongGuo &&
                                                    item.column4 !== I18N.fourcolumns.index.yiZhi &&
                                                    item.column4 !== I18N.fourcolumns.index.buYiZhi &&
                                                    '#D96156',
                                                fontWeight: item.column4 !== I18N.fourcolumns.index.jiaoYanTongGuo && '500'
                                            }}>
                                            <Ellipsis widthLimit={160} title={item.column4} />
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    );
                    var obj = {
                        children: root,
                        props: {
                            colSpan: 4
                        }
                    };

                    return obj;
                }
            },
            {
                dataIndex: 'column2',
                keys: 'column2',
                title: columnName[1],
                width: 270,
                render: () => {
                    var obj = {
                        props: {
                            colSpan: 0
                        }
                    };
                    return obj;
                }
            },
            {
                dataIndex: 'column3',
                keys: 'column3',
                title: columnName[2],

                width: 285,
                render: () => {
                    var obj = {
                        props: {
                            colSpan: 0
                        }
                    };
                    return obj;
                }
            },
            {
                dataIndex: 'column4',
                keys: 'column4',
                title: columnName[3],
                render: () => {
                    var obj = {
                        props: {
                            colSpan: 0
                        }
                    };
                    return obj;
                }
            }
        ]);
    };

    const getPageSize = (data) => {
        var map = new Set();
        var arr = [];
        var obj;
        data.map((item) => {
            if (!map.has(item.uniqueKey)) {
                if (obj && obj?.children1?.length > 0) {
                    arr.push(obj);
                }
                map.add(item.uniqueKey);
                obj = {
                    uniqueKey: item.uniqueKey,
                    children1: []
                };
                obj.children1.push(item);
            } else {
                obj.children1.push(item);
            }
        });
        if (obj?.children1?.length > 0) {
            arr.push(obj);
        }
        setDataSource(arr);
        return arr;
    };
    return (
        columns.length > 0 &&
        dataSource.length > 0 && (
            <Table
                className="rowSpan-table"
                dataSource={dataSource}
                columns={columns}
                pagination={{
                    // showTotal: () => null,
                    pageSize: size,
                    onChange: (current) => {
                        setCurrent(current);
                    },
                    current
                }}
            />
        )
    );
};
