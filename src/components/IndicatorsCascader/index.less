.virtual-indicators-cascader{
    width: 100%;
}

.tntd-cascader-menu{
    width: 130px;
    .rc-virtual-list{
        width: 100%;
    }
}

.tntd-cascader-menu-1{
    width: 250px;
    .rc-virtual-list{
        width: 100%;
    }
}

.cascader-search-result-wrapper{
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.virtual-cascader-enpty-wrapper{
    box-sizing: border-box;
    position: absolute;
    z-index: 1010;
    top: 0;
    width: 100%;
    left: 0;
    height: 180px;
    background-color: white;

    .virtual-cascader-enpty-content{
        text-align: center;
        margin-top: 33px;
    }

    .no-data-text{
        margin-top: 6;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #454F64;
        letter-spacing: 0;
        font-weight: 400;
        width: 180px;
        margin: auto;
        white-space: break-spaces;
    }
}
.has-error{
    .tntd-select:not(.tntd-select-customize-input) .tntd-select-selector{
        border:1px solid #F06555;
    }
}
