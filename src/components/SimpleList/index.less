.s-page-header{
    overflow: hidden;
    background: #fff;
    padding: 0 20px;
    border-bottom: 1px solid #e8e8e8;
    height: 40px;
    line-height: 40px;
    h2{
        display: inline-block;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0,0,0,.85);
        margin-bottom: 0;
    }
}
.s-page-body{
    padding: 16px 20px;
    // min-width: 1100px;
    overflow: auto;
    height: calc(100vh - 90px);
    .tnt-current-v3 & {
        padding: 0;
        height: auto !important;
        overflow: initial;
    }
    .s-page-search{
        padding-bottom: 12px;
        .item{
            position: relative;
            float: left;
            margin-right: 12px;

			&.right {
				float: right;
				margin-left: 12px;
			}
        }
        &::after{
            content: "";
            height: 0;
            display: block;
            clear: both;
        }
    }
    .s-page-body-in{
        background: #fff;
        .s-page-table-header{
            padding: 12px 15px;
            .table-left-params{
                float: left;
            }
            .table-right-params{
                float: right;
            }
        }
        .s-page-body-main{
            padding-bottom: 15px;
            .s-page-body-pagination{
                margin-top: 12px;
                height: 28px;
                line-height: 28px;
                .count{
                    float: left;
                    margin-left: 16px;
                }
                .ant-pagination{
                    float: right;
                    margin-right: 16px;
                }
            }
        }
    }
}

.collapsed-true {
    .s-page-body {
        min-width: 1100px;
        width: ~"calc(100vw - 80px)";
    }
    .s-page-tabs {
        .ant-tabs {
            width: ~"calc(100vw - 80px)";
            min-width: 1100px;
        }
    }
}
.collapsed-false {
    .s-page-body {
        min-width: 1100px;
        width: ~"calc(100vw - 220px)";
    }
    .s-page-tabs {
        .ant-tabs {
            width: ~"calc(100vw - 220px)";
            min-width: 1100px;
        }
    }
}
