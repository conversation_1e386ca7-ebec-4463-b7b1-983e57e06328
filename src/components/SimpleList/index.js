/*
 * @Author: liubo
 * @CreatDate: 2019-08-13 19:04:55
 * @Describe: 简单的列表查询页面组件
 */

import I18N from '@/utils/I18N';
import './index.less';
import { PureComponent, Fragment } from 'react';
import PropTypes from 'prop-types';
import { Table, Pagination } from 'tntd';
import NoPermission from '@/components/NoPermission';

class SimpleList extends PureComponent {

	getSearchItem = () => {
		const { searchParams } = this.props;
		if (searchParams.length === 0) return;
		return (
			<div className="s-page-search">
				{
					searchParams.map((item, index) => (
						<div className="item" key={index}>
							{item.render}
						</div>
					))
				}
				{this.getLeftInfo()}
				{this.getRightInfo()}
			</div>
		);
	}

	getRightInfo = () => {
		const { tableParams } = this.props;
		if (tableParams.length === 0) return;

		let rightInfo = (
			tableParams.length > 0 &&
			tableParams.map((item, index) => {
				let dom;
				if (item.position === 'right') {
					dom = (
						<div className="item right" key={index}>
							{item.render}
						</div>
					);
				}
				return dom;
			})
		);
		return rightInfo;
	}

	getLeftInfo = () => {
		const { tableParams } = this.props;
		if (tableParams.length === 0) return;

		let leftInfo = (
			tableParams.map((item, index) => {
				let dom;
				if (item.position === 'left') {
					dom = (
						<div className="item" key={index}>
							{item.render}
						</div>
					);
				}
				return dom;
			})
		);
		return leftInfo;
	}

	render() {
		const searchItem = this.getSearchItem();
		const { columns, dataSource, loading, total, curPage, pageSize, title, scroll, rowKey, tableParams, hasPermission } = this.props;

		return (
			<div style={{ height: 'auto', overflow: 'hidden' }}>
				{
					title &&
					<div className="s-page-header">
						<h2>{title}</h2>
					</div>
				}
				{
					hasPermission &&
					<div className="s-page-body">
						{searchItem}
						<div className="s-page-body-in">
							<div className="s-page-body-main">
								{
									scroll
										? <Table
											columns={columns}
											pagination={false}
											dataSource={dataSource}
											loading={loading}
											rowKey={rowKey}
											scroll={scroll}
											onChange={this.props.onSort}
										/>
										: <Table
											columns={columns}
											pagination={false}
											dataSource={dataSource}
											loading={loading}
											rowKey={rowKey}
										/>
								}
								<div className="s-page-body-pagination">
                                    <span className="count">{I18N.template(I18N.simplelist.index.tiaoJiLu, { val1: total })}</span>
									<Pagination
										showQuickJumper
										showSizeChanger
										onChange={(curPage, pageSize) => this.props.onChange(curPage, pageSize)}
										onShowSizeChange={(curPage, pageSize) => this.props.onChange(curPage, pageSize)}
										current={curPage}
										total={total}
										pageSize={pageSize}
									/>
								</div>
							</div>
						</div>
					</div>
				}
				{
					!hasPermission && <NoPermission />
				}
			</div>
		);
	}
}

SimpleList.propTypes = {
	columns: PropTypes.array,
	dataSource: PropTypes.array,
	loading: PropTypes.bool,
	total: PropTypes.number,
	curPage: PropTypes.number,
	pageSize: PropTypes.string,
	tableParams: PropTypes.array,
	searchParams: PropTypes.array,
	title: PropTypes.string,
	rowKey: PropTypes.string,
	onChange: PropTypes.func,
	onSort: PropTypes.func
};

SimpleList.defaultProps = {
	columns: [],
	dataSource: [],
	loading: false,
	total: 0,
	curPage: 1,
	pageSize: 10,
	tableParams: [],
	searchParams: [],
	title: '',
	rowKey: 'id'
};

export default SimpleList;
