# 私有云列表页模板组件

## props参数：
| 参数         | 类型    |  默认值   | 是否必填 | 描述  |
| :--------:  | :-----: | :----:   | :----: |:----: |
| title      | String  |  ""  | 否      | 页面头部，tabs页面不设置 |
| searchParams | Array  |   []     | 否    | 表格搜索模块 |
| tableParams   | Array  |   []    | 否    | 表格功能项 |
| columns   | Array  |   []      | 是    | 同antd |
| dataSource   | Array  |   []    | 否    | 同antd |
| total   | Number  |   0      | 否    | 同antd |
| curPage   | Number  |   1     | 否    | 同antd |
| pageSize   | Number  |   10     | 否    | 同antd |
| rowKey   | String  |   "id"     | 否    | 同antd |
| loading   | Boolean  |   false     | 否    | 同antd |
| scroll   | Object  |   -     | 否    | 同antd |
| onChange   | Func  |   -     | 是    | 同antd |

## searchParams
```jsx
const searchParams = [
    {
        render: (
            <Input
                placeholder="请输入模板名称"
                onChange={(e) => {
                    setTableInfo({
                        ...tableInfo,
                        name: e.target.value
                    });
                }}
            />
        )
    },
    {
        render: (
            <Button
                onClick={() => getList()}
                type="primary"
            >
                搜索
            </Button>
        )
    }
];
```

## tableParams
```jsx
let tableParams = [
    {
        position: "left",
        render: (
            <Button
                onClick={() => setAddVisible(true)}
                type="primary"
            >
                创建
            </Button>
        )
    },
    {
        position: "right",
        render: (
            <Button
                onClick={() => setAddVisible(true)}
                type="primary"
            >
                右边按钮
            </Button>
        )
    }
];
```