// IE兼容性polyfills - 必须在最前面引入
import './polyfills';

import dva from 'dva';
import ReactDOM from 'react-dom';
import { message } from 'tntd';
import router from './router';

import './styles/style.less';

import history, { createHistory } from '@/utils/history';
import { $, isAppListVisible, isOrgAppListUnVisible, isOrgListVisible } from './utils/utils';
import { getLang } from '@/utils/I18N';
// const browserHistory = require('history').createBrowserHistory;

const app = dva({
    history,
    onError(e) {
        message.error(e.message, 3);
    }
});

const registerModels = (app) => {
    app.model(require('./models/global').default);
    app.model(require('./models/login').default);

    // 数据大盘
    app.model(require('./models/dashboard/appChannel').default);
    app.model(require('./models/dashboard/dataService').default);
    app.model(require('./routes/Dashboard/ConsumerData/model').default);
    app.model(require('./routes/Dashboard/ProviderData/model').default);
    // 三方数据管理
    app.model(require('./models/supplierManagement/supplierList').default);
    app.model(require('./models/supplierManagement/dataServiceList').default);
    app.model(require('./models/supplierManagement/etl').default);
    app.model(require('./models/supplierManagement/systemFields').default);
    app.model(require('./models/supplierManagement/contractList').default);
    // 计费管理
    app.model(require('./models/accountManagement/external').default);
    app.model(require('./models/accountManagement/internal').default);
    app.model(require('./models/accountManagement/billingDetail').default);
    // 应用服务中心
    app.model(require('./models/appServiceCenter/appServiceList').default);
    // 调用查询
    app.model(require('./models/dataManagement/threeCallDetail').default);
    app.model(require('./models/dataManagement/businessChannel').default);
    app.model(require('./models/dataManagement/exceptionList').default);
    app.model(require('./models/dataManagement/pageQuery').default);
    // 监控管理
    app.model(require('./models/monitorWarning/threeDataMonitor').default);
    app.model(require('./models/monitorWarning/threeDataRemainWarn').default);
    app.model(require('./models/monitorWarning/supplierWarning').default);
    // 数据质量分析
    app.model(require('./models/qualityAnalysis/costAnalysis').default);
    app.model(require('./models/qualityAnalysis/agingAnalysis').default);
};

registerModels(app);
if (!window.isInLightBox) {
    const lang = getLang();
    document.getElementsByTagName('body')[0].className = `lang-${lang}`;
    $(document.getElementsByTagName('body')[0])
        .removeClass(/lang-*/)
        .addClass(`lang-${lang}`);
    app.router(router);
    app.start('#root');
}

// export 微前端spa所需的生命周期钩子函数
export async function bootstrap() {
    console.log('react app bootstrapped');
}

// 只要设置机构可见 即最高优先级为机构 其次才走老逻辑
const initVisible = (actions) => {
    actions.setAppListVisible(isAppListVisible());
    actions.setOrgListVisible(isOrgListVisible()); // 机构可见
    actions.setOrgAppListVisible(!isOrgAppListUnVisible()); // 机构下的应用可见
};

let onPopstate = (actions) => {
    return initVisible(actions);
};

// ！！！ 重要
// registerModels(app);
export const mount = async ({ actions }) => {
    initVisible(actions);
    onPopstate = onPopstate.bind(null, actions);
    window.addEventListener('popstate', onPopstate);
    // app.router(router);
    // 这里每次挂载时需要重新创建history对象，
    // 解决二次挂载时用到了前一次挂载的history对象而导致路由render异常问题
    app.router(({ app }) =>
        router({
            history: createHistory(),
            app,
            actions
        })
    );
    app.start('#root');
};

// ！！！ 重要
export const unmount = async () => {
    ReactDOM.unmountComponentAtNode(document.getElementById('root'));
    window.removeEventListener('popstate', onPopstate);
    /* app._models.forEach(model => {
		app.unmodel(model.namespace);
	}); */
};

export const getAppStore = () => app._store;

export const getLanguage = () => {
    const globalStore = getAppStore().getState().global;
    const { personalMode } = globalStore;

    return personalMode.lang === 'cn' ? 'cn' : 'en';
};
