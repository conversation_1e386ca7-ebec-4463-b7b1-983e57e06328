import queryString from 'query-string';
import Store from 'store';
import Config from '../common/config';

const getUrl = (url, query) => {
	return url + '?' + queryString.stringify(query);
};
const getHeader = () => {
	let headers = {};
	headers['X-User-Token'] = Store.get(Config.X_USER_TOKEN);
	headers['X-Cf-Random'] = sessionStorage.getItem('_csrf_');
	return headers;
};
const getApiDetailHeader = () => {
	let headers = {};
	headers['X-User-Token'] = Store.get(Config.X_USER_TOKEN);
	headers['X-Cf-Random'] = sessionStorage.getItem('_csrf_');
	headers['DETAILRESPONSE'] = 1;
	return headers;
};
const deleteEmptyObjItem = (obj) => {
	for (let i in obj) {
		let value = obj[i];
		if (value === null || value === undefined || value === '' || !value && value !== 0 && value !== false) {
			delete obj[i];
		}
	}
	return obj;
};

const getUpload = () => {
	let headers = {};
	headers['X-User-Token'] = Store.get(Config.X_USER_TOKEN);
	headers['Content-Type'] = 'multipart/form-data';
	headers['X-Cf-Random'] = sessionStorage.getItem('_csrf_');
	return headers;

};
export {
	getUrl,
	getHeader,
	getApiDetailHeader,
	deleteEmptyObjItem,
	getUpload
};
