import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request, { downloadFileHandle } from '../../utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(
        getUrl('/bridgeApi/contract/getContractList', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取所有合同数据
const getListAll = async (params) => {
    return request(
        getUrl('/bridgeApi/contract/getSimpleContractList', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request(
        '/bridgeApi/contract/add',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 删除数据
const deleteData = async (params) => {
    return request(
        '/bridgeApi/contract/delete',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 更新数据
const updateData = async (params) => {
    return request(
        '/bridgeApi/contract/update',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 查询单条合同信息
const getInfo = async (params) => {
    return request(
        getUrl('/bridgeApi/contract/select', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 附件下载
const downloadFj = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/contract/download', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        name,
        fileType,
        null,
        true
    );
};

export default {
    getList,
    addData,
    deleteData,
    updateData,
    getListAll,
    getInfo,
    downloadFj
};
