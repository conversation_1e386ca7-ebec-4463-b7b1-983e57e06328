import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';
import { getHeader } from '@/utils/common';

// 获取表格数据
const getList = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceProvider/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取所有供应商数据
const getListAll = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceProvider/listAll', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request(
        '/bridgeApi/serviceProvider/add',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 删除数据
const deleteData = async (params) => {
    return request(
        '/bridgeApi/serviceProvider/delete',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 更新数据
const updateData = async (params) => {
    return request(
        '/bridgeApi/serviceProvider/update',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params, ['contractCode'])
        },
        true
    );
};

export default {
    getList,
    addData,
    deleteData,
    updateData,
    getListAll
};
