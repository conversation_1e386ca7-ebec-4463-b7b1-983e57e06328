import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(getUrl('/field/list', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 获取所有字段数据
const getListAll = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/common/field/listAll', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request('/field/add', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 删除数据
const deleteData = async (params) => {
    return request('/field/delete', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 更新数据
const updateData = async (params) => {
    return request('/field/update', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 检查字段是否被服务使用
const checkUsed = async (params) => {
    return request(getUrl('/field/checkUsed', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

export default {
    getList,
    addData,
    deleteData,
    updateData,
    getListAll,
    checkUsed
};
