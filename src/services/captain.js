import { getHeader, getUrl } from '../utils/common';
import request from '../utils/request';

const getDocumentTypeList = async () => {
    return request(
        '/captainApi/documentType/list',
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const getIndexPackage = async (params) => {
    return request(
        getUrl('/captainApi/indexPackage/simpleList', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getDeriveIndexPackage = async (params) => {
    return request(
        getUrl('/captainApi/indexPackage/list', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

export default {
    getDocumentTypeList,
    getIndexPackage,
    getDeriveIndexPackage
};
