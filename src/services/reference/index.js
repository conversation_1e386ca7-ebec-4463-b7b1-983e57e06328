import request from '@/utils/request';
import { getUrl, deleteEmptyObjItem, getHeader } from '../common';
const getRelationResult = async (params) => {
    params = deleteEmptyObjItem(params);
    let url = '/bridgeApi/bifrost/relationAnalysis';
    if (params.relationType) {
        url += `/${params.relationType}`;
        delete params.relationType;
    }
    return request(
        getUrl(url, params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const checkComponentReference = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(
        getUrl('/bridgeApi/bifrost/checkComponentReference', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const onlineValidate = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(
        getUrl('/bridgeApi/layout/component/onlineValidate', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const batchCheckComponentReference = async (params) => {
    return request(
        '/bridgeApi/bifrost/batchCheckComponentReference',
        {
            method: 'POST',
            headers: {
                ...getHeader(),
                'Content-Type': 'application/json'
            },
            body: params
        },
        true
    );
};

export default {
    batchCheckComponentReference,
    getRelationResult,
    checkComponentReference,
    onlineValidate
};
