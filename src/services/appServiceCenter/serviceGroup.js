import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request, { downloadFileHandle } from '../../utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(getUrl('/channelServiceGroup/list', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 获取表格数据
const getOutPutConfig = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/getOutPutConfig', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request('/channelServiceGroup/insertServiceGroup', {
        method: 'POST',
        body: params
    });
};

// 删除数据
const deleteData = async (params) => {
    return request('/channelServiceGroup/deleteServiceGroup', {
        method: 'POST',
        body: params
    });
};

// 更新数据
const updateData = async (params) => {
    return request('/channelServiceGroup/updateServiceGroupInfo', {
        method: 'POST',
        body: params
    });
};

// 上下线
const setOnline = async (params) => {
    return request('/channelServiceGroup/online', {
        method: 'POST',
        body: params
    });
};

// 根据数据源标识获取入参配置
const getServiceInfo = async (params) => {
    return request(getUrl('/channelServiceGroup/serviceGroupInputInfo', params), {
        method: 'GET'
    });
};

// 获取表格数据
const geServiceGroupInfo = async (params) => {
    return request(getUrl('/channelServiceGroup/serviceGroupInfo', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

const downloadDoc = (params, name) => {
    return downloadFileHandle(
        getUrl('/channelServiceGroup/docFileDownload', params),
        {
            method: 'GET'
        },
        name,
        'pdf'
    );
};

const getRelationOrgList = (params) => {
    return request(getUrl('/channelServiceGroup/getRelationOrgList', params), {
        method: 'GET'
    });
};

export default {
    getList,
    addData,
    deleteData,
    updateData,
    getOutPutConfig,
    setOnline,
    getServiceInfo,
    geServiceGroupInfo,
    downloadDoc,
    getRelationOrgList
};
