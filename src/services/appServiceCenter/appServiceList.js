import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request, { downloadFileHandle } from '../../utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(getUrl('/channelService/list', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

const getService = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/getService', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

const getAvailableService = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/getAvailableService', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};
const findRouteServiceList = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/findRouteServiceList', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request('/channelService/add', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 删除数据
const deleteData = async (params) => {
    return request('/channelService/delete', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 更新数据
const updateData = async (params) => {
    return request('/channelService/update', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 上下线
const setOnline = async (params) => {
    return request('/channelService/online', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 重置密钥
const setSecretKey = async (params) => {
    return request('/channelService/buildSecretKey', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 获取路由列表
const findRouteService = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/findRouteService', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

const findDefaultRouteServiceList = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/findDefaultRouteServiceList', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

const downloadDoc = (params, name) => {
    return downloadFileHandle(
        getUrl('/channelService/docFileDownload', params),
        {
            method: 'GET'
        },
        name,
        'pdf'
    );
};

const getRelationOrgList = (params) => {
    return request(getUrl('/channelService/getRelationOrgList', params), {
        method: 'GET'
    });
};
const reBuildSecretKey = (params) => {
    return request('/channelService/buildSecretKey', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

const reBuildGroupSecretKey = (params) => {
    return request('/channelServiceGroup/buildSecretKey', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

export default {
    getList,
    addData,
    getService,
    deleteData,
    updateData,
    setOnline,
    getAvailableService,
    findRouteServiceList,
    setSecretKey,
    findRouteService,
    findDefaultRouteServiceList,
    downloadDoc,
    getRelationOrgList,
    reBuildSecretKey,
    reBuildGroupSecretKey
};
