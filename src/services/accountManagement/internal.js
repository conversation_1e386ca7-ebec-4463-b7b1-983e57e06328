import { getUrl, deleteEmptyObjItem } from "../../utils/common";
import request from "../../utils/request";
import { downloadFileHandle } from "../../utils/request";

// 业务渠道计费列表
const getChannelList = async(params) => {
	return request(getUrl("/charge/internal/getChannelList", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 导出 - 业务渠道计费列表
const exportChannelList = async(params, name, fileType) => {
	return downloadFileHandle(getUrl("/charge/internal/channelList/export", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

// 导出 - 业务渠道计费明细列表
const exportChannelListDetail = async(params, name, fileType) => {
	return downloadFileHandle(getUrl("/charge/internal/channelBillList/export", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

// 业务机构计费列表
const getOrganizeList = async(params) => {
	return request(getUrl("/charge/internal/getOrganizeList", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 导出 - 业务机构计费列表
const exportOrganizeList = async(params, name, fileType) => {
	return downloadFileHandle(getUrl("/charge/internal/organizeList/export", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

// 导出 - 业务机构计费明细列表
const exportOrganizeListDetail = async(params, name, fileType) => {
	return downloadFileHandle(getUrl("/charge/internal/organizeBillList/export", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

// 内部计费弹框列表
const getBusinessBillList = async(params) => {
	let path = "/charge/internal/getBusinessBillList";
	if (params.businessType === 1) path = `${path}/channel`;
	if (params.businessType === 2) path = `${path}/org`;
	return request(getUrl(path, deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

export default {
	getChannelList,
	exportChannelList,
	getBusinessBillList,
	getOrganizeList,
	exportOrganizeList,
	exportChannelListDetail,
	exportOrganizeListDetail
};
