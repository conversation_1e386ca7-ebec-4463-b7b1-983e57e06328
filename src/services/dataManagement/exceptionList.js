import { getUrl, deleteEmptyObjItem } from "../../utils/common";
import request from "../../utils/request";
import { downloadFileHandle } from "../../utils/request";

// 业务渠道异常列表
const getList = async(params) => {
	return request(getUrl("/dataManage/channel/getInvokeFailList", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 业务渠道异常列表导出
const exportList = async(params, name, fileType) => {
	return downloadFileHandle(getUrl("/dataManage/channel/invokeFailList/export", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

export default {
	getList,
	exportList
};
