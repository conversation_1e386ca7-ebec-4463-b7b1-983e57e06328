import { getUrl, deleteEmptyObjItem } from "../../utils/common";
import request from "../../utils/request";

// 业务渠道查询明细
const getList = async (params) => {
	return request(getUrl("/dataManage/channel/getInvokeDetailList", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取所有调用方服务
const getListAll = async (params) => {
	return request(getUrl("/channelServiceGroup/getListAll", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

const getDetail = async (params) => {
	return request(getUrl("/dataManage/channel/getInvokeDetail", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

export default {
	getList,
	getListAll,
	getDetail
};
