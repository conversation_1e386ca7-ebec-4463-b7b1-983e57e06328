import { deleteEmptyObjItem, getUrl } from '../../utils/common';
import request, { downloadFileHandle } from '../../utils/request';

// 三方服务查询
const getThreeServiceSearch = async (params, type) => {
    return request(
        `/bridgeApi/serviceConfig/sync/test/${type}`,
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 批次列表
const batchList = async (params) => {
    return request(getUrl('/invokeCommon/batchList', params), {
        method: 'GET'
    });
};

// 批次明细
const batchDetails = async (params) => {
    return request(getUrl('/invokeCommon/batchDetails', params), {
        method: 'GET'
    });
};

// 模板下载
const exportTemplate = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/invokeCommon/exportServiceInvokeTemplate', params),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

// 结果下载
const exportResult = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/invokeCommon/exportServiceInvokeDetail', params),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

// 执行查询
const submitInvoke = async (params) => {
    return request('/invokeCommon/submitInvoke', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

// 批次详情
const getBatchDetail = async (params) => {
    return request(getUrl('/invokeCommon/getBatchDetail', params), {
        method: 'GET'
    });
};

export default {
    getThreeServiceSearch,
    batchList,
    exportTemplate,
    batchDetails,
    exportResult,
    submitInvoke,
    getBatchDetail
};
