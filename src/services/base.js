import { getHeader, getUrl } from '../utils/common';
import request, { downloadFileHandle } from '../utils/request';

const getAllMap = async () => {
    return request(
        '/bridgeApi/layout/common/dict',
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const getAllAppName = async () => {
    return request('/applicationName/all', {
        method: 'GET',
        headers: getHeader()
    });
};

// 是否隐藏相关配置信息（是否与天策集成）
const getIntegrationInfo = async () => {
    return request(
        '/bridgeApi/layout/common/mode/integration',
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

// 导出通用接口
const exportComponent = async (param) => {
    return downloadFileHandle(
        getUrl(`/bridgeApi/component/export/${param.componentCategory}`, param),
        {
            method: 'GET'
        },
        param.fileName,
        param.componentShowType,
        undefined,
        true
    );
};
/**
 * 获取所有变量类型及其下面的指标
 * @param {*} type 变量类型
 * @returns
 */
const getVariableList = () => {
    return request(
        '/bridgeApi/workflow/variable/select',
        {
            method: 'GET'
        },
        true
    );
};

// 测试的时候获取机构树
const getOrgAll = async (param) => {
    return request(
        getUrl('/bridgeApi/org', param),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

export default {
    exportComponent,
    getAllMap,
    getAllAppName,
    getIntegrationInfo,
    getVariableList,
    getOrgAll
};
