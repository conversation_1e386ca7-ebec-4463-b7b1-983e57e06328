import { getUrl, deleteEmptyObjItem } from "../../utils/common";
import request from "../../utils/request";

// 获取数据类型数据
const getStatisticResult = async(params) => {
	return request(getUrl("/statistics/thirdService/getServiceTypeAndDetail", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取三方数据明细
const getStatisticBill = async(params) => {
	return request(getUrl("/statistics/thirdService/getServiceDetailForType", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取三方数据大盘调用结果
const getInvokeResult = async(params) => {
	return request(getUrl("/statistics/thirdService/getInvokeDetailForDay", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取三方数据大盘服务调用排行
const getInvokeRank = async(params) => {
	return request(getUrl("/statistics/thirdService/getInvokeRank", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取三方数据大盘按天获取调用结果
const getInvokeResultForDay = async(params) => {
	return request(getUrl("/statistics/thirdService/getInvokeResultForDay", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 获取三方数据大盘统计结果
const getHeaderData = async(params) => {
	return request(getUrl("/statistics/thirdService/getStatisticResult", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

export default {
	getStatisticResult,
	getStatisticBill,
	getInvokeResult,
	getInvokeRank,
	getInvokeResultForDay,
	getHeaderData
};
