import { getAppStore } from "../../app";

const getRecords = (number) => {
	let globalStore = getAppStore().global;
	let { personalMode } = globalStore;
	let cn = "共" + number + "条记录";
	let en = "A total of " + number + " records";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const getDeleteInfo = (name) => {
	let globalStore = getAppStore().global;
	let { personalMode } = globalStore;
	let cn = name ? `确认删除${name}吗？` : "确认删除吗？";
	let en = name ? `Confirm delete ${name}?` : "Confirm delete?";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const getDeleteInfo1 = () => {
	let globalStore = getAppStore().global;
	let { personalMode } = globalStore;
	let cn = "确认删除吗？";
	let en = "Confirm delete this one?";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

export default {
	getRecords,
	getDeleteInfo,
	getDeleteInfo1
};
