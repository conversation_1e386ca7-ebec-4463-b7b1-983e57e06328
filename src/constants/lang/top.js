import { getLanguage } from "../../app";

const common = (field) => {
	let lang = getLanguage();
	let params = {
		"logo": {
			"cn": "知识图谱",
			"en": "Data Center"
		},
		"allApplication": {
			"cn": "全部渠道",
			"en": "All application"
		},
		"personalSet": {
			"cn": "个人设置",
			"en": "Personal Settings"
		},
		"changePwd": {
			"cn": "修改密码",
			"en": "Change the password"
		},
		"exit": {
			"cn": "退出",
			"en": "Exit"
		},
		"noNickname": {
			"cn": "暂无昵称",
			"en": "No nickname"
		}
	};
	return params[field][lang];
};

export default {
	common
};
