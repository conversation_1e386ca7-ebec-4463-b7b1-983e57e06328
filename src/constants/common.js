import I18N from '@/utils/I18N';
const conditionTypeMap = {
	'&&': I18N.constants.common.manZuSuoYouTiao,
	'||': I18N.constants.common.manZuRenYiTiao,
	'!&&': I18N.constants.common.tiaoJianJunBuMan
};

const FieldType = [
	{ name: 'string', value: 1 },
	{ name: 'int', value: 2 },
	{ name: 'double', value: 3 },
	{ name: 'date', value: 4 },
	{ name: 'boolean', value: 5 }
];

const gutterSpan = 10;

export {
	conditionTypeMap,
	gutterSpan,
	FieldType
};
