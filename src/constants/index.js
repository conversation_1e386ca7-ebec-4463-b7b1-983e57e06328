import I18N from '@/utils/I18N';
import moment from 'moment';
import CommonConstants from './common';

const DateRanges = {
    [I18N.constants.index.jinRi]: [moment(), moment()],
    [I18N.constants.index.zuoRi]: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    [I18N.constants.index.zuiJinRi3]: [moment().subtract(6, 'days'), moment()],
    [I18N.constants.index.zuiJinRi2]: [moment().subtract(29, 'days'), moment()],
    [I18N.constants.index.zuiJinRi]: [moment().subtract(89, 'days'), moment()]
};

const TYPE_MAP = {
    INT: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7'
    },
    DOUBLE: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2'
    },
    STRING: {
        displayName: I18N.constants.index.ziFu,
        icon: 'string',
        color: '#826AF9'
    },
    ENUM: {
        displayName: I18N.constants.index.meiJu,
        icon: 'enum',
        color: '#00C5DC'
    },
    BOOLEAN: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7'
    },
    DATETIME: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9'
    },
    INTEGER: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7'
    },
    FLOAT: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2'
    },
    BOOL: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7'
    },
    DATE: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9'
    },
    ARRAY: {
        displayName: I18N.constants.index.shuZu,
        icon: 'array',
        color: '#00D2C2'
    }
};

{
    /* 数据类型(1字符型/2整型/3小数型/4日期型/5枚举/6布尔) */
}

const DATA_TYPE_MAP = {
    1: {
        displayName: I18N.constants.index.ziFu,
        icon: 'string',
        color: '#826AF9',
        type: 'STRING',
        dataType: 1
    },
    2: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7',
        type: 'INT',
        dataType: 2
    },
    3: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2',
        type: 'DOUBLE',
        dataType: 3
    },
    4: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9',
        type: 'DATETIME',
        dataType: 4
    },
    5: {
        displayName: I18N.constants.index.meiJu,
        icon: 'enum',
        color: '#00C5DC',
        type: 'ENUM',
        dataType: 5
    },
    6: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7',
        type: 'BOOLEAN',
        dataType: 6
    }
};

export { CommonConstants, DateRanges, TYPE_MAP, DATA_TYPE_MAP };
