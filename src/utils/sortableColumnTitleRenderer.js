import I18N from '@/utils/I18N';
export const sortableColumnTitleRenderer = (title) =>  ({ sortOrder }) => {
	const htmlTitle = (() => {
		if (sortOrder === 'ascend') {
			return I18N.utils.sortablecolumntitlerenderer.jiangXu
		}

		if (sortOrder === 'descend') {
			return I18N.utils.sortablecolumntitlerenderer.moRenPaiXu
		}

		return I18N.utils.sortablecolumntitlerenderer.shengXu
	})()

	return <span title={htmlTitle}>{title}</span>
}
