import { searchToObject } from "@/utils/utils";
export const findMenuInfoByPath = (menuTree, path) => {
	let menu;
	let subMenu;
	if (path) {
		for (let i = 0; i < menuTree.length; i++) {
			menu = menuTree[i];

			for (let j = 0; j < (menu.children || []).length; j++) {
				if (menu.children[j].path === path) {
					subMenu = menu.children[j];
					break;
				}
			}

			if (subMenu) {
				break;
			}
		}

		return {
			subMenu,
			menu
		};
	}

	return { subMenu, menu };
};

export function isNewTarget(path) {
	if (path.startsWith("http")) {
		try {
			const pathObj = new URL(path);
			const { search } = pathObj || {};
			if (search) {
				const { blankType } = searchToObject(pathObj.search) || {};
				if (blankType === "newTarget") {
					return true;
				}
			}
			return false;
		} catch (e) {
			return false;
		}
	}
	return false;
}

// 过滤需要新开窗口的链接
export function openNewTarget({ path, userInfo }) {
	if (path.indexOf("{esAuth}") > -1) {
		const csrf = encodeURIComponent(sessionStorage.getItem("_csrf_"));
		const ac = userInfo.token;
		path = path.replace("{esAuth}", `ac=${ac}&cf=${csrf}`);
	}
	let a = document.createElement("a");
	a.href = path;
	a.target = "_blank";
	document.body.appendChild(a);
	a.click();
	a.remove();
}
