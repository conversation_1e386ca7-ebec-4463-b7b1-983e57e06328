// 授权过滤ruleAndIndexFieldList
const filterAvailableFieldList = ({ allMap = {}, appCode, orgCode, exclude = [] }) => {
    if (!allMap || !allMap.ruleAndIndexFieldList) {
        return [];
    }

    const { ruleAndIndexFieldList = [] } = allMap || {};

    let newFieldList = [];
    newFieldList = ruleAndIndexFieldList?.filter((item) => {
        // 排除的字段或指标
        if (exclude.includes(item.name)) {
            return false;
        }
        let showOption = !item.apps;
        if (!showOption && item.apps) {
            showOption = item.apps.indexOf(appCode) > -1 || item.apps.includes('all') || [null, undefined, ''].includes(appCode);
        }
        if (showOption && orgCode && item.orgs && Array.isArray(item.orgs)) {
            showOption = item.orgs.includes(orgCode);
        }
        if (showOption) {
            return item;
        }
    });
    return newFieldList;
};

export { filterAvailableFieldList };
