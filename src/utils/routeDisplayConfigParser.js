import I18N from '@/utils/I18N';
const routeStatusMapper = {
	'1': I18N.utils.routedisplayconfigparser.zaiXian,
	'2': I18N.utils.routedisplayconfigparser.liXian,
	'-1': I18N.utils.routedisplayconfigparser.yiShanChu
}

export const routeDisplayConfigParser = (threeServiceList = []) => (route) => {
	const threeService = threeServiceList.find(({ name }) => name === route.name);

	if (route.status === 1) {
		return { name: route.name, displayName: threeService ? threeService.displayName : route.name }
	}
	if (route.status === 2) {
		return { name: route.name, displayName: ` （${routeStatusMapper[`${route.status}`]}）${threeService ? threeService.displayName : route.name}` }
	}

	return { name: route.name, displayName: `（${routeStatusMapper[`${route.status}`]}）${route.name}` }
}
