import queryString from 'query-string';
import Store from 'store';
import Config from '../common/config';

const getUrl = (url, query) => {
    return url + '?' + queryString.stringify(query);
};
const getHeader = () => {
    let headers = {};
    headers['X-Cf-Random'] = sessionStorage.getItem('_csrf_');
    return headers;
};
const deleteEmptyObjItem = (obj, ignoreKeys = []) => {
    for (let i in obj) {
        let value = obj[i];

        if (ignoreKeys.includes(i)) {
            continue;
        }

        if (value === null || value === undefined || value === '' || (!value && value !== 0)) {
            delete obj[i];
        }
    }
    return obj;
};
export { getUrl, getHeader, deleteEmptyObjItem };
