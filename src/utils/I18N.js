import KiwiIntl from 'kiwi-intl';
import Cookies from 'universal-cookie';
import zhCN from '../../.octopus/zh-CN';
import enUS from '../../.octopus/en-US';

const cookies = new Cookies();
export const getLang = () => {
    return cookies.get('lang') || 'cn';
};

export const createOtp = (obj = {}) => {
    const lang = getLang();
    return obj[lang] || {};
};

const kiwiIntl = KiwiIntl.init(getLang(), {
    cn: zhCN,
    en: enUS
});

export default kiwiIntl;
