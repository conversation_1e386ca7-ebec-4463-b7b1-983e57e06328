import queryString from 'query-string';
// 解决url链接带参数之后进行查询，多签页切换之后用户上次查询失效的场景
function QueryRouterFun() {
    this.curPathName = window.location.pathname;
    this.isFirstQuery = true;
    this.init = () => {
        this.isFirstQuery = true;
    };
    this.handle = () => {
        if (this.isFirstQuery) {
            this.isFirstQuery = false;
            return;
        }
        let { search, pathname } = location;
        const searchObj = queryString.parse(search) || {};
        const { currentTab } = searchObj;
        const keepSearch = queryString.stringify({ currentTab });
        if (keepSearch) {
            pathname += `?${keepSearch}`;
        }
        if (this.curPathName === window.location.pathname) {
            history.replaceState({}, 0, `${pathname}`);
        }
    };
}

export const queryRouterHandle = new QueryRouterFun();
