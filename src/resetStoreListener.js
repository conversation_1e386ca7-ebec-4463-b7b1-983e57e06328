import { getAppStore } from '@/app';

export default (modelNamespace, resetFn) => {
    const path = location.pathname;
    if (window.lightBoxActions) {
        const { EVENTS_ENUM, on } = window.lightBoxActions;
        const resetModel =
            resetFn ||
            (() => {
                const { dispatch } = getAppStore();

                dispatch?.({
                    type: `${modelNamespace}/reset`
                });
            });
        const reset = (tab) => {
            if (path.startsWith(tab?.path)) {
                resetModel();
            }
        };

        // 关闭页签时，重置数据
        on(EVENTS_ENUM.ON_TAB_CLOSE, reset);
        on(EVENTS_ENUM.MENU_SELECT, ({ data }) => reset(data));
        on(EVENTS_ENUM.CURRENT_APP_CHANGE, () => () => {
            resetModel();
        });
    }
};
