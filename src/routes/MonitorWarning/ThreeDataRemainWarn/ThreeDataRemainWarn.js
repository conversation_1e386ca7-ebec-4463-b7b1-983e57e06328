/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 三方数据剩余流量预警
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { TableContainer, Table, Pagination, Select, message, Tooltip } from 'tntd';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { dataServiceListAPI } from '@/services';

const Option = Select.Option;

class ThreeDataRemainWarn extends PureComponent {
    state = {
        threeServiceList: []
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0602', 'query')) {
                    this.search();
                    this.getServiceList();
                }
            }
        }, 100);
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'threeDataRemainWarn/reset'
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, threeDataRemainWarnStore } = this.props;
        const { searchParams } = threeDataRemainWarnStore;
        dispatch({
            type: 'threeDataRemainWarn/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    getServiceList = (type) => {
        dataServiceListAPI.getListAll2({ type }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res.data.contents ? res.data.contents : []
                });
            } else {
                message.error(res.msg || res?.message);
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            val = [e[0].valueOf(), e[1].valueOf()];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        obj[field] = val;
        dispatch({
            type: 'threeDataRemainWarn/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });

        this.search();
    }

    // 查看 跳转到三方调用量明细 带上 三方服务code
    look(record) {
        const { history } = this.props;
        let path = '/handle/dataManagement/threeCallDetail';
        history.push(path, {
            serviceCode: record.serviceCode
        });
    }

    render() {
        const { threeServiceList } = this.state;
        const { threeDataRemainWarnStore, globalStore } = this.props;
        const { menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading } = threeDataRemainWarnStore;
        const { serviceCode } = searchParams;

        const columns = [
            {
                title: I18N.threedataremainwarn.shuJuYuanFuWu, // 三方服务名
                dataIndex: 'serviceName',
                key: 'serviceName',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedataremainwarn.shuJuLeiXing, // 数据类型
                dataIndex: 'serviceTypeName',
                key: 'serviceTypeName'
            },
            {
                title: I18N.threedataremainwarn.heZuoFangMingCheng, // 供应商名称
                dataIndex: 'supplierName',
                key: 'supplierName',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedataremainwarn.yuJingXinXi, // 预警信息
                dataIndex: 'warningTypeName',
                key: 'warningTypeName',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 25) {
                        dom = <Tooltip title={text}>{text.substr(0, 25)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedataremainwarn.zongLiuLiang, // 总流量
                dataIndex: 'totalCount',
                key: 'totalCount'
            },
            {
                title: I18N.threedataremainwarn.yiShiYongLiuLiang, // 已使用流量
                dataIndex: 'usedCount',
                key: 'usedCount'
            },
            {
                title: I18N.threedataremainwarn.shengYuLiuLiang, // 剩余流量
                dataIndex: 'remainingCount',
                key: 'remainingCount'
            }
            // {
            // 	title: "操作",
            // 	dataIndex: "operate",
            // 	key: "operate",
            // 	width: 60,
            // 	render: (text, record) => {
            // 		let dom = (
            // 			<span
            // 				className="u-operate"
            // 				onClick={() => this.look(record)}
            // 			>
            // 				查看
            // 			</span>
            // 		);
            // 		return dom;
            // 	}
            // }
        ];

        return (
            <div>
                {/* <div className="page-global-header">
					<div className="left-info">
						<h2>{I18N.threedataremainwarn.shuJuYuanZongLiu
							// 三方早期预警
						}</h2>
					</div>
				</div> */}
                {menuTreeReady && checkFunctionHasPermission('TZ0602', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    placeholder={I18N.threedataremainwarn.shuJuYuanFuWu}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={serviceCode ? serviceCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceCode')}>
                                    {threeServiceList.map((item, index) => {
                                        return (
                                            <Option value={item.name} key={index}>
                                                {item.displayName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </div>
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.index}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.threedataremainwarn.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0602', 'query') && <NoPermission />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    threeDataRemainWarnStore: state.threeDataRemainWarn
}))(TableContainer(ThreeDataRemainWarn));
