/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 供应商超期预警
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Table, Pagination, TableContainer, InputNumber, Tooltip, Ellipsis } from 'tntd';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import otp from './otp';
class SupplierWarning extends PureComponent {
    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0603', 'query')) {
                    this.search();
                }
            }
        }, 100);
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'supplierWarning/reset'
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, supplierWarningStore } = this.props;
        const { searchParams } = supplierWarningStore;
        dispatch({
            type: 'supplierWarning/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        obj[field] = val;
        dispatch({
            type: 'supplierWarning/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });
    }

    render() {
        const { supplierWarningStore, globalStore } = this.props;
        const { menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading } = supplierWarningStore;
        const { remainingDay } = searchParams;

        const columns = [
            {
                title: I18N.supplierwarning.heZuoFangMingCheng, // 供应商名称
                dataIndex: 'supplierName',
                key: 'supplierName',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 10) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierwarning.heTongBianHao, // 合同编号
                dataIndex: 'contractCode',
                key: 'contractCode',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 15) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierwarning.heTongMingCheng, // 合同名称
                dataIndex: 'contractName',
                key: 'contractName',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 10) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierwarning.shuJuYuanFuWu2, // 三方服务标识
                dataIndex: 'serviceCode',
                key: 'serviceCode',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 15) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierwarning.shuJuLeiXing, // 数据类型
                dataIndex: 'serviceTypeName',
                key: 'serviceTypeName',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                }
            },
            {
                title: I18N.supplierwarning.shuJuYuanFuWu, // 三方服务名
                dataIndex: 'serviceName',
                key: 'serviceName',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 10) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierwarning.caiGouDaoQiRi, // 采购到期日
                dataIndex: 'purchaseEndDate',
                key: 'purchaseEndDate',
                width: 190,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                }
            },
            {
                title: I18N.supplierwarning.yuJingXinXi, // 预警信息
                dataIndex: 'warnDay',
                key: 'warnDay',
                width: 190,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                }
            }
        ];

        return (
            <div>
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2>{I18N.supplierwarning.shuJuYuanQiXian}</h2>
                    </div>
                </div> */}
                {menuTreeReady && checkFunctionHasPermission('TZ0603', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <InputNumber
                                    style={{ width: otp.inputWidth }}
                                    min={1}
                                    max={1000}
                                    step={1}
                                    precision={0}
                                    placeholder={I18N.supplierwarning.xiaoYuShengYuHe} // 小于剩余合作天数
                                    value={remainingDay}
                                    onChange={(e) => this.changeField(e, 'select', 'remainingDay')}
                                />
                            </div>
                            <div className="item">
                                <Button
                                    type="primary"
                                    // loading={loading}
                                    onClick={() => this.search()}>
                                    {
                                        // 查询
                                        I18N.supplierwarning.chaXun
                                    }
                                </Button>
                            </div>
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.index}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 1580 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.supplierwarning.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0603', 'query') && <NoPermission />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    supplierWarningStore: state.supplierWarning
}))(TableContainer(SupplierWarning));
