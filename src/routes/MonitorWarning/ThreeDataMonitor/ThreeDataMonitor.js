/*
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 三方数据监控列表
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Table, Pagination, Select, DatePicker, message, Tooltip, Icon, TableContainer, HandleIcon } from 'tntd';
import moment from 'moment';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { DateRanges } from '@/constants';
import { dataServiceListAPI } from '@/services';

const Option = Select.Option;
const { RangePicker } = DatePicker;
class ThreeDataMonitor extends PureComponent {
    state = {
        threeServiceList: []
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0601', 'query')) {
                    this.search();
                    this.getServiceList();
                }
            }
        }, 100);
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'threeDataMonitor/reset'
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, threeDataMonitorStore } = this.props;
        const { searchParams } = threeDataMonitorStore;
        dispatch({
            type: 'threeDataMonitor/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    getServiceList = (type) => {
        dataServiceListAPI.getListAll2({ type }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res.data.contents ? res.data.contents : []
                });
            } else {
                message.error(res.msg || res?.message);
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            const st = e[0].valueOf();
            const et = e[1].valueOf();
            const rangeDay = (et - st) / 1000 / 60 / 60 / 24;
            if (rangeDay > 89) return message.warning(I18N.threedatamonitor.xiTongYiCiZui); // 系统一次最多支持查询90天
            val = [e[0].valueOf(), e[1].valueOf()];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        obj[field] = val;
        dispatch({
            type: 'threeDataMonitor/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });

        this.search();
    }

    // 查看 跳转到三方调用量明细 带上 三方服务code
    look(record) {
        let path = '/handle/dataManagement/threeCallDetail';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    serviceCode: record.serviceCode,
                    returnResult: 'fail',
                    docId: record.docId,
                    date: [moment(record.warningDate).valueOf(), moment(record.warningDate).valueOf()]
                })
            )}`,
            '_blank'
        );
    }

    render() {
        const { threeServiceList } = this.state;
        const { threeDataMonitorStore, globalStore } = this.props;
        const { menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading } = threeDataMonitorStore;
        const { serviceCode } = searchParams;

        const columns = [
            {
                title: I18N.threedatamonitor.shuJuYuanFuWu, // 三方服务名称
                dataIndex: 'serviceName',
                key: 'serviceName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedatamonitor.shuJuLeiXing, // 数据类型
                dataIndex: 'serviceTypeName',
                key: 'serviceTypeName',
                width: 120
            },
            {
                title: I18N.threedatamonitor.jiGou, // 机构
                dataIndex: 'organizeName',
                key: 'organizeName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedatamonitor.heZuoFangMingCheng, // 供应商名称
                dataIndex: 'supplierName',
                key: 'supplierName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedatamonitor.yuJingXinXi, // 预警类型
                dataIndex: 'warningTypeName',
                key: 'warningTypeName',
                width: 180,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 15) {
                        dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threedatamonitor.yuJingRiQi, // 预警日期
                dataIndex: 'warningDate',
                key: 'warningDate',
                width: 220
            },
            {
                title: I18N.threedatamonitor.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 90,
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <HandleIcon>
                            <Tooltip title={I18N.threedatamonitor.chaKan}>
                                <Icon type="profile" style={{ fontSize: 20 }} onClick={() => this.look(record)} />
                            </Tooltip>
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];

        return (
            <div>
                {/* <div className="page-global-header">
					<div className="left-info"> */}
                {/* 三方数据监控列表 */}
                {/* <h2>{I18N.threedatamonitor.shuJuYuanYiChang}</h2>
					</div>
				</div> */}
                {menuTreeReady && checkFunctionHasPermission('TZ0601', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <RangePicker
                                    allowClear={false}
                                    className="middle-calendar-picker"
                                    value={searchParams.date ? [moment(searchParams.date[0]), moment(searchParams.date[1])] : null}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'date')}
                                    ranges={DateRanges}
                                />
                                {/* 系统一次最多支持查询90天 */}
                                <Tooltip title={I18N.threedatamonitor.xiTongYiCiZui}>
                                    <Icon type="question-circle" style={{ fontSize: '16px', marginLeft: '5px' }} />
                                </Tooltip>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    placeholder={I18N.threedatamonitor.shuJuYuanFuWu}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={serviceCode ? serviceCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceCode')}>
                                    {threeServiceList.map((item, index) => {
                                        return (
                                            <Option value={item.name} key={index}>
                                                {item.displayName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </div>
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.index}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 1300 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.threedatamonitor.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0601', 'query') && <NoPermission />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    threeDataMonitorStore: state.threeDataMonitor
}))(TableContainer(ThreeDataMonitor));
