import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { message, Icon, Select, Input, InputNumber, Tooltip, Ellipsis } from 'tntd';

const { Option } = Select;

class FieldPricesFlowRange extends PureComponent {

	componentDidMount = () => {
		const { contractListStore, dispatch } = this.props;
		const { modalType } = contractListStore;
		if (modalType === 1) {
			let data = [{
				hierarchyDetail: [{ begin: null, end: -99, price: null }],
				matchFields: [{
					fieldCode: null,
					matchType: null,
					matchConfig: {
						regex: null
					}
				}]
			}];
			dispatch({
				type: 'contractList/setAttrValue',
				payload: {
					dialogData: {
						addEditModalData: {
							fieldPricesFlowRange: data
						}
					}
				}
			});
		}
	};

	// 匹配字段数据变化
	handleFieldChange = (fieldName, value, index, subIndex = undefined) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		if (fieldName === 'matchConfig.regex') {
			fieldPricesFlowRange[index].matchFields[subIndex].matchConfig.regex = value;
		} else {
			fieldPricesFlowRange[index].matchFields[subIndex][fieldName] = value;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	}

	// 添加父项
	addItem = () => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		fieldPricesFlowRange.push({
			hierarchyDetail: [{
				begin: null,
				end: -99,
				price: null
			}],
			matchFields: [
				{
					fieldCode: null,
					matchType: null,
					matchConfig: {
						regex: null
					}
				}
			]
		});
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	};

	// 删除父项
	deleteItem = (index) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		fieldPricesFlowRange.splice(index, 1);
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	}

	// 添加费用项
	addPriceFlow = (index, subIndex) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		let data = fieldPricesFlowRange[index].hierarchyDetail;
		if (data.length === 1) {
			data.unshift({
				begin: 1, end: null, price: null
			});
		} else {
			data.splice(data.length - 1, 0, {
				begin: data[subIndex].end + 1, end: null, price: null
			});
			data[subIndex + 2].begin = null;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	}

	// 删除费用项
	deletePriceFlow = (index, subIndex) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		let data = fieldPricesFlowRange[index].hierarchyDetail;
		data.splice(subIndex, 1);
		data[0].begin = 1;
		if (data.length > 1 && subIndex !== 0) {
			data[subIndex].begin = data[subIndex - 1].end + 1;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	}

	// 改变费用项表单值
	changePriceFlowField = (index, subIndex, fieldName, val) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		let data = fieldPricesFlowRange[index].hierarchyDetail;
		// fieldPricesFlowRange[index].hierarchyDetail[subIndex][fieldName] = val;
		data[subIndex][fieldName] = val;
		if (fieldName === 'end') {
			data[subIndex + 1].begin = val + 1;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						fieldPricesFlowRange
					}
				}
			}
		});
	}

	// 每项第二个费用失去焦点时
	flowBlur = (i) => {
		const { contractListStore } = this.props;
		const { dialogData: { addEditModalData: { fieldPricesFlowRange } } } = contractListStore;
		let data = fieldPricesFlowRange[i].hierarchyDetail;

		data.forEach((item, index) => {
			if (item.end <= item.begin && index !== data.length - 1) {
				this.changePriceFlowField(i, index, 'end', null);
				return message.warning(I18N.inner.fieldpricesflowrange.diErGeQuJian); // 第二个区间输入值要大于第一个区间输入值
			}
		});
	}

	// 正则表单失焦事件
	handleRegexBlur = (e, fieldPricesFlowRange) => {
		const val = e.target.value;
		if (!val) return;
		const arr = fieldPricesFlowRange.map(v => v.matchFields[0].matchConfig.regex === val)?.filter(v => v);
		if (arr?.length >= 2) {
			message.warning(I18N.inner.fieldpricesflowrange.geKongJianZhongDe);
		}
	}

	render() {
		const { contractListStore, disabled, fields } = this.props;
		const { dialogData } = contractListStore;
		const { addEditModalData } = dialogData;
		const { fieldPricesFlowRange } = addEditModalData;

		return (
			<div className="price-config-box">
				{fieldPricesFlowRange?.map((v, i) => (
					<div className="price-config-item" key={i}>
						<div className="price-item">
							<span className="u-label">{I18N.inner.fieldpricesflowrange.feiYong}</span>
							<div className="wrap field-prices-flow-wrap">
								{v.hierarchyDetail.map((item, index) => {
									return (
										<div className="box" key={index}>
											<InputNumber
												min={0}
												precision={0}
												className="mr5"
												disabled={disabled || v.hierarchyDetail.length > 1}
												placeholder={I18N.inner.fieldpricesflowrange.xiTongZiDongTian} // 系统自动填充
												value={item.begin}
												onChange={(e) => this.changePriceFlowField(i, index, 'begin', e)}
											/>
											{/* 至 */}
											{I18N.inner.fieldpricesflowrange.zhi}
											<InputNumber
												precision={0}
												className="ml5 mr5"
												placeholder={I18N.inner.fieldpricesflowrange.shuRuQuJianZhi} // 输入区间值
												disabled={disabled || v.hierarchyDetail.length - 1 === index}
												value={item.end}
												onChange={(e) => this.changePriceFlowField(i, index, 'end', e)}
												onBlur={() => this.flowBlur(i)}
											/>
											<InputNumber
												min={0}
												precision={2}
												placeholder={I18N.inner.fieldpricesflowrange.shuRuFeiYongYuan} // 输入费用/元
												disabled={disabled}
												value={item.price}
												onChange={(e) => this.changePriceFlowField(i, index, 'price', e)}
											/>
											{
												v.hierarchyDetail.length === index + 1 &&
												// 用-99表示区间的正无穷，此项不可删除，系统默认配置项
												<Tooltip title={I18N.inner.fieldpricesflowrange.yongBiaoShiQuJian}>
													<Icon type="question-circle" />
												</Tooltip>
											}
											{
												(v.hierarchyDetail.length === 1 || index === v.hierarchyDetail.length - 2) &&
												!disabled &&
												<Icon type="plus-circle" onClick={() => this.addPriceFlow(i, index)} />
											}
											{
												v.hierarchyDetail.length !== index + 1 &&
												!disabled &&
												<Icon type="delete" onClick={() => this.deletePriceFlow(i, index)} />
											}
										</div>
									);
								})}
							</div>
						</div>
						<div className="field-item">
							<span className="u-label">{I18N.inner.fieldpricesflowrange.piPeiZiDuan}</span>
							<div>
								<Select
									showSearch
									placeholder={I18N.inner.fieldpricesflowrange.xuanZeYiLaiXi}
									style={{ width: 268 }}
									value={v.matchFields[0].fieldCode || undefined}
									onChange={val => this.handleFieldChange('fieldCode', val, i, 0)}
									optionFilterProp="children"
								>
									{
										fields?.map(vv => (
											<Option key={vv.name} value={vv.name}>{vv.displayName}【{vv.name}】</Option>
										))
									}
								</Select>
								<Select
									placeholder={I18N.inner.fieldpricesflowrange.qingXuanZe}
									style={{ width: 100, margin: '0 8px' }}
									value={v.matchFields[0].matchType || undefined}
									onChange={val => this.handleFieldChange('matchType', val, i, 0)}
								>
									<Option key={1} value={1}><Ellipsis title={I18N.inner.fieldpricesflowrange.zhengZe} /></Option>
								</Select>
								<Input
									placeholder={I18N.inner.fieldpricesflowrange.qingShuRuZhengZe}
									style={{ width: 240 }}
									value={v.matchFields[0].matchConfig.regex}
									onChange={e => this.handleFieldChange('matchConfig.regex', e.target.value, i, 0)}
									onBlur={e => this.handleRegexBlur(e, fieldPricesFlowRange)}
								/>
							</div>
						</div>
						<div className="operate-btns">
							{i === fieldPricesFlowRange.length - 1 ? (
								<Icon onClick={this.addItem} type="plus-circle" />
							) : null}
							{fieldPricesFlowRange.length > 1 ? (
								<Icon onClick={() => this.deleteItem(i)} type="delete" />
							) : null}
						</div>
					</div>
				))}
			</div>
		);
	}
}

export default connect(state => ({
	contractListStore: state.contractList
}))(FieldPricesFlowRange);
