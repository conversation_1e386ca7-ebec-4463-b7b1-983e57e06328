import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Icon, InputNumber, Select, Input, message, Ellipsis } from 'tntd';

const { Option } = Select;

class CountRange extends PureComponent {

	componentDidMount = () => {
		const { contractListStore, dispatch } = this.props;
		const { modalType } = contractListStore;
		if (modalType === 1) {
			let data = [{
				price: null,
				matchFields: [
					{
						fieldCode: null,
						matchType: null,
						matchConfig: {
							regex: null
						}
					}
				]
			}];
			dispatch({
				type: 'contractList/setAttrValue',
				payload: {
					dialogData: {
						addEditModalData: {
							countConfig: data
						}
					}
				}
			});
		}
	};

	handleCountFieldChange = (fieldName, value, index, subIndex = undefined) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { countConfig } } } = contractListStore;
		if (!subIndex && subIndex !== 0) {
			countConfig[index].price = value;
		} else if (fieldName === 'matchConfig.regex') {
			countConfig[index].matchFields[subIndex].matchConfig.regex = value;
		} else {
			countConfig[index].matchFields[subIndex][fieldName] = value;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						countConfig
					}
				}
			}
		});
	}

	addCountItem = () => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { countConfig } } } = contractListStore;
		countConfig.push({
			price: null,
			matchFields: [
				{
					fieldCode: null,
					matchType: null,
					matchConfig: {
						regex: null
					}
				}
			]
		});
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						countConfig
					}
				}
			}
		});
	};

	handleRegexBlur = (e, countConfig) => {
		const val = e.target.value;
		if (!val) return;
		const arr = countConfig.map(v => v.matchFields[0].matchConfig.regex === val)?.filter(v => v);
		if (arr?.length >= 2) {
			message.warning(I18N.inner.countrange.geKongJianZhongDe);
		}
	}

	deleteCountItem = (index) => {
		const { contractListStore, dispatch } = this.props;
		const { dialogData: { addEditModalData: { countConfig } } } = contractListStore;
		countConfig.splice(index, 1);
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						countConfig
					}
				}
			}
		});
	}

	render() {
		const { contractListStore, disabled, fields } = this.props;
		const { dialogData } = contractListStore;
		const { addEditModalData } = dialogData;
		const { countConfig } = addEditModalData;

		return (
			<div className="price-config-box">
				{countConfig?.map((v, i) => (
					<div className="price-config-item" key={i}>
						<div className="price-item">
							<span className="u-label">
								<span className="required">{I18N.inner.countrange.feiYong}</span>
							</span>
							<InputNumber
								precision={2}
								style={{width: 268}}
								min={0}
								placeholder={I18N.inner.countrange.shuRuFeiYongYuan} // 输入费用/元
								disabled={disabled}
								value={v.price}
								onChange={(val) => this.handleCountFieldChange('price', val, i)}
							/>
							<span className="u-unit">{I18N.inner.countrange.yuan}</span>
						</div>
						<div className="field-item">
							<span className="u-label">{I18N.inner.countrange.piPeiZiDuan}</span>
							<div>
								<Select
									showSearch
									placeholder={I18N.inner.countrange.xuanZeYiLaiXi}
									style={{width: 268}}
									disabled={disabled}
									value={v.matchFields[0]?.fieldCode || undefined}
									onChange={val => this.handleCountFieldChange('fieldCode', val, i, 0)}
									optionFilterProp="children"
								>
									{fields?.map(vv => (
										<Option key={vv.name} value={vv.name}>{vv.displayName}【{vv.name}】</Option>
									))}
								</Select>
								<Select
									placeholder={I18N.inner.countrange.qingXuanZe}
									style={{width: 100, margin: '0 8px'}}
									disabled={disabled}
									value={v.matchFields[0]?.matchType || undefined}
									onChange={val => this.handleCountFieldChange('matchType', val, i, 0)}
								>
									<Option key={1} value={1}><Ellipsis title={I18N.inner.countrange.zhengZe} /></Option>
								</Select>
								<Input
									placeholder={I18N.inner.countrange.qingShuRuZhengZe}
									style={{width: 240}}
									disabled={disabled}
									value={v.matchFields[0]?.matchConfig?.regex}
									onChange={e => this.handleCountFieldChange('matchConfig.regex', e.target.value, i, 0)}
									onBlur={(e) => this.handleRegexBlur(e, countConfig)}
								/>
							</div>
						</div>
						<div className="operate-btns">
							{i === countConfig.length - 1 && !disabled ? (
								<Icon onClick={this.addCountItem} type="plus-circle" />
							) : null}
							{countConfig.length > 1 && !disabled ? (
								<Icon onClick={() => this.deleteCountItem(i)} type="delete" />
							) : null}
						</div>
					</div>
				))}
			</div>
		);
	}
}

export default connect(state => ({
	contractListStore: state.contractList
}))(CountRange);
