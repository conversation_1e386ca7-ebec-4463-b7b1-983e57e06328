import I18N from '@/utils/I18N';
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { message, Icon, InputNumber, Tooltip, DatePicker } from 'tntd';
import moment from 'moment';
import { formatST, formatET } from '@/utils/utils';

class DateRange extends PureComponent {

	componentDidMount() {
		const { contractListStore, dispatch } = this.props;
		const { modalType, dialogData } = contractListStore;
		const { startTime, endTime } = dialogData.addEditModalData;

		if (modalType === 1) { // 新增
			let dateRange = [
				{ begin: startTime, end: endTime, price: null }
			];
			dispatch({
				type: 'contractList/setAttrValue',
				payload: {
					dialogData: {
						addEditModalData: {
							dateRange
						}
					}
				}
			});
		}
	}

	componentDidUpdate(preProps) {
		const preST = preProps.contractListStore.dialogData.addEditModalData.startTime;
		const nextST = this.props.contractListStore.dialogData.addEditModalData.startTime;
		const preET = preProps.contractListStore.dialogData.addEditModalData.endTime;
		const nextET = this.props.contractListStore.dialogData.addEditModalData.endTime;
		if (preST !== nextST || preET !== nextET) {
			const { dispatch } = this.props;
			let dateRange = [
				{ begin: nextST, end: nextET, price: null }
			];
			dispatch({
				type: 'contractList/setAttrValue',
				payload: {
					dialogData: {
						addEditModalData: {
							dateRange
						}
					}
				}
			});
		}
	}

	// 添加日期区间计费方式
	addflowRange(i) {
		const { contractListStore, dispatch } = this.props;
		let { dateRange, startTime, endTime } = contractListStore.dialogData.addEditModalData;

		if (!startTime || !endTime) return message.warning(I18N.inner.daterange.qingXianWanShanHe); // 请先完善合同开始日期和合同结束日期

		if (dateRange.length === 1) {
			dateRange.unshift({
				begin: startTime, end: null, price: null
			});
			dateRange[1].begin = null;
		} else {
			const beginTime = dateRange[i].end ? formatST(moment(dateRange[i].end).add(1, 'days').valueOf()) : null;
			dateRange.splice(dateRange.length - 1, 0, {
				begin: beginTime, end: null, price: null
			});
			dateRange[i + 2].begin = null;
		}

		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						dateRange
					}
				}
			}
		});
	}

	// 删除日期区间计费方式
	deleteflowRange(i) {
		const { contractListStore, dispatch } = this.props;
		let { dateRange, startTime } = contractListStore.dialogData.addEditModalData;
		dateRange.splice(i, 1);
		dateRange[0].begin = startTime;
		if (dateRange.length > 1 && i !== 0) {
			dateRange[i].begin = formatST(moment(dateRange[i - 1].end).add(1, 'days').valueOf());
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						dateRange
					}
				}
			}
		});
	}

	// 日期区间计费方式赋值
	changeFlowField(e, field, i) {
		const { contractListStore, dispatch } = this.props;
		let { dateRange, endTime } = contractListStore.dialogData.addEditModalData;

		if (field === 'end') {
			let st = formatET(dateRange[i].begin);
			let et = formatET(e);
			if (moment(st).valueOf() > moment(et).valueOf()) {
				return message.warning(I18N.inner.daterange.diErGeRiQi); // 第二个日期要大于第一个日期
			}
			if (moment(et).valueOf() >= moment(endTime).valueOf()) {
				return message.warning(I18N.inner.daterange.riQiBuNengDa); // 日期不能大于等于合同结束日期
			}
			dateRange[i][field] = et;
			dateRange[i + 1].begin = formatST(moment(e).add(1, 'days').valueOf());
		} else {
			dateRange[i][field] = e;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						dateRange
					}
				}
			}
		});
	}

	render() {
		const { dateRange, disabled } = this.props;

		return (
			<div className="wrap">
				{
					dateRange &&
					dateRange.map((item, index) => {
						return (
							<div className="box" key={index}>
								<span className="s1">*</span>
								<DatePicker
									format="YYYY-MM-DD"
									allowClear={false}
									className="mr5"
									disabled
									style={{ width: '130px' }}
									placeholder={I18N.inner.daterange.xiTongZiDongTian} // 系统自动填充
									value={item.begin ? moment(item.begin) : null}
									onChange={(date, dateString) => this.changeFlowField(dateString, 'begin', index)}
								/>
								{/* 至 */}
								{I18N.inner.daterange.zhi}
								<DatePicker
									format="YYYY-MM-DD"
									allowClear={false}
									className="ml5 mr5"
									disabled={disabled || dateRange.length - 1 === index}
									style={{ width: '130px' }}
									value={item.end ? moment(item.end) : null}
									onChange={(date, dateString) => this.changeFlowField(dateString, 'end', index)}
								/>
								<InputNumber
									min={0}
									precision={2}
									placeholder={I18N.inner.daterange.shuRuFeiYongYuan} // 输入费用/元
									disabled={disabled}
									value={item.price}
									onChange={(e) => this.changeFlowField(e, 'price', index)}
								/>
								{
									dateRange.length === index + 1 &&
									// 日期区间仅可在合同有效期内选择，且完全覆盖合同有效期；起始时间时分秒固定00:00:00,截至时间 时分秒 固定为23:59:59
									<Tooltip title={I18N.inner.daterange.riQiQuJianJin}>
										<Icon type="question-circle" />
									</Tooltip>
								}
								{
									(dateRange.length === 1 || index === dateRange.length - 2) &&
									!disabled &&
									<Icon type="plus-circle" onClick={() => this.addflowRange(index)} />
								}
								{
									dateRange.length !== index + 1 &&
									!disabled &&
									<Icon type="delete" onClick={() => this.deleteflowRange(index)} />
								}
							</div>
						);
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	contractListStore: state.contractList
}))(DateRange);
