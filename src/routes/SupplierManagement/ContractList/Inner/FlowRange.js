import I18N from '@/utils/I18N';
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { message, Icon, InputNumber, Tooltip } from 'tntd';

class FlowRange extends PureComponent {

	componentDidMount() {
		const { contractListStore, dispatch } = this.props;
		const { modalType } = contractListStore;
		if (modalType === 1) { // 新增
			let flowRange = [
				{ begin: null, end: -99, price: null }
			];
			dispatch({
				type: 'contractList/setAttrValue',
				payload: {
					dialogData: {
						addEditModalData: {
							flowRange
						}
					}
				}
			});
		}
	}

	// 添加浏览区间计费方式
	addflowRange(i) {
		const { contractListStore, dispatch } = this.props;
		let { flowRange } = contractListStore.dialogData.addEditModalData;

		if (flowRange.length === 1) {
			flowRange.unshift({
				begin: 1, end: null, price: null
			});
		} else {
			flowRange.splice(flowRange.length - 1, 0, {
				begin: flowRange[i].end + 1, end: null, price: null
			});
			flowRange[i + 2].begin = null;
		}

		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						flowRange
					}
				}
			}
		});
	}

	// 删除浏览区间计费方式
	deleteflowRange(i) {
		const { contractListStore, dispatch } = this.props;
		let { flowRange } = contractListStore.dialogData.addEditModalData;
		flowRange.splice(i, 1);
		flowRange[0].begin = 1;
		if (flowRange.length > 1 && i !== 0) {
			flowRange[i].begin = flowRange[i - 1].end + 1;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						flowRange
					}
				}
			}
		});
	}

	// 流量区间计费方式赋值
	changeFlowField(e, field, i) {
		const { contractListStore, dispatch } = this.props;
		let { flowRange } = contractListStore.dialogData.addEditModalData;
		flowRange[i][field] = e;
		if (field === 'end') {
			flowRange[i + 1].begin = e + 1;
		}
		dispatch({
			type: 'contractList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						flowRange
					}
				}
			}
		});
	}

	// 流量区间计费方式移除焦点校验
	flowBlur() {
		const { contractListStore } = this.props;
		let { flowRange } = contractListStore.dialogData.addEditModalData;

		flowRange.forEach((item, index) => {
			if (item.end <= item.begin && index !== flowRange.length - 1) {
				this.changeFlowField(null, 'end', index);
				return message.warning(I18N.inner.flowrange.diErGeQuJian); // 第二个区间输入值要大于第一个区间输入值
			}
		});
	}

	render() {
		const { flowRange, disabled } = this.props;

		return (
			<div className="wrap">
				{
					flowRange &&
					flowRange.map((item, index) => {
						return (
							<div className="box" key={index}>
								<span className="s1">*</span>
								<InputNumber
									min={0}
									precision={0}
									className="mr5"
									disabled={disabled || flowRange.length > 1}
									placeholder={I18N.inner.flowrange.xiTongZiDongTian} // 系统自动填充
									value={item.begin}
									onChange={(e) => this.changeFlowField(e, 'begin', index)}
								/>
								{/* 至 */}
								{I18N.inner.flowrange.zhi}
								<InputNumber
									precision={0}
									className="ml5 mr5"
									placeholder={I18N.inner.flowrange.shuRuQuJianZhi} // 输入区间值
									disabled={disabled || flowRange.length - 1 === index}
									value={item.end}
									onChange={(e) => this.changeFlowField(e, 'end', index)}
									onBlur={() => this.flowBlur()}
								/>
								<InputNumber
									min={0}
									precision={2}
									placeholder={I18N.inner.flowrange.shuRuFeiYongYuan} // 输入费用/元
									disabled={disabled}
									value={item.price}
									onChange={(e) => this.changeFlowField(e, 'price', index)}
								/>
								{
									flowRange.length === index + 1 &&
									// 用-99表示区间的正无穷，此项不可删除，系统默认配置项
									<Tooltip title={I18N.inner.flowrange.yongBiaoShiQuJian}>
										<Icon type="question-circle" />
									</Tooltip>
								}
								{
									(flowRange.length === 1 || index === flowRange.length - 2) &&
									!disabled &&
									<Icon type="plus-circle" onClick={() => this.addflowRange(index)} />
								}
								{
									flowRange.length !== index + 1 &&
									!disabled &&
									<Icon type="delete" onClick={() => this.deleteflowRange(index)} />
								}
							</div>
						);
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	contractListStore: state.contractList
}))(FlowRange);
