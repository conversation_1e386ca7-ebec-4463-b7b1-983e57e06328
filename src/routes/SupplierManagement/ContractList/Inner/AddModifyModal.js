/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: Modal弹框
 */

import I18N from '@/utils/I18N';
import '../index.less';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, Input, Select, message, Button, DatePicker, Upload, Icon, Tooltip, InputNumber, Spin, Form, Ellipsis } from 'tntd';
import { contractListAPI, systemFieldsAPI } from '@/services';
import AceEditor from 'react-ace';
import moment from 'moment';
import { formatST, formatET } from '@/utils/utils';
import { getHeader } from '@/utils/common';
import CountRange from './CountRange';
import FlowRange from './FlowRange';
import FieldPricesFlowRange from './FieldPricesFlowRange';
import DateRange from './DateRange';

import 'ace-builds/src-noconflict/mode-java';
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/ext-language_tools';

const Option = Select.Option;

class AddModifyModal extends PureComponent {
    state = {
        loading: false,
        downLoadLoading: false,
        allFields: [],
        max: false,
        codeError: false
    };

    constructor(props) {
        super(props);
        const { dispatch, contractListStore } = props;
        const { modalType } = contractListStore;
        if (modalType === 1) {
            dispatch({
                type: 'contractList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            masterDimensions: ['dataSourceServiceName'] // 主属性
                        }
                    }
                }
            });
        }
    }

    componentDidMount() {
        this.getFieldAll();
    }

    getFieldAll = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (!res) return;
            if (res.success) {
                this.setState({
                    allFields: res.data || []
                });
            } else {
                message.error(res.message);
            }
        });
    };

    // 点击确定
    handleOk = () => {
        const { contractListStore, globalStore } = this.props;
        const { allMap } = globalStore;
        const { isIntegrationTG } = allMap;
        const { dialogData, modalType, updateId, searchParams } = contractListStore;
        const { providerUuid } = searchParams;
        const { addEditModalData } = dialogData;
        const {
            code,
            name,
            startTime,
            endTime,
            chargeType,
            attachFileList,
            price,
            masterDimensions,
            followDimensions,
            luaScript,
            countConfig,
            flowRange,
            dateRange,
            fieldPricesFlowRange
        } = addEditModalData;

        if (!code || !name || !startTime || !endTime || !chargeType) {
            return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
        }

        if (chargeType === 'count' || chargeType === 'packageYear' || chargeType === 'packageSeason' || chargeType === 'packageMonth') {
            if (price !== 0 && !price) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
        }

        if (name.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.changDuBuNengChao); // 长度不能超过200
        }
        if (code.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.changDuBuNengChao1); // 长度不能超过200
        }

        const findObj = allMap.chargeTypeList.find((item) => item.code === chargeType);

        let chargeConfig = {
            type: findObj ? findObj.type : null
        };
        if (findObj) {
            if ((findObj.type === 1 && chargeType === 'count') || findObj.type === 2) {
                chargeConfig.price = price;
            } else if (findObj.type === 1 && chargeType === 'countFieldPrices') {
                // 按次按维度
                if (countConfig.length === 1) {
                    let data = countConfig[0].matchFields;
                    if (countConfig[0].price !== 0 && !countConfig[0].price) {
                        return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang);
                    } // 存在必填项未填
                    if (data.length && (!data[0].fieldCode || !data[0].matchType)) {
                        return message.warning(I18N.inner.addmodifymodal.youWeiTianXieDe);
                    }
                    chargeConfig.fieldPrices = countConfig;
                } else {
                    let flag1 = false; // 费用必填
                    let flag2 = false; // 匹配字段控件
                    let flag3 = false; // 正则不能重复
                    countConfig.forEach((item) => {
                        if (item.price !== 0 && !item.price) flag1 = true;
                        const arr = countConfig
                            .map((v) => v.matchFields[0].matchConfig.regex === item.matchFields[0].matchConfig.regex)
                            ?.filter((v) => v);
                        if (arr?.length >= 2) flag3 = true;
                        if (!item.matchFields.length) {
                            flag2 = true;
                        } else {
                            item.matchFields.forEach((subItem) => {
                                if (!subItem.fieldCode || !subItem.matchType) {
                                    flag2 = true;
                                }
                            });
                        }
                    });
                    if (flag1) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
                    if (flag2) return message.warning(I18N.inner.addmodifymodal.youWeiTianXieDe);
                    if (flag3) return message.warning(I18N.inner.addmodifymodal.geKongJianZhongDe);
                    chargeConfig.fieldPrices = countConfig;
                }
            } else if (findObj.type === 3 && chargeType === 'hierarchyFlow') {
                let flag = false;
                flowRange.forEach((item) => {
                    if (!item.begin || !item.end || (!item.price && item.price !== 0)) flag = true;
                });
                if (flag) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
                chargeConfig.hierarchyDetail = flowRange;
            } else if (findObj.type === 3 && chargeType === 'hierarchyDimensionsFlow') {
                // 阶梯维度
                if (fieldPricesFlowRange.length === 1) {
                    let flag1 = false; // 费用标识
                    let flag2 = false; // 匹配字段控件
                    fieldPricesFlowRange[0].hierarchyDetail.forEach((item) => {
                        if (!item.begin || !item.end || (!item.price && item.price !== 0)) {
                            flag1 = true;
                        }
                    });
                    let data = fieldPricesFlowRange[0].matchFields;
                    if (data.length && (!data[0].fieldCode || !data[0].matchType)) {
                        flag2 = true;
                    }
                    if (flag1) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
                    if (flag2) return message.warning(I18N.inner.addmodifymodal.youWeiTianXieDe);
                    chargeConfig.fieldPrices = fieldPricesFlowRange;
                } else {
                    let flag1 = false; // 费用必填
                    let flag2 = false; // 匹配字段控件
                    let flag3 = false; // 正则不能重复
                    fieldPricesFlowRange.forEach((item) => {
                        const arr = fieldPricesFlowRange
                            .map((v) => v.matchFields[0].matchConfig.regex === item.matchFields[0].matchConfig.regex)
                            ?.filter((v) => v);
                        if (arr?.length >= 2) flag3 = true;
                        item.hierarchyDetail.forEach((subItem) => {
                            if (!subItem.begin || !subItem.end || (!subItem.price && subItem.price !== 0)) {
                                flag1 = true;
                            }
                        });
                        if (!item.matchFields.length) {
                            flag2 = true;
                        } else {
                            item.matchFields.forEach((subItem) => {
                                if (!subItem.fieldCode || !subItem.matchType) {
                                    flag2 = true;
                                }
                            });
                        }
                    });
                    if (flag1) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
                    if (flag2) return message.warning(I18N.inner.addmodifymodal.youWeiTianXieDe);
                    if (flag3) return message.warning(I18N.inner.addmodifymodal.geKongJianZhongDe);
                    chargeConfig.fieldPrices = fieldPricesFlowRange;
                }
            } else if (findObj.type === 4) {
                let flag = false;
                dateRange.forEach((item) => {
                    if (!item.begin || !item.end || (!item.price && item.price !== 0)) flag = true;
                });
                if (flag) return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
                chargeConfig.hierarchyDetail = dateRange;
            }
        }

        if (!masterDimensions?.length) {
            return message.warning(I18N.inner.addmodifymodal.cunZaiBiTianXiang); // 存在必填项未填
        }

        let reg = /^[a-z0-9]+$/i;
        if (!reg.test(code)) {
            return message.warning(I18N.inner.addmodifymodal.heTongBianHaoZhi);
        }

        let formData = new FormData();
        formData.append('providerUuid', providerUuid);
        formData.append('code', code);
        formData.append('name', name);
        formData.append('startTime', startTime);
        formData.append('endTime', endTime);
        formData.append('chargeType', chargeType ? chargeType : '');
        formData.append('chargeConfig', JSON.stringify(chargeConfig));
        formData.append('masterDimensions', masterDimensions);
        formData.append('followDimensions', followDimensions);
        !isIntegrationTG && formData.append('luaScript', luaScript || '');
        let oldContractFileNames = [];
        if (attachFileList.length > 0) {
            attachFileList.forEach((item) => {
                if (item.size) {
                    formData.append('contractFiles', item);
                } else {
                    oldContractFileNames.push(item.name);
                }
            });
        }

        if (modalType === 1) {
            this.addData(formData);
        } else if (modalType === 2) {
            formData.append('uuid', updateId);
            formData.append('oldContractFileNames', JSON.stringify(oldContractFileNames));
            this.updateData(formData);
        }
    };

    // 添加数据
    addData = (formData) => {
        this.setState({ loading: true });
        fetch('/bridgeApi/contract/add', {
            method: 'POST',
            headers: getHeader(),
            credentials: 'include',
            body: formData
        })
            .then((res) => {
                return res.json();
            })
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message);
                    const { dispatch } = this.props;
                    dispatch({
                        type: 'contractList/getList'
                    });
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 更新数据
    updateData = (formData) => {
        this.setState({ loading: true });
        fetch('/bridgeApi/contract/update', {
            method: 'POST',
            headers: getHeader(),
            credentials: 'include',
            body: formData
        })
            .then((res) => {
                return res.json();
            })
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message);
                    const { dispatch, contractListStore } = this.props;
                    const { searchParams } = contractListStore;
                    const { curPage, pageSize } = searchParams;
                    dispatch({
                        type: 'contractList/getList',
                        payload: {
                            curPage,
                            pageSize
                        }
                    });
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 点击取消
    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: false
                }
            }
        });
        setTimeout(() => {
            dispatch({
                type: 'contractList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            code: null, // 合同编号
                            version: null, // 合同版本
                            name: null, // 合同名称
                            startTime: null, // 合同开始日期
                            endTime: null, // 合同结束日期
                            chargeType: null, // 计费方式
                            docFileList: [], // 接口文档
                            attachFileList: [], // 合同附件
                            price: null, // 价格
                            masterDimensions: [], // 主属性
                            followDimensions: [], // 从属性
                            luaScript: '', // lua脚本
                            countConfig: [], // 按次计费的配置
                            flowRange: [], // 流量区间
                            fieldPricesFlowRange: [], // 阶梯字段匹配计费-流量
                            dateRange: [] // 日期区间
                        }
                    }
                }
            });
        }, 300);
        this.setState({
            loading: false,
            downLoadLoading: false
        });
    };

    // 改变参数
    changeField(e, field) {
        const { dispatch, contractListStore } = this.props;
        const { startTime, endTime } = contractListStore.dialogData.addEditModalData;
        let obj = {};
        let val = e;
        if (field === 'masterDimensions') {
            val = [e];
        }

        if (field === 'startTime' && val !== null) {
            val = formatST(val);
            if (endTime && moment(val).valueOf() > moment(endTime).valueOf()) {
                return message.warning(I18N.inner.addmodifymodal.kaiShiRiQiBu); // 开始日期不能大于结束日期
            }
        }
        if (field === 'endTime' && val !== null) {
            val = formatET(val);
            if (startTime && moment(val).valueOf() < moment(startTime).valueOf()) {
                return message.warning(I18N.inner.addmodifymodal.jieShuRiQiBu); // 结束日期不能小于开始日期
            }
        }

        obj[field] = val || val === 0 ? val : null;

        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...obj
                    }
                }
            }
        });
    }

    // 合同附件
    getAttachProps = (list) => {
        return {
            showUploadList: false,
            beforeUpload: (file) => {
                const { dispatch } = this.props;

                const obj = list.find((item) => item.name === file.name);
                const accept = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg', '.png', '.jpeg'];
                const acceptObj = accept.find((item) => file.name.includes(item));
                const size = file.size / 1024 / 1024;

                if (list.length >= 3) {
                    message.warning(I18N.inner.addmodifymodal.shangChuanWenJianShu); // 上传文件数最多3个
                    return false;
                } else if (!acceptObj) {
                    message.warning(I18N.inner.addmodifymodal.shangChuanGeShiJin); // 上传格式仅支持rar、zip、doc、docx、pdf、jpg、jpeg、png请检查文件格式
                    return false;
                } else if (size > 500) {
                    message.warning(I18N.inner.addmodifymodal.chaoChuWenJianDa); // 超出文件大小上限500M
                    return false;
                } else if (obj) {
                    message.warning(I18N.inner.addmodifymodal.gaiWenJianYiShang); // 该文件已上传
                    return false;
                }
                let attachFileList = list.concat(file);
                dispatch({
                    type: 'contractList/setAttrValue',
                    payload: {
                        dialogData: {
                            addEditModalData: {
                                attachFileList
                            }
                        }
                    }
                });

                return false;
            }
        };
    };

    // 删除附件
    closeContract(i) {
        const { dispatch, contractListStore } = this.props;
        let { attachFileList } = contractListStore.dialogData.addEditModalData;
        attachFileList.splice(i, 1);
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        attachFileList
                    }
                }
            }
        });
    }

    // 接口文档
    getDocProps = (list) => {
        return {
            showUploadList: false,
            beforeUpload: (file) => {
                const { dispatch } = this.props;

                const obj = list.find((item) => item.name === file.name);
                const accept = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg', '.png', '.jpeg'];
                const acceptObj = accept.find((item) => file.name.includes(item));
                const size = file.size / 1024 / 1024;

                if (list.length >= 3) {
                    message.warning(I18N.inner.addmodifymodal.shangChuanWenJianShu); // 上传文件数最多3个
                    return false;
                } else if (!acceptObj) {
                    message.warning(I18N.inner.addmodifymodal.shangChuanGeShiJin); // 上传格式仅支持rar、zip、doc、docx、pdf、jpg、jpeg、png请检查文件格式
                    return false;
                } else if (size > 500) {
                    message.warning(I18N.inner.addmodifymodal.chaoChuWenJianDa); // 超出文件大小上限500M
                    return false;
                } else if (obj) {
                    message.warning(I18N.inner.addmodifymodal.gaiWenJianYiShang); // 该文件已上传
                    return false;
                }
                let docFileList = list.concat(file);
                dispatch({
                    type: 'contractList/setAttrValue',
                    payload: {
                        dialogData: {
                            addEditModalData: {
                                docFileList
                            }
                        }
                    }
                });

                return false;
            }
        };
    };

    // 删除接口文档
    closeDoc(i) {
        const { dispatch, contractListStore } = this.props;
        let { docFileList } = contractListStore.dialogData.addEditModalData;
        docFileList.splice(i, 1);
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        docFileList
                    }
                }
            }
        });
    }

    // 下载文档
    downloadFile(type, fileName) {
        const { contractListStore } = this.props;
        const { updateId } = contractListStore;
        const params = {
            uuid: updateId,
            type,
            fileName
        };
        const index = fileName.lastIndexOf('.');
        const suffix = fileName.substr(index + 1);
        const trueFileName = fileName.substr(0, index);
        this.setState({ downLoadLoading: true });
        contractListAPI
            .downloadFj(params, trueFileName, suffix)
            .then(() => {
                this.setState({ downLoadLoading: false });
            })
            .catch(() => {
                this.setState({ downLoadLoading: false });
            });
    }

    // 全屏及还原
    screen(val) {
        this.setState({ max: val });
    }

    render() {
        const { loading, downLoadLoading, allFields, max } = this.state;
        const { contractListStore, globalStore } = this.props;
        const { allMap } = globalStore;
        const { dialogShow, modalType, dialogData } = contractListStore;
        const { addEditModalData } = dialogData;
        const {
            code,
            version,
            name,
            startTime,
            endTime,
            chargeType,
            price,
            masterDimensions,
            followDimensions,
            luaScript,
            docFileList,
            attachFileList,
            flowRange,
            dateRange
        } = addEditModalData;
        let Renderscreen = (
            <span className="u-screen">
                {!max ? (
                    <Icon type="fullscreen" onClick={() => this.screen(true)} />
                ) : (
                    <Icon type="fullscreen-exit" onClick={() => this.screen(false)} />
                )}
            </span>
        );
        let title = (
            <div>
                <span className="title">{I18N.inner.addmodifymodal.heTong}</span>
                {Renderscreen}
            </div>
        );
        let add = I18N.inner.addmodifymodal.xinZeng;
        let update = I18N.inner.addmodifymodal.xiuGai;
        let disabled = false;
        if (modalType === 1) {
            title = (
                <div>
                    <span className="title">{`${add}-${I18N.inner.addmodifymodal.heTong}`}</span>
                    {Renderscreen}
                </div>
            );
        } // 新增
        if (modalType === 2) {
            title = (
                <div>
                    <span className="title">{`${update}-${I18N.inner.addmodifymodal.heTong}`}</span>
                    {Renderscreen}
                </div>
            );
        }
        if (modalType === 3) disabled = true;

        const attachProps = this.getAttachProps(attachFileList);

        const footerDom = [
            <Button onClick={this.handleCancel} key="cancel">
                {/* 取消 */}
                {I18N.inner.addmodifymodal.quXiao}
            </Button>,
            <Button type="primary" onClick={this.handleOk} key="ok" loading={loading}>
                {/* 确定 */}
                {I18N.inner.addmodifymodal.queDing}
            </Button>
        ];

        return (
            <Modal
                title={title}
                width={1000}
                maskClosable={false}
                className={max ? 'm-contract-modal m-contract-modal-fullscreen' : 'm-contract-modal'}
                visible={dialogShow.addEditModal}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={disabled ? null : footerDom}>
                {/* 合同下载中... */}
                <Spin spinning={downLoadLoading} tip={I18N.inner.addmodifymodal.heTongXiaZaiZhong}>
                    <div className="modal-box">
                        <span className="u-label">
                            <b>*</b>
                            {/* 合同编号 */}
                            {I18N.inner.addmodifymodal.heTongBianHao}
                        </span>
                        <span className="u-input">
                            <Form.Item
                                validateStatus={this.state.codeError ? 'error' : null}
                                help={this.state.codeError ? I18N.inner.addmodifymodal.heTongBianHaoZhi : null}
                                className="code-input">
                                <Input
                                    placeholder={I18N.inner.addmodifymodal.qingShuRu} // 请输入
                                    disabled={disabled}
                                    className="u-input-code"
                                    value={code}
                                    onChange={(e) => {
                                        let reg = /^[a-z0-9]+$/i;
                                        if (!reg.test(e.target.value) && !!e.target.value) {
                                            this.setState({
                                                codeError: true
                                            });
                                            return;
                                        }
                                        this.setState({
                                            codeError: false
                                        });
                                        this.changeField(e.target.value, 'code');
                                    }}
                                    addonAfter={`V${version || 1}`}
                                />
                            </Form.Item>
                        </span>
                        <span className="u-label">
                            <b>*</b>
                            {/* 合同名称 */}
                            {I18N.inner.addmodifymodal.heTongMingCheng}
                        </span>
                        <span className="u-input">
                            <Input
                                placeholder={I18N.inner.addmodifymodal.qingShuRu} // 请输入
                                disabled={disabled}
                                value={name}
                                onChange={(e) => this.changeField(e.target.value, 'name')}
                            />
                        </span>
                    </div>
                    <div className="modal-box">
                        <span className="u-label">
                            <b>*</b>
                            {/* 合同开始日期 */}
                            {I18N.inner.addmodifymodal.kaiShiRiQi}
                        </span>
                        <span className="u-input">
                            <DatePicker
                                format="YYYY-MM-DD HH:mm:ss"
                                allowClear={false}
                                disabled={disabled}
                                style={{ width: '100%' }}
                                value={startTime ? moment(startTime) : null}
                                onChange={(date, dateString) => {
                                    this.changeField(dateString === '' ? null : dateString, 'startTime');
                                }}
                            />
                        </span>
                        <span className="u-label">
                            <b>*</b>
                            {/* 合同结束日期 */}
                            {I18N.inner.addmodifymodal.jieShuRiQi}
                        </span>
                        <span className="u-input">
                            <DatePicker
                                format="YYYY-MM-DD HH:mm:ss"
                                allowClear={false}
                                disabled={disabled}
                                style={{ width: '100%' }}
                                value={endTime ? moment(endTime) : null}
                                onChange={(date, dateString) => {
                                    this.changeField(dateString === '' ? null : dateString, 'endTime');
                                }}
                            />
                        </span>
                    </div>
                    <div className="modal-box">
                        <span className="u-label">
                            <b>*</b>
                            {/* 计费方式 */}
                            {I18N.inner.addmodifymodal.jiFeiFangShi}
                        </span>
                        <span className="u-select">
                            <Select
                                allowClear
                                showSearch
                                style={{ width: '100%' }}
                                disabled={disabled}
                                placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 请选择
                                optionFilterProp="children"
                                value={chargeType ? chargeType : undefined}
                                onChange={(e) => this.changeField(e, 'chargeType')}>
                                {allMap &&
                                    allMap.chargeTypeList &&
                                    allMap.chargeTypeList.map((item, index) => {
                                        return (
                                            <Option value={item.code} key={index}>
                                                <Ellipsis title={item.name} />
                                            </Option>
                                        );
                                    })}
                            </Select>
                        </span>
                    </div>
                    {chargeType === 'countFieldPrices' ? <CountRange disabled={disabled} fields={allFields} /> : null}
                    {(chargeType === 'count' ||
                        chargeType === 'packageYear' ||
                        chargeType === 'packageSeason' ||
                        chargeType === 'packageMonth') && (
                        <InputNumber
                            precision={2}
                            style={{ width: 120, margin: '-5px 0 20px 160px' }}
                            min={0}
                            placeholder={I18N.inner.addmodifymodal.shuRuFeiYongYuan} // 输入费用/元
                            disabled={disabled}
                            value={price}
                            onChange={(e) => this.changeField(e, 'price')}
                        />
                    )}
                    {chargeType === 'hierarchyFlow' && <FlowRange flowRange={flowRange} disabled={disabled} />}
                    {chargeType === 'hierarchyDimensionsFlow' ? <FieldPricesFlowRange disabled={disabled} fields={allFields} /> : null}
                    {chargeType === 'hierarchyDate' && <DateRange dateRange={dateRange} disabled={disabled} />}
                    <div className="modal-box">
                        <span className="u-label fl" style={{ marginTop: '4px' }}>
                            {I18N.inner.addmodifymodal.tongJiFangShi}
                        </span>
                        <span className="statistics-method">
                            <div className="attr-select">
                                <span className="required">{I18N.inner.addmodifymodal.zhuShuXing}</span>
                                <Select
                                    placeholder={I18N.inner.addmodifymodal.qingXuanZe}
                                    style={{ width: 268 }}
                                    disabled={disabled}
                                    value={masterDimensions?.[0] || []}
                                    onChange={(e) => this.changeField(e, 'masterDimensions')}>
                                    <Option key="dataSourceServiceName" value="dataSourceServiceName">
                                        {I18N.inner.addmodifymodal.shuJuYuan}
                                    </Option>
                                    <Option key="contractId" value="contractId">
                                        {I18N.inner.addmodifymodal.heTongID}
                                    </Option>
                                </Select>
                            </div>
                            <div className="attr-select">
                                <span>{I18N.inner.addmodifymodal.congShuXing}</span>
                                <Select
                                    showSearch
                                    placeholder={I18N.inner.addmodifymodal.qingXuanZe}
                                    style={{ width: 268 }}
                                    disabled={disabled}
                                    value={followDimensions || []}
                                    optionFilterProp="children"
                                    onChange={(e) => this.changeField(e, 'followDimensions')}
                                    mode="multiple">
                                    {allFields.map((v) => (
                                        <Option key={v.name} value={v.name}>
                                            {v.displayName}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        </span>
                    </div>
                    {chargeType !== 'noCharge' && (
                        <div className="modal-box">
                            <span className="u-label fl" style={{ marginTop: '4px' }}>
                                {I18N.inner.addmodifymodal.lUAJiaoBen}
                            </span>
                            <span>
                                <AceEditor
                                    mode="java"
                                    theme="monokai"
                                    value={luaScript || ''}
                                    onChange={(val) => this.changeField(val, 'luaScript')}
                                    editorProps={{ $blockScrolling: true }}
                                    className="ace-editor"
                                    minLines={max ? 24 : 18}
                                    maxLines={max ? 24 : 18}
                                    highlightActiveLine
                                    showPrintMargin={false}
                                    showGutter
                                    style={{
                                        width: 720
                                    }}
                                />
                            </span>
                        </div>
                    )}
                    <div className="modal-box clearfix">
                        <span className="u-label fl" style={{ marginTop: '4px' }}>
                            {/* 合同附件 */}
                            {I18N.inner.addmodifymodal.heTongFuJian}
                        </span>
                        <span className="u-input fl">
                            <Upload {...attachProps} disabled={disabled}>
                                <Button>
                                    <Icon type="upload" />
                                    {/* 上传文件 */}
                                    {I18N.inner.addmodifymodal.shangChuanWenJian}
                                </Button>
                            </Upload>
                            <span className="upload-tip">
                                {/* 支持扩展名 */}
                                {I18N.inner.addmodifymodal.zhiChiKuoZhanMing}
                                ：.rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg
                            </span>
                            {attachFileList &&
                                attachFileList.map((item, index) => {
                                    return (
                                        <Tooltip title={item.name}>
                                            <p className="upload-p" key={index}>
                                                <Icon type="link" />
                                                <span className="text-ellipsis" title={item.name}>
                                                    {item.name}
                                                </span>
                                                {!disabled && <Icon type="close" onClick={() => this.closeContract(index)} />}
                                                {!item.size && <Icon type="download" onClick={() => this.downloadFile(1, item.name)} />}
                                            </p>
                                        </Tooltip>
                                    );
                                })}
                        </span>
                    </div>
                </Spin>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    contractListStore: state.contractList
}))(AddModifyModal);
