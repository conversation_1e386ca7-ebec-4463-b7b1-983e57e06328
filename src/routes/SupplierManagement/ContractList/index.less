.g-contract-list {
    .bread-container {
        line-height: 40px;
        .u-link {
            cursor: pointer;
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
            &:hover {
                color: #03a9f4;
            }
        }
    }
    .custom-container-v3 {
        background-color: #fff;
        border-radius: @border-radius-base*2;
        padding: @border-radius-base*2;
    }
}

.m-contract-modal {
    .u-screen {
        position: absolute;
        right: 57px;
        // top: 7px;
        line-height: 25px;
        font-size: 18px;
        cursor: pointer;
    }
    .ant-modal-body {
        max-height: 560px;
        overflow: auto;
        &::-webkit-scrollbar {
            -webkit-appearance: none;
            width: 7px;
        }
        &::-webkit-scrollbar:vertical {
            /* 设置垂直滚动条宽度 */
            width: 8px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            border: 2px solid #777;
            background-color: #777;
        }
        .ace_editor {
            width: 720px !important;
            height: 300px !important;
        }
    }
    .price-config-box {
        .price-config-item {
            position: relative;
            width: 720px;
            padding: 16px;
            background-color: #f8f8f8;
            margin: 0 80px 16px 160px;
            border-radius: 2px;
            .price-item {
                margin-bottom: 8px;
                display: flex;
                .u-unit {
                    display: inline-block;
                    line-height: 28px;
                    margin-left: 4px;
                }
            }
            .u-label {
                width: 56px;
                display: inline-block;
                text-align: right;
                margin-right: 8px;
                line-height: 28px;
            }
            .required {
                position: relative;
                display: inline-block;
                &::before {
                    content: "*";
                    position: absolute;
                    left: -10px;
                    top: 2px;
                    font-weight: bold;
                    color: #f00;
                }
            }
            .field-item {
                display: flex;
                align-items: center;
            }
        }
        .operate-btns {
            position: absolute;
            display: flex;
            width: 44px;
            top: 8px;
            right: -54px;
            font-size: 20px;
            .anticon {
                color: #9e9e9e;
                margin-right: 4px;
                cursor: pointer;
                &:hover {
                    color: #03a9f4;
                }
            }
        }
    }
    .modal-box {
        .u-label {
            width: 160px;
        }
        .statistics-method {
            width: 720px;
            background-color: #f8f8f8;
            border-radius: 2px;
            box-sizing: border-box;
            padding: 16px;
            .attr-select {
                margin-bottom: 15px;
                &:last-of-type {
                    margin-bottom: 0;
                }
                & > span {
                    width: 56px;
                    text-align: right;
                    margin-right: 8px;
                    line-height: 28px;
                }
                .required {
                    position: relative;
                    &::before {
                        content: "*";
                        position: absolute;
                        right: 45px;
                        top: 2px;
                        font-weight: bold;
                        color: #f00;
                    }
                }
            }
        }
        .u-input,
        .u-select {
            width: 280px;
            position: relative;
            .u-input-code {
                margin-top: -4px;
                .ant-input-group {
                    display: flex;
                    .ant-input-group-addon {
                        width: 50px;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                    }
                }
            }
            .code-input {
                display: inline-block;
                position: relative;
                top: -4px;
                .ant-form-item-control {
                    line-height: 28px;
                    .u-input-code {
                        margin-top: 0px;
                    }
                }
                .ant-form-explain {
                    margin-top: 2px;
                }
            }
        }
        .upload-tip {
            position: absolute;
            left: 0;
            top: 40px;
            color: #c7c7c7;
            font-size: 12px;
            width: 400px;
        }
        .ant-upload-select {
            margin-bottom: 30px;
        }
        .ant-upload-list {
            width: 280px;
        }
        .upload-p {
            margin: 0px;
            padding: 5px 4px;
            line-height: 20px;
            .anticon-link {
                margin-right: 5px;
            }
            .anticon-close,
            .anticon-download {
                float: right;
                cursor: pointer;
                display: none;
                margin-top: 3px;
            }
            .anticon-download {
                margin-right: 2px;
            }
            span {
                width: 220px;
                vertical-align: middle;
                margin-top: -2px;
            }
            &:hover {
                background: #d3effb;
                .anticon-close,
                .anticon-download {
                    display: inline-block;
                }
            }
        }
    }
    .wrap {
        margin-bottom: 30px;
        .box {
            padding-left: 148px;
            margin-bottom: 15px;
            &:last-of-type {
                margin-bottom: 0;
            }
            .s1 {
                color: #f00;
                margin-right: 5px;
            }
            .mr5 {
                margin-right: 5px;
            }
            .ml5 {
                margin-left: 5px;
            }
            .anticon-question-circle,
            .anticon-plus-circle,
            .anticon-delete {
                font-size: 20px;
                vertical-align: middle;
                margin-top: -2px;
                margin-left: 8px;
                color: #9e9e9e;
                cursor: pointer;
                &:hover {
                    color: #03a9f4;
                }
            }
            .ant-input-number {
                width: 110px;
            }
        }
    }
    .field-prices-flow-wrap {
        margin-bottom: 0;
        .box {
            padding-left: 0;
        }
    }
    .anticon-question-circle {
        font-size: 20px;
        vertical-align: middle;
        margin-top: -2px;
        margin-left: 8px;
        color: #9e9e9e;
        cursor: pointer;
        &:hover {
            color: #03a9f4;
        }
    }
}

.m-contract-modal-fullscreen {
    width: 100% !important;
    top: 0 !important;
    padding-bottom: 0 !important;
    .ant-modal-body {
        height: ~"calc(100vh - 96px)";
        max-height: ~"calc(100vh - 96px)";
        .ace_editor {
            width: 1200px !important;
            height: 400px !important;
        }
    }
}
