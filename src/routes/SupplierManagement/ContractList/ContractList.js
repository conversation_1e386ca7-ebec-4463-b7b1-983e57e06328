/*
 * @Author: liubo
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 合同管理
 */

import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment, Suspense } from 'react';
import { connect } from 'dva';
import { Button, Table, Pagination, Popconfirm, Input, message, Select, Breadcrumb, Tooltip, Icon, HandleIcon } from 'tntd';
import { contractListAPI } from '@/services';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { getUrlKey } from '@/utils/utils';

const InputGroup = Input.Group;
const Option = Select.Option;

const AddModifyModal = React.lazy(() => import('./Inner/AddModifyModal'));

class ContractList extends PureComponent {
    constructor(props) {
        super(props);
        const { dispatch } = props;
        const searchKey = getUrlKey('searchKey');
        const searchValue = getUrlKey('searchValue');
        let str = 'name';
        if (searchKey === 'code') {
            str = 'code';
        }
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                searchParams: {
                    providerUuid: getUrlKey('uuid') || null,
                    [str]: searchValue || null
                }
            }
        });
    }

    state = {
        quickSearchParams: getUrlKey('searchKey') || 'name',
        breadcrumbName: getUrlKey('name') || null
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0201', 'query')) {
                    this.search();
                }
            }
        }, 100);
    }

    componentWillUnmount() {
        const { dispatch } = this.props;
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                searchParams: {
                    curPage: 1,
                    pageSize: 10,
                    name: null, // 合同名称
                    code: null, // 编号
                    status: null, // 状态
                    providerUuid: null
                }
            }
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, contractListStore } = this.props;
        const { searchParams } = contractListStore;
        dispatch({
            type: 'contractList/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 获取单条合同信息
    getInfo = (uuid, modalType) => {
        contractListAPI.getInfo({ uuid }).then((res) => {
            if (res.success) {
                if (!res.data) return;
                const {
                    code,
                    version,
                    name,
                    startTime,
                    endTime,
                    chargeType,
                    contractFileNames,
                    interfaceFileNames,
                    chargeConfig,
                    masterDimensions,
                    followDimensions,
                    luaScript
                } = res.data;
                const { dispatch } = this.props;

                let attachFileList = [];
                let contractList = contractFileNames ? JSON.parse(contractFileNames) : [];
                contractList.forEach((item) => {
                    attachFileList.push({ name: item });
                });

                let docFileList = [];
                let interfaceList = interfaceFileNames ? JSON.parse(interfaceFileNames) : [];
                interfaceList.forEach((item) => {
                    docFileList.push({ name: item });
                });

                let price = null;
                let obj = {
                    fieldCode: null,
                    matchType: null,
                    matchConfig: {
                        regex: null
                    }
                };
                let countConfig = [{ price: null, matchFields: [obj] }];
                let flowRange = [{ begin: null, end: -99, price: null }];
                let fieldPricesFlowRange = [
                    {
                        hierarchyDetail: [{ begin: null, end: -99, price: null }],
                        matchFields: [obj]
                    }
                ];
                let dateRange = [{ begin: startTime, end: endTime, price: null }];
                let config = chargeConfig ? JSON.parse(chargeConfig) : null;
                if (config) {
                    if (chargeType === 'countFieldPrices') {
                        countConfig = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDimensionsFlow') {
                        fieldPricesFlowRange = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDate') {
                        // 阶梯计费-日期区间
                        dateRange = config.hierarchyDetail;
                    } else if (chargeType === 'hierarchyFlow') {
                        // 阶梯计费-流量
                        flowRange = config.hierarchyDetail;
                    } else {
                        price = config.price;
                    }
                }

                dispatch({
                    type: 'contractList/setAttrValue',
                    payload: {
                        dialogShow: {
                            addEditModal: true
                        },
                        modalType,
                        updateId: uuid,
                        dialogData: {
                            addEditModalData: {
                                code, // 合同编号
                                version, // 合同版本
                                name, // 合同名称
                                startTime, // 合同开始日期
                                endTime, // 合同结束日期
                                chargeType, // 计费方式
                                docFileList, // 接口文档
                                attachFileList, // 合同附件
                                price, // 价格
                                masterDimensions, // 主属性
                                followDimensions, // 从属性
                                luaScript, // lua脚本
                                countConfig, // 按次字段匹配计费
                                flowRange, // 流量区间
                                fieldPricesFlowRange, // 阶梯字段匹配计费-流量
                                dateRange // 日期区间
                            }
                        }
                    }
                });
            } else {
                message.error(res.message);
            }
        });
    };

    // 新增
    add = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 1
            }
        });
    };

    // 修改
    modify(record) {
        this.getInfo(record.uuid, 2);
    }

    // 查看
    look(record) {
        this.getInfo(record.uuid, 3);
    }

    // 删除
    delete(record) {
        contractListAPI.deleteData({ uuid: record.uuid }).then((res) => {
            if (res && res.success) {
                message.success(res.message);
                const { dispatch, contractListStore } = this.props;
                const { searchParams, tableList } = contractListStore;
                let { curPage, pageSize } = searchParams;
                if (tableList.length === 1 && curPage > 1) curPage = curPage - 1;
                dispatch({
                    type: 'contractList/getList',
                    payload: {
                        curPage,
                        pageSize
                    }
                });
            } else {
                message.error(res.message);
            }
        });
    }

    // 处理快速搜索参数
    handleQuickSearchParamVal(val) {
        const { dispatch } = this.props;
        this.setState({
            quickSearchParams: val
        });
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                searchParams: {
                    name: null,
                    code: null
                }
            }
        });
    }

    // 处理快速搜索值
    handleQuickSearchVal(e) {
        const searchLabel = this.state.quickSearchParams;
        const { contractListStore, dispatch } = this.props;
        let { searchParams } = contractListStore;
        searchParams[searchLabel] = e.target.value;
        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                searchParams
            }
        });
    }

    // 改变参数
    changeField(e, field) {
        const { dispatch } = this.props;
        let obj = {};
        obj[field] = e || e === 0 ? e : null;

        dispatch({
            type: 'contractList/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });
    }

    goBack() {
        const { history } = this.props;
        let path = '/handle/supplierManagement/supplierList';
        history.push(path);
    }

    render() {
        const { breadcrumbName, quickSearchParams } = this.state;
        const { contractListStore, globalStore } = this.props;
        const { menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading, dialogShow } = contractListStore;

        const columns = [
            {
                title: I18N.contractlist.heTongBianHao, // 合同编号
                dataIndex: 'code',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 15) {
                        dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.contractlist.heTongBanBen,
                dataIndex: 'version',
                render: (text) => `V${text || 1}`
            },
            {
                title: I18N.contractlist.heTongMingCheng, // 合同名称
                dataIndex: 'name',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.contractlist.zhuangTai, // 状态
                dataIndex: 'status',
                render: (text) => {
                    const map = {
                        0: I18N.contractlist.shiXiao, // 失效
                        1: I18N.contractlist.zhengChang, // 正常
                        2: I18N.contractlist.daiShengXiao // 待生效
                    };
                    return map[text] ? map[text] : '--';
                }
            },
            {
                title: I18N.contractlist.kaiShiShiJian, // 开始时间
                dataIndex: 'startTime'
            },
            {
                title: I18N.contractlist.jieShuShiJian, // 结束时间
                dataIndex: 'endTime'
            },
            {
                title: I18N.contractlist.chuangJianShiJian, // 创建时间
                dataIndex: 'gmtCreate'
            },
            {
                title: I18N.contractlist.chuangJianRen, // 创建人
                dataIndex: 'creator'
            },
            {
                title: I18N.contractlist.xiuGaiShiJian, // 修改时间
                dataIndex: 'gmtModify'
            },
            {
                title: I18N.contractlist.xiuGaiRen, // 修改人
                dataIndex: 'operator'
            },
            {
                title: I18N.contractlist.caoZuo, // 操作
                dataIndex: 'operate',
                width: 130,
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <HandleIcon>
                            {checkFunctionHasPermission('TZ0201', 'updateContract') && (
                                <HandleIcon.Item title={I18N.contractlist.xiuGai}>
                                    <Icon
                                        type="form"
                                        onClick={() => {
                                            this.modify(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}
                            {checkFunctionHasPermission('TZ0201', 'queryContractDetail') && (
                                <HandleIcon.Item title={I18N.contractlist.chaKan}>
                                    <Icon
                                        type="profile"
                                        onClick={() => {
                                            this.look(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}
                            {checkFunctionHasPermission('TZ0201', 'deleteContract') && (
                                <HandleIcon.Item title={I18N.contractlist.shanChu}>
                                    <Popconfirm
                                        title={I18N.contractlist.queDingShanChuCi} // 确定删除此数据?
                                        placement="topRight"
                                        onConfirm={() => this.delete(record)}>
                                        <Icon type="delete" />
                                    </Popconfirm>
                                </HandleIcon.Item>
                            )}
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];

        return (
            <div className="g-contract-list">
                <div className={process.env.version === 'v2' ? 'page-global-header' : undefined}>
                    <div className="left-info">
                        <Breadcrumb separator=">" className="bread-container">
                            <Breadcrumb.Item className="u-link" onClick={() => this.goBack()}>
                                {/* {breadcrumbName} */}
                                <Icon type="left" />
                                {I18N.contractlist.fanHui}
                            </Breadcrumb.Item>
                            {/* <Breadcrumb.Item> */}
                            {/* 合同管理 */}
                            {/* {I18N.contractlist.heTongGuanLi}
                            </Breadcrumb.Item> */}
                        </Breadcrumb>
                    </div>
                </div>
                {menuTreeReady && checkFunctionHasPermission('TZ0201', 'query') && (
                    <div className={`page-global-body ${'custom-container-' + process.env.version}`}>
                        <div className="page-global-search">
                            <div className="item">
                                <InputGroup compact>
                                    <Select value={quickSearchParams} onChange={(val) => this.handleQuickSearchParamVal(val)}>
                                        <Option value="name">
                                            {/* 合同名称 */}
                                            {I18N.contractlist.heTongMingCheng}
                                        </Option>
                                        <Option value="code">
                                            {/* 合同编号 */}
                                            {I18N.contractlist.heTongBianHao}
                                        </Option>
                                    </Select>
                                    <Input
                                        style={{ width: 180 }}
                                        placeholder={I18N.contractlist.qingShuRuSouSuo} // 请输入搜索内容
                                        onChange={(e) => this.handleQuickSearchVal(e)}
                                        value={searchParams.name || searchParams.code || undefined}
                                    />
                                </InputGroup>
                            </div>
                            <div className="item">
                                <Select
                                    allowClear
                                    value={searchParams.status || searchParams.status === 0 ? searchParams.status : undefined}
                                    placeholder={I18N.contractlist.xuanZeZhuangTai} // 选择状态
                                    style={{ width: 140 }}
                                    onChange={(e) => this.changeField(e, 'status')}>
                                    <Option value={0}>{I18N.contractlist.shiXiao /** 失效 */}</Option>
                                    <Option value={1}>{I18N.contractlist.zhengChang /** 正常 */}</Option>
                                    <Option value={2}>{I18N.contractlist.daiShengXiao /** 待生效 */}</Option>
                                </Select>
                            </div>
                            <div className="item">
                                <Button type="primary" loading={loading} onClick={() => this.search()}>
                                    {/* 查询 */}
                                    {I18N.contractlist.chaXun}
                                </Button>
                            </div>
                            {checkFunctionHasPermission('TZ0201', 'addContract') && (
                                <div className="item right">
                                    <Button type="primary" icon="plus" onClick={this.add}>
                                        {/* 新增 */}
                                        {I18N.contractlist.xinZeng}
                                    </Button>
                                </div>
                            )}
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    size="middle"
                                    rowKey={(record) => record.uuid}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 1900 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.contractlist.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0201', 'query') && <NoPermission />}
                {dialogShow.addEditModal && (
                    <Suspense fallback={null}>
                        <AddModifyModal />
                    </Suspense>
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    contractListStore: state.contractList
}))(ContractList);
