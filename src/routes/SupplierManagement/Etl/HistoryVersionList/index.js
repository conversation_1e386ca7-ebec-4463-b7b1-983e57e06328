import I18N from '@/utils/I18N';
import React, { useState, Suspense } from 'react';
import { message, QueryListScene, Tag, Button, Modal, HandleIcon, Ellipsis, TableContainer, Icon } from 'tntd';

import { formatStandardTime, getUrlKey } from '@/utils/utils';
import { EtlTypeMap, statusMap } from '../constans';

import Status from '@/components/TablePage/components/Status';
import etlAPI from '@/routes/SupplierManagement/Etl/services';
import { connect } from 'dva';
const { confirm } = Modal;

const { createActions, QueryList, QueryForm } = QueryListScene;
const actions = createActions();

const AddModifyModal = React.lazy(() => import('../Inner/AddModifyModal'));

const EtlVersionHistory = (props) => {
    const { history, match } = props;
    const { params } = match || {};

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState(1);
    const [record, setRecord] = useState({});

    const modalClose = () => {
        setModalVisible(false);
        setRecord({});
        setModalType(1);
    };

    const query = (params = {}) => {
        const { current: curPage = 1, pageSize = 10, ...rest } = params;

        return etlAPI
            .getVersionList({
                ...rest,
                uuid: getUrlKey('uuid'),
                curPage,
                pageSize
            })
            .then((res) => ({
                data: res?.data?.contents || [],
                current: res?.data?.curPage,
                total: res?.data?.total,
                pageSize: res?.data?.pageSize
            }));
    };

    const onCover = (record) => {
        confirm({
            title: I18N.historyversionlist.index.fuGaiBianJiQu2,
            content: I18N.historyversionlist.index.ninQueDingYaoJiang,
            cancelText: I18N.historyversionlist.index.quXiao,
            okText: I18N.historyversionlist.index.queDing,
            onOk() {
                const verUuid = record?.uuid;
                etlAPI.ETLCover({ versionUuid: verUuid }).then((res) => {
                    if (res.success) {
                        message.success(I18N.historyversionlist.index.caoZuoChengGong);
                        actions.search();
                    }
                });
            }
        });
    };

    const columns = [
        {
            title: I18N.historyversionlist.index.eTLChuLi,
            dataIndex: 'displayName',
            width: 300,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.historyversionlist.index.banBenZhuangTai,
            width: 200,
            dataIndex: 'versionStatus',
            render: (text, record) => {
                return (
                    text && (
                        <div>
                            <Status text={statusMap[text].value} color={statusMap[text].color} />
                        </div>
                    )
                );
            }
        },
        {
            title: I18N.historyversionlist.index.banBenHao,
            dataIndex: 'version',
            width: 140,
            render: (text, record) => {
                return <Tag color="tnt-purple">V{text}</Tag>;
            }
        },
        {
            title: I18N.historyversionlist.index.biaoZhi,
            dataIndex: 'name',
            width: 140
        },
        {
            title: I18N.historyversionlist.index.leiXing,
            width: 140,
            dataIndex: 'type',
            render: (text) => {
                return (
                    <Tag color={text === 1 ? 'geekblue' : 'cyan'}>
                        <Ellipsis title={EtlTypeMap[text]} widthLimit={120} />
                    </Tag>
                );
            }
        },
        {
            title: I18N.historyversionlist.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.historyversionlist.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record.gmtModify || record.gmtCreate);
            }
        },
        {
            title: I18N.historyversionlist.index.caoZuo,
            dataIndex: 'operate',
            width: 120,
            operate: true,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <HandleIcon>
                        <HandleIcon.Item title={I18N.historyversionlist.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    setModalVisible(true);
                                    setModalType(3);
                                    setRecord(record);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.historyversionlist.index.fuGaiBianJiQu}>
                            <Icon
                                type="toggle"
                                onClick={() => {
                                    onCover(record);
                                }}
                            />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
            }
        }
    ];

    const extraActions = (
        <Button
            disabled={selectedRowKeys.length <= 1}
            type="primary"
            icon="compare"
            style={{ float: 'right', zIndex: 10, marginBottom: '13px' }}
            onClick={() => {
                history.push(
                    `/handle/supplierManagement/etl/version/diff?uuid1=${selectedRowKeys[0]}&uuid2=${selectedRowKeys[1]}&uuid=${getUrlKey(
                        'uuid'
                    )}`
                );
            }}>
            {I18N.historyversionlist.index.duiBi}
        </Button>
    );

    return (
        <>
            <QueryListScene query={query} actions={actions}>
                <QueryForm extraActions={extraActions} renderActions={() => null} />
                <QueryList
                    columns={columns}
                    rowSelection={{
                        selectedRowKeys,
                        columnTitle: ' ',
                        hideDefaultSelections: true,
                        onChange: (selectedRowKeys) => {
                            if (selectedRowKeys.length > 2) {
                                return message.warning(I18N.historyversionlist.index.zuiDuoTongShiXuan);
                            }
                            setSelectedRowKeys(selectedRowKeys);
                        }
                    }}
                    rowKey="uuid"
                    scroll={{ x: 1300 }}
                />
            </QueryListScene>
            <Suspense fallback={null}>
                <AddModifyModal
                    visible={modalVisible}
                    onClose={modalClose}
                    record={record}
                    modalType={modalType}
                    globalStore={props.globalStore}
                />
            </Suspense>
        </>
    );
};

export default connect((state) => ({
    globalStore: state.globalStore
}))(TableContainer(EtlVersionHistory));
