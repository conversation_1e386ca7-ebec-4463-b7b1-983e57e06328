import I18N from '@/utils/I18N';
import React, { Suspense, useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { connect } from 'dva';
import { Button, Popconfirm, message, Modal, QueryListScene, HandleIcon, Ellipsis, Tooltip, Icon, TntdAction } from 'tntd';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
import etlAPI from '@/routes/SupplierManagement/Etl/services';
import { getHeader } from '@/utils/common';
import { EtlTypeMap } from '../constans';
import ETLNameDisplay from '../Inner/ETLNameDisplay';
import Status from '../Inner/Status';
import { getUrlKey } from '@tntd/utils';
import FiledShowModal from '../Inner/FiledShow';

const { confirm } = Modal;

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const AddModifyModal = React.lazy(() => import('../Inner/AddModifyModal'));
const ImportModal = React.lazy(() => import('../Inner/ImportModal'));

const itemStatusMap = {
    '1': {
        value: I18N.edit.index.yiShangXian,
        color: '#07C790'
    },
    '2': {
        value: I18N.edit.index.daiTiJiao,
        color: '#126BFB'
    }
};

export default connect((state) => ({
    globalStore: state.global
}))((props) => {
    const { isSelf } = props;
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState(1);

    const [importVisible, setImportVisible] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [showModalInfo, setShowModalInfo] = useState({});
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [record, setRecord] = useState({});

    const [referenceDrawerData, setReferenceDrawerData] = useState(null);

    const modalRef = useRef({});

    let { curPage, pageSize } = actions.getFormData();

    const query = (params = {}) => {
        const { current: curPage = 1, pageSize = 10, ...rest } = params;

        return etlAPI
            .getList({
                ...rest,
                curPage,
                pageSize
            })
            .then((res) => {
                if (!res && !res.success) return message.error(res.message);
                return {
                    data: res.data.contents || [],
                    current: res.data.curPage,
                    total: res.data.total,
                    pageSize: res.data.pageSize
                };
            });
    };

    let currentTab = getUrlKey('currentTab') || '2';

    useEffect(() => {
        if (currentTab === '2') {
            actions.search({ curPage, pageSize });
            modalRef.current?.getCommonCode && modalRef.current?.getCommonCode();
        }
    }, [currentTab]);

    // 批量
    const batchOnline = (uuids) => {
        const params = {
            uuids: uuids.join(',')
        };
        etlAPI
            .batchOnline(params)
            .then((res) => {
                if (res && res.success) {
                    message.success(I18N.edit.index.caoZuoChengGong);
                    actions.search({ curPage, pageSize });
                    modalRef.current?.getCommonCode();
                } else {
                    message.error(res.message);
                }
            })
            .finally(() => {
                setSelectedRowKeys([]);
            });
    };

    // 上线
    const online = ({ uuid, name }) => {
        Modal.confirm({
            title: I18N.edit.index.shangXian,
            content: I18N.edit.index.queDingYaoShangXian,
            onOk: () => {
                ReferenceOnlineCheck({
                    rq: () =>
                        referenceAPI.onlineValidate({
                            componentType: 'DATASOURCE_ETL_HANDLER',
                            componentIds: name
                        })
                }).then(() => {
                    batchOnline([uuid]);
                });
            }
        });
    };

    // 查看
    const look = (record) => {
        setRecord({
            displayName: record.displayName, // ETL处理器名称
            name: record.name, // 标识
            script: record.script, // 脚本
            defaultScript: record.script, // 脚本
            type: `${record.type}`,
            comment: record.comment,
            commonUuids: record.commonUuids,
            tag: record.tag
        });
        setModalType(3);
        setModalVisible(true);
    };

    // 修改
    const modify = (record) => {
        setRecord({
            uuid: record.uuid,
            displayName: record.displayName, // ETL处理器名称
            name: record.name, // 标识
            script: record.script, // 脚本
            defaultScript: record.script, // 脚本
            type: `${record.type}`,
            comment: record.comment,
            commonUuids: record.commonUuids,
            tag: record.tag
        });
        setModalType(2);
        setModalVisible(true);
    };

    // 删除
    const deleteItem = (record) => {
        etlAPI.deleteData({ uuid: record.uuid }).then((res) => {
            if (res && res.success) {
                message.success(res.message);
                actions.search({ curPage, pageSize });
            } else {
                message.error(res.message);
            }
        });
    };

    // 导入弹框确定
    const okImport = (file, type) => {
        setConfirmLoading(true);
        let formData = new FormData();
        formData.append('type', type);
        formData.append('file', file);

        fetch('/bridgeApi/etlHandler/import', {
            method: 'POST',
            body: formData,
            headers: getHeader()
        })
            .then((res) => {
                return res.json();
            })
            .then((res) => {
                setConfirmLoading(false);
                if (!res) return;
                if (res.success) {
                    setShowModalInfo({
                        content: I18N.edit.index.daoRuChengGong2,
                        msg: I18N.edit.index.shiBieDaoYiXia,
                        dataSource: res.data || [],
                        success: true,
                        showVisible: true
                    });
                    message.success(I18N.edit.index.daoRuChengGong);
                    closeImportModal();
                } else {
                    setShowModalInfo({
                        content: I18N.edit.index.daoRuShiBai,
                        msg: res?.message,
                        dataSource: [],
                        showVisible: true,
                        success: false
                    });
                }
            })
            .catch(() => {
                message.error(I18N.edit.index.daoRuShiBaiQing);
                this.setState({ confirmLoading: false });
            });
    };

    const closeImportModal = () => {
        setImportVisible(false);
        setConfirmLoading(false);
        actions.search({ curPage, pageSize });
    };

    const columns = [
        {
            title: I18N.edit.index.eTLChuLi3,
            dataIndex: 'displayName',
            key: 'displayName',
            width: 300,
            render: (text, record) => {
                let dom = (
                    <>
                        <ETLNameDisplay name={text} status={record.versionStatus} version={record.version} nameWidthLimit={280} />
                    </>
                );
                return dom ? dom : '--';
            }
        },
        {
            title: I18N.edit.index.eTLChuLi2,
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text) => {
                return <Ellipsis title={text || '- - '} widthLimit={180} />;
            }
        },
        {
            title: I18N.edit.index.leiXing,
            dataIndex: 'type',
            key: 'type',
            width: 130,
            render: (text) => {
                return <Ellipsis title={EtlTypeMap[text] || '- - '} />;
            }
        },
        {
            title: I18N.edit.index.zhuangTai,
            dataIndex: 'status',
            key: 'status',
            width: 150,
            render: (text) => {
                let { value, color } = itemStatusMap[text] || {};

                return (
                    <div>
                        <Status type="dot" text={value} color={color} />
                    </div>
                );
            }
        },
        {
            title: I18N.edit.index.beiZhu,
            width: 260,
            dataIndex: 'comment',
            key: 'comment',
            render: (text) => {
                return <Ellipsis title={text || '- - '} widthLimit={240} />;
            }
        },
        {
            title: I18N.edit.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            key: 'gmtCreate',
            width: 190,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.edit.index.chuangJianRen,
            dataIndex: 'creator',
            width: 130,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.edit.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            key: 'gmtModify',
            width: 190,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.edit.index.xiuGaiRen,
            dataIndex: 'operator',
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },

        {
            title: I18N.edit.index.caoZuo,
            dataIndex: 'operate',
            key: 'operate',
            width: 200,
            fixed: 'right',
            render: (text, record) => {
                let { versionStatus, status } = record;
                let dom = (
                    <HandleIcon>
                        {[2, 3, 4].includes(versionStatus) && window.auth('TZ0203', 'look') && (
                            <HandleIcon.Item title={I18N.edit.index.shangXian}>
                                <Icon
                                    type="online"
                                    onClick={() => {
                                        online(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {window.auth('TZ0203', 'modify') && (
                            <HandleIcon.Item title={I18N.edit.index.xiuGai}>
                                <Icon
                                    type="form"
                                    onClick={() => {
                                        modify(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {record.status !== 1 && window.auth('TZ0203', 'delete') && (
                            <HandleIcon.Item title={I18N.edit.index.shanChu}>
                                <Popconfirm
                                    title={I18N.edit.index.queDingShanChuCi}
                                    placement="topRight"
                                    onConfirm={() => checkDelete(record)}>
                                    <Icon type="delete" />
                                </Popconfirm>
                            </HandleIcon.Item>
                        )}
                        {window.auth('TZ0203', 'look') && (
                            <HandleIcon.Item title={I18N.edit.index.chaKan}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        look(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {window.auth('TZ0203', 'versionhistroy') && (
                            <HandleIcon.Item title={I18N.edit.index.liShiBanBen}>
                                <Icon
                                    type="history"
                                    onClick={() => {
                                        props.history.push(`/handle/supplierManagement/etl/version?uuid=${record.uuid}&currentTab=2`);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        <HandleIcon.Item title={I18N.edit.index.yinYongGuanXi}>
                            <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
                return record.type !== 1 ? dom : '--';
            }
        }
    ];

    const onFormChange = (values, changeInfo) => {
        if (['type'].includes(changeInfo.name)) {
            actions.search(values);
        }
    };

    const showConfirm = () => {
        confirm({
            title: I18N.edit.index.piLiangShangXian,
            content: I18N.edit.index.queDingPiLiangShang,
            onOk() {
                batchOnline(selectedRowKeys);
            },
            onCancel() {}
        });
    };

    const extraActions = (
        <>
            <Button.Group>
                <TntdAction
                    actionProps="disabled"
                    hidden={!isSelf}
                    afterClose={() => setSelectedRowKeys([])}
                    title={I18N.template(I18N.edit.index.sELEC, { val1: selectedRowKeys.length })}>
                    <Tooltip title={I18N.edit.index.piLiangShangXian}>
                        <Button
                            check
                            text={I18N.edit.index.piLiangShangXian}
                            disabled={selectedRowKeys.length === 0}
                            onClick={() => {
                                showConfirm();
                            }}>
                            <Icon type="batch-online" />
                        </Button>
                    </Tooltip>
                    <Tooltip exclude title={I18N.edit.index.daoRu}>
                        <Button
                            onClick={() => {
                                setImportVisible(true);
                                setConfirmLoading(false);
                            }}>
                            <Icon type="import" />
                        </Button>
                    </Tooltip>
                </TntdAction>
            </Button.Group>
            <Button
                type="primary"
                icon="plus"
                style={{ marginLeft: 10 }}
                onClick={() => {
                    setModalVisible(true);
                    setRecord({});
                    setModalType(1);
                }}>
                {I18N.edit.index.xinZeng}
            </Button>
        </>
    );

    // 修改成分页也能选择
    const onSelectChange = (selectedRowKeys) => {
        setSelectedRowKeys(selectedRowKeys);
    };

    const rowSelection = {
        fixed: 'left',
        columnWidth: 30,
        selectedRowKeys,
        onChange: onSelectChange,
        // status === 1 不能选中
        getCheckboxProps: (record) => ({
            disabled: record.status === 1
        })
    };

    const modalClose = () => {
        setModalVisible(false);
        setRecord({});
        setModalType(1);
        actions.search({ pageSize, curPage });
    };

    // 删除校验
    const checkDelete = (record) => {
        ReferenceCheck({
            strongMsg: I18N.edit.index.cunZaiQiangYinYong,
            rq: () =>
                referenceAPI.checkComponentReference({
                    componentType: 'DATASOURCE_ETL_HANDLER',
                    componentId: record.name
                })
        }).then(() => {
            deleteItem(record);
        });
    };

    return (
        <QueryListScene query={query} actions={actions} initSearch={true}>
            <QueryForm onChange={onFormChange} extraActions={extraActions}>
                <Field
                    name="displayName"
                    type="input"
                    props={{
                        placeholder: I18N.edit.index.eTLChuLi,
                        onPressEnter: (evt) => {
                            const value = (evt.target.value || '').trim();
                            actions.setFormData({
                                ...actions.getFormData(),
                                displayName: value || undefined
                            });
                        }
                    }}
                />
                <Field
                    name="type"
                    type="select"
                    props={{
                        placeholder: I18N.edit.index.chuLiQiLeiXing,
                        options: [
                            { label: I18N.edit.index.neiZhiChuLiQi, value: 1 },
                            { label: I18N.edit.index.qianZhiETL, value: 2 },
                            { label: I18N.edit.index.houZhiETL, value: 3 },
                            { label: I18N.edit.index.gongGongDaiMaKuai, value: 4 }
                        ]
                    }}
                />
            </QueryForm>
            <QueryList columns={columns} rowSelection={rowSelection} rowKey="uuid" scroll={{ x: 'max-content' }} />

            <Suspense fallback={null}>
                <AddModifyModal
                    visible={modalVisible}
                    onClose={modalClose}
                    record={record}
                    modalType={modalType}
                    globalStore={props.globalStore}
                    refs={modalRef}
                />
            </Suspense>
            <Suspense fallback={null}>
                <ImportModal
                    confirmLoading={confirmLoading}
                    visible={importVisible}
                    onOk={(file, type) => okImport(file, type)}
                    onCancel={() => closeImportModal()}
                />
            </Suspense>

            <Suspense fallback={null}>
                <FiledShowModal
                    visible={showModalInfo.showVisible}
                    onCancel={() => {
                        setShowModalInfo({
                            content: '',
                            msg: '',
                            dataSource: [],
                            success: false,
                            showVisible: false
                        });
                    }}
                    content={showModalInfo.content}
                    title={I18N.edit.index.wenJianLieBiao}
                    msg={showModalInfo.msg}
                    dataSource={showModalInfo.dataSource || []}
                    fieldSuccess={showModalInfo.success}
                />
            </Suspense>

            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.name}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_ETL_HANDLER',
                        componentId: referenceDrawerData.name
                    });
                }}
            />
        </QueryListScene>
    );
});
