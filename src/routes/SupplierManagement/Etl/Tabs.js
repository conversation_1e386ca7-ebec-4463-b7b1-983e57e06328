import I18N from '@/utils/I18N';
import React, { useState } from 'react';
import { Tabs, TabsContainer } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import Run from './Run';
import Edit from './Edit';

import './index.less';

const { TabPane } = Tabs;

export default TabsContainer((props) => {
    const { location, history } = props;
    const { pathname } = location;
    const currentTab = getUrlKey('currentTab') || '1';

    const [key, setKey] = useState(currentTab || '1');

    // tab切换
    const changeTabHandle = (key) => {
        const search = '?currentTab=' + key;
        history.push(pathname + search);
        setKey(`${key}`);
    };

    return (
        <Tabs activeKey={currentTab || key} onChange={changeTabHandle} animated={false} type="ladder-card">
            <TabPane tab={I18N.etl.tabs.yunXingQu} key="1">
                <Run areaType={currentTab} history={history} isSelf={key === '1'} />
            </TabPane>
            <TabPane tab={I18N.etl.tabs.bianJiQu} key="2">
                <Edit areaType={currentTab} history={history} isSelf={key === '2'} />
            </TabPane>
        </Tabs>
    );
});
