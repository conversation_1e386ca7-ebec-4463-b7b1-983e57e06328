import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import { PageContainer } from 'tntd';

import BreadCrumb from '@tddc/bread-crumb';
import EtlTabs from './Tabs';
import HistoryVersionList from './HistoryVersionList';
import Diff from './Inner/Diff';

export default PageContainer(
    BreadCrumb(() => {
        return (
            <Switch>
                <Route name={I18N.etl.index.banBenDuiBi} exact component={Diff} path="/handle/supplierManagement/etl/version/diff" />
                <Route
                    name={I18N.etl.index.liShiBanBen}
                    exact
                    component={HistoryVersionList}
                    path="/handle/supplierManagement/etl/version"
                    query={[{ uuid: 'uuid' }]}
                />
                <Route name={I18N.etl.index.eTLChuLi} component={EtlTabs} path="/" />
            </Switch>
        );
    })
);
