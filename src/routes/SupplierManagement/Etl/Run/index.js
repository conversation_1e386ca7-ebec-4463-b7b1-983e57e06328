import I18N from '@/utils/I18N';
import React, { Suspense, useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Button, message, Modal, QueryListScene, HandleIcon, Ellipsis, Tooltip, Icon } from 'tntd';
import { ReferenceDrawer, ReferenceCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
import etlAPI from '@/routes/SupplierManagement/Etl/services';
import ETLNameDisplay from '../Inner/ETLNameDisplay';
import { getUrlKey } from '@/utils/utils';
import { EtlTypeMap } from '../constans';
import Status from '../Inner/Status';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const AddModifyModal = React.lazy(() => import('../Inner/AddModifyModal'));

const itemStatusMap = {
    '1': {
        value: I18N.run.index.yiShangXian,
        color: '#07C790'
    },
    '2': {
        value: I18N.run.index.daiTiJiao,
        color: '#126BFB'
    }
};

export default connect((state) => ({
    globalStore: state.global
}))((props) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState(1);

    const [referenceDrawerData, setReferenceDrawerData] = useState(null);

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [record, setRecord] = useState({});

    const modalRef = useRef({});

    let { curPage, pageSize, displayName, type } = actions.getFormData();

    let currentTab = getUrlKey('currentTab') || '1';

    const query = (params = {}) => {
        const { current: curPage = 1, pageSize = 10, ...rest } = params;

        return etlAPI
            .getList({
                ...rest,
                curPage,
                pageSize,
                status: 1
            })
            .then((res) => {
                if (res.success) {
                    return {
                        data: res.data.contents || [],
                        current: res.data.curPage,
                        total: res.data.total,
                        pageSize: res.data.pageSize
                    };
                }
                message.error(res.message);
            });
    };

    useEffect(() => {
        if (currentTab === '1') {
            actions.search({ curPage, pageSize });
            modalRef.current?.getCommonCode && modalRef.current?.getCommonCode();
        }
    }, [currentTab]);

    // 导出
    const exportFile = () => {
        const params = {
            displayName,
            type,
            ids: selectedRowKeys.length > 0 ? selectedRowKeys.join() : null
        };
        const fileName = I18N.run.index.eTLChuLi5;
        etlAPI.downloadFile(params, fileName);
        setSelectedRowKeys([]);
    };

    // 查看
    const look = (record) => {
        setRecord({
            displayName: record.displayName, // ETL处理器名称
            name: record.name, // 标识
            script: record.script, // 脚本
            defaultScript: record.script, // 脚本
            type: `${record.type}`,
            comment: record.comment,
            commonUuids: record.commonUuids,
            tag: record.tag
        });
        setModalType(3);
        setModalVisible(true);
    };

    const offLine = ({ uuid, name }) => {
        Modal.confirm({
            title: I18N.run.index.queRenXiaXianGai,
            onOk: () => {
                ReferenceCheck({
                    strongMsg: I18N.run.index.cunZaiQiangYinYong,
                    rq: () =>
                        referenceAPI.checkComponentReference({
                            componentType: 'DATASOURCE_ETL_HANDLER',
                            componentId: name
                        })
                }).then(() => {
                    etlAPI.etlOffline({ uuid }).then((res) => {
                        if (res && res.success) {
                            message.success(res.message);
                            actions.search({ curPage, pageSize });
                            modalRef.current?.getCommonCode();
                        } else {
                            message.error(res.message);
                        }
                    });
                });
            }
        });
    };

    const columns = [
        {
            title: I18N.run.index.eTLChuLi4,
            dataIndex: 'displayName',
            key: 'displayName',
            width: 300,
            render: (text, record) => {
                let dom = (
                    <>
                        <ETLNameDisplay name={text} version={record.version} nameWidthLimit={280} />
                    </>
                );
                return dom ? dom : '--';
            }
        },
        {
            title: I18N.run.index.eTLChuLi3,
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text) => {
                return <Ellipsis title={text || '- - '} widthLimit={200} />;
            }
        },
        {
            title: I18N.run.index.leiXing,
            dataIndex: 'type',
            key: 'type',
            width: 130,
            render: (text) => {
                return <Ellipsis title={EtlTypeMap[text] || '- - '} />;
            }
        },
        {
            title: I18N.run.index.zhuangTai,
            dataIndex: 'status',
            key: 'status',
            width: 150,
            render: (text) => {
                let { value, color } = itemStatusMap[text] || {};

                return (
                    <div>
                        <Status type="dot" text={<Ellipsis title={value || '- -'} widthLimit={120} />} color={color} />
                    </div>
                );
            }
        },
        {
            title: I18N.run.index.beiZhu,
            width: 260,
            dataIndex: 'comment',
            key: 'comment',
            render: (text) => {
                return <Ellipsis title={text || '- - '} widthLimit={240} />;
            }
        },
        {
            title: I18N.run.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            key: 'gmtCreate',
            width: 190,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.run.index.chuangJianRen,
            dataIndex: 'creator',
            width: 130,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.run.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            key: 'gmtModify',
            width: 190,
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },
        {
            title: I18N.run.index.xiuGaiRen,
            dataIndex: 'operator',
            render: (text) => {
                return <Ellipsis title={text || '- - '} />;
            }
        },

        {
            title: I18N.run.index.caoZuo,
            dataIndex: 'operate',
            key: 'operate',
            width: 160,
            fixed: 'right',
            render: (text, record) => {
                // let { versionStatus, status } = record;
                let dom = (
                    <HandleIcon>
                        {window.auth('TZ0203', 'etloffline') && (
                            <HandleIcon.Item title={I18N.run.index.xiaXian}>
                                <Icon
                                    type="offline"
                                    onClick={() => {
                                        offLine(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {window.auth('TZ0203', 'look') && (
                            <HandleIcon.Item title={I18N.run.index.chaKan}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        look(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {window.auth('TZ0203', 'versionhistroy') && (
                            <HandleIcon.Item title={I18N.run.index.liShiBanBen}>
                                <Icon
                                    type="history"
                                    onClick={() => {
                                        props.history.push(`/handle/supplierManagement/etl/version?uuid=${record.uuid}&currentTab=1`);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        <HandleIcon.Item title={I18N.run.index.yinYongGuanXi}>
                            <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
                return record.type !== 1 ? dom : '--';
            }
        }
    ];

    const onFormChange = (values, changeInfo) => {
        if (['type'].includes(changeInfo.name)) {
            actions.search(values);
        }
    };

    const extraActions = (
        <>
            <Tooltip title={I18N.run.index.daoChu}>
                <Button
                    disabled={selectedRowKeys.length === 0}
                    onClick={() => {
                        exportFile();
                    }}>
                    <Icon type="export" />
                </Button>
            </Tooltip>
        </>
    );

    // 修改成分页也能选择
    const onSelectChange = (selectedRowKeys) => {
        setSelectedRowKeys(selectedRowKeys);
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange
    };

    const modalClose = () => {
        setModalVisible(false);
        setRecord({});
        setModalType(1);
    };

    return (
        <div className="run-area">
            <QueryListScene query={query} actions={actions} initSearch={true}>
                <QueryForm onChange={onFormChange} extraActions={extraActions}>
                    <Field
                        name="displayName"
                        type="input"
                        props={{
                            placeholder: I18N.run.index.eTLChuLi2,
                            onPressEnter: (evt) => {
                                const value = (evt.target.value || '').trim();
                                actions.setFormData({
                                    ...actions.getFormData(),
                                    displayName: value || undefined
                                });
                            }
                        }}
                    />
                    <Field
                        name="type"
                        type="select"
                        props={{
                            placeholder: I18N.run.index.eTLChuLi,
                            options: [
                                { label: I18N.run.index.neiZhiChuLiQi, value: 1 },
                                { label: I18N.run.index.qianZhiETL, value: 2 },
                                { label: I18N.run.index.houZhiETL, value: 3 },
                                { label: I18N.run.index.gongGongDaiMaKuai, value: 4 }
                            ]
                        }}
                    />
                </QueryForm>
                <QueryList
                    columns={columns}
                    rowSelection={rowSelection}
                    rowKey="uuid"
                    scroll={{
                        x: 'max-content'
                    }}
                />
            </QueryListScene>
            <Suspense fallback={null}>
                <AddModifyModal
                    visible={modalVisible}
                    onClose={modalClose}
                    record={record}
                    modalType={modalType}
                    globalStore={props.globalStore}
                    refs={modalRef}
                />
            </Suspense>

            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.name}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_ETL_HANDLER',
                        componentId: referenceDrawerData.name
                    });
                }}
            />
        </div>
    );
});
