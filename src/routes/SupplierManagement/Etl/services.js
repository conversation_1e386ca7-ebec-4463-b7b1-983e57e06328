import { getUrl, deleteEmptyObjItem } from '@/utils/common';
import request, { downloadFileHandle } from '@/utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(
        getUrl('/bridgeApi/etlHandler/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 启用、禁用
const setOnline = async (params) => {
    return request(
        '/bridgeApi/etlHandler/enable',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request(
        '/bridgeApi/etlHandler/add',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 删除数据
const deleteData = async (params) => {
    return request(
        '/bridgeApi/etlHandler/delete',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 更新数据
const updateData = async (params) => {
    return request(
        '/bridgeApi/etlHandler/update',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 获取groovy代码
const getGroovyCode = async (params) => {
    return request(
        getUrl('/bridgeApi/etlHandler/viewScript', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 根据ETLlist
const getEtlList = async (params) => {
    const defaultParams = {
        status: 1
    };
    return request(
        getUrl('/bridgeApi/etlHandler/listByStatus', deleteEmptyObjItem({ ...params, ...defaultParams })),
        {
            method: 'GET'
        },
        true
    );
};

// 获取模板代码
const getGroovyTemplete = async (params) => {
    return request(
        getUrl('/bridgeApi/etlHandler/getGroovyTemplete', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取公共代码
const getCommonCode = async () => {
    return request(
        getUrl('/bridgeApi/etlHandler/listAllCommonCode'),
        {
            method: 'GET'
        },
        true
    );
};

// // 缓存
// const saveCache = async (params) => {
//     return request('/common/saveCache', {
//         method: 'POST',
//         body: params
//     });
// };

// // 获取缓存信息
// const getCache = async (params) => {
//     return request(getUrl('/common/getCache', deleteEmptyObjItem(params)), {
//         method: 'GET'
//     });
// };

// 导出
const downloadFile = async (param, name) => {
    let fileType = 'etl';
    return downloadFileHandle(
        getUrl('/bridgeApi/etlHandler/export', param),
        {
            method: 'GET'
        },
        name,
        fileType,
        null,
        true
    );
};

// 批量上线
const batchOnline = async (params) => {
    return request(
        '/bridgeApi/etlHandler/online',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// ETL处理器版本历史
const getVersionList = async (params) => {
    return request(
        getUrl('/bridgeApi/etlHandler/version/page', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// ETL处理器覆盖编辑区
const ETLCover = async (params) => {
    return request(
        '/bridgeApi/etlHandler/version/cover',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// ETL处理器下线
const etlOffline = async (params) => {
    return request(
        '/bridgeApi/etlHandler/offline',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// ETL处理器版本详情
const getEtlDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/etlHandler/version/detail', params),
        {
            method: 'GET'
        },
        true
    );
};

export default {
    getList,
    addData,
    deleteData,
    updateData,
    setOnline,
    getGroovyCode,
    getEtlList,
    getGroovyTemplete,
    // saveCache,
    // getCache,
    downloadFile,
    getCommonCode,
    batchOnline,
    getVersionList,
    ETLCover,
    etlOffline,
    getEtlDetail
};
