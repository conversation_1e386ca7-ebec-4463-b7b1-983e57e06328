/*
 * @CreatDate: 2020-10-15 16:25:13
 * @Describe: 导入弹框
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useEffect, useState } from 'react';
import { Upload, message, Modal, Row, Col, Radio, Select } from 'tntd';
import { connect } from 'dva';

const ImportModal = (props) => {
    const { visible, onOk, onCancel, confirmLoading, title = I18N.importmodal.index.daoRuETL, globalStore } = props;
    const { appList } = globalStore;

    const [type, setType] = useState(1);
    const [file, setFile] = useState(null);

    useEffect(() => {
        return () => {
            setType(1);
            setFile(null);
        };
    }, [visible]);

    const upLoadProps = {
        beforeUpload: (file) => {
            setFile(file);
            return false;
        }
    };

    const handleOk = () => {
        if (!file) return message.warning(I18N.importmodal.index.qingXuanZeWenJian);
        onOk(file, type);
    };

    return (
        <Modal
            title={title}
            className="filed-import-modal"
            visible={visible}
            onOk={handleOk}
            width={620}
            onCancel={() => {
                onCancel();
                setType(1);
                setFile(null);
            }}
            destroyOnClose
            maskClosable={false}
            confirmLoading={confirmLoading}>
            <Row style={{ marginBottom: '20px' }}>
                {/* <Col span={2}></Col> */}
                <Col span={11}>
                    <div className='label-bold '>
                        {I18N.importmodal.index.chaXunDaoXiangTong2}
                    </div>
                </Col>
                <Col span={13}>
                    <Radio.Group onChange={(e) => setType(e.target.value)} value={type}>
                        <Radio value={1} className="radioStyle">
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}</Radio>
                        <Radio value={2} className="radioStyle">
                            {I18N.importmodal.index.fuGaiXiangTongBiao}</Radio>
                        <Radio value={3} className="radioStyle">
                            {I18N.importmodal.index.chaXunDaoXiangTong}</Radio>
                    </Radio.Group>
                </Col>
            </Row>
            <Row>
                {/* <Col span={2} /> */}
                <Col span={3}>
                    <div className='label-bold'>
                        {I18N.importmodal.index.xuanZeWenJian}
                    </div>
                </Col>
                <Col span={18}>
                    <Upload {...upLoadProps} showUploadList={false} accept=".etl">
                        {I18N.importmodal.index.qingXuanZeWenJian}</Upload>
                </Col>
            </Row>
            <Row>
                <Col span={2} />
                <Col span={4} />
                <Col span={18}>{file && file.name}</Col>
            </Row>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ImportModal);
