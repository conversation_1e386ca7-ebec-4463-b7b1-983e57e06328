/*
 * @Author: liubo
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: Modal弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Modal, message, Button, Input, Icon, Select, Tooltip, Checkbox } from 'tntd';
import etlAPI from '@/routes/SupplierManagement/Etl/services';
import GroovyEditor from '@/components/GroovyEditor';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { EtlTypeMap } from '../constans';

const Option = Select.Option;
const { TextArea } = Input;

class AddModifyModal extends PureComponent {
    state = {
        loading: false,
        max: false,
        dHeight: 400,
        cachInfo: null,
        commonCode: [],
        addEditModalData: {}
    };

    componentDidMount() {
        this.getCommonCode();
        this.props.refs && (this.props.refs.current.getCommonCode = this.getCommonCode);
        document.addEventListener('keydown', this.escScreen);
    }

    componentDidUpdate(prevProps) {
        const { modalType, record } = this.props;
        if ([2, 3].includes(modalType) && prevProps.record !== record) {
            if (Object.keys(record).length === 0) return;
            if (modalType === 1) {
                this.getTemplate('2');
            } else if (modalType === 2 || modalType === 3) {
                let obj = {
                    ...this.state.addEditModalData,
                    ...cloneDeep(record),
                    defaultScript: this.state.addEditModalData?.script ? this.state.addEditModalData?.script : record.script
                };
                this.setState({
                    addEditModalData: obj
                });
            }
        }
    }

    componentWillUnmount() {
        this.setState({
            addEditModalData: {}
        });
        clearInterval(this.timer);
        document.removeEventListener('keydown', this.escScreen);
    }

    // esc最小化
    escScreen = (e) => {
        const { max } = this.state;
        if (e.key === 'Escape' && max) {
            this.screen(!max);
        }
    };

    getTemplate = (type) => {
        etlAPI.getGroovyTemplete({ type }).then((res) => {
            if (res && res.success) {
                this.setState({
                    addEditModalData: {
                        ...this.state.addEditModalData,
                        script: res.data ? res.data : '',
                        defaultScript: res.data ? res.data : ''
                    }
                });
            } else {
                message.error(res.message);
            }
        });
    };

    getCommonCode = () => {
        etlAPI.getCommonCode().then((res) => {
            if (res && res.success) {
                this.setState({
                    commonCode: res.data ? res.data : []
                });
            } else {
                message.error(res.message);
            }
        });
    };

    // 点击确定
    handleOk = (saveType) => {
        const { modalType } = this.props;
        const { addEditModalData } = this.state;
        const { displayName, name, script, uuid, type, comment, commonUuids, tag } = addEditModalData;
        if (!displayName) return message.warning(I18N.inner.addmodifymodal.mingChengBuNengWei); // 名称不能为空
        if (!name) return message.warning(I18N.inner.addmodifymodal.biaoZhiBuNengWei); // 标识不能为空
        if (comment && comment.length > 2000) return message.warning(I18N.inner.addmodifymodal.zuiDuoWeiGeZi);

        if (name.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.biaoZhiBuNengChaoGuo);
        }

        if (!/^[\w\s\u4E00-\u9FA5\-.]+$/.test(displayName)) {
            return message.warning(I18N.inner.addmodifymodal.mingChenZhiYunXu);
        }

        if (displayName.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.mingChengBuNengChaoGuo);
        }

        if (!/^[a-zA-Z0-9\_]+$/.test(name)) {
            return message.warning(I18N.inner.addmodifymodal.biaoShiZhiYunXu);
        }

        let params = {
            uuid,
            displayName,
            name,
            script,
            type,
            comment,
            commonUuids,
            tag,
            saveType
        };
        if (modalType === 1) {
            this.addData(params);
        } else if (modalType === 2) {
            delete params.name;
            this.updateData(params);
        }
    };

    // 添加数据
    addData = (params) => {
        this.setState({ loading: true });
        etlAPI
            .addData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    message.success(I18N.inner.addmodifymodal.caoZuoChengGong);
                    this.handleCancel();
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 更新数据
    updateData = (params) => {
        this.setState({ loading: true });
        etlAPI
            .updateData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    message.success(I18N.inner.addmodifymodal.caoZuoChengGong);
                    this.handleCancel();
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 点击取消
    handleCancel = () => {
        const { onClose } = this.props;
        onClose();
        this.setState({
            max: false,
            dHeight: 400,
            cachInfo: null,
            addEditModalData: {}
        });
    };

    // 改变参数
    changeField(e, type, field) {
        let val = null;
        let obj = cloneDeep(this.state.addEditModalData);
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        obj[field] = val ? val : null;

        if (field === 'type') {
            delete obj.tag;
            this.getTemplate(e);
        }

        this.setState({
            addEditModalData: {
                ...obj
            }
        });
    }

    getCode(code) {
        this.setState({
            addEditModalData: {
                ...this.state.addEditModalData,
                script: code
            }
        });
    }

    // 全屏及还原
    screen(val) {
        let dHeight = 400;
        if (val) {
            dHeight = document.body.clientHeight - 280;
            document.body.querySelectorAll('.m-etl-editor')[0].parentNode.style.overflow = 'hidden';
        }
        this.setState({ max: val, dHeight });
    }

    render() {
        const { loading, max, dHeight, addEditModalData, commonCode } = this.state;
        const { visible, modalType } = this.props;
        const { displayName, name, comment, type, script, defaultScript, commonUuids, tag } = addEditModalData;
        let Renderscreen = (
            <span className="u-screen">
                {!max ? (
                    <Icon type="fullscreen" onClick={() => this.screen(true)} />
                ) : (
                    <Icon type="fullscreen-exit" onClick={() => this.screen(false)} />
                )}
            </span>
        );
        let title = (
            <div>
                <span className="title">{I18N.inner.addmodifymodal.chaKan}</span>
                {Renderscreen}
            </div>
        );
        let disabled = false;
        let modifyDisabled = false;
        if (modalType === 1) {
            title = (
                <div>
                    <span className="title">{I18N.inner.addmodifymodal.xinZeng}</span>
                    {Renderscreen}
                </div>
            );
        } // 新增
        if (modalType === 2) {
            title = (
                <div>
                    <span className="title">{I18N.inner.addmodifymodal.xiuGai}</span>
                    {Renderscreen}
                </div>
            ); // 修改
            modifyDisabled = true;
        }
        if (modalType === 3) disabled = true;

        const footerDom = [
            <Button onClick={this.handleCancel} key="cancel">
                {I18N.inner.addmodifymodal.quXiao}
            </Button>,
            <Button onClick={() => this.handleOk(0)} key="ok" loading={loading}>
                {I18N.inner.addmodifymodal.zanCun}
            </Button>,
            <Button type="primary" onClick={() => this.handleOk(1)} key="ok" loading={loading}>
                {I18N.inner.addmodifymodal.baoCun}
            </Button>
        ];

        return (
            <Modal
                title={title}
                width={1100}
                maskClosable={false}
                keyboard={false}
                visible={visible}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={disabled ? null : footerDom}
                className={max ? 'm-etl-editor m-etl-editor-fullscreen' : 'm-etl-editor'}>
                <div className="modal-box">
                    <span className="u-label">
                        <b>*</b>
                        {/* ETL处理器类型 */}
                        {I18N.inner.addmodifymodal.eTLChuLi3}
                    </span>
                    <span className="u-input">
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled ? disabled : modifyDisabled}
                            dropdownMatchSelectWidth={false}
                            optionFilterProp="children"
                            value={type ? type : undefined}
                            onChange={(e) => this.changeField(e, 'select', 'type')}>
                            {Object.keys(EtlTypeMap)
                                .filter((i) => i !== '1')
                                .map((item) => {
                                    let name = EtlTypeMap[item];
                                    return (
                                        <Option key={name} value={item}>
                                            {name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    </span>
                    {/* 前置ETL处理器用于准备服务请求前的数据；后置ETL处理器用于组装服务返回后的结果。 */}
                    <Tooltip title={I18N.inner.addmodifymodal.qianZhiETL}>
                        <Icon className="u-icon" type="question-circle" />
                    </Tooltip>
                    <span className="u-label">
                        <b>*</b>
                        {/* ETL处理器名称 */}
                        {I18N.inner.addmodifymodal.eTLChuLi2}
                    </span>
                    <span className="u-input">
                        <Input disabled={disabled} value={displayName} onChange={(e) => this.changeField(e, 'input', 'displayName')} />
                    </span>
                    <span className="u-label">
                        <b>*</b>
                        {/* ETL处理器标识 */}
                        {I18N.inner.addmodifymodal.eTLChuLi}
                    </span>
                    <span className="u-input">
                        <Input
                            disabled={disabled ? disabled : modifyDisabled}
                            value={name}
                            onChange={(e) => this.changeField(e, 'input', 'name')}
                        />
                    </span>
                </div>
                {
                    // 后置ETL处理器才显示分页查询接口
                    Number(type) === 3 && (
                        <div className="modal-box">
                            <span className="u-label">{I18N.inner.addmodifymodal.shiYongFenYeCha}</span>
                            <span className="u-input">
                                <Checkbox
                                    disabled={disabled ? disabled : modifyDisabled}
                                    checked={!!tag}
                                    onChange={async (e) => {
                                        this.changeField(e.target.checked ? 1 : 0, 'select', 'tag');
                                        this.getTemplate(e.target.checked ? 5 : Number(type));
                                    }}
                                />
                            </span>
                        </div>
                    )
                }

                <div className="modal-box">
                    {Number(type) !== 4 && (
                        <Fragment>
                            <span className="u-label">
                                {/* 公共代码块 */}
                                {I18N.inner.addmodifymodal.gongGongDaiMaKuai}
                            </span>
                            <span className="u-input">
                                <Select
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    value={commonUuids ? commonUuids.split(',') : []}
                                    mode="multiple"
                                    onChange={(e) => this.changeField(e.join(','), 'select', 'commonUuids')}>
                                    {commonCode.map(({ displayName, uuid }) => (
                                        <Select.Option value={uuid} key={uuid}>
                                            {displayName}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </span>
                        </Fragment>
                    )}
                </div>
                <div className="modal-box">
                    <span className="u-label fl">{I18N.inner.addmodifymodal.beiZhu}</span>
                    <span className="u-textArea">
                        <TextArea
                            disabled={disabled}
                            rows={4}
                            placeholder={I18N.inner.addmodifymodal.zuiDuoGeZiFu}
                            value={comment}
                            onChange={(e) => this.changeField(e, 'input', 'comment')}
                        />
                    </span>
                </div>
                <div className="wrap-groovy-editor" style={{ width: '950px', margin: '0 auto' }}>
                    <GroovyEditor
                        id={modalType}
                        defaultCode={defaultScript ? defaultScript : ''}
                        theme="night"
                        readOnly={disabled}
                        height={dHeight}
                        onChange={(code) => this.getCode(code)}
                    />
                </div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    etlStore: state.etl
}))(AddModifyModal);
