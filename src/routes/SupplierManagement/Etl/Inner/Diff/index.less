.jsdiff-conent{
  background: #fff;
  display: flex;
  justify-content: space-between;
  .left,.right{
    width: 50%;
    padding: 10px;
    // overflow: initial;

    > .code{
      border: 1px solid #ddd;
      overflow: auto;
      height: calc(100vh - 130px);
      padding: 10px;
    }
    > .info{
      border: 1px solid #ddd;
      border-bottom: none;
      overflow: auto;
      height: 140px;
      padding: 10px;
    }
    .current{
    
    }
  
  }
}
.code-diff{
  background: #fff;
  display: flex;
  justify-content: space-between;
}
.currentcolor{
  background: rgb(236, 253, 240);
  overflow: initial;
}
.currentcolor1{
  background: rgb(251,233,235);
  overflow: initial;
}
.current{
	overflow: initial;
}

