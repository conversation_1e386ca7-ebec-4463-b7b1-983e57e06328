/*
 * @CreatDate: 2022-08-25 10:46:06
 * @Describe: 函数比对
 */

import I18N from '@/utils/I18N';
import React, { useState, useEffect } from 'react';

import { getUrlKey } from '@/utils/utils';

import './index.less';
import etlAPI from '@/routes/SupplierManagement/Etl/services';
import moment from 'moment';

const JsDiff = require('diff');

const EtlMap = {
    1: I18N.diff.index.neiZhiChuLiQi,
    2: I18N.diff.index.qianZhiETL,
    3: I18N.diff.index.houZhiETL,
    4: I18N.diff.index.gongGongDaiMaKuai
};

const Page = (props) => {
    const uuid1 = getUrlKey('uuid1');
    const uuid2 = getUrlKey('uuid2');

    const [data, setData] = useState([]);

    const formatDate = (date) => {
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    };

    useEffect(() => {
        if (uuid1 && uuid2) {
            Promise.all([etlAPI.getEtlDetail({ versionUuid: uuid1 }), etlAPI.getEtlDetail({ versionUuid: uuid2 })]).then((res) => {
                setData([res[0].data, res[1].data]);
                const diffInfo = JsDiff.diffLines(
                    I18N.template(I18N.diff.index.eTLChuLi2, {
                        val1: res[0].data.displayName,
                        val2: EtlMap[res[0].data.type],
                        val3: res[0].data.version,
                        val4: 
                        res[0].data.operator
                    ,
                        val5: formatDate(res[0].data.gmtModify)
                    }),
                    I18N.template(I18N.diff.index.eTLChuLi, {
                        val1: res[1].data.displayName,
                        val2: EtlMap[res[1].data.type],
                        val3: res[1].data.version,
                        val4: 
                        res[1].data.operator
                    ,
                        val5: formatDate(res[1].data.gmtModify)
                    })
                );
                const newJsDiffData = JsDiff.diffLines(res[0]?.data?.script, res[1]?.data?.script);
                setData([
                    {
                        ...res[0].data,
                        diffData: newJsDiffData,
                        diffInfo
                    },
                    {
                        ...res[1].data,
                        diffData: newJsDiffData,
                        diffInfo
                    }
                ]);
            });
        }
    }, []);

    return (
        <div className="jsdiff-conent">
            <div className="left">
                <div className="info">
                    {data[0]?.diffInfo?.map((item, inde) => {
                        if (!item?.added) {
                            return (
                                <pre key={inde} className={item?.removed ? 'currentcolor' : 'current'}>
                                    {item.value}
                                </pre>
                            );
                        }
                    })}
                </div>
                <div className="code">
                    {data[0]?.diffData?.map((item, inde) => {
                        if (!item?.added) {
                            return (
                                <pre key={inde} className={item?.removed ? 'currentcolor' : 'current'}>
                                    {item.value}
                                </pre>
                            );
                        }
                    })}
                </div>
            </div>
            <div className="right">
                <div className="info">
                    {data[1]?.diffInfo?.map((item, inde) => {
                        if (!item?.removed) {
                            return (
                                <pre key={inde} className={item?.added ? 'currentcolor1' : 'current'}>
                                    {item.value}
                                </pre>
                            );
                        }
                    })}
                </div>
                <div className="code">
                    {data[1]?.diffData?.map((item, inde) => {
                        if (!item?.removed) {
                            return (
                                <pre key={inde} className={item?.added ? 'currentcolor1' : 'current'}>
                                    {item.value}
                                </pre>
                            );
                        }
                    })}
                </div>
            </div>
        </div>
    );
};

export default Page;
