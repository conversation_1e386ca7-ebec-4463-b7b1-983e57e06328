import { Ellipsis, Tag, Tooltip } from 'tntd';
import { colorRgb } from '@/utils/utils';
import { statusMap } from '../../constans';
import './index.less';

export default (props) => {
    const { name, version, status, nameWidthLimit } = props || {};

    let statusObj = statusMap[status] || {};

    return (
        <div className="etl-display">
            <div className="version-tag">
                {status && (
                    <Tag
                        color={colorRgb(statusObj?.color, 0.1)}
                        style={{ border: `1px solid ${statusObj?.color}`, color: statusObj?.color }}>
                        <span
                            className="text-overflow"
                            style={{
                                display: 'inline-block',
                                verticalAlign: 'middle'
                            }}
                            title={statusObj?.value}>
                            {statusObj?.value}
                        </span>
                    </Tag>
                )}
                <Tag color="tnt-purple">V{version}</Tag>

                <span className="to-detail">
                    <Ellipsis title={name} widthLimit={nameWidthLimit} />
                </span>
            </div>
        </div>
    );
};
