/*
 * @CreatDate: 2020-10-15 16:25:13
 * @Describe: 导入弹框展示返回数据
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useEffect, useState } from 'react';
import { message, Modal, Table, Button } from 'tntd';
import copy from 'copy-to-clipboard';

const FiledShowModal = props => {
	const { visible, onCancel, title = '', content, msg, dataSource = [], fieldSuccess } = props;

	const columns = [
		{ title: I18N.filedshow.index.eTLChuLi2, dataIndex: 'displayName', width: 250 },
		{ title: I18N.filedshow.index.eTLChuLi, dataIndex: 'name' }
	];

	const copyToClipboard = () => {
		let str = `${dataSource[0].displayName}:${dataSource[0].name}`;
		for (let i = 1; i < dataSource.length; i++) {
			str = `${str}、${dataSource[i].displayName}:${dataSource[i].name}`;
		}
		if (copy(str)) {
			message.success(I18N.filedshow.index.fuZhiChengGong);
		}
	};

	return (
		<Modal
			title={title}
			className="filed-import-show-modal"
			visible={visible}
			footer={[
				<Button onClick={onCancel}>
					{I18N.filedshow.index.zhiDaoLe}</Button>
			]}
			onCancel={onCancel}
			destroyOnClose
			maskClosable={false}
		>
			<p style={{fontSize: 16}}>{content}</p>
			{!fieldSuccess ? <p>{I18N.filedshow.index.shiBaiYuanYin}{msg}</p> : null}
			{
				fieldSuccess && dataSource.length > 0 &&
				<>
					<p>
						{msg}
						{/* <Button type="primary" size="small" onClick={() => copyToClipboard()}>
							复制字段
						</Button> */}
					</p>
					<Table
						size="small"
						dataSource={dataSource}
						columns={columns}
						rowKey="name"
					/>
				</>
			}
		</Modal>
	);
};

export default FiledShowModal;
