import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, message, Input, Select } from 'tntd';
import { dataServiceListAPI } from '@/services';

const Option = Select.Option;
class CopyModal extends PureComponent {
    state = {
        loading: false,
        name: undefined,
        displayName: undefined
    };

    componentDidUpdate(old) {}

    handleOk = () => {
        const { name, displayName } = this.state;
        const { globalStore, record } = this.props;
        const { account } = globalStore.currentUser;
        const { isIntegrationTG } = globalStore.allMap;

        if (!name || !displayName) return message.warning(I18N.copymodal.index.cunZaiBiTianXiang); // 存在必填项未填

        if (!/^[\w\s\u4E00-\u9FA5\-.]+$/.test(displayName)) {
            message.warning(I18N.copymodal.index.shuJuYuanFuWuM); // 只支持中文, 英文, 数字, 空格, 下划线, -, .
            return;
        }

        if (!/^[a-zA-Z0-9\_]+$/.test(name)) {
            message.warning(I18N.copymodal.index.shuJuYuanFuWuB); // 只支持字母、数字、下划线的输入组合
            return;
        }

        const params = {
            uuid: record.uuid,
            creator: account,
            name,
            displayName
        };

        this.setState({ loading: true });
        dataServiceListAPI
            .copyData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.props.onOk();
                    this.setState({
                        name: undefined,
                        displayName: undefined
                    });
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    handleCancel = () => {
        this.props.onCancel();
        this.setState({
            loading: false,
            name: null,
            displayName: null
        });
    };

    changeField(e, field) {
        let obj = {};
        obj[field] = e ? e : null;
        this.setState({ ...obj });
    }

    render() {
        const { visible } = this.props;
        const { loading, name, displayName } = this.state;

        return (
            <Modal
                title={I18N.copymodal.index.fuZhi} // 复制
                width={500}
                visible={visible}
                maskClosable={false}
                confirmLoading={loading}
                onCancel={this.handleCancel}
                onOk={this.handleOk}>
                <div style={{ marginBottom: '2px' }}>
                    <b style={{ color: '#f00', marginRight: '2px' }}>*</b>
                    {/* 数据源服务接口名称 */}
                    {I18N.copymodal.index.shuJuYuanFuWu2}
                </div>
                <Input
                    placeholder={I18N.copymodal.index.qingShuRuShuJu2}
                    value={displayName}
                    maxLength={200}
                    onChange={(e) => this.changeField(e.target.value, 'displayName')}
                />
                <div className="mt20" style={{ marginBottom: '2px' }}>
                    <b style={{ color: '#f00', marginRight: '2px' }}>*</b>
                    {/* 数据源服务接口标识 */}
                    {I18N.copymodal.index.shuJuYuanFuWu}
                </div>
                <Input
                    placeholder={I18N.copymodal.index.qingShuRuShuJu}
                    value={name}
                    maxLength={200}
                    onChange={(e) => this.changeField(e.target.value, 'name')}
                />
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(CopyModal);
