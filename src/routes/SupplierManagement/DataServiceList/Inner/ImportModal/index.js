import I18N from '@/utils/I18N';
import './index.less';
import { useState } from 'react';
import { connect } from 'dva';
import { Modal, Upload, Button, Icon, Radio, message, Select } from 'tntd';
import { dataServiceListAPI } from '@/services';

const { Option } = Select;

const ImportModal = (props) => {
    const { updateVisible, importVisible, query, successMsg = I18N.importmodal.index.daoRuChengGong, globalStore } = props;
    const { allMap } = globalStore;
    const [fileList, setFileList] = useState([]);
    const [radioValues, setRadioValues] = useState({
        condition1: 'SKIP',
        condition2: 'SKIP',
        condition3: 'SKIP',
        condition4: 'SKIP',
        condition5: 'SKIP',
        condition6: 'SKIP',
        condition7: 'SKIP'
    });
    const [confirmLoading, setConfirmLoading] = useState(false);
    const beforeUpload = () => {
        // setFileList(fileList);
        return false;
    };

    const onUploadChange = (info) => {
        let fileList = [...info.fileList];
        if (fileList.length > 1) {
            return message.warning(I18N.importmodal.index.yiCiZuiDuoShang);
        }
        setFileList(fileList);
    };

    const radioStyle = {
        display: 'block',
        height: '30px',
        lineHeight: '30px'
    };

    const radioGroupOnChange = (key, value) => {
        let temp = { ...radioValues };
        temp[key] = value.target.value;
        setRadioValues(temp);
    };

    const onOk = () => {
        if (fileList === null || fileList.length === 0) {
            message.warning(I18N.importmodal.index.qingXianShangChuanWen);
            return;
        }
        setConfirmLoading(true);
        const temp = [];
        fileList.forEach((item) => {
            temp.push(item.originFileObj);
        });
        const params = {
            dataSourceImportDealType: radioValues.condition1,
            etlImportDealType: radioValues.condition2,
            partnerImportDealType: radioValues.condition3,
            contractImportDealType: radioValues.condition4,
            fieldImportDealType: radioValues.condition5,
            fieldGroupImportDealType: radioValues.condition6,
            commonEtlImportDealType: radioValues.condition7
        };

        if (allMap.isIntegrationTG) {
            delete params.contractImportDealType;
        }
        dataServiceListAPI
            .importRecord(params, temp, 'file')
            .then((res) => {
                if (res && res.success) {
                    message.success(successMsg); // 导入成功
                    query();
                    onCancel();
                } else {
                    message.error(
                        <span
                            style={{
                                width: 'calc(100% - 25px)',
                                display: 'inline-block',
                                verticalAlign: 'top',
                                wordBreak: 'break-all'
                            }}>
                            {res.message || res.msg}
                        </span>
                    );
                }
            })
            .finally(() => {
                setConfirmLoading(false);
            });
    };

    const onCancel = () => {
        updateVisible(false);
        setFileList([]);
        setRadioValues({
            condition1: 'SKIP',
            condition2: 'SKIP',
            condition3: 'SKIP',
            condition4: 'SKIP',
            condition5: 'SKIP',
            condition6: 'SKIP',
            condition7: 'SKIP'
        });
    };
    return (
        <Modal
            className="import-modal"
            title={I18N.importmodal.index.daoRuShuJuYuan}
            width={700}
            visible={importVisible}
            onCancel={onCancel}
            onOk={onOk}
            maskClosable={false}
            confirmLoading={confirmLoading}>
            <div className="file-select-container">
                <span>{I18N.importmodal.index.xuanZeWenJian}</span>
                <Upload action="123" beforeUpload={beforeUpload} fileList={fileList} onChange={onUploadChange} accept=".tar">
                    <Button>
                        <Icon type="upload" /> {I18N.importmodal.index.dianJiShangChuan}
                    </Button>
                </Upload>
            </div>
            <div className="content-container">
                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong7}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition1', e)} value={radioValues.condition1}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong6}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition2', e)} value={radioValues.condition2}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>

                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong5}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition3', e)} value={radioValues.condition3}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
                {!allMap?.isIntegrationTG && (
                    <div className="content-choice-container">
                        <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong4}</div>
                        <Radio.Group onChange={(e) => radioGroupOnChange('condition4', e)} value={radioValues.condition4}>
                            <Radio style={radioStyle} value={'SKIP'}>
                                {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                            </Radio>
                            <Radio style={radioStyle} value={'COVER'}>
                                {I18N.importmodal.index.fuGaiXiangTongBiao}
                            </Radio>
                        </Radio.Group>
                    </div>
                )}

                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong3}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition5', e)} value={radioValues.condition5}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong2}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition6', e)} value={radioValues.condition6}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition7', e)} value={radioValues.condition7}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
            </div>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ImportModal);
