import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Modal, message, Input, Checkbox, Icon, Tooltip } from 'tntd';
import { cloneDeep, uniq } from 'lodash';
import { dataServiceListAPI } from '@/services';
import './index.less';
import UseImg from './UseImg.svg';

const { Search } = Input;

const AuthDialog = (props) => {
    const { visible, onCancel, globalStore } = props;
    const { currentOrgCode } = globalStore;

    const [loading, setLoading] = useState(false);
    const [allPolicyList, setAllPolicyList] = useState([]);
    const [checkedPolicyList, setPolicyList] = useState([]);
    const [allPolicyMap, setAllPolicyMap] = useState({});

    useEffect(() => {
        if (visible) {
            onload();
        }
    }, [visible]);

    const onload = async () => {
        let res = await dataServiceListAPI.getAllPolicyList({
            orgCode: currentOrgCode,
        });
        var obj = {};

        if (res.success) {
            let list = res.data.map((v) => {
                obj[v.code] = v;
                return {
                    value: v.code,
                    label: v.name,
                };
            });
            setAllPolicyList(list);
        }

        let res2 = await dataServiceListAPI.getAuthPolicies({
            uuid: visible?.uuid,
        });
        if (res2.success) {
            let list = res2.data
                .filter((v) => v.policyCode)
                .map((v) => {
                    obj[v.policyCode].use = v.use;
                    return v.policyCode;
                });
            setPolicyList(list);
            setAllPolicyMap(obj);
        }
    };

    const handleOk = () => {
        // if (!checkedPolicyList.length)
        //     return message.warnning("请先选择需授权的策略");

        let params = {
          uuid: visible?.uuid,
          authPolicy: checkedPolicyList?.toString(),
        };
        dataServiceListAPI.handleAuthPolicy(params).then((res) => {
            message[res.success ? 'success' : 'error'](res.message);
            if (res.success) {
              onCancel()
            }
        });
    };

    const onClickCheckedItem = (value) => {
        let list = cloneDeep(checkedPolicyList);
        list = list.filter((v) => v != value);
        setPolicyList(list);
    };

    const onChangeSearch = (val) => {
        console.log(val);
    };

    const onChangeCheckbox = (value) => {
        let list = uniq(cloneDeep([...checkedPolicyList, value]));
        // console.log(list)
        setPolicyList(list);
    };
    console.log(allPolicyMap);

    return (
        <Modal
            title={'分配权限'}
            width={900}
            visible={!!visible}
            maskClosable={false}
            confirmLoading={loading}
            onCancel={onCancel}
            onOk={handleOk}
            className="data-server-auth"
        >
            <div className="auth-main">
                <div className="auth-l">
                    <Search
                        placeholder="请输入关键字"
                        onChange={(e) => onChangeSearch(e.target.value)}
                    />

                    <div className="auth-box">
                        {allPolicyList.map((v) => (
                            <div
                                className="auth-line"
                                onClick={(e) => onChangeCheckbox(v.value)}
                                key={v.value}
                            >
                                <Checkbox
                                    checked={checkedPolicyList.includes(
                                        v.value
                                    )}
                                >
                                    {v.label}
                                </Checkbox>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="auth-r">
                    <div className="auth-rt">
                        已选择 （{checkedPolicyList.length}/
                        {allPolicyList.length}）
                    </div>
                    <div className="auth-box">
                        <div className="auth-ds">
                            {checkedPolicyList.map((v) => {
                                if (!allPolicyMap[v]?.use) {
                                    return (
                                        <div className="auth-item" key={v}>
                                            <span className="checked-name">
                                                {allPolicyMap[v]?.name}
                                            </span>
                                            {!allPolicyMap[v]?.use && (
                                                <Icon
                                                    type="close"
                                                    onClick={() =>
                                                        onClickCheckedItem(v)
                                                    }
                                                />
                                            )}
                                        </div>
                                    );
                                } 
                                    return (
                                        <Tooltip title="该策略已在使用中，不能取消授权">
                                            <div className="auth-item" key={v}>
                                                <img
                                                    src={UseImg}
                                                    className="auth-use-img"
                                                />
                                                <span className="checked-name">
                                                    {allPolicyMap[v]?.name}
                                                </span>
                                            </div>
                                        </Tooltip>
                                    );
                                
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global,
}))(AuthDialog);
