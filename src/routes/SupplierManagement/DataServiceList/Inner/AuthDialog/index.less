.data-server-auth{
  .ant-modal-body{
    padding: 0 16px 16px;
  }

  .auth-main{
    display: flex;
    height: 60vh;

    .auth-l{
      flex: 1;
      height: 100%;
      padding-right: 20px;
      border-right: 1px solid #ddd;

    }

    .auth-r{
      flex: 1;
      height: 100%;
      overflow-y: scroll;
      padding-left: 20px;

      .auth-rt{
        height: 32px;
        margin: 0;
        color: #17233d;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        word-wrap: break-word;
      }
    }

    

    .auth-box{
      overflow-y: scroll;
      height: calc(100% - 32px);
      padding-top: 6px;

      .auth-line{
        margin-bottom: 10px;
        cursor: pointer;
      }
      .auth-ds{
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 30px;

        .auth-item{
          margin-right: 15px;
          margin-bottom: 15px;
          padding: 6px 4px;
          background-color: #d7d7d7;
          border-radius: 4px;
          
          .auth-use-img{
            width: 26px;
            height: 26px;
          }

          .checked-name{
            padding: 0 10px 0 4px;
          }

          .anticon-close{
            cursor: pointer;
          }
        }
      }
    }
  }

  
}