import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Modal, message, Input, Row, Col, Ellipsis } from 'tntd';
import { dataServiceListAPI, pageQueryAPI, systemFieldsAPI } from '@/services';
import { isJSON } from '@/utils/isJSON';
import cloneDeep from 'lodash.clonedeep';

class TestModal extends PureComponent {
    state = {
        loading: false,
        inputConfig: [],
        data: null,
        btnLoading: false,
        outputConfig: [], // 返回字段描述
        allFieldList: []
    };

    componentDidMount() {
        this.getAllFieldList();
    }

    componentDidUpdate(preProps) {
        const preId = preProps.uuid;
        const nextId = this.props.uuid;
        if (preId !== nextId && nextId) {
            this.getDetail(nextId);
        }
    }

    // 获取所有系统字段
    getAllFieldList = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res && res.success) {
                this.setState({
                    allFieldList: res.data ? res.data : []
                });
            }
        });
    };

    // 根据uuid获取数据服务
    getDetail = (uuid) => {
        this.setState({ inputConfig: [] });
        dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success && res.data) {
                const inputConfig = res.data.inputConfig;
                const outputConfig = res.data.outputConfig;
                this.setState({
                    inputConfig: isJSON(inputConfig) ? JSON.parse(inputConfig) : [],
                    outputConfig: isJSON(outputConfig) ? JSON.parse(outputConfig) : []
                });
            } else {
                message.error(res.msg);
            }
        });
    };

    handleCancel = () => {
        this.props.onCancel();
        this.setState({
            loading: false,
            inputConfig: [],
            data: null,
            btnLoading: false,
            outputConfig: [] // 返回字段描述
        });
    };

    // 改变参数
    changeField(e, index) {
        const { inputConfig } = this.state;
        let copyInputConfig = cloneDeep(inputConfig);
        copyInputConfig[index].value = e.target.value;
        if (e.target.value) {
            copyInputConfig[index].errMsg = false;
        }
        this.setState({
            inputConfig: copyInputConfig
        });
    }

    // 查询
    search = () => {
        const { name } = this.props;
        const { inputConfig } = this.state;
        let copyInputConfig = cloneDeep(inputConfig);
        let flag = false;
        copyInputConfig.forEach((item) => {
            if (item.mustInput && !item.value) {
                item.errMsg = true;
                flag = true;
            }
        });
        if (flag) {
            this.setState({
                inputConfig: copyInputConfig
            });
        } else {
            let arr = [];
            copyInputConfig.forEach((item) => {
                if (item.type !== 'constant' && item.value) {
                    arr.push({
                        paramName: item.field,
                        paramValue: item.value
                    });
                }
            });
            let params = {
                inputConfig: JSON.stringify(arr)
            };
            params.serviceName = name;
            params.type = 2;
            this.getThreeServiceSearch(params);
        }
    };

    // 三方服务查询
    getThreeServiceSearch = (params) => {
        this.setState({ btnLoading: true });
        pageQueryAPI
            .getThreeServiceSearch(params, 'third')
            .then((res) => {
                const { outputConfig } = this.state;
                res.outputConfig = outputConfig;
                this.setState({ data: res, btnLoading: false });
            })
            .catch(() => {
                this.setState({ btnLoading: false });
            });
    };

    render() {
        const { loading, inputConfig, data, btnLoading, allFieldList } = this.state;
        const { visible, title } = this.props;

        return (
            <Modal
                title={I18N.template(I18N.testmodal.index.chaXunTIT, { val1: title })} // 查询
                width={500}
                visible={visible}
                maskClosable={false}
                onCancel={this.handleCancel}
                footer={null}
                className="m-data-service-test">
                {inputConfig &&
                    inputConfig.length > 0 &&
                    inputConfig.map((item, index) => {
                        let dName = allFieldList.find((k) => k.name === item.field);
                        let disName = dName ? dName.displayName : item.displayName;
                        return (
                            <div className="box" key={index}>
                                <span className="lable">
                                    {/* {disName} */}
                                    {/* widthLimit={350} */}
                                    <Ellipsis widthLimit={350} prefix={item.mustInput ? <b>*</b> : null} title={disName} />
                                </span>
                                <Input
                                    placeholder={I18N.template(I18N.testmodal.index.qingShuRu, { val1: disName })} // 请输入
                                    value={item.value}
                                    className={item.errMsg ? 'red-border' : ''}
                                    disabled={item.type === 'constant'}
                                    onChange={(e) => this.changeField(e, index)}
                                />
                                {item.errMsg && (
                                    <span className="tip">
                                        {disName}
                                        {/* 必填 */}
                                        {I18N.testmodal.index.biTian}
                                    </span>
                                )}
                            </div>
                        );
                    })}
                <Button type="primary" className="btn" onClick={this.search} loading={btnLoading}>
                    {/* 测试 */}
                    {I18N.testmodal.index.ceShi}
                </Button>
                {data && (
                    <div className="body">
                        <div className="result">
                            <div className="result-header">
                                {/* 结果详情 */}
                                {I18N.testmodal.index.jieGuoXiangQing}
                            </div>
                            <div className="result-body">
                                {I18N.testmodal.index.chaXunJieGuo}
                                {data.success
                                    ? I18N.testmodal.index.chengGong
                                    : I18N.template(I18N.testmodal.index.shiBaiDAT, { val1: data.msg })}
                            </div>
                        </div>
                        <div className="result">
                            <div className="result-header">
                                {/* 返回报文 */}
                                {I18N.testmodal.index.fanHuiBaoWen}
                            </div>
                            <div className="result-body">
                                <pre>{data.data ? JSON.stringify(data.data, null, 4) : []}</pre>
                            </div>
                        </div>
                        <div className="result">
                            <div className="result-header">
                                {/* 返回字段描述 */}
                                {I18N.testmodal.index.fanHuiZiDuanMiao}
                            </div>
                            <div className="result-body">
                                {data.outputConfig &&
                                    data.outputConfig.length > 0 &&
                                    data.outputConfig.map((item, index) => {
                                        return (
                                            <Row key={index}>
                                                <Col span={12}>{item.field}</Col>
                                                <Col span={12}>{item.displayName}</Col>
                                            </Row>
                                        );
                                    })}
                            </div>
                        </div>
                    </div>
                )}
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(TestModal);
