.m-data-service-test{
    .ant-modal-body{
        padding: 24px 60px;
    }
    .box{
        position: relative;
        .lable{
            display: inline-block;
            margin-bottom: 5px;
            b{
                color: #f00;
                vertical-align: middle;
                margin-right: 3px;
            }
        }
        input{
            margin-bottom: 25px;
        }
        .red-border{
            border: #f5222d solid 1px;
        }
        .tip{
            position: absolute;
            left: 0;
            bottom: 5px;
            font-size: 12px;
            color: #f5222d;
        }
    }
    .btn{
        width: 100%;
        margin-bottom: 20px;
    }
    .result{
        margin-bottom: 30px;
        .result-header{
            position: relative;
            font-size: 17px;
            padding-left: 12px;
            margin-bottom: 10px;
            &::before{
                position: absolute;
                left: 0;
                top: 2px;
                width: 3px;
                height: 20px;
                background: #4b93e5;
                content: "";
            }
        }
        .result-body{
            padding: 20px;
            // border: 1px dashed #dcdcdc;
            background: #f5f5f5;
            .ant-row{
                padding: 5px 0;
                border-bottom: 1px #dc<PERSON>a dashed;
                &:last-child{
                    border: none;
                }
                .ant-col-12{
                    word-break: break-all;
                }
            }
        }
    }
}