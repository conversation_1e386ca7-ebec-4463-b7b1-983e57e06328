/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: mock 配置页
 */

import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent, Suspense, Fragment } from 'react';
import { connect } from 'dva';
import {
    Button,
    Table,
    Pagination,
    Select,
    message,
    Input,
    Switch,
    Breadcrumb,
    Divider,
    Popconfirm,
    Popover,
    Tooltip,
    Icon,
    PageContainer
} from 'tntd';
import { dataServiceListAPI } from '@/services';
import { checkFunctionHasPermission } from '@/utils/permission';
import { getUrlKey } from '@/utils/utils';
import AddModifyModal from './Inner/AddModifyModal';
import ImportModal from './Inner/ImportModal';
import ConfigModal from './Inner/ConfigModal';

const Option = Select.Option;
const { TextArea } = Input;
const InputGroup = Input.Group;

class MockConfig extends PureComponent {
    state = {
        loading: false,
        tableList: [],
        total: 0,
        searchParams: {
            curPage: 1,
            pageSize: 10
        },
        mockFlag: '0',
        mockType: '1',
        visible: false,
        importVisible: false,
        inputConfig: [],
        mockField: [],
        mockData: null,
        searchType: null,
        searchValue: null,
        configVisible: false,
        addModifyData: null
    };

    async componentDidMount() {
        await this.getDetail();
        this.getData();
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0202', 'query')) {
                    // this.search();
                }
            }
        }, 100);
    }

    getDetail = async () => {
        const uuid = getUrlKey('uuid');
        await dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success) {
                const data = res.data;
                let inputConfig = data.inputConfig ? JSON.parse(data.inputConfig) : [];
                let copyInputConfig = [];
                inputConfig.forEach((item) => {
                    if (item.type === 'variable') {
                        copyInputConfig.push(item);
                    }
                });
                this.setState({
                    mockFlag: data.mockFlag ? `${data.mockFlag}` : '0',
                    mockType: data.mockType ? `${data.mockType}` : '1',
                    inputConfig: copyInputConfig,
                    mockField: data.mockField ? JSON.parse(data.mockField) : [],
                    searchType: data.inputConfig ? copyInputConfig[0].field : null
                });
            } else {
                message.error(res.message);
            }
        });
    };

    updateMockConfig = (mockFlag, mockType, mockFields, mockData, type) => {
        const uuid = getUrlKey('uuid');
        const params = {
            uuid,
            mockFlag,
            mockType: mockType ? mockType : null,
            mockFields: mockFields ? mockFields : null,
            mockData: mockData ? mockData : null
        };
        dataServiceListAPI.updateMockConfig(params).then((res) => {
            if (res && res.success) {
                if (type) {
                    message.success(I18N.mockconfig.index.baoCunChengGong); // 保存成功
                } else {
                    this.getDetail();
                }
            } else {
                message.error(res.message);
            }
        });
    };

    handleCancel = () => {
        this.setState({
            visible: false,
            importVisible: false,
            configVisible: false
        });
    };

    // 获取列表数据
    getData = (curPage, pageSize) => {
        const uuid = getUrlKey('uuid');
        let { searchParams, searchType, searchValue, mockType } = this.state;
        if (curPage) searchParams.curPage = curPage;
        if (pageSize) searchParams.pageSize = pageSize;
        searchParams.uuid = uuid;
        searchParams.mockType = mockType;
        if (searchValue) {
            let obj = {};
            obj[searchType] = searchValue;
            searchParams.query = JSON.stringify(obj);
        }

        this.setState({ loading: true });
        dataServiceListAPI
            .getMockData({ ...searchParams })
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    if (mockType === '1') {
                        this.setState({
                            mockData: res.data.contents[0] ? res.data.contents[0].dataValue : null
                        });
                    } else {
                        this.setState({
                            total: res.data.total,
                            tableList: res.data.contents,
                            searchParams: {
                                curPage: res.data.curPage,
                                pageSize: res.data.pageSize
                            }
                        });
                    }
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 查询
    search = (curPage, pageSize) => {
        this.getData(curPage, pageSize);
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    async changeField(e, field) {
        const { mockFlag } = this.state;

        let obj = {};
        if (field === 'mockFlag' && !e) {
            obj.mockType = '1';
            obj[field] = '0';
            this.updateMockConfig('0', '1');
        } else if (field === 'mockFlag' && e) {
            obj[field] = '1';
            this.updateMockConfig('1', '1');
        } else if (field === 'mockType') {
            obj[field] = e;
            this.updateMockConfig(mockFlag, e);
        } else if (field === 'searchType') {
            obj[field] = e;
            obj.searchValue = null;
        } else {
            obj[field] = e;
        }
        await this.setState({ ...obj });
        if (field === 'mockType') {
            this.search();
        }
    }

    // 新增
    handleAdd = () => {
        const { inputConfig } = this.state;
        let mockDataInput = [];
        inputConfig.forEach((sItem) => {
            mockDataInput.push({
                name: sItem.field,
                displayName: sItem.displayName,
                value: null
            });
        });
        let addModifyData = {
            mockDataInput,
            mockDataOutput: null,
            type: 1
        };
        this.setState({
            visible: true,
            addModifyData
        });
    };

    // 导入
    handleImport = () => {
        this.setState({ importVisible: true });
    };

    // 保存
    save = () => {
        const { mockData } = this.state;
        this.updateMockConfig(null, null, null, mockData, 'save');
    };

    // 参数配置
    handleConfig = () => {
        this.setState({
            configVisible: true
        });
    };

    confirmDelete(uuid) {
        dataServiceListAPI.deleteMockData({ uuid }).then((res) => {
            if (res && res.success) {
                let { searchParams, tableList } = this.state;
                let { pageSize, curPage } = searchParams;
                if (tableList.length === 1 && curPage > 1) curPage = curPage - 1;
                this.paginationOnChange(curPage, pageSize);
            } else {
                message.error(res.message);
            }
        });
    }

    handleModify(record) {
        const { inputConfig } = this.state;
        let mockDataInput = [];
        inputConfig.forEach((sItem) => {
            mockDataInput.push({
                name: sItem.field,
                displayName: sItem.displayName,
                value: null
            });
        });
        if (record.queryParam) {
            const obj = JSON.parse(record.queryParam);
            for (let key in obj) {
                mockDataInput.forEach((item) => {
                    if (key === item.name) {
                        item.value = obj[key];
                    }
                });
            }
        }

        let addModifyData = {
            mockDataInput,
            mockDataOutput: record.dataValue,
            type: 2,
            mockId: record.uuid
        };
        this.setState({
            visible: true,
            addModifyData
        });
    }

    getColumns = () => {
        const columns = [
            {
                title: I18N.mockconfig.index.ruCanPiPeiZi, // 入参匹配字段
                dataIndex: 'queryParam',
                render: (text) => {
                    let dom;
                    let arr = [];
                    if (text) {
                        let obj;
                        obj = JSON.parse(text);
                        for (let key in obj) {
                            arr.push({
                                name: key,
                                value: obj[key]
                            });
                        }
                    }
                    return (
                        <Fragment>
                            {arr.map((item, index) => {
                                return (
                                    <span key={index}>
                                        {item.name}:{item.value};{' '}
                                    </span>
                                );
                            })}
                        </Fragment>
                    );
                }
            },
            {
                title: I18N.mockconfig.index.chuCanBaoWen, // 出参报文
                dataIndex: 'dataValue',
                render: (text) => {
                    const content = <pre>{text ? JSON.stringify(JSON.parse(text), null, 2) : []}</pre>;
                    return (
                        <Popover
                            content={content}
                            title={I18N.mockconfig.index.chuCanBaoWen} // 出参报文
                            placement="left">
                            <span className="u-ellipsis">{text}</span>
                        </Popover>
                    );
                }
            },
            {
                title: I18N.mockconfig.index.caoZuo, // 操作
                dataIndex: 'operate',
                width: 140,
                render: (text, record) => {
                    return (
                        <Fragment>
                            <a onClick={() => this.handleModify(record)}>{I18N.mockconfig.index.xiuGai}</a>
                            <Divider type="vertical" />
                            <Popconfirm title={I18N.mockconfig.index.queRenShanChuCi} onConfirm={() => this.confirmDelete(record.uuid)}>
                                <a>{I18N.mockconfig.index.shanChu}</a>
                            </Popconfirm>
                        </Fragment>
                    );
                }
            }
        ];
        return columns;
    };

    back = () => {
        this.props.history.push('/handle/supplierManagement/dataServiceList');
    };

    render() {
        const columns = this.getColumns();
        const {
            loading,
            tableList,
            total,
            searchParams,
            mockFlag,
            mockType,
            visible,
            importVisible,
            mockData,
            inputConfig,
            searchType,
            searchValue,
            configVisible,
            mockField,
            addModifyData
        } = this.state;

        const tipContent = (
            <div>
                <div>{I18N.mockconfig.index.mOCKNei2}</div>
                <div>{I18N.mockconfig.index.mOCKWai2}</div>
            </div>
        );

        return (
            <div className="g-mock-config">
                <div className="page-global-header">
                    <div className="left-info">
                        <Breadcrumb separator=">" className="bread-container">
                            <Breadcrumb.Item className="u-link" onClick={this.back}>
                                {/* 三方数据列表 */}
                                {/* {'数据源服务接口列表'} */}
                                <Icon type="left" />
                                {I18N.mockconfig.index.fanHui}
                            </Breadcrumb.Item>
                            {/* <Breadcrumb.Item> */}
                            {/* mock 配置 */}
                            {/* {I18N.mockconfig.index.mOCKPei}
                            </Breadcrumb.Item> */}
                        </Breadcrumb>
                    </div>
                </div>
                <div className="page-global-body">
                    <div className="page-global-body-search">
                        <div className="left-info">
                            <div className="left-info-item">
                                {I18N.mockconfig.index.shiFouKaiQiM}：
                                <Switch
                                    checkedChildren={I18N.mockconfig.index.shi} // 是
                                    unCheckedChildren={I18N.mockconfig.index.fou} // 否
                                    checked={mockFlag === '1'}
                                    onChange={(e) => this.changeField(e, 'mockFlag')}
                                />
                                <Select
                                    style={{ width: 140, marginLeft: '10px' }}
                                    dropdownMatchSelectWidth={false}
                                    value={mockType ? mockType : undefined}
                                    onChange={(e) => this.changeField(e, 'mockType')}>
                                    <Option value="1">{I18N.mockconfig.index.mOCKWai}</Option>
                                    <Option value="2">{I18N.mockconfig.index.mOCKNei}</Option>
                                </Select>
                                <Tooltip title={tipContent}>
                                    <Icon type="question-circle" className="u-tip" />
                                </Tooltip>
                                {/* {
									mockType === "2" &&
									<Button className="ml10" onClick={() => this.handleConfig()}>
										{'Mock参数配置'}
									</Button>
								} */}
                            </div>
                        </div>
                        {mockType === '2' && (
                            <div className="right-info">
                                <div className="right-info-item">
                                    <InputGroup compact>
                                        <Select
                                            value={searchType}
                                            dropdownMatchSelectWidth={false}
                                            dropdownStyle={{ width: 350 }}
                                            onChange={(e) => this.changeField(e, 'searchType')}>
                                            {inputConfig &&
                                                inputConfig.map((item, index) => {
                                                    return (
                                                        <Option value={item.field} key={index}>
                                                            {item.field}
                                                        </Option>
                                                    );
                                                })}
                                        </Select>
                                        <Input
                                            style={{ width: '160px' }}
                                            placeholder={I18N.mockconfig.index.qingShuRu} // 请输入
                                            value={searchValue}
                                            onChange={(e) => this.changeField(e.target.value, 'searchValue')}
                                        />
                                    </InputGroup>
                                </div>
                                <div className="right-info-item">
                                    <Button type="primary" onClick={() => this.search()}>
                                        {/* 搜索 */}
                                        {I18N.mockconfig.index.souSuo}
                                    </Button>
                                </div>
                                <div className="right-info-item">
                                    <Button type="primary" onClick={this.handleAdd}>
                                        {/* 新增 */}
                                        {I18N.mockconfig.index.xinZeng}
                                    </Button>
                                </div>
                                <div className="right-info-item">
                                    <Button type="primary" onClick={this.handleImport}>
                                        {/* 导入 */}
                                        {I18N.mockconfig.index.daoRu}
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                    {mockType === '1' && (
                        <div>
                            <TextArea rows={16} value={mockData} onChange={(e) => this.changeField(e.target.value, 'mockData')} />
                            <Button type="primary" className="mt10" onClick={this.save}>
                                {/* 保存 */}
                                {I18N.mockconfig.index.baoCun}
                            </Button>
                        </div>
                    )}
                    {mockType === '2' && (
                        <div className="page-global-body-main">
                            <Table
                                size="middle"
                                rowKey={(record) => record.uuid}
                                className="table-card-body"
                                columns={columns}
                                dataSource={tableList}
                                pagination={false}
                                loading={loading}
                            />
                            <div className="page-global-body-pagination">
                                <span className="ml20">{I18N.template(I18N.mockconfig.index.gongTOTA, { val1: total })}</span>
                                <Pagination
                                    showSizeChanger
                                    showQuickJumper
                                    current={searchParams.curPage}
                                    pageSize={searchParams.pageSize}
                                    total={total}
                                    onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                />
                            </div>
                        </div>
                    )}
                </div>
                <AddModifyModal
                    addModifyData={addModifyData}
                    visible={visible}
                    onCancel={this.handleCancel}
                    onRefresh={() => this.search()}
                />
                <ImportModal visible={importVisible} onCancel={this.handleCancel} onRefresh={() => this.search()} />
                <ConfigModal
                    inputConfig={inputConfig}
                    mockField={mockField}
                    visible={configVisible}
                    onCancel={this.handleCancel}
                    onRefresh={() => this.getDetail()}
                />
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(PageContainer(MockConfig));
