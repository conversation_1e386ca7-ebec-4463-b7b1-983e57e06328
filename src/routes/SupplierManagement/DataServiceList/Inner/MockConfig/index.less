.g-mock-config{
    .bread-container{
        line-height: 40px;
        .u-link{
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
            cursor: pointer;
            &:hover{
                color: #03A9F4;
            }
        }
    }
    .u-ellipsis{
        display: inline-block;
        max-width: 300px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
    }
    .u-tip{
        font-size: 18px;
        vertical-align: middle;
        margin-left: 10px;
        margin-top: -2px;
        cursor: pointer;
    }
}

.m-mock-config-dialog{
    .title{
        border-left: #f90 solid 4px;
        padding-left: 10px;
        margin-bottom: 10px;
        b{
            color: red;
            vertical-align: middle;
            margin-right: 2px;
        }
    }
    .m-ul{
        margin: 0;
        padding: 0px 15px 15px 15px;
        li{
            list-style: none;
            margin-bottom: 10px;
            .u-label{
                display: block;
            }
            .u-width{
                width: 100%;
            }
        }
    }
    .u-textarea{
        padding: 0 15px;
    }
    .box{
        padding: 10px;
        label{
            display: inline-block;
            width: 150px;
            text-align: right;
        }
        .s1{
            cursor: pointer;
            color: #03A9F4;
        }
    }
}
