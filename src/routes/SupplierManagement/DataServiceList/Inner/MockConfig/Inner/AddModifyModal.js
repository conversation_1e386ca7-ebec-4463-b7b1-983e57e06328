/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: Modal弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, Input, message } from 'tntd';
import { dataServiceListAPI } from '@/services';
import { isJSON } from '@/utils/isJSON';
import { getUrlKey } from '@/utils/utils';

const { TextArea } = Input;

class AddModifyModal extends PureComponent {
	state = {
		loading: false,
		mockDataInput: [],
		mockDataOutput: null,
		type: 1
	}

	componentDidUpdate(preProps) {
		const preVisible = preProps.visible;
		const nextVisible = this.props.visible;
		if (preVisible !== nextVisible && nextVisible) {
			const { addModifyData } = this.props;
			this.setState({
				...addModifyData
			});
		}
	}

	afterClose = () => {
		this.setState({
			loading: false,
			mockDataInput: [],
			mockDataOutput: null,
			type: 1
		});
	}

	handleOk = () => {
		const uuid = getUrlKey('uuid');
		const { mockDataInput, mockDataOutput, type, mockId } = this.state;

		if (!mockDataOutput) return message.warning(I18N.inner.addmodifymodal.baoWenBuNengWei); // 报文不能为空
		if (mockDataOutput && !isJSON(mockDataOutput)) return message.warning(I18N.inner.addmodifymodal.qingShuRuZhengQue); // 请输入正确的JSON报文
		let obj = {};
		mockDataInput.forEach(item => {
			if (item.value) {
				obj[item.name] = item.value;
			}
		});

		const params = {
			uuid,
			mockDataInput: JSON.stringify(obj),
			mockDataOutput
		};
		this.setState({ loading: true });
		if (type === 1) { // 新增
			dataServiceListAPI.addMockData(params).then(res => {
				this.setState({ loading: false });
				if (res && res.success) {
					message.success(I18N.inner.addmodifymodal.caoZuoChengGong); // 操作成功
					this.props.onCancel();
					this.props.onRefresh();
				} else {
					message.error(res.message);
				}
			}).catch(() => {
				this.setState({ loading: false });
			});
		} else {
			params.uuid = mockId;
			dataServiceListAPI.updateMockData(params).then(res => {
				this.setState({ loading: false });
				if (res && res.success) {
					message.success(I18N.inner.addmodifymodal.caoZuoChengGong); // 操作成功
					this.props.onCancel();
					this.props.onRefresh();
				} else {
					message.error(res.message);
				}
			}).catch(() => {
				this.setState({ loading: false });
			});
		}
	}

	changeField(e, field) {
		let obj = {};
		obj[field] = e;
		this.setState({
			...obj
		});
	}

	changeInputField(e, i) {
		const { mockDataInput } = this.state;
		let copyMockDataInput = Object.assign([], mockDataInput);
		copyMockDataInput[i].value = e;
		this.setState({
			mockDataInput: copyMockDataInput
		});
	}

	render() {
		const { mockDataInput, mockDataOutput, type, loading } = this.state;
		const { visible } = this.props;

		let title = I18N.inner.addmodifymodal.xinZengMOC; // 新增mock数据
		if (type === 2) title = I18N.inner.addmodifymodal.xiuGaiMOC; // 修改mock数据

		return (
			<Modal
				title={title}
				visible={visible}
				maskClosable={false}
				confirmLoading={loading}
				onOk={this.handleOk}
				onCancel={() => this.props.onCancel()}
				afterClose={this.afterClose}
				className="m-mock-config-dialog"
			>
				<p className="title">
					{/* 匹配字段 */}
					{I18N.inner.addmodifymodal.piPeiZiDuan}
				</p>
				<ul className="m-ul">
					{
						mockDataInput &&
						mockDataInput.map((item, index) => {
							return (
								<li key={index}>
									<span className="u-label">{item.name}：</span>
									<Input
										className="u-width"
										value={item.value}
										onChange={(e) => this.changeInputField(e.target.value, index)}
									/>
								</li>
							);
						})
					}
				</ul>
				<p className="title">
					<b>*</b>
					{/* 出参报文 */}
					{I18N.inner.addmodifymodal.chuCanBaoWen}
				</p>
				<div className="u-textarea">
					<TextArea
						rows={5}
						value={mockDataOutput}
						onChange={(e) => this.changeField(e.target.value, 'mockDataOutput')}
					/>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(AddModifyModal);
