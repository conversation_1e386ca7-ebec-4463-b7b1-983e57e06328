import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment } from 'react';
import { Select, Icon, InputNumber } from 'tntd';

const Option = Select.Option;

class FlowLimit extends PureComponent {
    state = {
        dateList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
        flowLimitList: []
    };

    componentDidMount() {
        const globalStore = this.props.globalStore;
        const { allMap } = globalStore;
        this.setState({
            flowLimitList: allMap ? allMap.flowLimitList : []
        });
    }

    componentDidUpdate(preProps) {
        const preAllMap = preProps.globalStore.allMap;
        const nextAllMap = this.props.globalStore.allMap;
        if (preAllMap !== nextAllMap) {
            this.setState({
                flowLimitList: nextAllMap ? nextAllMap.flowLimitList : []
            });
        }
    }

    add = () => {
        const { value, onChange } = this.props;
        let newValue = Object.assign([], value);
        newValue.push({
            type: null,
            limit: null
        });
        onChange(newValue);
    };

    delete = (i) => {
        const { value, onChange } = this.props;
        let newValue = Object.assign([], value);

        newValue.splice(i, 1);
        onChange(newValue);
    };

    changeField(e, field, i) {
        const { value, onChange } = this.props;
        let newValue = Object.assign([], value);

        newValue[i][field] = e;
        onChange(newValue);
    }

    focus = () => {
        const { flowLimitList } = this.state;
        const { value } = this.props;
        let newValue = Object.assign([], value);

        let copyList = Object.assign([], flowLimitList);

        copyList.forEach((item) => {
            item.disabled = false;
        });
        copyList.forEach((item) => {
            newValue.forEach((sItem) => {
                if (item.type === sItem.type && item.type !== 5) {
                    item.disabled = true;
                }
            });
        });

        this.setState({ flowLimitList: copyList });
    };

    render() {
        const { dateList, flowLimitList } = this.state;
        const { value, disabled } = this.props;

        return (
            <Fragment>
                {value &&
                    value.map((item, index) => {
                        return (
                            <div key={index} className="m-flow-limit">
                                <Select
                                    showSearch
                                    style={{ width: '140px' }}
                                    disabled={disabled}
                                    optionFilterProp="children"
                                    placeholder={I18N.addmodify.flowlimit.qingXuanZe}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 200 }}
                                    allowClear={true}
                                    value={item.type ? item.type : undefined}
                                    onChange={(e) => this.changeField(e, 'type', index)}
                                    onFocus={this.focus}>
                                    {flowLimitList &&
                                        flowLimitList.map((item, index) => {
                                            return (
                                                <Option value={item.type} key={index} disabled={item.disabled}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                                {item.type === 5 && (
                                    <Fragment>
                                        <Select
                                            showSearch
                                            disabled={disabled}
                                            placeholder={I18N.addmodify.flowlimit.qiShi}
                                            optionFilterProp="children"
                                            value={item.begin || item.begin === 0 ? item.begin : undefined}
                                            onChange={(e) => this.changeField(e, 'begin', index)}>
                                            {dateList.map((item, index) => {
                                                return (
                                                    <Option value={item} key={index}>
                                                        {item}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                        -
                                        <Select
                                            showSearch
                                            disabled={disabled}
                                            placeholder={I18N.addmodify.flowlimit.jieShu}
                                            style={{ marginLeft: '10px' }}
                                            optionFilterProp="children"
                                            value={item.end || item.end === 0 ? item.end : undefined}
                                            onChange={(e) => this.changeField(e, 'end', index)}>
                                            {dateList.map((item, index) => {
                                                return (
                                                    <Option value={item} key={index}>
                                                        {item}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </Fragment>
                                )}
                                <InputNumber
                                    style={{ width: 100 }}
                                    min={0}
                                    max={10000000000}
                                    disabled={disabled}
                                    placeholder={I18N.addmodify.flowlimit.shuRuZhi}
                                    value={item.limit}
                                    onChange={(e) => this.changeField(e, 'limit', index)}
                                />
                                {value.length === index + 1 && !disabled && <Icon type="plus-circle" onClick={this.add} />}
                                {value.length !== 1 && !disabled && <Icon type="delete" onClick={() => this.delete(index)} />}
                            </div>
                        );
                    })}
            </Fragment>
        );
    }
}

export default FlowLimit;
