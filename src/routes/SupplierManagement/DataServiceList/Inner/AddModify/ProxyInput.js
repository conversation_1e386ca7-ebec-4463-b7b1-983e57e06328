import I18N from '@/utils/I18N';
import { Input, Select } from 'tntd';
const Option = Select.Option;

export default (props) => {
    const { value, onChange, disabled, current } = props;

    let proxy = current === 1 ? value?.proxy : value?.asyncProxy;
    let proxyInfo = current === 1 ? value?.proxyInfo : value?.asyncProxyInfo;

    const onValueChange = (value) => {
        const { proxy, proxyInfo } = value;
        if (current === 1) {
            onChange({ proxy, proxyInfo });
        } else {
            onChange({ asyncProxy: proxy, asyncProxyInfo: proxyInfo });
        }
    };

    return (
        <div className="proxy-info">
            <Select
                showSearch
                style={proxy === '1' ? { width: '94px' } : { width: '100%' }}
                disabled={disabled}
                placeholder={I18N.addmodify.proxyinput.qingXuanZeShiFou}
                dropdownMatchSelectWidth={false}
                optionFilterProp="children"
                value={proxy ? proxy : undefined}
                onChange={(e) => {
                    onValueChange({ proxy: e, proxyInfo: '' });
                }}>
                <Option value={'0'}>{I18N.addmodify.proxyinput.fou}</Option>
                <Option value={'1'}>{I18N.addmodify.proxyinput.shi}</Option>
            </Select>
            {proxy === '1' && (
                <Input
                    disabled={disabled}
                    style={{ width: 'calc(100% - 94px)' }}
                    placeholder={I18N.addmodify.proxyinput.qingShuRuWangLuo}
                    value={proxyInfo}
                    onChange={(e) => {
                        onValueChange({ proxy: '1', proxyInfo: e.target.value });
                    }}
                />
            )}
        </div>
    );
};
