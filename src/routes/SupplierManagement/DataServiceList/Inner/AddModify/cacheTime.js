import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment } from 'react';
import { Select, Switch, InputNumber } from 'tntd';
import { cloneDeep } from 'lodash';
// import service from './service';
// import './index.less';

const Option = Select.Option;

export default (props) => {
    const { value = {}, onChange, disabled } = props;

    const { cacheOpen = 0, cacheday = 7, cacheInfrastructureType = 'nosql' } = value || {};

    const setting = (val, field, type) => {
        const newValues = cloneDeep(value || {});
        if (type === 'inputNumber') {
            const reRealNumber1 = /^\d+$/; // 正整数
            if (!val || !reRealNumber1.test(val)) return;
            if (val > 1000) return;
            newValues[field] = val;
        }
        newValues[field] = val;
        onChange(newValues);
    };

    return (
        <div className="cache-time">
            <Switch
                className="u-checked"
                checkedChildren={I18N.addmodify.cachetime.kai} // 开
                unCheckedChildren={I18N.addmodify.cachetime.guan} // 关
                checked={!!cacheOpen}
                disabled={disabled}
                onChange={(e) => {
                    setting(e ? 1 : 0, 'cacheOpen', 'select');
                }}
            />
            <div style={{ display: `${cacheOpen === 1 ? 'inline-block' : 'none'}`, verticalAlign: 'middle' }}>
                <InputNumber
                    disabled={disabled}
                    style={{
                        width: '65px',
                        marginLeft: '5px'
                    }}
                    min={1}
                    max={1000}
                    step={1}
                    precision={0}
                    value={cacheday}
                    onChange={(e) => setting(e, 'cacheday', 'inputNumber')}
                />{' '}
                {I18N.addmodify.cachetime.tian}{' '}
                <Select
                    disabled={disabled}
                    value={cacheInfrastructureType || 'nosql'}
                    style={{ width: 160 }}
                    onChange={(e) => setting(e, 'cacheInfrastructureType', 'select')}>
                    <Option value="nosql">{I18N.addmodify.cachetime.huanCun}</Option>
                    <Option value="elasticsearch">{I18N.addmodify.cachetime.chiJiuHuaKu}</Option>
                </Select>
            </div>
        </div>
    );
};
