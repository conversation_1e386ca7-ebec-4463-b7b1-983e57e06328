import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import { Form, Icon, message, PageContainer } from 'tntd';
import { connect } from 'dva';
import { parse } from 'query-string';

import classNames from 'classnames';

import { dataServiceListAPI, systemFieldsAPI, supplierListAPI, captainAPI } from '@/services';
import etlAPI from '../../../Etl/services';

import './index.less';

import Step1 from './FormList/Step1';
import Step2 from './FormList/Step2';
import Step3 from './FormList/Step3';

const AddModifyModal = connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))((props) => {
    const { location, globalStore } = props;

    const { modalType, dataSourceType, uuid } = useMemo(() => {
        const { search } = location;
        let state;

        if (search) {
            state = parse(search);
            state.modalType = Number(state.modalType);
        }

        return {
            modalType: state.modalType || 1,
            dataSourceType: state?.dataSourceType || 'SYNC',
            uuid: state?.uuid
        };
    }, [location]);

    const [addEditModalData, setAddEditModalData] = useState({});
    const [current, setCurrent] = useState(0);
    const [systemList, setSystemList] = useState([]); // 字段列表
    const [documentTypeList, setDocumentTypeList] = useState([]); // 报文列表
    const [providerList, setProviderList] = useState([]); // 供应商列表
    const [etlList, setEtlList] = useState([]); // ETL列表

    useEffect(() => {
        initList();
    }, []);

    useEffect(() => {
        if ([2, 3].includes(modalType)) {
            getDetail(uuid);
        }
    }, []);

    const back = () => {
        props.history.push('/handle/supplierManagement/dataServiceList');
    };

    // 根据ID获取数据服务
    const getDetail = (uuid) => {
        dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success && res.data) {
                const {
                    uuid,
                    partnerId,
                    dataType,
                    displayName,
                    confidence,
                    costLevel,
                    contractId,
                    chargeMethod,
                    cacheday,
                    cacheOpen,
                    dbPassword,
                    dbUserName,
                    cacheInfrastructureType,
                    invokePolicy,
                    limitConfig,
                    retry,
                    timeout,
                    name,
                    methodType,
                    url,
                    contentType,
                    postEtlHandlerName,
                    preEtlHandlerName,
                    inputTemplate,
                    outputTemplate,
                    inputConfig,
                    outputConfig,
                    proxy,
                    proxyInfo,
                    indexPackageName,
                    derivedIndexPackageName,
                    documentTypeUuid,
                    pagination,

                    // 异步接口相关数据
                    asyncPollTimeInterval,
                    asyncPollMaxCount,
                    asyncMethodType,
                    asyncUrl,
                    asyncContentType,
                    asyncProxy,
                    asyncProxyInfo,
                    asyncPreEtlHandlerName,
                    asyncPostEtlHandlerName,
                    asyncInputTemplate,
                    asyncOutputTemplate,
                    asyncInputConfig,
                    asyncOutputConfig
                } = res.data;
                const { globalStore } = props;
                const { allMap } = globalStore;

                let copyLimitConfig = limitConfig ? JSON.parse(limitConfig) : [];
                if (copyLimitConfig.length === 0) {
                    copyLimitConfig = [{ type: null, limit: null }];
                }

                let copyInputConfig = inputConfig ? JSON.parse(inputConfig) : [];
                copyInputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyAsyncInputConfig = asyncInputConfig ? JSON.parse(asyncInputConfig) : [];
                copyAsyncInputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyOutputConfig = outputConfig ? JSON.parse(outputConfig) : [];
                copyOutputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyAsyncOutputConfig = asyncOutputConfig ? JSON.parse(asyncOutputConfig) : [];
                copyAsyncOutputConfig.forEach((item, index) => {
                    item.uuid = index;
                });

                setAddEditModalData({
                    uuid,
                    partnerId, // 供应商名称
                    dataType, // 数据类型
                    displayName, // 三方服务接口名称
                    name, // 三方数据标识
                    confidence, // 置信度
                    costLevel, // 成本等级
                    contractId, // 合同
                    chargeMethod, // 计费类型
                    cacheday, // 数据缓存期（天）
                    cacheInfrastructureType,
                    dbPassword, // 数据库密码
                    dbUserName, // 数据库用户名
                    retry, // 数据重试次数
                    timeout, // 数据超时时间（ms）
                    cacheOpen, // 查询方式（old） 1缓存/0实时查询
                    invokePolicy, // 查询方式
                    limitConfig: copyLimitConfig,
                    methodType, // 接口类型（协议）
                    url, // url地址
                    contentType, // 调用方式（Method）
                    postEtlHandlerName, // 后置ETL处理器
                    preEtlHandlerName, // 前置ETL处理器
                    inputTemplate, // 输入处理器模板
                    outputTemplate, // 输出处理器模板
                    pagination, // 是否分页
                    inputConfig: copyInputConfig, // 服务入参
                    outputConfig: copyOutputConfig, // 服务出参
                    proxys: {
                        proxy: proxy ? JSON.stringify(proxy) : '0',
                        proxyInfo: proxyInfo ? proxyInfo : allMap ? allMap.proxyInfo : null
                    },
                    indexPackageName: indexPackageName ? indexPackageName?.split(',') : [],
                    documentTypeUuid,
                    derivedIndexPackageName,

                    // 异步接口相关数据
                    asyncPollTimeInterval, // 轮询时间间隔
                    asyncPollMaxCount, // 最大轮询次数
                    asyncMethodType, // async 接口类型（协议）
                    asyncUrl, // async url地址
                    asyncContentType, // async 调用方式（Method）
                    asyncProxys: {
                        asyncProxy: asyncProxy ? JSON.stringify(asyncProxy) : '0', // async 是否使用代理
                        asyncProxyInfo
                    },
                    asyncPreEtlHandlerName, // async 前置ETL处理器
                    asyncPostEtlHandlerName, // async 后置ETL处理器
                    asyncInputTemplate, // async 输入处理器模板
                    asyncOutputTemplate, // async 输出处理器模板
                    asyncInputConfig: copyAsyncInputConfig, // async 服务入参
                    asyncOutputConfig: copyAsyncOutputConfig // async 服务出参
                });
            } else {
                message.error(res.message);
            }
        });
    };

    const initList = async () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res.success) {
                setSystemList(res.data);
            }
        });
        captainAPI.getDocumentTypeList().then((res) => {
            if (res?.data) {
                setDocumentTypeList(res.data);
            }
        });
        supplierListAPI.getList().then((res) => {
            if (res.success) {
                setProviderList(res?.data?.contents || []);
            }
        });
        etlAPI.getEtlList().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setEtlList(res.data);
            } else {
                message.error(res.message);
            }
        });
    };

    const isSync = dataSourceType === 'SYNC';
    const title = modalType === 1 ? I18N.addmodify.index.xinZeng : I18N.addmodify.index.xiuGai;

    let steps = [
        { title: I18N.addmodify.index.diYiBuTianXie },
        { title: isSync ? I18N.addmodify.index.diErBuTianXie2 : I18N.addmodify.index.diErBuTianXie }
    ];
    if (!isSync) {
        steps.push({ title: I18N.addmodify.index.diSanBuTianXie });
    }

    let disabled = modalType === 3;

    return (
        <div className="g-dataservice-addmodify">
            <div className="page-global-header">
                <div className="left-info">
                    <span className="u-back" onClick={back}>
                        <Icon type="left" />
                        {I18N.addmodify.index.fanHui}
                    </span>
                    {/* <span className="u-title">
                        {title}
                        {I18N.addmodify.index.shuJuYuanFuWu}</span> */}
                </div>
            </div>
            <div className="page-global-body">
                <div className="page-global-body-main">
                    <div className="steps">
                        {steps.map((item, index) => {
                            return (
                                <div
                                    key={index}
                                    className={classNames('step', {
                                        'step-active': current === index,
                                        'last-step': current === steps.length - 1
                                    })}>
                                    {item.title}
                                    <span>
                                        <i />
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                    <div className="wrap-box">
                        <Form layout="vertical">
                            {/* 同步接口第一步内容 */}
                            {current === 0 ? (
                                <Step1
                                    value={addEditModalData}
                                    onChange={(value) => {
                                        setAddEditModalData({
                                            ...addEditModalData,
                                            ...value
                                        });
                                    }}
                                    globalStore={globalStore}
                                    current={current}
                                    setCurrent={setCurrent}
                                    modalType={modalType}
                                    disabled={disabled}
                                    systemList={systemList}
                                    documentTypeList={documentTypeList}
                                    providerList={providerList}
                                />
                            ) : null}
                            {/* 同步接口第二步内容 */}
                            {current === 1 ? (
                                <Step2
                                    value={addEditModalData}
                                    onChange={(value) => {
                                        setAddEditModalData({
                                            ...addEditModalData,
                                            ...value
                                        });
                                    }}
                                    globalStore={globalStore}
                                    history={props.history}
                                    current={current}
                                    setCurrent={setCurrent}
                                    disabled={disabled}
                                    modalType={modalType}
                                    etlList={etlList}
                                    systemList={systemList}
                                    isSync={isSync}
                                />
                            ) : null}

                            {/* 异步接口第三步内容 */}
                            {current === 2 ? (
                                <Step3
                                    value={addEditModalData}
                                    onChange={(value) => {
                                        setAddEditModalData({
                                            ...addEditModalData,
                                            ...value
                                        });
                                    }}
                                    globalStore={globalStore}
                                    history={props.history}
                                    current={current}
                                    setCurrent={setCurrent}
                                    disabled={disabled}
                                    modalType={modalType}
                                    etlList={etlList}
                                    systemList={systemList}
                                    isSync={isSync}
                                />
                            ) : null}
                        </Form>
                    </div>
                </div>
            </div>
        </div>
    );
});

export default PageContainer(AddModifyModal);
