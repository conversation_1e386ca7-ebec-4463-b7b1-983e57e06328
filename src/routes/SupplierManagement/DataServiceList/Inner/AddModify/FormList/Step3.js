import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import { Input, Select, Form, Button, InputNumber, Icon, Tooltip, message, Ellipsis } from 'tntd';
import { connect } from 'dva';

import { captainAPI, dataServiceListAPI } from '@/services';

import ProxyInput from '../ProxyInput';
import ServiceInput from '../ServiceInput';
import ServiceOutput from '../ServiceOutput';

import { callMethodControl } from '../constant';

const Option = Select.Option;

const TextArea = Input.TextArea;

const GetFormItemList = Form.create({ name: 'form2' })((props) => {
    let { value, onChange, form, current, setCurrent, modalType, disabled, etlList = [], systemList = [], globalStore, isSync } = props;

    const { getFieldDecorator, setFieldsValue, getFieldsValue, validateFields } = form;
    const { allMap } = globalStore;
    const { isIntegrationTG, captainEnable } = allMap;
    const { asyncMethodType, documentTypeUuid, asyncPostEtlHandlerName, asyncContentType } = getFieldsValue();

    const [featureSetList, setFeatureSetList] = useState([]);
    const [documentTypeList, setDocumentTypeList] = useState([]);

    useEffect(() => {
        if (current === 2) {
            const {
                asyncMethodType,
                asyncUrl,
                asyncContentType,
                asyncPostEtlHandlerName,
                asyncPreEtlHandlerName,
                asyncInputConfig,
                asyncOutputConfig,
                asyncProxys,
                indexPackageName,
                documentTypeUuid,
                asyncPollTimeInterval,
                asyncPollMaxCount,
                asyncOutputTemplate,
                asyncInputTemplate
            } = value || {};

            getDocumentTypeList();

            setFieldsValue({
                asyncMethodType,
                asyncUrl,
                asyncContentType,
                asyncPostEtlHandlerName,
                asyncPreEtlHandlerName: asyncPreEtlHandlerName || undefined,
                asyncInputConfig,
                asyncOutputConfig,
                asyncProxys,
                indexPackageName,
                documentTypeUuid: documentTypeUuid || undefined,
                asyncPollTimeInterval,
                asyncPollMaxCount,
                asyncOutputTemplate,
                asyncInputTemplate
            });
        }
    }, [current, value]);

    useEffect(() => {
        if (documentTypeUuid) {
            setFeatureSetList([]);
            getFeatureSetList(documentTypeUuid);
        }
    }, [documentTypeUuid]);

    // 获取特征集列表
    const getFeatureSetList = (documentTypeUuid) => {
        if (!documentTypeUuid) {
            setFeatureSetList([]);
            return;
        }
        const params = {
            documentTypeUuid
        };
        captainAPI.getIndexPackage(params).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setFeatureSetList(res.data);
            } else {
                message.error(res.message);
            }
        });
    };

    const getDocumentTypeList = () => {
        captainAPI.getDocumentTypeList().then((res) => {
            if (res?.data) {
                setDocumentTypeList(res.data);
            }
        });
    };

    // 接口类型选择，联动调用方式list
    let methodList = [];
    if (asyncMethodType && allMap && allMap.contentTypeList && allMap.methodTypeList) {
        const obj = allMap.methodTypeList.find((item) => item.code === asyncMethodType);
        if (obj) {
            allMap.contentTypeList.forEach((item) => {
                if (item.type === obj.type) {
                    methodList.push(item);
                }
            });
        }
    }

    const onMethodTypeChange = () => {
        setFieldsValue({
            asyncUrl: undefined,
            asyncContentType: undefined
        });
    };

    // 数据源服务接口信息
    const defaultFormList1 = [
        {
            name: 'asyncMethodType',
            label: I18N.formlist.step3.jieKouLeiXingXie,
            options: {
                initialValue: undefined,
                rules: [{ required: true, message: I18N.formlist.step3.qingXuanZeJieKou }],
                onChange: (e) => {
                    onMethodTypeChange(e);
                }
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step3.qingXuanZeJieKou}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children">
                    {allMap &&
                        allMap.methodTypeList &&
                        allMap.methodTypeList
                            .filter((i) => {
                                return !['mysql', 'oracle', 'postgreSQL'].includes(i.code);
                            })
                            .map((item, index) => {
                                return (
                                    <Option value={item.code} key={index}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                </Select>
            )
        },
        {
            name: 'asyncUrl',
            label: I18N.formlist.step3.uRLDiZhi,
            options: {
                initialValue: undefined,
                rules: [
                    { required: true, message: I18N.formlist.step3.qingTianXieUR },
                    {
                        validator: (rule, value, callback) => {
                            let { asyncMethodType } = getFieldsValue() || {};
                            if (!!value && asyncMethodType === 'socket' && !/^(([0-9]{1,3}\.){3}[0-9]{1,3})(:[0-9]{1,5})?$/.test(value)) {
                                return callback(new Error(I18N.formlist.step3.sOCKE));
                            } else if (
                                !!value &&
                                asyncMethodType !== 'socket' &&
                                !/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/.test(
                                    value
                                )
                            ) {
                                return callback(new Error(I18N.formlist.step3.uRLGeShi));
                            }
                            callback();
                        }
                    }
                ]
            },
            component: <Input placeholder={I18N.formlist.step3.qingTianXieUR} disabled={disabled} />
        },
        {
            name: 'asyncContentType',
            label: I18N.formlist.step3.diaoYongFangShi,
            options: {
                initialValue: undefined,
                rules: [{ required: true, message: I18N.formlist.step3.qingXuanZeDiaoYong }]
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step3.qingXuanZeDiaoYong}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children">
                    {methodList.map((item, index) => {
                        return (
                            <Option value={item.code} key={index}>
                                {item.code}
                            </Option>
                        );
                    })}
                </Select>
            )
        },
        {
            name: 'asyncProxys',
            label: I18N.formlist.step3.shiFouShiYongDai,
            options: {
                initialValue: undefined,
                rules: [{ required: true, message: I18N.formlist.step3.qingTianXieWanZheng }]
            },
            component: <ProxyInput disabled={disabled} isSync={false} current={current} />
        },
        {
            name: 'asyncPreEtlHandlerName',
            label: I18N.formlist.step3.qianZhiETL,
            options: {
                initialValue: undefined
            },
            component: (
                <Select
                    showSearch
                    allowClear
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step3.qingXuanZeET}
                    optionFilterProp="children">
                    {etlList &&
                        etlList.map((item, index) => {
                            let dom = null;
                            if (item.type === 2) {
                                dom = (
                                    <Option value={item.name} key={index}>
                                        <Ellipsis title={item.displayName} />
                                    </Option>
                                );
                            }
                            return dom;
                        })}
                </Select>
            )
        },
        {
            name: 'asyncPostEtlHandlerName',
            label: I18N.formlist.step3.houZhiETL,
            options: {
                rules: [{ required: true, message: I18N.formlist.step3.qingXuanZeET }]
            },
            component: (
                <Select
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step3.qingXuanZeET}
                    optionFilterProp="children">
                    {etlList &&
                        etlList.map((item, index) => {
                            let dom = null;
                            if (item.type === 1 || item.type === 3) {
                                dom = (
                                    <Option value={item.name} key={index}>
                                        {item.displayName}
                                    </Option>
                                );
                            }
                            return dom;
                        })}
                </Select>
            )
        },
        {
            name: 'asyncPollTimeInterval',
            label: (
                <>
                    <span>{I18N.formlist.step3.lunXunShiJianJian}</span>
                    <Tooltip title={I18N.formlist.step3.diErBuQingQiu2}>
                        <Icon type="question-circle" />
                    </Tooltip>
                </>
            ),
            options: {},
            component: (
                <InputNumber
                    style={{ width: '100%' }}
                    min={1}
                    step={1}
                    precision={0}
                    placeholder={I18N.formlist.step3.moRenMS}
                    disabled={disabled}
                />
            )
        },
        {
            name: 'asyncPollMaxCount',
            label: (
                <>
                    <span>{I18N.formlist.step3.zuiDaLunXunCi}</span>
                    <Tooltip title={I18N.formlist.step3.diErBuQingQiu}>
                        <Icon type="question-circle" />
                    </Tooltip>
                </>
            ),
            options: {},
            component: (
                <InputNumber
                    style={{ width: '100%' }}
                    min={1}
                    step={1}
                    precision={0}
                    placeholder={I18N.formlist.step3.moRenCi}
                    disabled={disabled}
                />
            )
        },
        {
            name: 'documentTypeUuid',
            label: I18N.formlist.step3.baoWen,
            options: {},
            component: (
                <Select
                    allowClear
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step3.qingXuanZeBaoWen}
                    optionFilterProp="children"
                    onChange={() => {
                        setFieldsValue({
                            indexPackageName: undefined
                        });
                    }}>
                    {documentTypeList?.map((v) => (
                        <Option key={v.uuid} value={v.uuid}>
                            {v.displayName}
                        </Option>
                    ))}
                </Select>
            )
        },
        {
            name: 'indexPackageName',
            label: I18N.formlist.step3.zhiBiaoJi,
            options: {},
            component: (
                <Select
                    mode="multiple"
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step3.qingXuanZeZhiBiao}
                    optionFilterProp="children">
                    {featureSetList &&
                        featureSetList.map((item) => (
                            <Option value={item.name} key={item.name}>
                                <Ellipsis title={item.displayName} />
                            </Option>
                        ))}
                </Select>
            )
        },
        {
            name: 'asyncInputTemplate',
            label: I18N.formlist.step3.shuRuChuLiQi,
            options: {
                initialValue: value?.asyncInputTemplate
            },
            component: <TextArea rows={5} disabled={disabled} />,
            style: { display: 'block', width: '100%' }
        },
        {
            name: 'asyncOutputTemplate',
            label: I18N.formlist.step3.shuChuChuLiQi,
            options: {
                initialValue: value?.asyncOutputTemplate
            },
            component: <TextArea rows={5} disabled={disabled} />,
            style: { display: 'block', width: '100%' }
        }
    ];

    let configList = useMemo(() => {
        let list = [];

        list = defaultFormList1.filter((i) => {
            // 调用方式 联动显示 输入处理器模板
            if ('asyncInputTemplate' === i.name && !callMethodControl[asyncContentType]) {
                return false;
            }
            // 后置ETL处理器 联动显示 输出处理器模板
            if ('asyncOutputTemplate' === i.name && asyncPostEtlHandlerName !== 'json') {
                return false;
            }

            if (!captainEnable && (i.name === 'indexPackageName' || i.name === 'documentTypeUuid')) {
                return false;
            }

            return true;
        });

        return list;
    }, [asyncMethodType, asyncPostEtlHandlerName, documentTypeList, featureSetList, asyncContentType]);

    const validate = () => {
        let { chargeMethod } = value;
        let { asyncInputConfig, asyncOutputConfig } = getFieldsValue();

        // 校验服务入参是否有未填项 - async
        let inputConfigFlag = true;
        // 服务入参 - 查找是否有重名参数标识、至少选择一个作为缓存key - async
        let asyncRepeatName = null;
        let asyncKeyFlag = false;
        // socket 不需要校验 sendSpace
        let isSocket = asyncMethodType === 'socket';

        if (asyncOutputConfig?.length) {
            asyncInputConfig.forEach((item) => {
                if (!item.serviceParam || !(item.sendSpace || isSocket)) {
                    inputConfigFlag = false;
                }

                const obj = asyncInputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
                if (obj && item.uuid !== obj.uuid) asyncRepeatName = obj.serviceParam;
                if (item.keyInclude) asyncKeyFlag = true;
            });

            if (!inputConfigFlag) {
                message.warning(I18N.formlist.step3.fuWuRuCanYou);
                return false;
            }

            if (asyncRepeatName) {
                message.warning(I18N.template(I18N.formlist.step3.fuWuRuCanBu2, { val1: asyncRepeatName })); // 服务入参不能存在相同参数标识
                return false;
            }

            if (!asyncKeyFlag) {
                message.warning(I18N.formlist.step3.qingZhiShaoXuanZe);
                return false;
            }
        } else {
            message.warning(I18N.formlist.step3.fuWuChuCanBu);
            return false;
        }

        // 校验服务出参是否有未填项 - async
        let asyncFlag2 = false;

        if (asyncOutputConfig?.length) {
            asyncOutputConfig.forEach((item) => {
                if (item.type === 'variable') {
                    // 变量
                    if (!item.serviceParam || !item.field) {
                        asyncFlag2 = true;
                    }
                } else {
                    // 常量
                    if (!item.field || !item.value) {
                        asyncFlag2 = true;
                    }
                }
                if (item.checked && item.includeCheck.checkType === 2 && !item.includeCheck.checkValue) {
                    asyncFlag2 = true;
                }
            });
        }

        if (asyncFlag2) {
            message.warning(I18N.formlist.step3.fuWuChuCanYou); // 服务出参有未填字段
            return false;
        }

        // 服务出参 - 查找是否有重名参数标识、查得计费至少配一个字段为查得 - async
        let asyncRepeatName2 = null;
        let asyncCheckFlag = false;
        asyncOutputConfig.forEach((item) => {
            const obj = asyncOutputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
            if (obj && item.uuid !== obj.uuid) asyncRepeatName2 = obj.serviceParam;
            if (item.checked) asyncCheckFlag = true;
        });
        if (asyncRepeatName2) {
            message.warning(I18N.template(I18N.formlist.step3.fuWuChuCanBu, { val1: asyncRepeatName2 })); // 服务出参不能存在相同参数标识
            return false;
        }
        if (!asyncCheckFlag && chargeMethod === 2) {
            message.warning(I18N.formlist.step3.dangQianJiFeiFang);
            return false;
        }
        return true;
    };

    const save = () => {
        validateFields((errors, values) => {
            if (!errors) {
                if (!validate()) return;

                let { asyncProxys, ...rest } = values;
                let { asyncProxy, asyncProxyInfo } = asyncProxys;

                let params = {
                    ...value,
                    ...rest,
                    asyncProxy,
                    asyncProxyInfo,
                    dataSourceType: 'ASYNC'
                };

                delete params.asyncProxys;
                delete params.proxys;
                delete params.cacheTime;
                submit(params);
            }
        });
    };

    const back = () => {
        // 路由跳转
        props.history.push('/handle/supplierManagement/dataServiceList');
    };

    const submit = (params) => {
        const { account } = globalStore.currentUser;
        let { limitConfig, inputConfig, outputConfig, cacheTime } = value;
        let { asyncInputConfig, asyncOutputConfig, indexPackageName = [] } = getFieldsValue();
        if (params.proxy === '0') params.proxyInfo = null;
        if (limitConfig?.length === 1 && !limitConfig[0].type && !limitConfig[0].limit) {
            limitConfig = [];
        }
        params = {
            ...params,
            ...cacheTime
        };
        params.indexPackageName = indexPackageName.join(',');
        params.inputConfig = JSON.stringify(inputConfig);
        params.outputConfig = JSON.stringify(outputConfig);
        params.asyncInputConfig = JSON.stringify(asyncInputConfig);
        params.asyncOutputConfig = JSON.stringify(asyncOutputConfig);

        params.limitConfig = JSON.stringify(limitConfig);

        if (params.asyncProxy === '0') params.asyncProxyInfo = null;
        if (modalType === 1) {
            // 新增
            params.creator = account;

            dataServiceListAPI
                .addData(params)
                .then((res) => {
                    if (res && res.success) {
                        message.success(I18N.formlist.step3.caoZuoChengGong);

                        back();
                    } else {
                        message.error(res.message);
                    }
                })
                .catch(() => {});
        } else {
            params.operator = account;
            dataServiceListAPI
                .updateData(params)
                .then((res) => {
                    if (res && res.success) {
                        message.success(I18N.formlist.step3.caoZuoChengGong);
                        back();
                    } else {
                        message.error(res.message);
                    }
                })
                .catch(() => {});
        }
    };

    const upStep = () => {
        onChange({
            ...getFieldsValue()
        });
        setCurrent(current - 1);
    };

    return (
        <>
            <div className="title">{I18N.formlist.step3.fuWuJieKou}</div>
            <div className="api">
                {configList.map((item, index) => {
                    return (
                        <Form.Item key={index} label={item.label} style={item.style || {}}>
                            {getFieldDecorator(item.name, item.options)(item.component)}
                        </Form.Item>
                    );
                })}
            </div>
            <div className="title">{I18N.formlist.step3.fuWuRuCan}</div>
            <div className="input-config">
                <Form.Item>
                    {getFieldDecorator(
                        'asyncInputConfig',
                        {}
                    )(
                        <ServiceInput
                            modalType={modalType}
                            current={current}
                            isSync={false}
                            disabled={disabled}
                            systemList={systemList}
                            methodType={asyncMethodType}
                        />
                    )}
                </Form.Item>
            </div>
            <div className="title">{I18N.formlist.step3.fuWuChuCan}</div>
            <div className="output-config">
                <Form.Item>
                    {getFieldDecorator(
                        'asyncOutputConfig',
                        {}
                    )(
                        <ServiceOutput
                            modalType={modalType}
                            current={current}
                            isSync={false}
                            disabled={disabled}
                            systemList={systemList}
                            methodType={asyncMethodType}
                            isIntegrationTG={isIntegrationTG}
                        />
                    )}
                </Form.Item>
            </div>
            <Button type="default" onClick={() => upStep()} style={{ marginRight: 10 }}>
                {I18N.formlist.step3.shangYiBu}
            </Button>

            {modalType === 3 ? (
                <Button
                    type="primary"
                    onClick={() => {
                        props.history.push('/handle/supplierManagement/dataServiceList');
                    }}>
                    {I18N.formlist.step3.fanHui}
                </Button>
            ) : (
                <Button type="primary" onClick={() => save()}>
                    {I18N.formlist.step3.tiJiao}
                </Button>
            )}
        </>
    );
});

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(GetFormItemList);
