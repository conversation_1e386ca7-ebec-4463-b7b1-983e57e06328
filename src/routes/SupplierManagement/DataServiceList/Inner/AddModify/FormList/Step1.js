import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import { Input, Select, Form, message, Radio, Tooltip, Icon, InputNumber, Button, Switch, Ellipsis } from 'tntd';
import { connect } from 'dva';
import classNames from 'classnames';
import FlowLimit from '../FlowLimit';
import { contractListAPI, supplierListAPI, dataServiceListAPI } from '@/services';

import ContractModal from '@/routes/SupplierManagement/ContractList/Inner/AddModifyModal';
import { cloneDeep } from 'lodash';
import CacheTime from '../cacheTime';

const Option = Select.Option;

const GetFormItemList = Form.create({ name: 'form1' })((props) => {
    let { value, onChange, current, setCurrent, form, modalType, disabled, globalStore, systemList = [] } = props;
    const { getFieldDecorator, setFieldsValue, getFieldsValue, validateFields } = form;

    const { partnerId, invokePolicy } = getFieldsValue();

    const { allMap } = globalStore;
    const { isIntegrationTG, chargeMethodList = [] } = allMap;
    const { serviceTypeList = [] } = allMap;

    const initCacheTime = {
        cacheOpen: 0,
        cacheInfrastructureType: 'nosql',
        cacheday: 7
    };

    const [providerList, setProviderList] = useState([]); // 供应商列表
    const [contractList, setContractList] = useState([]); // 合同列表

    useEffect(() => {
        initList();
    }, []);

    useEffect(() => {
        const {
            displayName,
            name,
            dataType,
            partnerId,
            contractId,
            confidence,
            costLevel,
            limitConfig,
            timeout,
            retry,
            invokePolicy,
            chargeMethod,
            cacheOpen,
            cacheInfrastructureType,
            cacheday
        } = value || {};

        if (current === 0 && name) {
            setFieldsValue({
                partnerId,
                dataType,
                displayName,
                confidence,
                costLevel,
                contractId,
                chargeMethod,
                invokePolicy,
                limitConfig,
                retry,
                timeout,
                name,
                cacheTime: {
                    cacheOpen: cacheOpen || initCacheTime.cacheOpen,
                    cacheInfrastructureType: cacheInfrastructureType || initCacheTime.cacheInfrastructureType,
                    cacheday: cacheday || initCacheTime.cacheday
                }
            });
        }
    }, [value]);

    useEffect(() => {
        if (value.partnerId && current === 0) {
            getContractList(value.partnerId);
        }
    }, [value, providerList]);

    // 获取供应商列表
    const initList = async () => {
        supplierListAPI.getList().then((res) => {
            if (res.success) {
                setProviderList(res?.data?.contents || []);
            }
        });
    };

    // 获取合同列表
    const getContractList = (partnerId) => {
        const findObj = providerList.find((item) => item.uuid === partnerId);
        if (!findObj) return;
        contractListAPI.getList({ providerUuid: findObj?.uuid }).then((res) => {
            if (res.success) {
                setContractList(res?.data?.contents);
            }
        });
    };

    // 数据源服务接口信息
    const Info = [
        {
            name: 'displayName',
            label: I18N.formlist.step1.shuJuYuanFuWu3,
            options: {
                rules: [
                    { required: true, message: I18N.formlist.step1.qingTianXieShuJu }, // 添加正则校验 /^[\w\s\u4E00-\u9FA5\-.]+$/，支持中文、英文、数字、空格、下划线、-、.
                    {
                        pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                        message: I18N.formlist.step1.shuJuYuanFuWuM
                    }
                ]
            },
            component: <Input placeholder={I18N.formlist.step1.qingTianXieShuJu} disabled={disabled} />
        },
        {
            name: 'name',
            label: I18N.formlist.step1.shuJuYuanFuWu2,
            options: {
                rules: [
                    { required: true, message: I18N.formlist.step1.shuJuYuanFuWu2 },
                    {
                        pattern: /^[a-zA-Z0-9\_]+$/,
                        message: I18N.formlist.step1.shuJuYuanFuWuB
                    }
                ]
            },
            component: <Input placeholder={I18N.formlist.step1.qingTianXieShuJu} disabled={disabled || modalType === 2} />
        },
        {
            name: 'dataType',
            label: I18N.formlist.step1.shuJuLeiXing,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeShuJu }]
            },
            component: (
                <Select showSearch disabled={disabled} placeholder={I18N.formlist.step1.qingXuanZeShuJu} optionFilterProp="children">
                    {serviceTypeList?.map((item, index) => {
                        return (
                            <Option value={item.dataType} key={index}>
                                {item.name}
                            </Option>
                        );
                    })}
                </Select>
            )
        },
        {
            name: 'partnerId',
            label: I18N.formlist.step1.heZuoFangMingCheng,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeHeZuo }],
                onChange: (e) => {
                    setFieldsValue({
                        contractId: undefined
                    });
                    getContractList(e);
                }
            },
            component: (
                <Select
                    allowClear
                    showSearch
                    style={{ width: '100%' }}
                    disabled={disabled}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step1.qingXuanZeHeZuo}
                    optionFilterProp="children">
                    {providerList &&
                        providerList
                            .filter(({ status }) => status !== 2)
                            .map((item, index) => {
                                return (
                                    <Option value={item.uuid} key={index}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                </Select>
            )
        },
        {
            name: 'contractId',
            label: I18N.formlist.step1.heTong,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeHeTong }]
            },
            component: (
                <Select
                    allowClear
                    showSearch
                    style={{ width: '100%' }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step1.qingXuanZeHeTong}
                    optionFilterProp="label">
                    {contractList &&
                        contractList.map((item, index) => {
                            return (
                                <Option
                                    value={item.uuid}
                                    key={index}
                                    label={item.name}
                                    title={item.name}
                                    chargeType={item.chargeType || ''}>
                                    <Tooltip title={item.name} placement="top">
                                        <span
                                            className={classNames('u-contract-option', {
                                                // 非正常状态置灰
                                                'u-contract-option-disabled': item.status !== '1'
                                            })}>
                                            {item.name}
                                        </span>
                                    </Tooltip>
                                    <span
                                        className="u-contract-option-detail"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleContract(item);
                                        }}>
                                        {I18N.formlist.step1.chaKanXiangQing}
                                    </span>
                                </Option>
                            );
                        })}
                </Select>
            )
        },
        {
            name: 'confidence',
            label: I18N.formlist.step1.zhiXinDu,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeZhiXin }]
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder={I18N.formlist.step1.qingXuanZeZhiXin}
                    disabled={disabled}
                    optionFilterProp="children">
                    <Option value={1}>{I18N.formlist.step1.gao}</Option>
                    <Option value={2}>{I18N.formlist.step1.zhong}</Option>
                    <Option value={3}>{I18N.formlist.step1.di}</Option>
                </Select>
            )
        },
        {
            name: 'costLevel',
            label: I18N.formlist.step1.chengBenDengJi,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeChengBen }]
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step1.qingXuanZeChengBen}
                    optionFilterProp="children">
                    <Option value={1}>{I18N.formlist.step1.gao}</Option>
                    <Option value={2}>{I18N.formlist.step1.zhong}</Option>
                    <Option value={3}>{I18N.formlist.step1.di}</Option>
                </Select>
            )
        },
        {
            name: 'chargeMethod',
            label: I18N.formlist.step1.jiFeiLeiXing,
            options: {
                rules: [{ required: true, message: I18N.formlist.step1.qingXuanZeJiFei }]
            },
            component: (
                <Select
                    showSearch
                    allowClear
                    disabled={disabled}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step1.qingXuanZeJiFei}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children">
                    {chargeMethodList &&
                        chargeMethodList.map((item, index) => {
                            return (
                                <Option value={item.code} key={index}>
                                    {item.name}
                                </Option>
                            );
                        })}
                </Select>
            )
        }
    ];

    // 接口调用机制
    const Mechanism = [
        {
            name: 'invokePolicy',
            label: (
                <div style={{ verticalAlign: 'middle' }}>
                    <span>{I18N.formlist.step1.chaXunFangShi}</span>{' '}
                    <Tooltip title={I18N.formlist.step1.keYiPeiZhiDuo}>
                        <Icon type="question-circle" />
                    </Tooltip>
                </div>
            ),
            options: {
                initialValue: 0,
                onChange: (e) => {
                    if (e.target.value === 2) {
                        setFieldsValue({
                            cacheOpen: 0,
                            cacheInfrastructureType: 0,
                            cacheday: 0
                        });
                    }
                }
            },
            component: (
                <Radio.Group disabled={disabled}>
                    <Radio value={0}>
                        {I18N.formlist.step1.benDiHuanCunYou}
                        <Tooltip title={I18N.formlist.step1.xianChaXunBenDi}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Radio>
                    <Radio value={1}>
                        {I18N.formlist.step1.zhiJieJieKou}
                        <Tooltip title={I18N.formlist.step1.zhiJieFaQiJie}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Radio>
                    <Radio value={2}>
                        {I18N.formlist.step1.chuanCanYouXian}
                        <Tooltip title={I18N.formlist.step1.tongGuoJieKouTiao}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Radio>
                </Radio.Group>
            )
        },
        {
            name: 'retry',
            label: I18N.formlist.step1.shuJuZhongShiCi,
            options: {},
            component: <InputNumber min={0} style={{ width: '100%' }} placeholder={I18N.formlist.step1.moRenCi} disabled={disabled} />
        },
        {
            name: 'timeout',
            label: I18N.formlist.step1.shuJuChaoShiShi,
            options: {},
            component: (
                <InputNumber
                    style={{ width: '100%' }}
                    min={1}
                    step={1}
                    precision={0}
                    placeholder={I18N.formlist.step1.ruGuoBuPeiZhi}
                    disabled={disabled}
                />
            )
        },
        {
            name: 'cacheTime',
            label: I18N.formlist.step1.huanCunKaiGuan,
            options: {
                initialValue: {
                    cacheday: value?.cacheTime?.cacheday || initCacheTime.cacheday,
                    cacheOpen: value?.cacheOpen || initCacheTime.cacheOpen,
                    cacheInfrastructureType: value?.cacheInfrastructureType || initCacheTime.cacheInfrastructureType
                }
            },
            component: <CacheTime disabled={disabled} />
        },
        {
            name: 'limitConfig',
            label: (
                <div style={{ verticalAlign: 'middle' }}>
                    <span>{I18N.formlist.step1.liuLiangShangXianKong2}</span>{' '}
                    <Tooltip title={I18N.formlist.step1.keYiPeiZhiDuo}>
                        <Icon type="question-circle" />
                    </Tooltip>
                </div>
            ),
            options: {
                initialValue: [{ type: undefined, limit: undefined }],
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            let limitConfig = value;
                            // // 流量上限控制是否有未填项
                            let limitFlag = false;
                            if (limitConfig.length === 1) {
                                if ((limitConfig[0].type && !limitConfig[0].limit) || (!limitConfig[0].type && limitConfig[0].limit)) {
                                    callback(new Error(I18N.formlist.step1.liuLiangShangXianKong));
                                }
                            } else {
                                limitConfig.forEach((item) => {
                                    if (!item.type || !item.limit) limitFlag = true;
                                });
                            }
                            if (limitFlag) callback(new Error(I18N.formlist.step1.liuLiangShangXianKong));
                            callback();
                        }
                    }
                ],
                validateTrigger: 'onChange'
            },
            component: <FlowLimit disabled={disabled} modalType={modalType} globalStore={globalStore} />
        }
    ];

    // 查看合同详情
    const handleContract = (data) => {
        getInfo(data.uuid, 3);
    };

    // 获取单条合同信息
    const getInfo = (uuid, modalType) => {
        contractListAPI.getInfo({ uuid }).then((res) => {
            if (res.success) {
                if (!res.data) return;
                const {
                    code,
                    version,
                    name,
                    startTime,
                    endTime,
                    chargeType,
                    contractFileNames,
                    interfaceFileNames,
                    chargeConfig,
                    masterDimensions,
                    followDimensions,
                    luaScript
                } = res.data;
                const { dispatch } = props;

                let attachFileList = [];
                let contractList = contractFileNames ? JSON.parse(contractFileNames) : [];
                contractList.forEach((item) => {
                    attachFileList.push({ name: item });
                });

                let docFileList = [];
                let interfaceList = interfaceFileNames ? JSON.parse(interfaceFileNames) : [];
                interfaceList.forEach((item) => {
                    docFileList.push({ name: item });
                });

                let price = null;
                let obj = {
                    fieldCode: null,
                    matchType: null,
                    matchConfig: {
                        regex: null
                    }
                };
                let countConfig = [{ price: null, matchFields: [obj] }];
                let flowRange = [{ begin: null, end: -99, price: null }];
                let fieldPricesFlowRange = [
                    {
                        hierarchyDetail: [{ begin: null, end: -99, price: null }],
                        matchFields: [obj]
                    }
                ];
                let dateRange = [{ begin: startTime, end: endTime, price: null }];
                let config = chargeConfig ? JSON.parse(chargeConfig) : null;
                if (config) {
                    if (chargeType === 'countFieldPrices') {
                        countConfig = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDimensionsFlow') {
                        fieldPricesFlowRange = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDate') {
                        // 阶梯计费-日期区间
                        dateRange = config.hierarchyDetail;
                    } else if (chargeType === 'hierarchyFlow') {
                        // 阶梯计费-流量
                        flowRange = config.hierarchyDetail;
                    } else {
                        price = config.price;
                    }
                }

                dispatch({
                    type: 'contractList/setAttrValue',
                    payload: {
                        dialogShow: {
                            addEditModal: true
                        },
                        modalType,
                        updateId: uuid,
                        dialogData: {
                            addEditModalData: {
                                code, // 合同编号
                                version, // 合同版本
                                name, // 合同名称
                                startTime, // 合同开始日期
                                endTime, // 合同结束日期
                                chargeType, // 计费方式
                                docFileList, // 接口文档
                                attachFileList, // 合同附件
                                price, // 价格
                                masterDimensions, // 主属性
                                followDimensions, // 从属性
                                luaScript, // lua脚本
                                countConfig, // 按次字段匹配计费
                                flowRange, // 流量区间
                                fieldPricesFlowRange, // 阶梯字段匹配计费-流量
                                dateRange // 日期区间
                            }
                        }
                    }
                });
            } else {
                message.error(res.message);
            }
        });
    };
    const getRelationInputConfig = (uuid) => {
        const { inputConfig = [] } = value;
        let copyInputConfig = cloneDeep(inputConfig);
        copyInputConfig = copyInputConfig.filter((v) => !v.fromContract);
        copyInputConfig.forEach((item, index) => {
            item.uuid = index;
        });

        return dataServiceListAPI.getRelationInputConfig({ uuid }).then((res) => {
            if (!res?.success) return;
            if (res?.data) {
                const { chargeType, chargeConfig, followDimensions } = res.data;
                let replaceArr = [];
                let fieldArr = cloneDeep(followDimensions);
                let obj = JSON.parse(chargeConfig);
                if (chargeType === 'countFieldPrices' || chargeType === 'hierarchyDimensionsFlow') {
                    const { fieldPrices } = obj;
                    const arr = fieldPrices.map((v) => v.matchFields[0].fieldCode);
                    fieldArr = [...arr, ...fieldArr];
                }
                let configArr = fieldArr.map((code) => {
                    const fieldInfo = systemList.find((vv) => vv.name === code);
                    // 去除和合同关联字段相同的系统字段
                    for (var i in copyInputConfig) {
                        if (copyInputConfig[i]?.field === code) {
                            replaceArr.push(fieldInfo.displayName);
                            copyInputConfig = copyInputConfig.filter((v) => v.field !== code);
                        }
                    }
                    // 覆盖时，尽量继承用户上次输入的数据
                    const preObj = inputConfig.find((v) => v.field === code);
                    return {
                        field: code,
                        displayName: fieldInfo?.displayName || null, // 字段名称
                        serviceParam: preObj?.serviceParam || code?.split('_')?.[2] || null, // 字段标识
                        type: 'variable', // 字段类型(constant常量/variable变量)
                        value: null, // 值类型：type=constant时的常量值
                        dataType: fieldInfo?.dataType || null, // 值类型(1字符型/2整型/3小数型/4布尔型/5日期型)
                        mustInput: true, // 是否必填
                        sendSpace: preObj?.sendSpace || null, // 类型
                        keyInclude: preObj?.keyInclude || false, // 缓存key
                        paasDown: preObj?.paasDown || false, // 向下传递
                        fromContract: true // 关联合同携带的字段
                    };
                });
                if (replaceArr?.length) {
                    message.warning(I18N.template(I18N.formlist.step1.dangQianHeTongGuan, { val1: replaceArr?.join(',') }));
                }

                configArr = [...configArr, ...copyInputConfig];

                // configArr 通过uuid从小到大排序
                configArr.sort((a, b) => {
                    return a.uuid - b.uuid;
                });
                // 重新赋值uuid
                if (configArr?.length) {
                    configArr?.forEach((item, index) => {
                        item.uuid = index;
                    });
                }

                return {
                    inputConfig: configArr
                };
            }
        });
    };

    const nextStep = () => {
        if (modalType === 1 || modalType === 2) {
            validateFields(async (errors, values) => {
                if (!errors) {
                    let { contractId } = values;

                    // 集成天宫时（isIntegrationTG = true)
                    if (isIntegrationTG) {
                        onChange({
                            ...values
                        });
                        setCurrent(current + 1);
                    } else {
                        // 合同关联字段处理
                        getRelationInputConfig(contractId)
                            .then((res) => {
                                let { cacheTime = {}, ...rest } = values;
                                if (invokePolicy === 1 || cacheTime.cacheOpen === 0) {
                                    cacheTime.cacheOpen = 0;
                                    cacheTime.cacheInfrastructureType = 0;
                                    cacheTime.cacheday = 0;
                                }
                                onChange({
                                    ...rest,
                                    cacheOpen: cacheTime?.cacheOpen,
                                    cacheInfrastructureType: cacheTime?.cacheInfrastructureType,
                                    cacheday: cacheTime?.cacheday,
                                    inputConfig: res?.inputConfig
                                });
                            })
                            .finally(() => {
                                setCurrent(current + 1);
                            });
                    }
                }
            });
        } else {
            setCurrent(current + 1);
        }
    };

    // 默认显示的字段，集成时不显示
    let defaultList = ['contractId', 'dataType', 'confidence', 'costLevel', 'chargeMethod'];

    const formList = useMemo(() => {
        return (
            // 循环Info
            Info.filter((i) => {
                // 集成天宫时（isIntegrationTG = true），不显示合同编号、数据类型、数据可信度、成本等级、计费方式
                if (isIntegrationTG && defaultList.includes(i.name)) {
                    return false;
                }
                return true;
            }).map((item, index) => {
                return (
                    <Form.Item key={index} label={item.label}>
                        {getFieldDecorator(item.name, item.options)(item.component)}
                    </Form.Item>
                );
            })
        );
    }, [getFieldsValue()]);

    const formList1 = useMemo(() => {
        return (
            // 循环Mechanism
            Mechanism.filter((i) => {
                // 集成天宫时（isIntegrationTG = true），不显示流量上限控制
                if (isIntegrationTG && ['limitConfig'].includes(i.name)) {
                    return false;
                }
                return true;
            }).map((item, index) => {
                let style = {};

                if (invokePolicy === 1 && item.name === 'cacheTime') {
                    style = {
                        display: 'none'
                    };
                } else if (['timeout', 'limitConfig'].includes(item.name)) {
                    style = {
                        display: 'block'
                    };
                } else {
                    style = {
                        display: 'inline-block'
                    };
                }

                return (
                    <Form.Item
                        key={index}
                        label={item.label}
                        style={{
                            ...style
                        }}>
                        {getFieldDecorator(item.name, item.options)(item.component)}
                    </Form.Item>
                );
            })
        );
    }, [getFieldsValue()]);

    return (
        <>
            <div className="title">{I18N.formlist.step1.shuJuYuanFuWu}</div>
            <div className="info">{formList}</div>

            <div className="title">{I18N.formlist.step1.jieKouDiaoYongJi}</div>
            <div className="mechanism">{formList1}</div>
            <Button type="primary" onClick={() => nextStep()}>
                {I18N.formlist.step1.xiaYiBu}
            </Button>
            <ContractModal />
        </>
    );
});

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(GetFormItemList);
