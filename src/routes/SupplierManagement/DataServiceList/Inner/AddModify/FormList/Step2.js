import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import { Input, Select, Form, Button, message, Spin, Ellipsis } from 'tntd';
import { connect } from 'dva';

import { captainAPI, dataServiceListAPI } from '@/services';

import ProxyInput from '../ProxyInput';
import ServiceInput from '../ServiceInput';
import ServiceOutput from '../ServiceOutput';

import { callMethodControl } from '../constant';
import { cloneDeep } from 'lodash';

const Option = Select.Option;
const TextArea = Input.TextArea;
const sqlTypeList = ['mysql', 'oracle', 'postgreSQL'];

const sqlTypeMap = {
    mysql: {
        regx: /^jdbc:mysql:\/\/.*$/,
        errorMessage: I18N.formlist.step2.jDBCGe
    },
    oracle: {
        regx: /^jdbc:oracle:thin:@.*$/,
        errorMessage: I18N.formlist.step2.oRACL
    },
    postgreSQL: {
        regx: /^jdbc:postgresql:\/\/.*$/,
        errorMessage: I18N.formlist.step2.pOSTG
    }
};

const GetFormItemList = Form.create({
    name: 'form2'
})((props) => {
    let { value, onChange, form, current, setCurrent, modalType, disabled, etlList = [], systemList = [], globalStore, isSync } = props;

    const { getFieldDecorator, setFieldsValue, getFieldsValue, validateFields } = form;
    const { allMap } = globalStore;
    const { isIntegrationTG, captainEnable } = allMap;
    const { methodType, contentType, postEtlHandlerName, documentTypeUuid, inputConfig, pagination } = getFieldsValue();

    const [featureSetList, setFeatureSetList] = useState([]);
    const [documentTypeList, setDocumentTypeList] = useState([]);
    const [deriveIndexPackage, setDeriveIndexPackage] = useState([]);

    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (current !== 1) return;
        const {
            methodType,
            url,
            contentType,
            postEtlHandlerName,
            preEtlHandlerName,
            inputConfig,
            outputConfig,
            outputTemplate,
            inputTemplate,
            proxys,
            pagination,
            dbPassword,
            dbUserName,
            derivedIndexPackageName,
            documentTypeUuid
        } = value || {};

        let copyInput = cloneDeep(inputConfig || []);
        // 填充sendSpace字段
        if (copyInput.find((i) => i.sendSpace === 'sql') && sqlTypeMap[methodType]) {
            copyInput.forEach((i) => {
                i.sendSpace = i.sendSpace || 'sqlParam';
            });
        }

        setFieldsValue({
            methodType,
            url,
            contentType,
            postEtlHandlerName,
            preEtlHandlerName: preEtlHandlerName || undefined,
            inputConfig: copyInput,
            outputConfig,
            outputTemplate,
            inputTemplate,
            proxys,
            pagination,
            dbPassword,
            dbUserName,
            derivedIndexPackageName: derivedIndexPackageName
                ? Array.isArray(derivedIndexPackageName)
                    ? derivedIndexPackageName
                    : derivedIndexPackageName.split(',').filter(Boolean)
                : [],
            documentTypeUuid
        });
    }, [value]);

    useEffect(() => {
        getDocumentTypeList();
    }, []);

    useEffect(() => {
        if (documentTypeUuid) {
            setFeatureSetList([]);

            getFeatureSetList(documentTypeUuid);
        }
    }, [documentTypeUuid]);

    useEffect(() => {
        if (documentTypeUuid) {
            setDeriveIndexPackage([]);

            getDeriveIndexPackages(documentTypeUuid);
        }
    }, [documentTypeUuid]);

    // 接口类型选择，联动调用方式list
    const methodList = useMemo(() => {
        let list = [];
        if (methodType && allMap && allMap.contentTypeList && allMap.methodTypeList) {
            const obj = allMap.methodTypeList.find((item) => item.code === methodType);
            if (obj) {
                allMap.contentTypeList.forEach((item) => {
                    if (item.type === obj.type) {
                        list.push(item);
                    }
                });
            }
        }
        return list;
    }, [methodType, allMap]);
    // 获取特征集列表
    const getFeatureSetList = (documentTypeUuid) => {
        if (!documentTypeUuid) {
            setFeatureSetList([]);
            return;
        }
        const params = {
            documentTypeUuid
        };
        captainAPI.getIndexPackage(params).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setFeatureSetList(res.data);
            } else {
                message.error(res.message);
            }
        });
    };

    const getDocumentTypeList = () => {
        captainAPI.getDocumentTypeList().then((res) => {
            if (res?.data) {
                setDocumentTypeList(res.data);
            }
        });
    };
    const getDeriveIndexPackages = (documentTypeUuid) => {
        if (!documentTypeUuid) {
            setDeriveIndexPackage([]);
            return;
        }
        setLoading(true);
        captainAPI
            .getIndexPackage({ documentTypeUuid, indexPackagePurpose: 'DERIVE' })
            .then((res) => {
                setLoading(false);
                if (res?.success) {
                    setDeriveIndexPackage(res.data || []);
                } else {
                    setDeriveIndexPackage([]);
                }
            })
            .catch((error) => {
                setLoading(false);
                setDeriveIndexPackage([]);
            });
    };

    let hostReg =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;

    // 数据源服务接口信息
    const defaultConfigList = [
        {
            name: 'methodType',
            label: I18N.formlist.step2.jieKouLeiXingXie,
            options: {
                rules: [{ required: true, message: I18N.formlist.step2.qingXuanZeJieKou }],
                onChange: (e) => {
                    onMethodTypeChange(e);
                }
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step2.qingXuanZeJieKou}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children">
                    {allMap &&
                        allMap.methodTypeList &&
                        allMap.methodTypeList
                            .filter((i) => {
                                if (sqlTypeList.includes(i.code) && !isSync) {
                                    return false;
                                }
                                return true;
                            })
                            .map((item, index) => {
                                return (
                                    <Option value={item.code} key={index}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                </Select>
            )
        },
        {
            name: 'url',
            label: I18N.formlist.step2.uRLDiZhi,
            options: {
                rules: [
                    { required: true, message: I18N.formlist.step2.qingTianXieUR },
                    {
                        validator: (rule, value, callback) => {
                            let { methodType } = getFieldsValue() || {};
                            if (!!value && methodType === 'socket' && !/^(([0-9]{1,3}\.){3}[0-9]{1,3})(:[0-9]{1,5})?$/.test(value)) {
                                return callback(new Error(I18N.formlist.step2.sOCKE));
                            } else if (!!value && sqlTypeList.includes(methodType) && !sqlTypeMap[methodType]?.regx?.test(value)) {
                                return callback(new Error(sqlTypeMap[methodType]?.errorMessage));
                            } else if (
                                !!value &&
                                !sqlTypeList.includes(methodType) &&
                                methodType !== 'socket' &&
                                !/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/.test(
                                    value
                                )
                            ) {
                                return callback(new Error(I18N.formlist.step2.uRLGeShi));
                            }
                            callback();
                        }
                    }
                ]
            },
            component: <Input placeholder={I18N.formlist.step2.qingTianXieUR} disabled={disabled} />
        },
        {
            name: 'contentType',
            label: I18N.formlist.step2.diaoYongFangShi,
            options: {
                rules: [{ required: true, message: I18N.formlist.step2.qingXuanZeDiaoYong }]
            },
            component: (
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    placeholder={I18N.formlist.step2.qingXuanZeDiaoYong}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children">
                    {methodList.map((item, index) => {
                        return (
                            <Option value={item.code} key={index}>
                                {item.code}
                            </Option>
                        );
                    })}
                </Select>
            )
        },
        {
            name: 'proxys',
            label: I18N.formlist.step2.shiFouShiYongDai,
            options: {
                validateTrigger: 'onChange',
                rules: [
                    { required: true, message: I18N.formlist.step2.qingTianXieWanZheng },
                    {
                        validator: (rule, value, callback) => {
                            let { proxy, proxyInfo } = value || {};
                            if (!proxy) {
                                callback(new Error(I18N.formlist.step2.qingTianXieWanZheng));
                            }
                            if (!hostReg.test(proxyInfo) && proxy === '1') {
                                callback(new Error(I18N.formlist.step2.daiLiXinXiGe));
                            }
                            callback();
                        }
                    }
                ]
            },
            component: <ProxyInput disabled={disabled} isSync={isSync} current={current} />
        },
        {
            name: 'preEtlHandlerName',
            label: I18N.formlist.step2.qianZhiETL,
            options: {},
            component: (
                <Select
                    showSearch
                    allowClear
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    placeholder={I18N.formlist.step2.qingXuanZeET}
                    optionFilterProp="children">
                    {etlList &&
                        etlList.map((item, index) => {
                            let dom = null;
                            if (item.type === 2) {
                                dom = (
                                    <Option value={item.name} key={index}>
                                        <Ellipsis title={item.displayName} />
                                    </Option>
                                );
                            }
                            return dom;
                        })}
                </Select>
            )
        },
        {
            name: 'pagination',
            label: I18N.formlist.step2.shiFouFenYeJie,
            options: {
                initialValue: value.pagination || false,
                onChange: (e) => {
                    setFieldsValue({
                        postEtlHandlerName: undefined
                    });
                },
                rules: [{ required: true, message: I18N.formlist.step2.qingXuanZeShiFou }]
            },
            component: (
                <Select disabled={disabled} dropdownMatchSelectWidth={false} placeholder={I18N.formlist.step2.qingXuanZe}>
                    <Option key={0} value={false}>
                        {I18N.formlist.step2.fou}
                    </Option>
                    <Option key={1} value={true}>
                        {I18N.formlist.step2.shi}
                    </Option>
                </Select>
            )
        },
        {
            name: 'postEtlHandlerName',
            label: I18N.formlist.step2.houZhiETL,
            options: {
                rules: [{ required: true, message: I18N.formlist.step2.qingXuanZeET }]
            },
            component: (
                <Select
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step2.qingXuanZeET}
                    optionFilterProp="children">
                    {etlList &&
                        etlList
                            .filter((i) => {
                                if (i.type === 1 || i.type === 3) {
                                    let isPagination = getFieldsValue()?.pagination ? 1 : 0;
                                    if (i.tag !== isPagination) return false;
                                    return true;
                                }
                                return false;
                            })
                            .map((item, index) => {
                                return (
                                    <Option value={item.name} key={index}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                </Select>
            )
        },
        {
            name: 'documentTypeUuid',
            label: I18N.formlist.step2.baoWen,
            options: {
                initialValue: value.documentTypeUuid
            },
            component: (
                <Select
                    allowClear
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step2.qingXuanZeBaoWen}
                    optionFilterProp="children"
                    onChange={() => {
                        setFieldsValue({
                            indexPackageName: undefined,
                            derivedIndexPackageName: undefined
                        });
                    }}>
                    {documentTypeList?.map((v) => (
                        <Option key={v.uuid} value={v.uuid}>
                            <Ellipsis title={v.displayName} />
                        </Option>
                    ))}
                </Select>
            )
        },
        {
            name: 'indexPackageName',
            label: I18N.formlist.step2.zhiBiaoJi,
            options: {
                initialValue: value.indexPackageName
            },
            component: (
                <Select
                    mode="multiple"
                    showSearch
                    disabled={disabled}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    placeholder={I18N.formlist.step2.qingXuanZeZhiBiao}
                    optionFilterProp="children">
                    {featureSetList &&
                        featureSetList.map((item) => (
                            <Option value={item.name} key={item.name}>
                                <Ellipsis title={item.displayName} />
                            </Option>
                        ))}
                </Select>
            )
        },
        {
            name: 'derivedIndexPackageName',
            label: '异步衍生指标集',
            options: {
                initialValue: value?.derivedIndexPackageName
                    ? Array.isArray(value.derivedIndexPackageName)
                        ? value.derivedIndexPackageName
                        : value.derivedIndexPackageName.split(',').filter(Boolean)
                    : []
            },
            component: (
                <Select
                    showSearch
                    mode="multiple"
                    dropdownStyle={{ width: 350 }}
                    disabled={disabled}
                    placeholder={'请选择异步衍生指标集'}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="children"
                    allowClear>
                    {deriveIndexPackage &&
                        deriveIndexPackage?.map((item) => (
                            <Option value={item.name} key={item.name}>
                                <Ellipsis title={item.displayName} />
                            </Option>
                        ))}
                </Select>
            )
        },
        {
            name: 'inputTemplate',
            label: I18N.formlist.step2.shuRuChuLiQi,
            options: {
                initialValue: value?.inputTemplate
            },
            component: <TextArea rows={5} disabled={disabled} />,
            style: { display: 'block', width: '100%' }
        },
        {
            name: 'outputTemplate',
            label: I18N.formlist.step2.shuChuChuLiQi,
            options: {
                initialValue: value?.outputTemplate
            },
            component: <TextArea rows={5} disabled={disabled} />,
            style: { display: 'block', width: '100%' }
        }
    ];

    const sqlLoginInfo = [
        {
            name: 'dbUserName',
            label: I18N.formlist.step2.yongHuMing,
            options: {
                initialValue: value.dbUserName,
                rules: [{ required: true, message: I18N.formlist.step2.qingTianXieYongHu }]
            },
            component: <Input placeholder={I18N.formlist.step2.qingTianXieYongHu} disabled={disabled} />
        },
        {
            name: 'dbPassword',
            label: I18N.formlist.step2.miMa,
            options: {
                initialValue: value.dbPassword,
                rules: [{ required: true, message: I18N.formlist.step2.qingTianXieMiMa }]
            },
            component: <Input.Password placeholder={I18N.formlist.step2.qingTianXieMiMa} disabled={disabled} password />
        }
    ];

    // 表单项联动逻辑
    let configList = useMemo(() => {
        let list = defaultConfigList.slice();
        if (!isSync) {
            // 使用filter过滤掉不需要的配置项
            list = list.filter((item) => {
                // 允许显示derivedIndexPackageName
                return !['pagination', 'documentTypeUuid', 'indexPackageName'].includes(item.name);
            });
        } else if (sqlTypeList.includes(methodType)) {
            // 同步情况下, 数据源类型为mysql、oracle、postgreSQL时，显示用户名、密码
            list = list.filter((item) => {
                return !['pagination', 'proxys', 'contentType'].includes(item.name);
            });
            list.splice(2, 0, ...sqlLoginInfo);
        }

        // // 集成TianGong
        // if (isIntegrationTG) {
        //     list = list.filter((item) => {
        //         return !['indexPackageName', 'documentTypeUuid'].includes(item.name);
        //     });
        // }

        list = list.filter((i) => {
            // 调用方式 联动显示 输入处理器模板
            if ('inputTemplate' === i.name && !callMethodControl[contentType]) {
                return false;
            }
            // 后置ETL处理器 联动显示 输出处理器模板
            if ('outputTemplate' === i.name && postEtlHandlerName !== 'json') {
                return false;
            }
            // sqlTypeList里面的类型不显示 指标和报文
            if (
                (!captainEnable &&
                    (i.name === 'indexPackageName' || i.name === 'derivedIndexPackageName' || i.name === 'documentTypeUuid')) ||
                (sqlTypeList.includes(methodType) &&
                    (i.name === 'indexPackageName' || i.name === 'derivedIndexPackageName' || i.name === 'documentTypeUuid'))
            ) {
                return false;
            }

            if (methodType === 'socket' && i.name === 'pagination') {
                return false;
            }

            return true;
        });
        return list;
    }, [
        isSync,
        methodType,
        isIntegrationTG,
        contentType,
        postEtlHandlerName,
        getFieldsValue()?.pagination,
        documentTypeList,
        featureSetList,
        deriveIndexPackage
    ]);

    // 校验
    const validate = () => {
        let { inputConfig, outputConfig } = getFieldsValue();
        let { chargeMethod } = value;

        // 服务入参 - 是否填写完整
        let inputConfigFlag;
        // 服务入参 - 查找是否有重名参数标识、至少选择一个作为缓存key
        let repeatName = null;
        let repeatValue = null;
        // socket 不需要校验 sendSpace
        let isSocket = methodType === 'socket';
        // 服务入参校验
        if (inputConfig?.length) {
            inputConfig.forEach((item) => {
                if (!item.serviceParam || !(item.sendSpace || isSocket)) {
                    inputConfigFlag = false;
                } else {
                    inputConfigFlag = true;
                }

                inputConfig.forEach((subItem) => {
                    if (subItem.serviceParam === item.serviceParam && subItem.uuid !== item.uuid) {
                        repeatName = subItem.serviceParam;
                    }

                    if (subItem.type === 'constant' && subItem.value === item.value && subItem.uuid !== item.uuid) {
                        repeatValue = subItem.value;
                    }
                });
            });
            if (!inputConfigFlag) {
                message.warning(I18N.formlist.step2.fuWuRuCanYou);
                return false;
            }

            if (repeatName) {
                message.warning(I18N.template(I18N.formlist.step2.fuWuRuCanBu2, { val1: repeatName })); // 服务入参不能存在相同参数标识
                return false;
            }

            if (repeatValue) {
                message.warning(I18N.template(I18N.formlist.step2.fuWuRuCanBu3, { val1: repeatValue })); // 服务入参不能存在相同参数标识
                return false;
            }
        } else {
            message.warning(I18N.formlist.step2.fuWuRuCanBu);
            return false;
        }

        // 校验服务出参是否有未填项
        let flag2 = false;
        // 服务出参 - 查找是否有重名参数标识、查得计费至少配一个字段为查得
        let repeatName2 = null;
        let checkFlag = false;

        // 服务出参校验
        if (outputConfig?.length) {
            outputConfig.forEach((item) => {
                // {------------ 服务出参 - 是否填写完整
                if (item.type === 'variable') {
                    // 变量
                    if (!item.serviceParam || !item.field) {
                        flag2 = true;
                    }
                } else {
                    // 常量
                    if (!item.field || !item.value) {
                        flag2 = true;
                    }
                }
                if (item.checked && item.includeCheck.checkType === 2 && !item.includeCheck.checkValue) flag2 = true;
                // ------------}

                // {------------ 服务出参 - 查找是否有重名参数标识
                const obj = outputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
                if (obj && item.uuid !== obj.uuid) repeatName2 = obj.serviceParam;
                if (item.checked) checkFlag = true;
                // ------------}
            });

            if (flag2) {
                message.warning(I18N.formlist.step2.fuWuChuCanYou);
                return false;
            }

            if (repeatName2) {
                message.warning(I18N.template(I18N.formlist.step2.fuWuChuCanBu2, { val1: repeatName2 })); // 服务出参不能存在相同参数标识
                return false;
            }
        } else {
            message.warning(I18N.formlist.step2.fuWuChuCanBu);
            return false;
        }

        // 服务出参 - 查得计费至少配一个字段为查得
        if (!checkFlag && chargeMethod === 2) {
            message.warning(I18N.formlist.step2.dangQianJiFeiFang);
            return false;
        }

        // 服务出入参 一些业务逻辑校验
        let inputConfigQueryFlag; // 是否有查询字段

        inputConfig.forEach((item) => {
            if (item.keyInclude) {
                inputConfigQueryFlag = true; //符合条件
            }
        });

        if (inputConfig.length === 0) {
            message.warning(I18N.formlist.step2.fuWuRuCanYou);
            return false;
        }
        if (!inputConfigQueryFlag) {
            message.warning(I18N.formlist.step2.fuWuRuCanZhong);
            return false;
        }
        return true;
    };

    const nextStep = () => {
        validateFields((errors, values) => {
            if (!errors) {
                // 校验
                if (!validate()) return;

                let { inputConfig, outputConfig } = getFieldsValue();
                let { asyncInputConfig } = value;

                let outputConfigCopy = cloneDeep(outputConfig || []);

                outputConfigCopy.forEach((item) => {
                    if (item.field.charAt(3 - 1) == 'S') {
                        item.dataType = 1;
                    }
                    if (item.field.charAt(3 - 1) == 'N') {
                        item.dataType = 2;
                    }
                    if (item.field.charAt(3 - 1) == 'F') {
                        item.dataType = 3;
                    }
                    if (item.field.charAt(3 - 1) == 'D') {
                        item.dataType = 4;
                    }
                    if (item.field.charAt(3 - 1) == 'B') {
                        item.dataType = 5;
                    }
                });

                let copyAsyncInputConfig = cloneDeep(asyncInputConfig || []);

                // 新增和编辑
                if (modalType === 1 || modalType === 2) {
                    // 向下传递
                    let arr = cloneDeep(inputConfig);
                    // 将向下传递的入参添加到第三步入参中
                    arr.map((i) => {
                        // 定值和变量区分开来判断
                        let callback = (item) => {
                            let bool =
                                i.type === 'constant'
                                    ? item.serviceParam === i.serviceParam || item.value === i.value
                                    : item.field === i.field;
                            return bool;
                        };

                        let findIndex = copyAsyncInputConfig.findIndex(callback);

                        if (findIndex === -1) {
                            i.mustInput = i.mustInput || false;
                            i.keyInclude = i.keyInclude || false;
                            // copyAsyncInputConfig = [...copyAsyncInputConfig, i];
                            copyAsyncInputConfig.push(i);
                        }
                    });

                    // 参数覆盖 keyInclude 和 mustInput
                    copyAsyncInputConfig.forEach((item) => {
                        if (item.type === 'constant') return true;
                        let index = arr.findIndex((i) => i.field === item.field);
                        if (index !== -1 && modalType === 1) {
                            item.keyInclude = item.keyInclude || arr[index].keyInclude;
                            item.mustInput = item.keyInclude || arr[index].mustInput;
                        }
                    });

                    // 添加uuid，用于新增、修改、删除
                    copyAsyncInputConfig.forEach((item, index) => {
                        item.uuid = index;
                    });
                }

                // ----------------------------------------------

                if (isSync) {
                    let { proxys, ...rest } = getFieldsValue();
                    let { proxy, proxyInfo } = proxys || {};
                    let allData = cloneDeep(value);

                    allData = {
                        ...allData,
                        ...allData.cacheTime
                    };
                    // 同步时，删除异步入参
                    delete allData.asyncContentType;
                    delete allData.asyncInputTemplate;
                    delete allData.asyncMethodType;
                    delete allData.asyncOutputTemplate;
                    delete allData.asyncPollMaxCount;
                    delete allData.asyncPollTimeInterval;
                    delete allData.asyncPostEtlHandlerName;
                    delete allData.asyncPreEtlHandlerName;
                    delete allData.asyncProxys;
                    delete allData.asyncUrl;

                    delete allData.proxys;
                    delete allData.cacheTime;

                    let params = {
                        ...allData,
                        ...rest,
                        dataSourceType: 'SYNC'
                    };

                    // 同步情况下, 数据源类型为mysql、oracle、postgreSQL时，不校验代理信息
                    if (!sqlTypeList.includes(methodType)) {
                        params.proxy = proxy;
                        params.proxyInfo = proxyInfo;
                    }

                    delete allData.cacheTime;

                    // 保存
                    save(params);
                } else {
                    onChange({
                        ...getFieldsValue(),
                        outputConfig: outputConfigCopy,
                        asyncInputConfig: copyAsyncInputConfig
                    });
                    setCurrent(current + 1);
                }
            }
        });
    };

    const save = (params) => {
        const { account } = globalStore.currentUser;
        let { limitConfig, timeout } = value;
        let { inputConfig, outputConfig, indexPackageName = [], derivedIndexPackageName = [] } = getFieldsValue();
        if (params.proxy === '0') params.proxyInfo = null;
        if (limitConfig && limitConfig.length === 1 && !limitConfig[0].type && !limitConfig[0].limit) {
            limitConfig = [];
        }
        params.indexPackageName = indexPackageName.join(',');
        params.derivedIndexPackageName = Array.isArray(derivedIndexPackageName)
            ? derivedIndexPackageName.join(',')
            : derivedIndexPackageName;
        params.limitConfig = JSON.stringify(limitConfig);
        params.inputConfig = JSON.stringify(inputConfig);
        params.outputConfig = JSON.stringify(outputConfig);

        params.timeout = timeout ? timeout : 20000;

        if (modalType === 1) {
            // 新增
            params.creator = account;

            setLoading(true);
            dataServiceListAPI
                .addData(params)
                .then((res) => {
                    setLoading(false);
                    if (res && res.success) {
                        message.success(
                            res.data ? I18N.formlist.step2.caoZuoChengGong2 + res.data : I18N.formlist.step2.caoZuoChengGong,
                            res.data ? 5 : 1
                        );
                        back();
                    } else {
                        message.error(res.message);
                    }
                })
                .catch(() => {
                    setLoading(false);
                });
        } else {
            params.operator = account;
            setLoading(true);
            dataServiceListAPI
                .updateData(params)
                .then((res) => {
                    setLoading(false);
                    if (res && res.success) {
                        message.success(
                            res.data ? I18N.formlist.step2.caoZuoChengGong2 + res.data : I18N.formlist.step2.caoZuoChengGong,
                            res.data ? 5 : 1
                        );
                        back();
                    } else {
                        message.error(res.message);
                    }
                })
                .catch(() => {
                    setLoading(false);
                });
        }
    };
    const back = () => {
        // 路由跳转
        props.history.push('/handle/supplierManagement/dataServiceList');
    };

    const upStep = () => {
        onChange({
            ...getFieldsValue()
        });

        setCurrent(current - 1);
    };

    const onMethodTypeChange = (value) => {
        let params = {
            url: undefined,
            postEtlHandlerName: undefined,
            pagination: false
        };

        // 同步情况下, 数据源类型为mysql、oracle、postgreSQL时，显示用户名、密码
        let inputConfigCopy = cloneDeep(inputConfig || []);
        if (isSync) {
            if (sqlTypeList.includes(value)) {
                inputConfigCopy = [
                    {
                        uuid: 0,
                        fromContract: false,
                        keyInclude: false,
                        mustInput: false,
                        sendSpace: 'sql',
                        serviceParam: 'sql',
                        type: 'constant',
                        value: ''
                    }
                ];
            } else {
                inputConfigCopy = inputConfigCopy.filter((i) => !['sql', 'sqlParam'].includes(i.sendSpace));
            }

            params.inputConfig = inputConfigCopy;
            setFieldsValue(params);
        } else {
            params.contentType = undefined;
            setFieldsValue(params);
        }
    };

    return (
        <Spin spinning={loading}>
            <div className="title">{I18N.formlist.step2.fuWuJieKou}</div>
            <div className="api">
                {configList.map((item) => {
                    return (
                        <Form.Item key={item.name} label={item.label} style={item.style || {}}>
                            {getFieldDecorator(item.name, item.options)(item.component)}
                        </Form.Item>
                    );
                })}
            </div>
            <div className="title">{I18N.formlist.step2.fuWuRuCan}</div>
            <div className="input-config">
                <Form.Item>
                    {getFieldDecorator(
                        'inputConfig',
                        {}
                    )(
                        <ServiceInput
                            modalType={modalType}
                            current={current}
                            isSync={isSync}
                            disabled={disabled}
                            systemList={systemList}
                            methodType={methodType}
                            isSql={sqlTypeList.includes(methodType)}
                        />
                    )}
                </Form.Item>
            </div>
            <div className="title">{I18N.formlist.step2.fuWuChuCan}</div>
            <div className="output-config">
                <Form.Item>
                    {getFieldDecorator(
                        'outputConfig',
                        {}
                    )(
                        <ServiceOutput
                            modalType={modalType}
                            current={current}
                            isSync={isSync}
                            disabled={disabled}
                            systemList={systemList}
                            methodType={methodType}
                            isIntegrationTG={isIntegrationTG}
                        />
                    )}
                </Form.Item>
            </div>
            <Button type="default" onClick={() => upStep()} style={{ marginRight: 10 }}>
                {I18N.formlist.step2.shangYiBu}
            </Button>
            {isSync && modalType !== 3 && (
                <Button
                    type="primary"
                    onClick={() => {
                        nextStep();
                    }}
                    style={{ marginRight: 10 }}>
                    {I18N.formlist.step2.baoCun}
                </Button>
            )}
            {!isSync && (
                <Button
                    type="primary"
                    onClick={() => {
                        nextStep();
                    }}
                    style={{ marginRight: 10 }}>
                    {I18N.formlist.step2.xiaYiBu}
                </Button>
            )}
            {modalType === 3 && (
                <Button type="default" onClick={() => back()} style={{ marginRight: 10 }}>
                    {I18N.formlist.step2.fanHui}
                </Button>
            )}
        </Spin>
    );
});

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(GetFormItemList);
