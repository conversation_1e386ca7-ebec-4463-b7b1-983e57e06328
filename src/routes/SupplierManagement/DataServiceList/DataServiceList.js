/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 数据服务列表
 */

import I18N from '@/utils/I18N';
import React, { Suspense, Fragment, useEffect, useState, useCallback } from 'react';
import { connect } from 'dva';
import {
    Button,
    Table,
    Pagination,
    message,
    Dropdown,
    Menu,
    Switch,
    Modal,
    Tooltip,
    Icon,
    HandleIcon,
    QueryForm,
    TableContainer
} from 'tntd';
import { dataServiceListAPI, referenceAPI } from '@/services';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { sortableColumnTitleRenderer } from '@/utils/sortableColumnTitleRenderer';
import { getUrlKey } from '@/utils/utils';
import resetStoreListener from '@/resetStoreListener';
import ImportModal from './Inner/ImportModal';
import AuthDialog from './Inner/AuthDialog';
import Ellipsis from '@/components/TablePage/components/Ellipsis';
import './index.less';

import { debounce } from 'lodash';
const TestModal = React.lazy(() => import('./Inner/TestModal'));
const CopyModal = React.lazy(() => import('./Inner/CopyModal'));

const { confirm } = Modal;
const { Field } = QueryForm;

const DataServiceList = (props) => {
    const { globalStore } = props;
    const { currentApp } = globalStore;

    const [testVisible, setTestVisible] = useState(false);
    const [testId, setTestId] = useState(null);
    const [threeServiceList2, setThreeServiceList2] = useState([]);
    const [testTitle, setTestTitle] = useState(null);
    const [testName, setTestName] = useState('');
    const [copyVisible, setCopyVisible] = useState(false);
    const [record, setRecord] = useState(null);
    const [importVisible, setImportVisible] = useState(false);
    const [referenceDrawerData, setReferenceDrawerData] = useState(null);
    const [isShowAuthDialog, setIsShowAuthDialog] = useState(null);

    const sDisplayName = getUrlKey('displayName');
    const sName = getUrlKey('name');
    const sStatus = getUrlKey('status');

    useEffect(() => {
        if (window.lightBoxActions && !allMap?.isIntegrationTG) {
            window.lightBoxActions.setOrgAppListVisible(true); // 机构下的应用可见
        }
    }, []);

    useEffect(() => {
        const { dispatch } = props;

        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                searchParams: {
                    displayName: sDisplayName,
                    name: sName,
                    status: sStatus
                }
            }
        });
        if (checkFunctionHasPermission('TZ0202', 'query')) {
            const { dataServiceListStore } = props;
            const { curPage, pageSize } = dataServiceListStore.searchParams;
            search(curPage, pageSize);
            getThreeServiceList();
        }
    }, [currentApp.name]);

    message.config({
        maxCount: 1
    });

    // 查询
    const search = (curPage, pageSize, noLoading) => {
        const { dispatch, dataServiceListStore } = props;
        const { searchParams } = dataServiceListStore;
        dispatch({
            type: 'dataServiceList/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize,
                sortField: searchParams.sortField,
                sortRule: searchParams.sortRule,
                noLoading
            }
        });
    };

    // 分页
    const paginationOnChange = (curPage, pageSize) => {
        search(curPage, pageSize);
    };

    // 新增下拉菜单
    const addMenu = () => {
        return (
            <Menu>
                <Menu.Item>
                    <a onClick={() => add('SYNC')}>{I18N.dataservicelist.tongBuJieKou}</a>
                </Menu.Item>
                <Menu.Item>
                    <a onClick={() => add('ASYNC')}>{I18N.dataservicelist.yiBuJieKou}</a>
                </Menu.Item>
            </Menu>
        );
    };

    // 新增
    const add = (dataSourceType = 'SYNC') => {
        const { dispatch, history } = props;
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 1
            }
        });
        let path = '/handle/supplierManagement/dataServiceList/addModify';
        props.history.push(`${path}?modalType=1&dataSourceType=${dataSourceType}`);
    };

    // 修改
    const modify = (record) => {
        const { dispatch, history } = props;
        const { uuid, dataSourceType = 'SYNC' } = record;
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 2
            }
        });
        let path = '/handle/supplierManagement/dataServiceList/addModify';
        history.push(`${path}?modalType=2&uuid=${uuid}&dataSourceType=${dataSourceType}`);
    };

    // 查看
    const look = (record) => {
        const { dispatch, history } = props;
        const { uuid, dataSourceType = 'SYNC' } = record;
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 3
            }
        });
        let path = '/handle/supplierManagement/dataServiceList/addModify';
        history.push(`${path}?modalType=3&uuid=${uuid}&dataSourceType=${dataSourceType}`);
    };

    // 复制
    const copy = (record) => {
        setCopyVisible(true);
        setRecord(record);
    };

    // 导出
    const exportData = (record) => {
        const { displayName } = record;
        const params = {
            uuid: record.uuid
        };
        dataServiceListAPI.exportData(params, displayName, 'xlsx');
    };

    const importData = () => {
        updateImportVisible(true);
    };

    // 上下线切换
    const upDownLine = (e, record) => {
        const { globalStore } = props;
        const { account } = globalStore.currentUser;
        const params = {
            uuid: record.uuid,
            status: e ? 1 : 2,
            operator: account
        };

        //上下线校验
        if (params.status === 1) {
            ReferenceOnlineCheck({
                rq: () => {
                    return referenceAPI.onlineValidate({
                        componentType: 'DATASOURCE_SERVICE',
                        componentIds: record.name
                    });
                }
            }).then(() => {
                dataServiceListAPI.setOnline(params).then((res) => {
                    if (res && res.success) {
                        message.success(I18N.dataservicelist.caoZuoChengGong); // 操作成功
                        const { dataServiceListStore } = props;
                        const { searchParams } = dataServiceListStore;
                        const { curPage, pageSize } = searchParams;
                        search(curPage, pageSize, 'noLoading');
                    } else {
                        message.error(res.message);
                    }
                });
            });
        } else {
            ReferenceCheck({
                rq: () => {
                    return referenceAPI.checkComponentReference({
                        componentType: 'DATASOURCE_SERVICE',
                        componentId: record.name
                    });
                }
            }).then(() => {
                dataServiceListAPI.setOnline(params).then((res) => {
                    if (res && res.success) {
                        message.success(I18N.dataservicelist.caoZuoChengGong); // 操作成功
                        const { dataServiceListStore } = props;
                        const { searchParams } = dataServiceListStore;
                        const { curPage, pageSize } = searchParams;
                        search(curPage, pageSize, 'noLoading');
                    } else {
                        message.error(res.message);
                    }
                });
            });
        }
    };

    // 删除
    const deleteData = (record) => {
        confirm({
            title: I18N.dataservicelist.queDingShanChuCi, // 确定删除此数据?
            onOk: () => {
                ReferenceCheck({
                    strongMsg: I18N.dataservicelist.cunZaiQiangYinYong,
                    rq: () =>
                        referenceAPI.checkComponentReference({
                            componentType: 'DATASOURCE_SERVICE',
                            componentId: record.name
                        })
                }).then(() => {
                    dataServiceListAPI.deleteData({ uuid: record.uuid }).then((res) => {
                        if (res && res.success) {
                            const { dataServiceListStore } = props;
                            const { searchParams, tableList } = dataServiceListStore;
                            let { curPage, pageSize } = searchParams;
                            if (tableList.length === 1 && curPage > 1) {
                                curPage = curPage - 1;
                            }
                            search(curPage, pageSize);
                            getThreeServiceList();
                        } else {
                            message.error(res.message);
                        }
                    });
                });
            }
        });
    };

    const onAuth = record => {
        setIsShowAuthDialog(record)
    }

    // 测试
    const test = (record) => {
        setTestVisible(true);
        setTestId(record.uuid);
        setTestTitle(record.displayName);
        setTestName(record.name);
    };

    const updateImportVisible = (visible) => {
        setImportVisible(visible);
    };

    // 关闭弹框
    const handleCancel = () => {
        setTestVisible(false);
        setTestId(null);
        setTestTitle(null);
        setTestName(null);
        setCopyVisible(false);
        setRecord(null);
    };

    // 防抖查询
    const debouncedSearch = useCallback(
        debounce(() => search(), 300),
        []
    );

    // 改变参数
    const changeField = async (obj) => {
        const { dispatch, dataServiceListStore } = props;
        const { searchParams } = dataServiceListStore || {};
        const newData = {
            curPage: 1,
            pageSize: 10,
            ...searchParams,
            ...obj
        };
        // let val = null;
        // let obj = {};
        // if (e) {
        //     if (type === 'select') val = e;
        //     if (type === 'input') val = e.target.value;
        // }
        // obj[field] = val;
        await dispatch({
            type: 'dataServiceList/updateSearchParams',
            payload: newData
        });

        debouncedSearch();
    };

    const getThreeServiceList = () => {
        dataServiceListAPI.getListAll2().then((res) => {
            if (!res) return;
            if (res && !res.success) return message.error(res.message);
            if (!res.data) return;
            let data = [];
            // 过滤掉没有表示的数据
            res.data.contents.forEach((item) => {
                if (item.name) {
                    data.push(item);
                }
            });
            setThreeServiceList2(data || []);
        });
    };

    const handleMock = (record) => {
        const { history } = props;
        history.push(`/handle/supplierManagement/dataServiceList/mockConfig?uuid=${record.uuid}`);
    };

    const handleTableSort = (pagination, filters, sorter) => {
        const { dispatch, dataServiceListStore } = props;
        const { searchParams } = dataServiceListStore;

        if (sorter && sorter.order) {
            dispatch({
                type: 'dataServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: sorter.field,
                    sortRule: sorter.order === 'ascend' ? 'asc' : 'desc'
                }
            });
        } else {
            dispatch({
                type: 'dataServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: undefined,
                    sortRule: undefined
                }
            });
        }
    };

    const copyModalOk = () => {
        handleCancel();
        search();
        getThreeServiceList();
    };

    const exportRecord = (record) => {
        dataServiceListAPI.exportRecord({ uuid: record.uuid }, record.name, 'tar', I18N.dataservicelist.daoChuShiBai);
    };

    const { dataServiceListStore } = props;
    const { allMap, providerList, menuTreeReady, appList } = globalStore;
    const { tableList, total, searchParams, loading } = dataServiceListStore;
    // const { partnerId, displayName, dataType, dataSourceType, status, name } = searchParams;

    const columns = [
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.shuJuYuanFuWu2), // 三方服务接口名称
            dataIndex: 'displayName',
            key: 'displayName',
            sorter: true,
            width: 200,
            render: (text) => {
                let dom = text;
                if (text && text.length > 10) {
                    dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                }
                return dom ? dom : '--';
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.shuJuYuanFuWu), // 三方数据标识
            dataIndex: 'name',
            key: 'name',
            sorter: true,
            width: 200,
            render: (text) => {
                let dom = text;
                if (text && text.length > 15) {
                    dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                }
                return dom ? dom : '--';
            }
        },
        ...((!allMap.isIntegrationTG && [
            {
                title: sortableColumnTitleRenderer(I18N.dataservicelist.shuJuLeiXing), // 数据类型
                dataIndex: 'dataType',
                key: 'dataType',
                sorter: true,
                render: (text) => {
                    let str;
                    if (allMap && allMap.serviceTypeList) {
                        const obj = allMap.serviceTypeList.find((item) => item.dataType === text);
                        if (obj) str = obj.name;
                    }
                    return str || '- -';
                }
            }
        ]) ||
            []),
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.heZuoFangMingCheng), // 供应商名称
            width: 150,
            dataIndex: 'partnerDisplayName',
            key: 'partnerDisplayName',
            sorter: true,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis text={text || '- -'} />;
            }
        },
        {
            title: I18N.dataservicelist.jieKouLeiXing,
            dataIndex: 'dataSourceType',
            key: 'dataSourceType',
            render: (text) => (text === 'ASYNC' ? I18N.dataservicelist.yiBuJieKou : I18N.dataservicelist.tongBuJieKou)
        },
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.zhuangTai), // 状态
            dataIndex: 'status',
            key: 'status',
            sorter: true,
            width: 120,
            render: (text, record) => {
                const map = {
                    '-1': I18N.dataservicelist.yiShanChu, // 已删除
                    2: I18N.dataservicelist.yiXiaXian, // 已下线
                    1: I18N.dataservicelist.yiShangXian // 已上线
                };
                let dom = (
                    <Switch
                        className="u-checked"
                        checkedChildren={I18N.dataservicelist.yiShangXian} // 已上线
                        unCheckedChildren={I18N.dataservicelist.yiXiaXian} // 已下线
                        checked={text === 1}
                        onChange={(e) => {
                            if (!checkFunctionHasPermission('TZ0202', 'online')) {
                                return message.info(I18N.dataservicelist.zanWuQuanXian); // 暂无权限
                            }
                            upDownLine(e, record);
                        }}
                    />
                );
                return text === -1 ? map[text] : dom;
            }
        },
        {
            title: (
                <Fragment>
                    {/* 缓存有效期 */}
                    {I18N.dataservicelist.huanCunYouXiaoQi}
                    {/* 控制调用过程中的结果数据是否保存，如保存，设置保存的周期 */}
                    <Tooltip title={I18N.dataservicelist.kongZhiDiaoYongGuo}>
                        <Icon type="question-circle" className="ml5" />
                    </Tooltip>
                </Fragment>
            ),
            width: 150,
            dataIndex: 'cacheday',
            key: 'cacheday',
            render: (text, record) => {
                let value = text > 0 ? I18N.template(I18N.dataservicelist.tEXTTian, { val1: text }) : '- -';

                if (record.cacheOpen === 0) {
                    value = '- -';
                }

                return value;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.chuangJianShiJian), // 创建时间
            dataIndex: 'gmtCreate',
            key: 'gmtCreate',
            sorter: true,
            width: 190
        },
        {
            title: I18N.dataservicelist.chuangJianRen, // 创建人
            dataIndex: 'creator',
            key: 'creator',
            render: (text) => {
                return <Ellipsis text={text || '- -'} />;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.xiuGaiShiJian), // 修改时间
            dataIndex: 'gmtModify',
            key: 'gmtModify',
            sorter: true,
            width: 190,
            render: (text) => {
                return <Ellipsis text={text || '- -'} />;
            }
        },
        {
            title: I18N.dataservicelist.xiuGaiRen, // 修改人
            dataIndex: 'operator',
            key: 'operator',
            render: (text) => {
                return <Ellipsis text={text || '- -'} />;
            }
        },
        {
            title: I18N.dataservicelist.caoZuo, // 操作
            dataIndex: 'operate',
            key: 'operate',
            width: 200,
            fixed: 'right',
            render: (text, record) => {
                let flag = false;
                if (
                    !checkFunctionHasPermission('TZ0202', 'copy') &&
                    !checkFunctionHasPermission('TZ0202', 'export') &&
                    !checkFunctionHasPermission('TZ0202', 'delete')
                ) {
                    flag = true;
                }

                let dom = (
                    <HandleIcon>
                        {checkFunctionHasPermission('TZ0202', 'modify') && (
                            <HandleIcon.Item title={I18N.dataservicelist.xiuGai}>
                                <Icon
                                    type="form"
                                    onClick={() => {
                                        modify(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {checkFunctionHasPermission('TZ0202', 'look') && (
                            <HandleIcon.Item title={I18N.dataservicelist.chaKan}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        look(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {!flag && checkFunctionHasPermission('TZ0202', 'test') && (
                            <HandleIcon.Item title={I18N.dataservicelist.ceShi}>
                                <Icon
                                    type="debug"
                                    onClick={() => {
                                        test(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        <HandleIcon.Item title={I18N.dataservicelist.shouQuan}>
                            <Icon type="authority-transfer"  
                              onClick={() => {
                                    onAuth(record);
                                }}/>
                        </HandleIcon.Item>

                        {!flag && checkFunctionHasPermission('TZ0202', 'copy') && (
                            <HandleIcon.Item title={I18N.dataservicelist.fuZhi}>
                                <Icon
                                    type="copy"
                                    onClick={() => {
                                        copy(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {!flag && checkFunctionHasPermission('TZ0202', 'mockConfig') && (
                            <HandleIcon.Item title={I18N.dataservicelist.mOCKPei}>
                                <Icon
                                    type="mock"
                                    onClick={() => {
                                        handleMock(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {!flag && (
                            <HandleIcon.Item title={I18N.dataservicelist.daoChu}>
                                <Icon
                                    type="export"
                                    onClick={() => {
                                        exportRecord(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {record.status == 1 && !flag
                            ? null
                            : checkFunctionHasPermission('TZ0202', 'delete') && (
                                  <HandleIcon.Item title={I18N.dataservicelist.shanChu}>
                                      <Icon
                                          type="delete"
                                          onClick={() => {
                                              deleteData(record);
                                          }}
                                      />
                                  </HandleIcon.Item>
                              )}
                        {!flag && (
                            <HandleIcon.Item title={I18N.dataservicelist.yinYongGuanXi}>
                                <Icon
                                    type="correlation"
                                    onClick={() => {
                                        setReferenceDrawerData(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                    </HandleIcon>
                );
                return dom;
            }
        }
    ];

    return (
        <>
            {menuTreeReady && checkFunctionHasPermission('TZ0202', 'query') && (
                <div className="page-global-body data-source">
                    <QueryForm
                        initialValues={{ displayName: sDisplayName || undefined, status: sStatus || undefined, name: sName || undefined }}
                        extraActions={
                            <>
                                <Tooltip exclude title={I18N.dataservicelist.daoRu}>
                                    <Button onClick={importData}>
                                        <Icon type="import" />
                                    </Button>
                                </Tooltip>
                                {window.auth('TZ0202', 'add') && (
                                    <Dropdown overlay={addMenu}>
                                        <Button type="primary" icon="plus">
                                            {I18N.dataservicelist.xinZeng}
                                        </Button>
                                    </Dropdown>
                                )}
                            </>
                        }
                        onSearch={() => search()}
                        onReset={() => {
                            changeField({
                                partnerId: undefined,
                                displayName: undefined,
                                name: undefined,
                                dataType: undefined,
                                dataSourceType: undefined,
                                status: undefined
                            });
                        }}
                        onChange={(values) => {
                            changeField(values);
                        }}>
                        <Field
                            type="select"
                            name="partnerId"
                            props={{
                                placeholder: I18N.dataservicelist.qingXuanZeHeZuo, // 请选择供应商名称
                                dropdownStyle: { width: 350 },
                                dropdownMatchSelectWidth: false,
                                options: providerList?.map((item) => ({
                                    label: item.displayName,
                                    value: item.displayName
                                }))
                            }}
                        />
                        <Field
                            type="select"
                            name="displayName"
                            props={{
                                dropdownMatchSelectWidth: false,
                                placeholder: I18N.dataservicelist.qingXuanZeShuJu2, // 请选择三方服务接口名称
                                dropdownStyle: { width: 350 },
                                options: threeServiceList2?.map((item) => {
                                    if (item.status === -1) {
                                        return null;
                                    }
                                    return {
                                        label: item.displayName,
                                        value: item.displayName
                                    };
                                })
                            }}
                        />
                        <Field
                            type="input"
                            name="name"
                            props={{
                                placeholder: I18N.dataservicelist.qingShuRuFuWu
                            }}
                        />

                        {!allMap.isIntegrationTG && (
                            <Field
                                type="select"
                                name="dataType"
                                props={{
                                    placeholder: I18N.dataservicelist.qingXuanZeShuJu, // 请选择数据类型
                                    dropdownStyle: { width: 350 },
                                    options: allMap?.serviceTypeList?.map((item) => ({
                                        label: item.name,
                                        value: item.dataType
                                    }))
                                }}
                            />
                        )}

                        <Field
                            type="select"
                            name="dataSourceType"
                            props={{
                                placeholder: I18N.dataservicelist.qingXuanZeJieKou2,
                                options: [
                                    {
                                        label: I18N.dataservicelist.tongBuJieKou,
                                        value: 'SYNC'
                                    },
                                    {
                                        label: I18N.dataservicelist.yiBuJieKou,
                                        value: 'ASYNC'
                                    }
                                ]
                            }}
                        />

                        <Field
                            type="select"
                            name="status"
                            props={{
                                placeholder: I18N.dataservicelist.qingXuanZeJieKou,
                                options: [
                                    {
                                        label: I18N.dataservicelist.shangXian,
                                        value: '1'
                                    },
                                    {
                                        label: I18N.dataservicelist.xiaXian,
                                        value: '2'
                                    }
                                ]
                            }}
                        />
                    </QueryForm>
                    <div className="page-global-body-in">
                        <div className="page-global-body-main">
                            <Table
                                rowKey={(record) => record.uuid}
                                className="table-card-body"
                                columns={columns}
                                dataSource={tableList}
                                pagination={false}
                                loading={loading}
                                scroll={{ x: 2450 }}
                                onChange={handleTableSort}
                            />
                            <div className="page-global-body-pagination">
                                <span className="ml20">
                                    {/* 共 {total} 条记录 */}
                                    {I18N.template(I18N.dataservicelist.tiaoJiLu, { val1: total })}
                                </span>
                                <Pagination
                                    showSizeChanger
                                    showQuickJumper
                                    current={searchParams.curPage}
                                    pageSize={searchParams.pageSize}
                                    total={total}
                                    onChange={(curPage, pageSize) => paginationOnChange(curPage, pageSize)}
                                    onShowSizeChange={(curPage, pageSize) => paginationOnChange(curPage, pageSize)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {menuTreeReady && !checkFunctionHasPermission('TZ0202', 'query') && <NoPermission />}
            <Suspense fallback={null}>
                <TestModal title={testTitle} uuid={testId} name={testName} visible={testVisible} onCancel={handleCancel} />
            </Suspense>
            <Suspense fallback={null}>
                <CopyModal record={record} visible={copyVisible} onCancel={handleCancel} onOk={copyModalOk} />
            </Suspense>
            <ImportModal
                importVisible={importVisible}
                updateVisible={updateImportVisible}
                query={() => {
                    const { dataServiceListStore } = props;
                    const { searchParams } = dataServiceListStore;
                    const { curPage, pageSize } = searchParams;
                    search(curPage, pageSize, 'noLoading');
                }}
                successMsg={I18N.dataservicelist.daoRuChengGong}
            />
            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.name}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_SERVICE',
                        componentId: referenceDrawerData.name
                    });
                }}
            />
            <AuthDialog 
                visible={isShowAuthDialog}
                onCancel={() => setIsShowAuthDialog(null)}
            />
        </>
    );
};
resetStoreListener('dataServiceList');
export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(TableContainer(DataServiceList));
