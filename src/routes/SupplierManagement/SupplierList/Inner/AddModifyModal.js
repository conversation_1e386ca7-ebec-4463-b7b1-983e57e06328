import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, Input, Select, message, Button, Alert } from 'tntd';
import { supplierListAPI } from '@/services';
import { cloneDeep } from 'lodash';

const Option = Select.Option;
class AddModifyModal extends PureComponent {
    state = {
        loading: false,
        showTip: false
    };

    componentDidMount() {
        const { status } = this.props.supplierListStore.dialogData.addEditModalData;
        if (status === 2) {
            this.setState({ showTip: true });
        }
    }

    // 点击确定
    handleOk = () => {
        const { supplierListStore, globalStore } = this.props;
        const { allMap, currentUser } = globalStore;
        const { account } = currentUser;
        const { dialogData, modalType, updateId } = supplierListStore;
        const { addEditModalData } = dialogData;
        let { displayName, status, type, contractCode } = addEditModalData;
        if (!displayName) return message.warning(I18N.inner.addmodifymodal.heZuoFangMingCheng3); // 合作方名称不能为空
        if (displayName.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.heZuoFangMingCheng2);
        } // 合作方名称不能超过30长度
        if (!status) return message.warning(I18N.inner.addmodifymodal.heZuoFangHeZuo); // 合作方合作状态不能为空
        addEditModalData.creator = account;
        let addParams = cloneDeep(addEditModalData);

        if (modalType === 1) {
            this.addData(addParams);
        } else if (modalType === 2) {
            const params = {
                uuid: updateId,
                status,
                displayName,
                type,
                contractCode,
                modifyUser: account
            };
            this.updateData(params);
        }
    };

    // 添加数据
    addData = (params) => {
        this.setState({ loading: true });
        supplierListAPI
            .addData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message);
                    const { dispatch } = this.props;
                    dispatch({
                        type: 'supplierList/getList'
                    });
                    dispatch({
                        type: 'global/getAllProvider'
                    });
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 更新数据
    updateData = (params) => {
        this.setState({ loading: true });
        supplierListAPI
            .updateData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message);
                    const { dispatch, supplierListStore } = this.props;
                    const { searchParams } = supplierListStore;
                    const { curPage, pageSize } = searchParams;
                    dispatch({
                        type: 'supplierList/getList',
                        payload: {
                            curPage,
                            pageSize
                        }
                    });
                    dispatch({
                        type: 'global/getAllProvider'
                    });
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 点击取消
    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: false
                }
            }
        });
        setTimeout(() => {
            dispatch({
                type: 'supplierList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            displayName: null, // 供应商名称
                            name: null, // 供应商标识
                            status: null, // 供应商合作状态
                            type: null, // 供应商类型
                            contractCode: null, // 合同号
                            creator: null, // 创建人
                            gmtCreate: null // 创建时间
                        }
                    }
                }
            });
        }, 300);
    };

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        obj[field] = val ? val : null;

        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...obj
                    }
                }
            }
        });
        if (field === 'status' && val === 2) {
            this.setState({ showTip: true });
        } else if (field === 'status' && val === 1) {
            this.setState({ showTip: false });
        }
    }

    render() {
        const { loading, showTip } = this.state;
        const { supplierListStore, globalStore } = this.props;
        const { appList } = globalStore;

        const { dialogShow, modalType, dialogData } = supplierListStore;
        const { addEditModalData } = dialogData;
        const { displayName, status, contractCode, creator, gmtCreate } = addEditModalData;

        let title = I18N.inner.addmodifymodal.heZuoFangJiBen;
        let disabled = false;
        const add = I18N.inner.addmodifymodal.xinZeng;
        const update = I18N.inner.addmodifymodal.xiuGai;
        if (modalType === 1) title = `${add}-${title}`; // 新增
        if (modalType === 2) {
            title = `${update}-${title}`; // 修改
        }
        if (modalType === 3) disabled = true;

        const footerDom = [
            <Button onClick={this.handleCancel} key="cancel">
                {/* 取消 */}
                {I18N.inner.addmodifymodal.quXiao}
            </Button>,
            <Button type="primary" onClick={this.handleOk} key="ok" loading={loading}>
                {/* 确定 */}
                {I18N.inner.addmodifymodal.queDing}
            </Button>
        ];

        return (
            <Modal
                className="basic-info-add-edit-modal"
                title={title}
                width={800}
                maskClosable={false}
                visible={dialogShow.addEditModal}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={disabled ? null : footerDom}>
                <div className="modal-box ">
                    {showTip && (
                        <Alert type="warning" style={{ marginBottom: '12px' }} message={I18N.inner.addmodifymodal.ruoTingYongDangQian} />
                    )}
                    <span className="u-label">
                        <b>*</b>
                        {/* 供应商名称 */}
                        {I18N.inner.addmodifymodal.heZuoFangMingCheng}
                    </span>
                    <span className="u-input">
                        <Input
                            placeholder={I18N.inner.addmodifymodal.qingShuRu} // 请填写供应商名称
                            disabled={disabled}
                            value={displayName}
                            onChange={(e) => this.changeField(e, 'input', 'displayName')}
                        />
                    </span>
                    <span className="u-label">
                        <b>*</b>
                        {/* 供应商合作状态 */}
                        {I18N.inner.addmodifymodal.heZuoZhuangTai}
                    </span>
                    <span className="u-select">
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 请选择供应商合作状态
                            optionFilterProp="children"
                            value={status ? status : undefined}
                            onChange={(e) => this.changeField(e, 'select', 'status')}>
                            <Option value={1}>
                                {I18N.inner.addmodifymodal.zhengChang}
                                {/* 正常 */}
                            </Option>
                            <Option value={2}>
                                {I18N.inner.addmodifymodal.jinYong}
                                {/* 停用 */}
                            </Option>
                        </Select>
                    </span>
                </div>

                {/* 若停用，当前供应商下的三方服务将全部停用 */}
                {/* {showTip && (
                    <div>
                        <span className="u-label" />
                        <span
                            className="u-textarea"
                            style={{
                                color: '#f00',
                                fontSize: '12px',
                                position: 'absolute',
                                top: '88px',
                                left: '515px'
                            }}>
                            {I18N.inner.addmodifymodal.ruoTingYongDangQian}
                        </span>
                    </div>
                )} */}
                {/* 合同号 */}
                {/* <div className="modal-box">
					<span className="u-label fl">
						{'合同号'}
					</span>
					<span className="u-textarea">
						<TextArea
							rows={4}
							maxLength={200}
							placeholder={'请输入'} // 请填写合同号
							disabled={disabled}
							value={contractCode}
							onChange={(e) =>
								this.changeField(e, "input", "contractCode")
							}
						/>
					</span>
				</div> */}
                {modalType !== 1 && (
                    <div className="modal-box">
                        <span className="u-label">
                            {/* 创建时间 */}
                            {I18N.inner.addmodifymodal.chuangJianShiJian}
                        </span>
                        <span className="u-input">
                            <Input disabled value={gmtCreate} />
                        </span>
                        <span className="u-label">
                            {/* 创建人 */}
                            {I18N.inner.addmodifymodal.chuangJianRen}
                        </span>
                        <span className="u-input">
                            <Input disabled value={creator} />
                        </span>
                    </div>
                )}
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    supplierListStore: state.supplierList
}))(AddModifyModal);
