/*
 * @CreatDate: 2020-12-25 10:06:30
 * @Describe: 下载模板弹框
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useState } from 'react';
import { Input, Select, message, Modal, Button, Icon } from 'tntd';
import moment from 'moment';
import service from '../service';

const InputGroup = Input.Group;

const Option = Select.Option;

const TemplateModal = (props) => {
    const { visible, onCancel, callerList = [] } = props;
    const [id, setId] = useState();
    const [appDisplayName, setAppDisplayName] = useState();

    const afterClose = () => {
        setId(null);
        setAppDisplayName(null);
    };

    const changeField = (e) => {
        setId(e);
        const obj = callerList.find((k) => k.id === e);
        if (obj) {
            setAppDisplayName(obj.appDisplayName);
        } else {
            setAppDisplayName(null);
        }
    };

    const handleDownlod = () => {
        if (!id) return message.warning(I18N.templatemodal.index.qingXianXuanZeTiao);
        const obj = callerList.find((k) => k.id === id);
        console.log(obj);
        const params = {
            name: obj?.name,
            type: obj?.type
        };
        const name = `${obj?.name}_${moment().valueOf()}`;
        service.downloadTemplate(params, name, 'xlsx');
    };

    return (
        <Modal
            title={I18N.templatemodal.index.xiaZaiMuBan}
            visible={visible}
            onCancel={onCancel}
            afterClose={afterClose}
            footer={[
                <Button onClick={onCancel}>{I18N.templatemodal.index.quXiao}</Button>,
                <Button type="primary" onClick={() => handleDownlod()}>
                    {I18N.templatemodal.index.xiaZaiMuBan}
                </Button>
            ]}
            maskClosable={false}
            className="m-template-modal"
            width={500}>
            <InputGroup style={{ display: 'flex', justifyContent: 'center' }}>
                <Select
                    showSearch
                    allowClear
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    style={{ width: '350px' }}
                    placeholder={I18N.templatemodal.index.qingXuanZeDiaoYong}
                    optionFilterProp="children"
                    value={id || undefined}
                    onChange={(e) => changeField(e)}>
                    {callerList.map((item, index) => {
                        return (
                            <Option value={item.id} key={index}>
                                {item.displayName}
                                {item.type === 2 ? I18N.templatemodal.index.zu : ''}
                            </Option>
                        );
                    })}
                </Select>
            </InputGroup>
            {appDisplayName && (
                <div className="u-tip">
                    <Icon type="info-circle" />
                    {I18N.templatemodal.index.gaiDiaoYongFangZu}
                    {appDisplayName}
                    {I18N.templatemodal.index.quDaoQingQueRen}
                </div>
            )}
        </Modal>
    );
};

export default TemplateModal;
