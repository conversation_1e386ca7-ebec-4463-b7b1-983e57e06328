/*
 * @CreatDate: 2020-12-23 15:58:28
 * @Describe: 批量调用任务管理
 */

import I18N from '@/utils/I18N';
import './index.less';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { HandleIcon, QueryListScene, Button, Tooltip, Dropdown, Icon, Alert, message, TableContainer, Modal } from 'tntd';
import TablePage from '@/components/TablePage';
import { DateRanges } from '@/constants';
import { formatStartTime, formatEndTime } from '@/utils/utils';
import { checkFunctionHasPermission } from '@/utils/permission';
import service from './service';
import TemplateModal from './TemplateModal';
import DetailModal from './DetailModal';
const { createActions } = QueryListScene;
const actions = createActions();
const BatchCall = (props) => {
    const { globalStore } = props;
    const { currentApp, allMap, currentUser } = globalStore;

    const [callerList, setCallerList] = useState([]);
    const [detailModalData, setDetailModalData] = useState({});

    let taskStatusList = [];
    const taskDetailStatusList = [];
    allMap?.taskStatusList.forEach((item) => {
        taskStatusList.push({
            label: item.name,
            value: item.type
        });
    });
    allMap?.taskDetailStatusList.forEach((item) => {
        taskDetailStatusList.push({
            label: item.name,
            value: item.type
        });
    });

    useEffect(() => {
        if (actions) {
            actions.search({ current: 1 });
        }
        getNames();
    }, [currentApp]);

    // 获取调用方/调用方组下拉数据
    const getNames = () => {
        service
            .getName({
                appNames: currentApp?.name,
                account: currentUser?.account
            })
            .then((res) => {
                if (!res) return;
                if (res.success) {
                    let data = res?.data || [];
                    const zu = I18N.tasklist.index.zu;
                    data.forEach((item) => {
                        item.label = `${item.displayName}${item.type === 2 ? `(${zu})` : ''}`;
                        item.value = item.id;
                    });
                    setCallerList(data);
                } else {
                    message.error(res.msg);
                }
            });
    };

    const look = (record) => {
        setDetailModalData({
            visible: true,
            record
        });
    };

    const columns = [
        {
            title: I18N.tasklist.index.quDao,
            dataIndex: 'appDisplayName',
            width: 120
        },
        {
            title: I18N.tasklist.index.diaoYongFangZu,
            dataIndex: 'displayName',
            width: 160,
            render: (text, record) => {
                let dom = text ? text : '--';
                if (text && text.length > 20) {
                    dom = <Tooltip title={text}>{text.substr(0, 20)}...</Tooltip>;
                }
                return dom;
            }
        },
        {
            title: I18N.tasklist.index.renWuLeiXing,
            dataIndex: 'taskType'
        },
        {
            title: I18N.tasklist.index.yuanWenJianDiZhi,
            dataIndex: 'sourceFile',
            render: (text) => {
                let dom = text ? text : '--';
                if (text && text.length > 20) {
                    dom = <Tooltip title={text}>{text.substr(0, 20)}...</Tooltip>;
                }
                return dom;
            }
        },
        {
            title: I18N.tasklist.index.jieGuoWenJianDi,
            dataIndex: 'resultFile',
            render: (text) => {
                let dom = text ? text : '--';
                if (text && text.length > 20) {
                    dom = <Tooltip title={text}>{text.substr(0, 20)}...</Tooltip>;
                }
                return dom;
            }
        },
        {
            title: I18N.tasklist.index.renWuZhuangTai,
            dataIndex: 'status',
            width: 130,
            render: (text) => {
                const map = {
                    1: I18N.tasklist.index.weiKaiShi,
                    2: I18N.tasklist.index.yunXingZhong,
                    3: I18N.tasklist.index.wanCheng
                };
                const colorMap = {
                    1: '#BEC6D3',
                    2: '#7DA4F8',
                    3: '#5AD8A6'
                };
                let dom = (
                    <span className="u-status" style={{ color: colorMap[text] }}>
                        <b style={{ background: colorMap[text] }} />
                        {map[text]}
                    </span>
                );
                return text ? dom : '--';
            }
        },
        {
            title: I18N.tasklist.index.chuangJianRen,
            dataIndex: 'createUser',
            width: 100
        },
        {
            title: I18N.tasklist.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 190
        },
        {
            title: I18N.tasklist.index.caoZuo,
            dataIndex: 'operation',
            width: 125,
            // align: "center",
            fixed: 'right',
            render: (val, record) => {
                let permission = checkFunctionHasPermission('TZ0801', 'delete');
                let dom = (
                    <HandleIcon>
                        <HandleIcon.Item title={I18N.tasklist.index.mingXi}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    look(record);
                                }}
                            />
                        </HandleIcon.Item>
                        {record.status !== 3 && permission && (
                            <HandleIcon.Item title={I18N.tasklist.index.tingZhi}>
                                <Icon
                                    type="quit"
                                    onClick={() => {
                                        Modal.confirm({
                                            title: I18N.tasklist.index.tingZhiQueRen,
                                            content: I18N.tasklist.index.jiangTingZhiWeiKai,
                                            onOk() {
                                                onDelete(record.id);
                                            }
                                        });
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                    </HandleIcon>
                );
                return dom;
            }
        }
    ];

    const onDelete = (taskId) => {
        service.deleteTask({ taskId }).then((res) => {
            if (!res) return;
            if (res.success) {
                message.success(res.msg);
                actions.search();
            } else {
                message.error(res.msg);
            }
        });
    };

    const query = ({ current = 1, pageSize = 10, ...rest }) => {
        const obj = callerList.find((k) => k.id === rest?.name);
        let params = {
            curPage: current,
            pageSize,
            appNames: currentApp.name,
            ...rest,
            type: obj?.type,
            name: obj?.name,
            startTime: rest?.date?.length > 0 ? formatStartTime(rest.date[0].valueOf()) : null,
            endTime: rest?.date?.length > 0 ? formatEndTime(rest.date[1].valueOf()) : null
        };
        delete params?.date;

        return service.getList(params).then((res) => {
            if (!res.success) return message.error(res?.msg);
            if (res.success) {
                let data = res?.data;
                return {
                    pageSize: Number(data?.pageSize),
                    current: Number(data?.curPage),
                    total: Number(data?.total),
                    data: data?.contents || []
                };
            }
        });
    };

    const queryForm = [
        {
            type: 'select',
            name: 'name',
            props: {
                placeholder: I18N.tasklist.index.diaoYongFangDiaoYong,
                style: { width: '200px' },
                dropdownMatchSelectWidth: false,
                options: callerList.map((item) => ({ ...item, title: item.label })),
                dropdownStyle: {
                    width: '200px'
                }
            }
        },
        {
            type: 'select',
            name: 'status',
            props: {
                placeholder: I18N.tasklist.index.renWuZhuangTai,
                style: { width: '200px' },
                options: taskStatusList
            }
        },
        {
            type: 'dateRange',
            name: 'date',
            props: {
                ranges: DateRanges,
                style: { width: '240px' }
            }
        }
    ];

    let toolbar = [
        {
            hasPermission: checkFunctionHasPermission('TZ0801', 'download'),
            render: (
                <Dropdown
                    overlay={
                        <div>
                            <Alert
                                message={I18N.tasklist.index.xinJianPiLiangRen}
                                description={I18N.tasklist.index.jianYiDanGeWen}
                                type="info"
                                showIcon
                            />
                        </div>
                    }
                    trigger={['hover']}>
                    <Button type="warning" onClick={() => setVisible(true)}>
                        {I18N.tasklist.index.xiaZaiMuBan}
                        <Icon type="info-circle" />
                    </Button>
                </Dropdown>
            )
        },
        {
            hasPermission: checkFunctionHasPermission('TZ0801', 'add'),
            render: (
                <Button type="primary" icon="plus" onClick={() => handleAdd()}>
                    {I18N.tasklist.index.xinZeng}
                </Button>
            )
        }
    ];

    const handleAdd = () => {
        const { history } = props;
        history.push(`/handle/batchCall/taskList/add?appName=${currentApp?.name}`);
    };

    const [visible, setVisible] = useState(false);

    return (
        <div className="g-batch-call">
            <TablePage
                actionKey="taskList"
                // title={I18N.tasklist.index.piLiangDiaoYongRen}
                query={query}
                actions={actions}
                queryForm={queryForm}
                extralActions={toolbar}
                queryList={{
                    rowKey: 'id',
                    columns,
                    scroll: { x: 1430 }
                    // expandedRowRender
                }}
                hasPermission={checkFunctionHasPermission('TZ0801', 'query')}
            />
            <TemplateModal callerList={callerList} visible={visible} onCancel={() => setVisible(false)} />
            <DetailModal
                visible={detailModalData.visible}
                record={detailModalData.record || {}}
                taskDetailStatusList={taskDetailStatusList}
                onCancel={() => setDetailModalData({})}
            />
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(TableContainer(BatchCall));
