import { getUrl, deleteEmptyObjItem } from "@/utils/common";
import request, { downloadFileHandle, PostForm } from "@/utils/request";

// 批量任务列表
const getList = async (params) => {
	return request(getUrl("/batchTask/list", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 删除任务
const deleteTask = async (params) => {
	return request("/batchTask/delete", {
		method: "POST",
		body: params
	});
};

// 下载模板
const downloadTemplate = async (params, name, fileType) => {
	return downloadFileHandle(getUrl("/batchTask/download", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};

// 获取当前应用下的所有调用方和调用方组
const getName = async (params) => {
	return request(getUrl("/batchTask/getName", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 新增批量调用任务
const addTask = async (params, fileList) => {
	return PostForm("/batchTask/add", "POST", params, fileList, "uploadFile");
};

// 校验ftp服务
const checkFtp = async (params) => {
	return request("/batchTask/check", {
		method: "POST",
		body: params
	});
};

// 下载结果文件
const downloadResultFile = async (params, name, fileType = "zip") => {
	return downloadFileHandle(getUrl("/batchTask/downloadResult", deleteEmptyObjItem(params)), {
		method: "GET"
	}, name, fileType);
};
// 获取任务明细列表
const getTaskDetail = async (params) => {
	return request(getUrl("/batchTask/detail", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

export default {
	getList,
	deleteTask,
	downloadTemplate,
	getName,
	addTask,
	checkFtp,
	downloadResultFile,
	getTaskDetail
};
