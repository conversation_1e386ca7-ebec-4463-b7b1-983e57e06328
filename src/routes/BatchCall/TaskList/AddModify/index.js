/*
 * @CreatDate: 2020-12-25 18:16:12
 * @Describe: 新增
 */

import I18N from '@/utils/I18N';
import './index.less';
import { useEffect, useState, useRef } from 'react';
import { Button, message, Icon, Tooltip, Alert, PageContainer } from 'tntd';
import { connect } from 'dva';
import service from '../service';
import { getUrlKey } from '@/utils/utils';
import OneStep from './Steps/One';
import TwoStep from './Steps/Two';
import ThreeStep from './Steps/Three';
import TemplateModal from '../TemplateModal';

const AddModify = (props) => {
    const { globalStore, history } = props;
    const { currentUser } = globalStore;

    const [current, setCurrent] = useState(1);
    const [visible, setVisible] = useState(false);
    const [callerList, setCallerList] = useState([]);

    useEffect(() => {
        getNames();
    }, []);

    const back = () => {
        history.push('/handle/batchCall/taskList');
    };
    // 获取调用方/调用方组下拉数据
    const getNames = () => {
        const appName = getUrlKey('appName');
        const account = currentUser?.account;
        service.getName({ appNames: appName, account }).then((res) => {
            if (!res) return;
            if (res.success) {
                let data = res?.data || [];
                setCallerList(data);
            } else {
                message.error(res.msg);
            }
        });
    };

    const oneRef = useRef();
    const twoRef = useRef();
    const threeRef = useRef();

    const [oneNextLoading, setOneNextLoading] = useState(false);
    const [okLoading, setOkLoading] = useState(false);

    const handleSteps = async (step, type) => {
        if (type === 'next') {
            if (step === 1) {
                // one next step
                if (oneRef.current) {
                    const validate = oneRef.current.validateParams();
                    if (!validate) return;
                    setOneNextLoading(true);
                    const checkFtp = await oneRef.current.checkFtp();
                    setOneNextLoading(false);
                    if (!checkFtp) return;
                }
            } else {
                // two next step
                if (twoRef.current) {
                    const validate = twoRef.current.validateParams();
                    if (!validate) return;
                }
            }
            setCurrent(step + 1);
        } else if (type === 'up') {
            setCurrent(step - 1);
        } else {
            if (threeRef.current) {
                const validate = threeRef.current.validateParams();
                if (!validate) return;
            }
            let params = {
                ...oneRef.current.getParams(),
                ...twoRef.current.getParams(),
                ...threeRef.current.getParams()
            };
            delete params?.fileList;
            setOkLoading(true);
            service
                .addTask(params, oneRef.current.getParams()?.fileList)
                .then((res) => {
                    setOkLoading(false);
                    if (res?.success) {
                        message.success(res?.msg);
                        back();
                    } else {
                        message.error(res?.msg);
                    }
                })
                .catch(() => {
                    setOkLoading(false);
                });
        }
    };

    return (
        <div className="g-batch-call-add">
            <div className="page-global-header">
                <div className="left-info">
                    <span className="u-back" onClick={back}>
                        <Icon type="left" />
                        {I18N.addmodify.index.fanHui}
                    </span>
                    <span className="u-title">{I18N.addmodify.index.xinZengPiLiangTiao}</span>
                </div>
            </div>
            <div className="page-global-body">
                <div className="page-global-body-main">
                    <div className="steps">
                        <div className={current === 1 ? 'step one' : 'step one opacity'}>
                            {I18N.addmodify.index.diYiBuQingXuan}
                            {current === 1 && (
                                <div className="u-divider1">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                            {current === 2 && (
                                <div className="u-divider3">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                            {current === 3 && (
                                <div className="u-divider4">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                        </div>
                        <div className={current === 2 ? 'step two' : current !== 1 ? 'step opacity' : 'step'}>
                            {I18N.addmodify.index.diErBuQingXuan}
                            {current === 1 && (
                                <div className="u-divider2">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                            {current === 2 && (
                                <div className="u-divider1">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                            {current === 3 && (
                                <div className="u-divider3">
                                    <span className="s1" />
                                    <span className="s2" />
                                </div>
                            )}
                        </div>
                        <div className={current === 3 ? 'step three' : 'step'}>{I18N.addmodify.index.diSanBuQingXuan}</div>
                    </div>
                    <div className="wraps">
                        <OneStep callerList={callerList} ref={oneRef} style={current !== 1 ? { display: 'none' } : {}} />
                        <TwoStep ref={twoRef} style={current !== 2 ? { display: 'none' } : {}} />
                        <ThreeStep ref={threeRef} style={current !== 3 ? { display: 'none' } : {}} />
                        <div className="btns">
                            {(current === 2 || current === 3) && (
                                <Button onClick={() => handleSteps(current, 'up')}>{I18N.addmodify.index.shangYiBu}</Button>
                            )}
                            {(current === 1 || current === 2) && (
                                <Button type="primary" onClick={() => handleSteps(current, 'next')} loading={oneNextLoading}>
                                    {I18N.addmodify.index.xiaYiBu}
                                </Button>
                            )}
                            {current === 3 && (
                                <Button type="primary" onClick={() => handleSteps(current, 'submit')} loading={okLoading}>
                                    {I18N.addmodify.index.tiJiao}
                                </Button>
                            )}
                        </div>
                        {current === 1 && (
                            <div>
                                {I18N.addmodify.index.ruoHaiWeiZhunBei}
                                <a onClick={() => setVisible(true)}>{I18N.addmodify.index.xiaZaiMuBan}</a>
                                {I18N.addmodify.index.jinXingShuJuZhun}
                                <Tooltip
                                    title={<Alert message={I18N.addmodify.index.jianYiDanGeWen} type="info" showIcon />}
                                    overlayClassName="m-tooltip-alert">
                                    <a className="ml10">
                                        <Icon type="info-circle" />
                                    </a>
                                </Tooltip>
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <TemplateModal callerList={callerList} visible={visible} onCancel={() => setVisible(false)} />
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(PageContainer(AddModify));
