.g-batch-call-add{
    .page-global-header{
        .u-back{
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
            cursor: pointer;
            &:hover{
                color: #2196F3;
            }
        }
        .u-title{
            margin-left: 20px;
            font-size: 14px;
        }
    }
    .page-global-body-main{
        border-radius: @border-radius-base;
        .tnt-current-v3 & {
            padding: 16px;
        }
        min-height: 100%;
        background: #FFFFFF;
        box-shadow: 0 0 6px 0 rgba(215,219,243,0.50);
        .steps{
            background: #E1E6EE;
            display: flex;
            height: 48px;
            border-radius: @border-radius-base;
            .step{
                flex: 1;
                line-height: 48px;
                padding-left: 40px;
                position: relative;
                .u-divider1, .u-divider2, .u-divider3, .u-divider4{
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 48px;
                    height: 48px;
                    background: #E1E6EE;
                    .s1{
                        position: absolute;
                        right: 0;
                        top: 0;
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-width: 24px;
                        border-style: solid;
                        border-color: transparent transparent transparent #126BFB;
                    }
                    .s2{
                        position: absolute;
                        left: -16px;
                        top: 7px;
                        display: inline-block;
                        border-top: #fff solid 1px;
                        border-right: #fff solid 1px;
                        transform: rotate(45deg);
                        width: 34px;
                        height: 34px;
                    }
                }
                .u-divider2{
                    .s1{
                        border-color: transparent transparent transparent #E1E6EE;
                    }
                }
                .u-divider3{
                    background: #126BFB;
                    .s1{
                        border-color: transparent transparent transparent #5E9BFF;
                    }
                }
                .u-divider4{
                    background: #5E9BFF;
                    .s1{
                        border-color: transparent transparent transparent #5E9BFF;
                    }
                }
            }
            .one, .two, .three{
                background: #126BFB;
                color: #fff;
            }
            .one {
                border-radius: @border-radius-base 0 0 @border-radius-base;
            }
            .three {
                border-radius: 0 @border-radius-base @border-radius-base 0;
            }
            .opacity{
                background: #5E9BFF;
                color: #fff;
            }
        }
        .wraps{
            width: 650px;
            padding: 40px 0 20px 0;
            margin: 0 auto;
            .wrap{
                background: #F9FAFC;
                padding: 20px 0;
                margin-top: 20px;
                border-radius: @border-radius-base;
                .box{
                    padding: 10px 20px;
                    .label{
                        display: inline-block;
                        width: 116px;
                        text-align: right;
                        margin-right: 10px;
                        b{
                            color: #f00;
                            vertical-align: middle;
                            margin-right: 3px;
                        }
                    }
                    .upload-name{
                        font-size: 12px;
                        position: relative;
                        height: 34px;
                        padding-left: 126px;
                        margin-top: 10px;
                        // span:first-of-type{
                        //     display: inline-block;
                        //     width: 100%;
                        //     overflow: auto;
                        //     text-overflow: ellipsis;
                        //     white-space: pre-wrap;

                        // }
                        > .tntd-ellipsis{
                            display: inline-block;
                        }
                        img{
                            width: 18px;
                            vertical-align: text-top;
                            margin-right: 5px;
                            margin-top: 2px;
                        }
                        i{
                            float: right;
                            cursor: pointer;
                            font-size: 14px;
                            margin-top: 8px;
                            margin-right: 5px;
                        }
                        .size{
                            position: absolute;
                            left: 148px;
                            top: 14px;
                        }
                    }
                    .ant-select, .ant-input, .ant-upload{
                        width: 450px;
                    }
                    .ant-upload{
                        display: inline-block;
                        height: 180px;
                        padding-top: 18px;
                    }
                    .ant-upload.ant-upload-drag .ant-upload-drag-container{
                        display: block;
                        .ant-upload-text{
                            opacity: 0.3;
                            font-family: PingFangSC-Regular;
                            font-size: 14px;
                            color: #17233D;
                            letter-spacing: 0;
                        }
                        .ant-upload-hint{
                            font-family: PingFangSC-Regular;
                            font-size: 12px;
                            color: #F7B036;
                            letter-spacing: 0;
                        }
                    }
                    .ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon{
                        color: #17233D;
                        font-size: 32px;
                    }
                    .ant-upload.ant-upload-drag{
                        background: #fff;
                    }
                }
            }
            .btns{
                margin: 30px 0 10px 0;
                .ant-btn{
                    margin-right: 10px;
                }
            }
        }
    }
    .one-step, .three-step{
        .ant-radio-wrapper:first-child{
            margin-right: 140px;
        }
    }
    .two-step{
        .u-radio{
            display: block;
            height: 30px;
            line-height: 30px;
        }
        .u-row{
            margin-bottom: 20px;
            .ant-radio-wrapper{
                // width: 120px;
                margin-right: 0px;
            }
            .unit {
                display: inline-block;
                padding: 0 3px;
            }
        }
    }
}
.task-add-datepicker{
    .ant-calendar-today-btn{
        display: none !important;
    }
}
