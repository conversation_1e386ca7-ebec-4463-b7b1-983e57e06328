/*
 * @CreatDate: 2020-12-28 13:42:12
 * @Describe: 第二步
 */

import I18N from '@/utils/I18N';
import { useState, useImperativeHandle, forwardRef } from 'react';
import { Input, Select, Radio, DatePicker, message } from 'tntd';
import clonedeep from 'lodash.clonedeep';
import moment from 'moment';
import cronParser from 'cron-parser';

const { Option } = Select;

const weekList = [
    { name: I18N.steps.two.xingQiYi, value: 0 },
    { name: I18N.steps.two.xingQiEr, value: 1 },
    { name: I18N.steps.two.xingQiSan, value: 2 },
    { name: I18N.steps.two.xingQiSi, value: 3 },
    { name: I18N.steps.two.xingQiWu, value: 4 },
    { name: I18N.steps.two.xingQiLiu, value: 5 },
    { name: I18N.steps.two.xingQiRi, value: 6 }
];

let dateList = [];
for (let i = 1; i <= 31; i++) {
    dateList.push(i);
}

let hoursList = [];
for (let i = 0; i <= 23; i++) {
    let name = `${i}`;
    if (i < 10) {
        name = `0${i}`;
    }
    hoursList.push({
        name,
        value: `${i}`
    });
}

let secondList = [];
for (let i = 0; i <= 59; i++) {
    let name = `${i}`;
    if (i < 10) {
        name = `0${i}`;
    }
    secondList.push({
        name,
        value: `${i}`
    });
}

const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
        result.push(i);
    }
    return result;
};

let TwoStep = (props, ref) => {
    const [data, setData] = useState([
        { ruleType: 1, hour: null, second: null, minute: null, name: I18N.steps.two.meiTianGuDingShi, checked: true },
        { ruleType: 2, hour: null, second: null, minute: null, week: [], name: I18N.steps.two.meiZhouGuDingShi, checked: false },
        { ruleType: 3, hour: null, second: null, minute: null, date: [], name: I18N.steps.two.meiYueGuDingShi, checked: false },
        { ruleType: 4, time: null, name: I18N.steps.two.zhiDingMouYiShi, checked: false },
        { ruleType: 5, cron: null, name: I18N.steps.two.cRONBiao2, checked: false }
    ]);

    useImperativeHandle(ref, () => ({
        getParams: () => {
            let obj = data.find((k) => k.checked);
            const ruleContext = {
                hour: Number(obj?.hour),
                second: Number(obj?.second),
                minute: Number(obj?.minute),
                week: obj?.week,
                date: obj?.date,
                time: obj?.time ? moment(obj?.time).format('YYYY-MM-DD HH:mm:ss') : null,
                cron: obj?.cron
            };
            const params = {
                ruleType: obj?.ruleType,
                ruleContext: JSON.stringify(ruleContext)
            };
            return params;
        },
        validateParams: () => {
            const obj = data.find((k) => k.checked);
            const ruleType = obj?.ruleType;
            let flag = false;
            if (ruleType === 1 || ruleType === 2 || ruleType === 3) {
                if (!obj?.hour || !obj?.second || !obj?.minute) {
                    flag = true;
                }
                if (ruleType === 2 && obj?.week?.length === 0) {
                    flag = true;
                }
                if (ruleType === 3 && !obj?.date?.length === 0) {
                    flag = true;
                }
            } else if (ruleType === 4 && !obj?.time) {
                flag = true;
            } else if (ruleType === 5 && !obj?.cron) {
                flag = true;
            }
            if (flag) {
                message.warning(I18N.steps.two.cunZaiBiTianXiang);
                return false;
            }
            if (ruleType === 5) {
                try {
                    cronParser.parseExpression(obj?.cron);
                    return true;
                } catch (err) {
                    message.warning(I18N.steps.two.cRONBiao);
                    return false;
                }
            }
            return true;
        }
    }));

    const changeField = (e, i, field) => {
        let copyData = clonedeep(data);
        if (field === 'checked') {
            copyData.forEach((k) => {
                k.checked = false;
            });
        }
        copyData[i][field] = e;
        setData(copyData);
    };

    const [dropDownCurrent, setDropDownCurrent] = useState();

    return (
        <div className="two-step" style={props.style}>
            {data.map((item, index) => {
                let dom;
                if (item.ruleType === 1 || item.ruleType === 2 || item.ruleType === 3) {
                    let subDom;
                    if (item.ruleType === 2) {
                        subDom = (
                            <Select
                                style={{ width: 125 }}
                                placeholder={I18N.steps.two.qingXuanZe}
                                mode="multiple"
                                value={item.week}
                                onChange={(e) => changeField(e, index, 'week')}>
                                {weekList.map((subItem, subIndex) => (
                                    <Option key={subIndex} value={subItem.value}>
                                        {subItem.name}
                                    </Option>
                                ))}
                            </Select>
                        );
                    } else if (item.ruleType === 3) {
                        subDom = (
                            <Select
                                style={{ width: 125 }}
                                mode="multiple"
                                placeholder={I18N.steps.two.qingXuanZe}
                                value={item.date}
                                onChange={(e) => changeField(e, index, 'date')}>
                                {dateList.map((subItem, subIndex) => (
                                    <Option key={subIndex} value={subItem}>
                                        {subItem}
                                    </Option>
                                ))}
                            </Select>
                        );
                    }
                    dom = (
                        <>
                            {subDom && (
                                <>
                                    {subDom}
                                    <span className="unit"> -- </span>
                                </>
                            )}
                            <Select style={{ width: 60 }} value={item.hour} onChange={(e) => changeField(e, index, 'hour')}>
                                {hoursList.map((subItem, subIndex) => (
                                    <Option key={subIndex} value={subItem.value}>
                                        {subItem.name}
                                    </Option>
                                ))}
                            </Select>
                            <span className="unit"> {I18N.steps.two.shi}</span>
                            <Select style={{ width: 60 }} value={item.minute} onChange={(e) => changeField(e, index, 'minute')}>
                                {secondList.map((subItem, subIndex) => (
                                    <Option key={subIndex} value={subItem.value}>
                                        {subItem.name}
                                    </Option>
                                ))}
                            </Select>
                            <span className="unit"> {I18N.steps.two.fen}</span>
                            <Select style={{ width: 60 }} value={item.second} onChange={(e) => changeField(e, index, 'second')}>
                                {secondList.map((subItem, subIndex) => (
                                    <Option key={subIndex} value={subItem.value}>
                                        {subItem.name}
                                    </Option>
                                ))}
                            </Select>
                            <span className="unit"> {I18N.steps.two.miao}</span>
                        </>
                    );
                } else if (item.ruleType === 4) {
                    dom = (
                        <DatePicker
                            dropdownClassName="task-add-datepicker"
                            style={{ width: 205 }}
                            showTime
                            value={item?.time}
                            disabledDate={(current) => {
                                return current && current < moment().subtract(1, 'days');
                            }}
                            disabledTime={(current) => {
                                let hours = [];
                                let munutes = [];
                                if (current.date() <= moment(dropDownCurrent).date()) {
                                    const nextTenMinuteCurrent = moment(dropDownCurrent).add(10, 'minutes');
                                    hours = range(0, nextTenMinuteCurrent.hour());
                                    if (current.hour() <= moment(dropDownCurrent).hour()) {
                                        munutes = range(0, nextTenMinuteCurrent.minute());
                                    }
                                }
                                return {
                                    disabledHours: () => hours,
                                    disabledMinutes: () => munutes
                                };
                            }}
                            onOpenChange={(status) => {
                                if (status) {
                                    setDropDownCurrent(moment().valueOf());
                                } else {
                                    if (item?.time) {
                                        const nextTenMinuteCurrent = moment(dropDownCurrent).add(10, 'minutes').valueOf();
                                        const chooseTime = item?.time.valueOf();
                                        if (chooseTime < nextTenMinuteCurrent) {
                                            message.warning(I18N.steps.two.zhiNengXuanZeFen);
                                            changeField(null, index, 'time');
                                        }
                                    }
                                    setDropDownCurrent(null);
                                }
                            }}
                            onChange={(e) => changeField(e, index, 'time')}
                        />
                    );
                } else if (item.ruleType === 5) {
                    dom = (
                        <Input
                            style={{ width: 205 }}
                            placeholder={I18N.steps.two.ru}
                            value={item.cron}
                            onChange={(e) => changeField(e.target.value, index, 'cron')}
                        />
                    );
                }
                return (
                    <div key={index} className="u-row">
                        <Radio checked={item.checked} onChange={(e) => changeField(e, index, 'checked')}>
                            {item.name}
                        </Radio>
                        {dom}
                    </div>
                );
            })}
        </div>
    );
};

TwoStep = forwardRef(TwoStep);

export default TwoStep;
