/*
 * @CreatDate: 2020-12-28 13:42:12
 * @Describe: 第三步
 */

import I18N from '@/utils/I18N';
import { useState, useImperativeHandle, forwardRef } from 'react';
import { Input, Radio, message } from 'tntd';

let ThreeStep = (props, ref) => {
	const { style } = props;
	const [params, setParams] = useState({
		resultUploadType: 1,
		resultFtpAddress: null,
		resultFile: null,
		resultUser: null,
		resultPwd: null
	});

	const changeField = (e, field) => {
		let obj = {};
		obj[field] = e;
		setParams({
			...params,
			...obj
		});
	};

	const {
		resultUploadType,
		resultFtpAddress,
		resultFile,
		resultUser,
		resultPwd
	} = params;

	useImperativeHandle(ref, () => ({
		getParams: () => {
			let filterParams;
			if (resultUploadType === 1) {
				filterParams = {
					resultUploadType,
					resultFtpAddress,
					resultFile,
					resultUser,
					resultPwd
				};
			} else {
				filterParams = {
					resultUploadType
				};
			}
			return filterParams;
		},
		validateParams: () => {
			if (resultUploadType === 1 && (!resultFtpAddress || !resultFile || !resultUser || !resultPwd)) {
				message.warning(I18N.steps.three.cunZaiBiTianXiang);
				return false;
			}
			return true;
		}
	}));

	return (
		<div className="three-step" style={style}>
			<Radio.Group
				onChange={(e) => changeField(e.target.value, 'resultUploadType')}
				value={resultUploadType}
			>
				<Radio value={1}>{I18N.steps.three.fTPSF}</Radio>
				<Radio value={2}>{I18N.steps.three.benDiWenJian}</Radio>
			</Radio.Group>
			<div
				className="wrap"
				style={resultUploadType === 1 ? { display: 'block' } : { display: 'none' }}
			>
				<div className="box">
					<span className="label"><b>*</b>{I18N.steps.three.fuWuQiDiZhi}</span>
					<Input
						placeholder={I18N.steps.three.qingShuRuFuWu}
						value={resultFtpAddress}
						onChange={(e) => changeField(e.target.value, 'resultFtpAddress')}
					/>
				</div>
				<div className="box">
					<span className="label"><b>*</b>{I18N.steps.three.jieGuoShuJuWen}</span>
					<Input
						placeholder={I18N.steps.three.qingShuRuJieGuo}
						value={resultFile}
						onChange={(e) => changeField(e.target.value, 'resultFile')}
					/>
				</div>
				<div className="box">
					<span className="label"><b>*</b>{I18N.steps.three.dengLuYongHuMing}</span>
					<Input
						placeholder={I18N.steps.three.qingShuRuDengLu}
						value={resultUser}
						onChange={(e) => changeField(e.target.value, 'resultUser')}
					/>
				</div>
				<div className="box">
					<span className="label"><b>*</b>{I18N.steps.three.dengLuMiMa}</span>
					<Input
						placeholder={I18N.steps.three.qingShuRuYongHu}
						value={resultPwd}
						onChange={(e) => changeField(e.target.value, 'resultPwd')}
					/>
				</div>
			</div>
		</div>
	);
};

ThreeStep = forwardRef(ThreeStep);

export default ThreeStep;
