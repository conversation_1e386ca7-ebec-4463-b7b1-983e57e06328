/*
 * @CreatDate: 2020-12-25 10:06:30
 * @Describe: 下载模板弹框
 */

import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { message, Modal, Table, Tooltip, Popover, Button } from 'tntd';
import moment from 'moment';
import service from '../service';
import './index.less';

const DetailModal = (props) => {
    const { visible, record, taskDetailStatusList, onCancel } = props;
    const [curPage, setCurPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [total, setTotal] = useState(0);
    const [records, setRecords] = useState([]);
    const [permission, setPermission] = useState(false);

    useEffect(() => {
        record?.id && getDetail();
    }, [record?.id, curPage, pageSize]);

    useEffect(() => {
        if (!visible) {
            setCurPage(1);
            setPageSize(10);
            setTotal(0);
            setRecords([]);
            setPermission(false);
        }
    }, [visible]);

    const getDetail = async () => {
        const res = await service.getTaskDetail({
            taskId: record.id,
            curPage,
            pageSize
        });
        if (res?.success) {
            const data = res?.data || {};
            const { total, contents = [] } = data;
            setTotal(total);
            setRecords(contents);
            setPermission(true);
        } else {
            message.warning(res.msg);
            setPermission(false);
        }
    };

    const onPageChange = (page, pageSize) => {
        setCurPage(page);
        setPageSize(pageSize);
    };

    const handleDownload = (taskDetailId) => {
        const params = {
            taskDetailId
        };
        const name = I18N.template(I18N.detailmodal.index.renWuZhiXingJie, { val1: moment().valueOf() });
        service.downloadResultFile(params, name);
    };
    const renderFileNameTable = (resultFileName, text, status) => {
        // 未开始状态不显示文件
        if (status === 1) {
            return '--';
        }

        if (!resultFileName) {
            return text;
        }

        const fileName = (() => {
            try {
                return JSON.parse(resultFileName)
                    .map((name) => {
                        const result = name && name.substring(name.lastIndexOf('/') + 1);

                        if (result) {
                            return {
                                fileName: result
                            };
                        }
                    })
                    .filter((name) => !!name);
            } catch (e) {
                return [];
            }
        })();

        return (
            <Popover
                content={
                    <Table
                        size="middle"
                        bordered
                        columns={[
                            {
                                dataIndex: 'fileName',
                                title: I18N.detailmodal.index.wenJianMing,
                                width: 600
                            }
                        ]}
                        dataSource={fileName}
                        pagination={false}
                        rowKey="fileName"
                    />
                }
                placement="left">
                <a style={{ cursor: 'default' }}>{text}</a>
            </Popover>
        );
    };
    const columns = [
        {
            title: I18N.detailmodal.index.renWuKaiShiShi,
            dataIndex: 'startDate',
            key: 'startDate',
            width: 190
        },
        {
            title: I18N.detailmodal.index.renWuJieShuShi,
            dataIndex: 'endDate',
            key: 'endDate',
            width: 190
        },
        {
            title: I18N.detailmodal.index.yuanWenJianGeShu,
            dataIndex: 'sourceFileCount',
            key: 'sourceFileCount',
            width: 100,
            render(text, record) {
                const { sourceFileName, status } = record;

                return renderFileNameTable(sourceFileName, text, status);
            }
        },
        {
            title: I18N.detailmodal.index.jieGuoWenJianGe,
            dataIndex: 'resultFileCount',
            key: 'resultFileCount',
            width: 110,
            render(text, record) {
                const { resultFileName, status } = record;

                return renderFileNameTable(resultFileName, text, status);
            }
        },
        {
            title: I18N.detailmodal.index.jieKouDiaoYongLiang,
            dataIndex: 'requestCount',
            key: 'requestCount',
            width: 100
        },
        {
            title: I18N.detailmodal.index.zhiXingJieGuo,
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render(text, record) {
                let { errorInfo } = record;
                let map = {};
                taskDetailStatusList.forEach((item) => {
                    map[item.value] = item.label;
                });
                const colorMap = {
                    1: '#BEC6D3',
                    2: '#7DA4F8',
                    4: '#5AD8A6',
                    3: '#EC8172'
                };
                let dom = (
                    <Tooltip title={errorInfo}>
                        <span className="u-status" style={{ color: colorMap[text] }}>
                            <b style={{ background: colorMap[text] }} />
                            {map[text]}
                        </span>
                    </Tooltip>
                );

                return text ? dom : '--';
            }
        },
        {
            title: I18N.detailmodal.index.caoZuo,
            dataIndex: 'uuid',
            width: 170,
            render(uuid, { status }) {
                if (record.resultType === 3 && status === 4) {
                    return (
                        <a
                            onClick={() => {
                                handleDownload(uuid);
                            }}>
                            {I18N.detailmodal.index.xiaZaiJieGuo}
                            {/* Download Result */}
                        </a>
                    );
                }
            }
        }
    ];
    if (!permission) {
        return '';
    }
    return (
        <Modal
            title={I18N.detailmodal.index.renWuMingXi}
            visible={visible}
            onCancel={onCancel}
            footer={null}
            maskClosable={false}
            className="task-detail-modal"
            width={1200}>
            <Table
                columns={columns}
                dataSource={records}
                pagination={{
                    current: curPage,
                    pageSize,
                    total,
                    onChange: onPageChange,
                    showSizeChanger: true,
                    pageSizeOptions: ['10', '20', '30', '50'],
                    onShowSizeChange: onPageChange,
                    showTotal: (total) => I18N.template(I18N.detailmodal.index.gongTOTA, { val1: total })
                }}
                tableLayout="fixed"
                rowKey="uuid"
            />
        </Modal>
    );
};

export default DetailModal;
