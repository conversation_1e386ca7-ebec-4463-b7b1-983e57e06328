:global{
    .m-dataservice-header{
        .dataservice-box{
            .gutter-row{
                .gutter-box{
                    position: relative;
                    height: 100px;
                    border-radius: 4px;
                    color: #fff;
                    padding: 14px 22px;
                    .title{
                        font-size: 16px;
                    }
                    .count{
                        font-size: 20px;
                    }
                    .rate{
                        font-size: 12px;
                        color: #efefef;
                        i{
                            display: inline-block;
                            width: 10px;
                            height: 6px;
                            border-radius: 2px;
                            margin-right: 4px;
                            vertical-align: middle;
                            margin-top: -2px;
                        }
                        .green{
                            background: #12D5A8;
                        }
                        .red{
                            background: #FE7276;
                        }
                        .yellow{
                            background: #efbb1e;
                        }
                    }
                    img{
                        position: absolute;
                        right: 12px;
                        top: 30px;
                        width: 40px;
                        opacity: 0.8;
                    }
                }
                .dyl-bg{
                    background: linear-gradient(to right, #4d93f5, #4ea1ff);
                }
                .success-bg{
                    background: linear-gradient(to right, #05BE88, #12D5A8);
                }
                .fail-bg{
                    background: linear-gradient(to right, #FC524B, #FE7276);
                }
                .kul-bg{
                    background: linear-gradient(to right, #FB823C, #efbb1e);
                }
            }
        }
    }
}