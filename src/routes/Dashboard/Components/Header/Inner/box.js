/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: box组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Row, Col } from 'tntd';
import { connect } from 'dva';
import { dashboardImages } from '@/constants/images';

class BoxItem extends PureComponent {
    render() {
        const { data } = this.props;
        const { serviceInvokeCount, successCount, successRate, failCount, failRate, nonSearchCount, nonSearchRate } = data;

        return (
            <div className="dataservice-box">
                <Row gutter={16}>
                    <Col className="gutter-row" span={6}>
                        <div className="gutter-box dyl-bg">
                            <div className="title">{I18N.inner.box.diaoYongLiang}</div>
                            <div className="count">{serviceInvokeCount}</div>
                            <img src={dashboardImages.db1} />
                        </div>
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <div className="gutter-box success-bg">
                            <div className="title">
                                {/* 成功量 */}
                                {I18N.inner.box.chengGongLiang}
                            </div>
                            <div className="count">{successCount}</div>
                            <div className="rate">
                                <i className="green" />
                                {/* 成功率： */}
                                {I18N.inner.box.chengGongLu}：<span>{successRate === 0 ? 0 : `${successRate}%`}</span>
                            </div>
                            <img src={dashboardImages.db2} />
                        </div>
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <div className="gutter-box fail-bg">
                            <div className="title">
                                {/* 失败量 */}
                                {I18N.inner.box.shiBaiLiang}
                            </div>
                            <div className="count">{failCount}</div>
                            <div className="rate">
                                <i className="red" />
                                {/* 失败率： */}
                                {I18N.inner.box.shiBaiLu}：<span>{failRate === 0 ? 0 : `${failRate}%`}</span>
                            </div>
                            <img src={dashboardImages.db3} />
                        </div>
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <div className="gutter-box kul-bg">
                            <div className="title">{I18N.inner.box.kuWuLiang}</div>
                            <div className="count">{nonSearchCount}</div>
                            <div className="rate">
                                <i className="yellow" />
                                {I18N.inner.box.kuWuLu}：<span>{nonSearchRate === 0 ? 0 : `${nonSearchRate}%`}</span>
                            </div>
                            <img src={dashboardImages.db4} />
                        </div>
                    </Col>
                </Row>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(BoxItem);
