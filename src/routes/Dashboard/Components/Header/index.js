/*
 * @Author: l<PERSON><PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 今天，昨天，全部组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Card, Tabs, Row, Col, message, Spin } from 'tntd';
import { connect } from 'dva';
import { dataServiceAPI } from '@/services';
import BoxItem from './Inner/box';
import './index.less';

const TabPane = Tabs.TabPane;

class Header extends PureComponent {
    state = {
        loading: false,
        today: {
            serviceInvokeCount: 0, // 调用量
            successCount: 0, // 成功量
            successRate: 0, // 成功率
            failCount: 0, // 失败量
            failRate: 0, // 失败率
            nonSearchCount: 0, // 库无量
            nonSearchRate: 0 // 库无率
        },
        yesterday: {
            serviceInvokeCount: 0, // 调用量
            successCount: 0, // 成功量
            successRate: 0, // 成功率
            failCount: 0, // 失败量
            failRate: 0, // 失败率
            nonSearchCount: 0, // 库无量
            nonSearchRate: 0 // 库无率
        },
        all: {
            serviceInvokeCount: 0, // 调用量
            successCount: 0, // 成功量
            successRate: 0, // 成功率
            failCount: 0, // 失败量
            failRate: 0, // 失败率
            nonSearchCount: 0, // 库无量
            nonSearchRate: 0 // 库无率
        }
    };

    componentDidMount() {
        this.getHeaderData(1);
        this.getHeaderData(2);
        this.getHeaderData(3);
    }

    changeTabs = (key) => {
        // this.getHeaderData(key);
    };

    getHeaderData = (key) => {
        const params = {
            statisticDimension: key
        };
        this.setState({ loading: true });
        dataServiceAPI
            .getHeaderData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    if (!res.data) return;
                    const { serviceInvokeCount, successCount, successRate, failCount, failRate, nonSearchCount, nonSearchRate } = res.data;
                    const obj = {
                        serviceInvokeCount: serviceInvokeCount ? serviceInvokeCount : 0,
                        successCount: successCount ? successCount : 0,
                        successRate: successRate ? successRate : 0,
                        failCount: failCount ? failCount : 0,
                        failRate: failRate ? failRate : 0,
                        nonSearchCount: nonSearchCount ? nonSearchCount : 0,
                        nonSearchRate: nonSearchRate ? nonSearchRate : 0
                    };
                    if (key === 1) this.setState({ today: { ...obj } });
                    if (key === 2) this.setState({ yesterday: { ...obj } });
                    if (key === 3) this.setState({ all: { ...obj } });
                } else {
                    message.error(res.msg);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    render() {
        const { loading, today, yesterday, all } = this.state;

        return (
            <div className="m-dataservice-header">
                <Card hoverable bordered={false} style={{ minHeight: '200px' }}>
                    <Tabs defaultActiveKey="1" onChange={this.changeTabs}>
                        <TabPane tab={I18N.header.index.jinTian} key="1">
                            <BoxItem data={today} />
                        </TabPane>
                        <TabPane tab={I18N.header.index.zuoTian} key="2">
                            <BoxItem data={yesterday} />
                        </TabPane>
                        <TabPane tab={I18N.header.index.quanBu} key="3">
                            <BoxItem data={all} />
                        </TabPane>
                    </Tabs>
                </Card>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(Header);
