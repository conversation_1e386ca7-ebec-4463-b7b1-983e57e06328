/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 饼状图表组件 - 采用echarts第三方库 - macarons主题
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts/lib/echarts';
// 再引入你需要使用的图表类型，标题，提示信息等
import 'echarts/lib/chart/pie';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';

import '../Theme/macarons';

export default class PieChart extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw(idName, chartData) {
		if (!chartData) return;
		const { type, name } = this.props;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, 'macarons');
		const option = {
			tooltip: {
				trigger: 'item',
				formatter: type ? '{a} <br/>{b} : {c}' : '{a} <br/>{b} : {c}%'
			},
			series: [
				{
					name: name ? name : I18N.piechart.index.shuJuLeiXing,
					minAngle: 5, // 最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
					avoidLabelOverlap: true, // 是否启用防止标签重叠策略
					type: 'pie',
					radius: '65%',
					center: ['50%', '50%'],
					data: chartData.seriesData ? chartData.seriesData : [],
					itemStyle: {
						emphasis: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)'
						}
					},
					label: {
						formatter: (params) => {
							const value = params.name;
							let val = null;
							if (value) {
								if (value.length < 10) {
									val = value;
								} else {
									val = `${value.substr(0, 10)}...`;
								}
							}
							return val;
						}
					}
				}
			]
		};
		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
			if (type === 'click') {
				myChart.off('click');
				myChart.on('click', (params) => {
					const { serviceType } = params.data;
					this.props.onClick(serviceType);
				});
			}
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div id={idName} style={{ height: realHeight }}></div>
		);
	}
}
