## 堆叠柱状图表组件 - 采用echarts第三方库 - macarons主题

## props参数：
| 参数         | 类型    |  默认值   | 是否必填 | 描述  |
| :--------:  | :-----: | :----:   | :----: |:----: |
| height      | String  |   300    | 否      | 图表的高度 |
| idName      | String  |   -      | 必填    | 容器的ID |
| chartData   | Object  |   -      | 必填    | 图表数据 |

## chartData结构
| 参数        | 类型     |  说明    |
| :--------:  | :-----:  | :----:   |
| legend.data | Array    | 图例数据 |
| xAxis.rotate | Number  | X轴标签旋转的角度  |
| xAxis.data  | Array    | X轴数据  |
| series      | Array    | 图表数据 |

## series结构
```
series: [
            {
                name:"通过",
                type:"bar",
                stack: "总量",
                itemStyle: {
                    barBorderRadius: 4,
                    color: "#4fcb73"
                },
                data:[2.0, 4.9, 7.0, 23.2, 25.6]
            },
            {
                name:"拒绝",
                type:"bar",
                stack: "总量",
                itemStyle: {
                    barBorderRadius: 4,
                    color: "#4fcb73"
                },
                data:[2.6, 5.9, 9.0, 26.4, 28.7]
            },
            {
                name:"人工审核",
                type:"bar",
                stack: "总量",
                itemStyle: {
                    barBorderRadius: 4,
                    color: "#4fcb73"
                },
                data:[1.6, 4.9, 8.0, 20.4, 20.7]
            },
        ]
```
