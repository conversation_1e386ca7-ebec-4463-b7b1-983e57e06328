import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Row, Col, Icon, Tooltip } from 'tntd';
import AHref from '@/components/AHref';
import NoPermission from '@/components/NoPermission';
import { formatNumComma, toChinese } from '@/utils/utils';
import OverViewAPI from './service';
import './index.less';
export default () => {
    const [data, setData] = useState({});
    const { businessOverviewBoard = {}, systemOverviewBoard = {} } = data || {};
    const { policyOverviewBoard = {}, indexOverviewBoard = {} } = businessOverviewBoard || {}; // 业务资产概览
    const { orgBoard = {} } = systemOverviewBoard || {};
    // TODO: 以下权限控制待完善
    useEffect(() => {
        if (window.auth('TZ0103', 'SystemOverview')) {
            OverViewAPI.getOverviewSystem().then((res) => {
                setData(res?.data || {});
            });
        }
    }, []);
    if (!window.auth('TZ0103', 'SystemOverview')) {
        return <NoPermission />;
    }
    return (
        <div className="dashboard-overview" id="testId">
            <Row type="flex" className="over-view-wrap-row over-view-wrap-large">
                <div className="dashboard-total dashboard-business">
                    <h4>
                        {I18N.overviewknowledge.index.yeWuZiChanGai}{/* <Tooltip title="统计生效（上线、启用、正式等）状态数据">
                            <Icon type="exclamation-circle" className="icon-info-warn" />
                        </Tooltip> */}
                    </h4>
                    {/* <div className="total-count-wrap">
                        <h5>{formatNumComma(policyOverviewBoard?.total || 0)}</h5>
                        策略总计
                    </div> */}
                </div>
                <Row type="flex" className="dashboard-detail" gutter={10} style={{ margin: 0 }}>
                    {[
                        {
                            title: I18N.overviewknowledge.index.baoWen,
                            key: 'documentTypeCount',
                            className: 'documentTypeCount',
                            href: '/params/parameter?currentTab=1&valid=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.zhiBiaoMuBan,
                            key: 'indexTemplateCount',
                            className: 'indexTemplateCount',
                            href: '/params/template?currentTab=1&valid=1&status=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.yanShengZhiBiao,
                            key: 'indexCount',
                            className: 'indexCount',
                            href: '/params/target?currentTab=1&status=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.zhiBiaoJi,
                            key: 'indexPackageCount',
                            className: 'indexPackageCount',
                            href: '/params/indexpackage'
                        },
                        {
                            title: I18N.overviewknowledge.index.sanFangJieKou,
                            key: 'dataSourceServiceCount',
                            className: 'dataSourceService',
                            href: '/handle/supplierManagement/dataServiceList?currentTab=1&valid=1&status=1'
                        }
                    ].map((item, index) => {
                        return (
                            <Col span={6} className={index !== 4 && 'mb-10'} key={item.key}>
                                <AHref target="_blank" href={item.href}>
                                    <div className="detail-item business">
                                        <div className="detail-item-info">
                                            <div className={`icon ${item.className}`} />
                                        </div>
                                        <div className="detail-item-info">
                                            <h5>{formatNumComma(businessOverviewBoard?.[item.key] || 0)}</h5>
                                            {item.title}
                                        </div>
                                    </div>
                                </AHref>
                            </Col>
                        );
                    })}

                    {[
                        {
                            title: I18N.overviewknowledge.index.ziDuanGuanLi,
                            key: 'fieldCount',
                            className: 'fieldCount',
                            href: '/bridge/fields/fieldManage?currentTab=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.eTLChuLi,
                            key: 'etlCount',
                            className: 'etlCount',
                            href: '/handle/supplierManagement/etl?status=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.heZuoFang,
                            key: 'partnerCount',
                            className: 'partnerCount',
                            href: '/handle/supplierManagement/supplierList?status=1'
                        }
                    ].map((item) => {
                        return (
                            <Col span={6} key={item.key}>
                                <AHref target="_blank" href={item.href}>
                                    <div className="detail-item business">
                                        <div className="detail-item-info">
                                            <div className={`icon ${item.className}`} />
                                        </div>
                                        <div className="detail-item-info">
                                            <h5>{formatNumComma(businessOverviewBoard?.[item.key] || 0)}</h5>
                                            {item.title}
                                        </div>
                                    </div>
                                </AHref>
                            </Col>
                        );
                    })}
                </Row>
            </Row>

            <Row type="flex" className="over-view-wrap-row over-view-wrap-small mt20 mb20">
                <div className="dashboard-total dashboard-system">
                    <h4>
                        {I18N.overviewknowledge.index.xiTongGaiLan}{/* <Tooltip title="统计生效（上线、启用、正式等）状态数据">
                            <Icon type="exclamation-circle" className="icon-info-warn" />
                        </Tooltip> */}
                    </h4>
                </div>
                <Row type="flex" className="dashboard-detail" gutter={10} style={{ margin: 0 }}>
                    <Col span={6} className="org-wrap">
                        <div className="detail-item  system">
                            <div className="org-scroll">
                                <AHref target="_blank" href="/bridge/permission/organization">
                                    {Object.keys(orgBoard)?.map((key, i) => {
                                        return (
                                            <div className="detail-item-info" key={i}>
                                                <h5>{formatNumComma(orgBoard[key] || 0)}</h5>
                                                {toChinese(key)}{I18N.overviewknowledge.index.jiJiGou}</div>
                                        );
                                    })}
                                </AHref>
                            </div>
                        </div>
                    </Col>
                    {[
                        {
                            title: I18N.overviewknowledge.index.quDao,
                            key: 'appCount',
                            className: 'appCount',
                            href: '/bridge/application'
                        },
                        {
                            title: I18N.overviewknowledge.index.diaoYongFang,
                            key: 'channelServiceCount',
                            className: 'channelServiceCount',
                            href: '/handle/appServiceCenter/appServiceList?status=1'
                        },
                        {
                            title: I18N.overviewknowledge.index.diaoYongFangZu,
                            key: 'channelServiceGroupCount',
                            className: 'channelServiceGroupCount',
                            href: '/handle/appServiceCenter/serviceGroup?status=1'
                        }
                    ].map((item) => {
                        return (
                            <Col span={6} key={item.key}>
                                <AHref target="_blank" href={item.href}>
                                    <div className="detail-item system">
                                        <div className="detail-item-info">
                                            <div className={`icon ${item.className}`} />
                                        </div>
                                        <div className="detail-item-info">
                                            <h5>{formatNumComma(systemOverviewBoard?.[item.key] || 0)}</h5>
                                            {item.title}
                                        </div>
                                    </div>
                                </AHref>
                            </Col>
                        );
                    })}
                </Row>
            </Row>
        </div>
    );
};
