.dashboard-overview {
  height: 100%;
  padding: 20px;
  padding-bottom: 0;
  line-height: 1.5;
  overflow-y: scroll;
}
.dashboard-overview .icon-info-warn {
  font-size: 14px;
  margin-left: 10px;
}
.dashboard-overview .over-view-wrap-row {
  flex-wrap: nowrap;
}
.dashboard-overview .over-view-wrap-row.over-view-wrap-large {
  height: 60%;
  min-height: 450px;
}
.dashboard-overview .over-view-wrap-row.over-view-wrap-small {
  height: 35%;
  min-height: 220px;
}
.dashboard-overview .over-view-wrap-row.mt-30 {
  margin-top: 30px;
}
.dashboard-overview .dashboard-total {
  width: 228px;
  border-radius: 2px;
  margin-right: 5px;
  position: relative;
}
.dashboard-overview .dashboard-total.dashboard-business {
  background-image: linear-gradient(0deg, #1593F9 1%, #0D6EFB 97%);
}
.dashboard-overview .dashboard-total.dashboard-business::after {
  content: "";
  background: url(../../../sources/images/overview/business_bg.svg) no-repeat;
  height: 165px;
}
.dashboard-overview .dashboard-total.dashboard-business h4 {
  display: flex;
  align-items: center;
}
.dashboard-overview .dashboard-total.dashboard-business h4::before {
  content: '';
  background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 9%, rgba(255, 255, 255, 0.32) 100%);
}
.dashboard-overview .dashboard-total.dashboard-system {
  background-image: linear-gradient(0deg, #00CF78 0%, #00BA70 100%);
}
.dashboard-overview .dashboard-total.dashboard-system::after {
  content: "";
  background: url(../../../sources/images/overview/system_bg.svg) no-repeat;
  height: 135px;
}
.dashboard-overview .dashboard-total.dashboard-system h4 {
  display: flex;
  align-items: center;
}
.dashboard-overview .dashboard-total.dashboard-system h4::before {
  content: '';
  background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 9%, rgba(255, 255, 255, 0.32) 100%);
}
.dashboard-overview .dashboard-total::after {
  content: "";
  width: 190px;
  position: absolute;
  bottom: 0;
  right: 0;
}
.dashboard-overview .dashboard-total h4 {
  font-family: PingFangSC-Medium;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 500;
  position: relative;
  margin-top: 30px;
  padding-left: 20px;
}
.dashboard-overview .dashboard-total h4::before {
  content: '';
  position: absolute;
  width: 5px;
  height: 20px;
  left: 0;
  top: 5px;
}
.dashboard-overview .dashboard-total .total-count-wrap {
  padding-left: 20px;
  margin-top: 50px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 0;
  margin-bottom: 10px;
}
.dashboard-overview .dashboard-total .total-count-wrap h5 {
  font-family: DINAlternate-Bold;
  font-size: 40px;
  color: #FFFFFF;
  font-weight: 700;
  margin: 0;
}
.dashboard-detail {
  flex: 1;
  margin: 0 !important;
  overflow: hidden;
}
.dashboard-detail .mb-10 {
  margin-bottom: 10px;
}
.dashboard-detail .detail-item {
  background: #FFFFFF;
  border-radius: 2px;
  height: 100%;
  padding: 40px 10px 20px 30px;
  display: flex;
  flex-wrap: wrap;
  cursor: pointer;
  position: relative;
}
.dashboard-detail .detail-item a {
  display: inline-block;
  width: 100%;
  color: #8B919E;
}
.dashboard-detail .detail-item a:hover {
  opacity: 0.65;
  transition: all ease 0.3s;
}
.dashboard-detail .detail-item:hover {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  transition: all ease-in 0.1s;
}
.dashboard-detail .detail-item:hover::before {
  content: "";
  animation: hoverIn 1s forwards;
  position: absolute;
  height: 3px;
  top: 0;
  left: 0;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.dashboard-detail .detail-item.business:hover::before {
  content: "";
  background-image: linear-gradient(270deg, #1593F9 8%, #0D6EFB 96%);
}
.dashboard-detail .detail-item.system:hover::before {
  content: "";
  background-image: linear-gradient(270deg, #00CF78 10%, #00BA70 96%);
}
.dashboard-detail .detail-item .org-scroll {
  height: 100%;
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}
.dashboard-detail .detail-item .org-scroll .detail-item-info {
  height: 52px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.dashboard-detail .detail-item .org-scroll .detail-item-info h5 {
  margin-bottom: 6px;
}
.dashboard-detail .detail-item .detail-item-info {
  width: 100%;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  height: 70px;
  color: #8B919E;
}
.dashboard-detail .detail-item .detail-item-info:not(:first-of-type) {
  margin-top: 20px;
}
.dashboard-detail .detail-item .detail-item-info h5 {
  font-family: DIN-Bold;
  font-size: 24px;
  color: #17233D;
  font-weight: 700;
  margin: 0;
  line-height: 24px;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dashboard-detail .detail-item .icon {
  width: 40px;
  height: 40px;
}
.dashboard-detail .detail-item .icon.documentTypeCount {
  background: url(../../../sources/images/overview/documentTypeCount.svg);
}
.dashboard-detail .detail-item .icon.indexTemplateCount {
  background: url(../../../sources/images/overview/indexTemplateCount.svg);
}
.dashboard-detail .detail-item .icon.indexCount {
  background: url(../../../sources/images/overview/indexCount.svg);
}
.dashboard-detail .detail-item .icon.indexPackageCount {
  background: url(../../../sources/images/overview/indexPackageCount.svg);
}
.dashboard-detail .detail-item .icon.dataSourceService {
  background: url(../../../sources/images/overview/dataSourceService.svg);
}
.dashboard-detail .detail-item .icon.fieldCount {
  background: url(../../../sources/images/overview/fieldCount.svg);
}
.dashboard-detail .detail-item .icon.etlCount {
  background: url(../../../sources/images/overview/etlCount.svg);
}
.dashboard-detail .detail-item .icon.partnerCount {
  background: url(../../../sources/images/overview/partnerCount.svg);
}
.dashboard-detail .detail-item .icon.appCount {
  background: url(../../../sources/images/overview/appCount.svg);
}
.dashboard-detail .detail-item .icon.channelServiceCount {
  background: url(../../../sources/images/overview/channelServiceCount.svg);
}
.dashboard-detail .detail-item .icon.channelServiceGroupCount {
  background: url(../../../sources/images/overview/channelServiceGroupCount.svg);
}
.org-wrap {
  height: 100%;
  overflow: hidden;
}
@keyframes hoverIn {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
