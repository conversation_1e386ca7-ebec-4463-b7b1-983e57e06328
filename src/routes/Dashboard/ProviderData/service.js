import { getUrl, deleteEmptyObjItem } from '@/utils/common';
import request from '@/utils/request';

// 实时概览
const getWholeView = async (params) => {
    return request(getUrl('/statistics/thirdService/getWholeView', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 调用明细统计
const getInvokeDetailStat = async (params) => {
    return request(getUrl('/statistics/getInvokeDetailStat', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 调用状态和耗时明细 按天调用明细
const getInvokeDetailForDay = async (params) => {
    return request(getUrl('/statistics/thirdService/getInvokeDetailForDay', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 获取所有三方数据
const getServiceConfig = async (params) => {
    params.includeDelete = true;
    return request(
        getUrl('/bridgeApi/serviceConfig/listAll', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 服务调用排行榜
const getInvokeRankStat = async (params) => {
    return request(getUrl('/statistics/getInvokeRankStat', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 错误码统计
const getReasonCodeStatistic = async (params) => {
    return request(getUrl('/statistics/getReasonCodeStatistic', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 调用来源类型分布明细
const getInvokeSourceTypeStat = async (params) => {
    return request(getUrl('/statistics/getInvokeSourceTypeStat', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 手动统计
const statistical = async () => {
    const params = {
        type: 2
    };
    return request('/statistics/es/statics', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

export default {
    getWholeView,
    getInvokeDetailForDay,
    getServiceConfig,
    getInvokeRankStat,
    getReasonCodeStatistic,
    getInvokeDetailStat,
    getInvokeSourceTypeStat,
    statistical
};
