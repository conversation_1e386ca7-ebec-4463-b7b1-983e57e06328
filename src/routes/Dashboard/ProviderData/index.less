:global {
    .p-provider-data {
        .section {
            margin-bottom: 15px;
            background: #fff;
            padding: 0 14px;
            padding-bottom: 20px;
            border-radius: @border-radius-base;

			&:last-child {
				margin-bottom: 0;
			}

			.header, .header2 {
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                h2 {
                    font-weight: initial;
                    font-size: 14px;
                    color: #333333;
                }

                .filter-search {
                    display: flex;
                    align-items: center;
                    .ant-select {
                        margin-left: 10px;
                    }
                }
            }

            .filter-results {
                display: flex;
                align-items: center;
                height: 130px;
                background: #F9F9FC;
                border-radius: 2px;
                margin-bottom: 20px;
                border-radius: calc(@border-radius-base);

                .item {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.5s;
                    height: 130px;
                    &:hover{
                        background: #fff;
                    }
                    p {
                        margin: 0;
                    }

                    .title {
                        font-size: 14px;
                        color: #8B94AB;
                    }

                    .percent {
                        font-size: 24px;
                        color: #333333;
                    }

                    .num {
                        font-size: 12px;
                        color: #8B94AB;
                    }

                    .pic {
                        width: 60px;
                        height: 14px;
                        margin-bottom: 4px;
                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }

            .bottom-chart {
                padding: 0 16px;
                margin-top: 10px;

                .ant-card {
                    border-radius: calc(@border-radius-base);
                }
                .ant-card-head {
                    background: #F9F9FC;
                    height: 46px;
                    font-size: 14px;
                    color: #606266;
                    display: flex;
                    align-items: center;
                    font-weight: initial;
                }

                .invokeRank {
                    height: 290px;
                    overflow: hidden;
                    padding-top: 5px;
                    .ant-spin-container {
                        height: 290px;
                    }

                    .ant-table-thead>tr>th,
                    .ant-table-tbody>tr>td {
                        padding: 9px 16px;
                        font-size: 14px;
                        color: #909399;
                        font-weight: initial;
                        background: #fff;
                    }

                    .ant-table-pagination.ant-pagination {
                        margin: 10px 10px 0 0;
                        float: none;
                        position: absolute;
                        bottom: -10px;
                        .ant-pagination-item {
                            border: 1px solid transparent;
                        }

                        .ant-pagination-item-link {
                            // border: 1px solid transparent;
                        }
                    }

                    .invoke-count {
                        display: flex;
                        align-items: center;

                        .count {
                            margin: 0;
                            width: 60px;
                            text-align: left;
                            overflow: hidden;
                        }

                        .percent {
                            width: 120px;
                            background: #3A84FF;
                            box-shadow: 0 2px 7px 0 rgba(0, 122, 255, 0.28);
                            border-radius: 4px;
                            height: 8px;
                            margin: 0;
                        }
                    }
                }
            }

            .card-filter {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .ant-select {
                    margin-left: 10px;
                }
            }

            .average-time {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .card-container {
                border-radius: calc(@border-radius-base);

                .ant-card-head {
                    background: none;
                }
                .title {
                    font-size: 14px;
                    font-weight: initial;
                    color: #606266;
                    padding: 8px 0 10px;
                    margin: 0;
                }

                .ant-card-body {
                    padding: 0;
                    padding-bottom: 16px;
                }

                .pd16 {
                    padding: 16px;
                }

                .divider-line {
                    width: 100%;
                    height: 1px;
                    background: #EBEEF5;
                }
            }
            .ant-empty {
                margin: 0;
                padding: 30px;
            }
            .header2{
                height: 32px;
                margin-bottom: 20px;
                justify-content: flex-end;
            }
            .u-title{
                font-size: 16px;
                font-weight: initial;
                color: #333333;
                padding: 8px 0 10px;
                margin: 0;
                .u-time{
                    font-size: 12px;
                    margin: 0 10px;
                }
                .ant-btn{
                    font-size: 12px;
                }
            }
        }

        .realtime {

            .title {
                font-size: 16px;
                font-weight: initial;
                color: #333333;
                padding: 8px 0 10px;
                margin: 0;
            }

            .header {
                position: relative;
                width: 100%;
                height: 60px;
                background-size: cover;
                background-repeat: no-repeat;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-radius: 2px 2px 0 0;

                .profile {
                    color: #fff;
                    font-size: 16px;
                    padding-left: 16px;
                    height: 60px;
                    display: flex;
                    align-items: center;

                    .u-num {
                        font-size: 34px;
                        padding: 0 2px 0 10px;
                    }
                    .u-sub{
                        display: inline-block;
                        margin-top: 13px;
                        font-size: 12px;
                    }
                    .u-img{
                        margin: 0 0 0 5px;
                    }

                    .line {
                        width: 1px;
                        height: 20px;
                        background: #fff;
                        margin: 0 10px;
                    }
                }

                .tag {
                    position: absolute;
                    top: -3px;
                    right: 20px;
                    width: 53px;
                    height: 46px;
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    color: #fff;
                    font-size: 12px;
                    text-align: center;
                    line-height: 33px;
                }
            }

            .container {
                border: 1px solid #EBEEF5;
                // border-radius: 2px;
                border-radius: calc(@border-radius-base);
                
                .header {
                    border-top-left-radius: calc(@border-radius-base);
                    border-top-right-radius: calc(@border-radius-base);
                }
                .borderR {
                    border-right: 1px solid #EBEEF5;
                }

                .title {
                    padding-left: 16px;
                    color: #606266;
                    margin-top: 12px;
                }
            }
        }

        .ant-card-head {
            background: #F9F9FC;
            padding: 0 16px;
            font-size: 14px;
            color: #333333;
            font-weight: initial;
        }
    }
}
