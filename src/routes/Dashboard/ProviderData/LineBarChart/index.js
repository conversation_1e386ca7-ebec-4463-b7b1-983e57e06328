import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';

export default class LineBarChart extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw = (idName, chartData) => {
		if (!chartData.length) return;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, 'business');

		const seriesData = chartData.map(res => {
			return res.count;
		});
		const xAxisData = chartData.map(res => {
			return res.serviceName;
		});

		const option = {
			color: ['#007AFF'],
			tooltip: {
				trigger: 'axis',
				axisPointer: { // 坐标轴指示器，坐标轴触发有效
					type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
				}
			},
			grid: {
				left: 20,
				right: 20,
				bottom: 30,
				top: 30,
				containLabel: true
			},
			xAxis: {
				type: 'category',
				data: xAxisData,
				axisLine: {
					lineStyle: {
						color: '#EBEEF5'
					}
				},
				axisLabel: {
					color: '#909399'
				},
				axisTick: {
					show: false
				}
			},
			dataZoom: [
				{
					type: 'slider',
					show: true,
					height: 12,
					xAxisIndex: [0],
					bottom: '2%',
					start: 0,
					end: 90,
					textStyle: {
						color: '#909399'
					}
				}
			],
			yAxis: {
				type: 'value',
				axisLine: {
					show: false,
					lineStyle: {
						color: '#909399'
					}
				},
				axisTick: {
					show: false
				},
				splitArea: {
					areaStyle: {
						color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
					}
				},
				splitLine: {
					lineStyle: {
						color: ['#EBEEF5', '#EBEEF5'],
						type: 'solid'
					}
				}
			},
			series: [
				{
					name: I18N.linebarchart.index.sanFangFuWuJie,
					type: 'bar',
					barWidth: 15,
					data: seriesData
				}
			]
		};

		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName, chartData } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div style={{ height: realHeight }}>
				{
					chartData.length
						? <div id={idName} style={{ height: realHeight }}></div> : null
				}
				{
					!chartData.length &&
					<NoData />
				}
			</div>
		);
	}
}
