import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Select, Card, Row, Col, Icon, Radio, Tooltip, Spin, Button, message, PageContainer } from 'tntd';
import service from './service';

import { chartImages } from '@/constants/images';

import <PERSON><PERSON><PERSON> from './PieChart';
import Pie<PERSON>hart2 from './PieChart2';
import PieChart3 from './PieChart3';
import LineBarChart2 from './LineBarChart2';
import LineBarChart3 from './LineBarChart3';
import TopTable from '../common/TopTable';
import RangeData from '../common/RangeData';
import CodePieChart from '../common/CodePieChart';

import './index.less';
import { dataServiceListAPI } from '@/services';

const Option = Select.Option;

const timeMap = {
    '200': {
        minCost: 0,
        maxCost: 200
    },
    '500': {
        minCost: 200,
        maxCost: 500
    },
    '1000': {
        minCost: 500,
        maxCost: 1000
    },
    '5000': {
        minCost: 1000,
        maxCost: 5000
    },
    '5000d': {
        minCost: 5000,
        maxCost: ''
    },
    all: {
        minCost: undefined,
        maxCost: undefined
    }
};

class ConsumerData extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            filter: {},
            subfilter: {
                averageTime: 'all',
                minCost: undefined,
                maxCost: undefined
            },
            threeServiceList: [],
            loading: false
        };
    }

    componentDidMount() {
        const { dispatch } = this.props;
        dispatch({
            type: 'provider/getWholeView'
        });

        this.getServiceList();
    }

    componentDidUpdate(preProps) {
        const preLang = preProps.globalStore.personalMode.lang;
        const nextLang = this.props.globalStore.personalMode.lang;
        if (preLang !== nextLang) {
            const { dispatch } = this.props;
            dispatch({
                type: 'provider/getWholeView'
            });
            this.filterDetail();
        }
    }

    getServiceList = () => {
        dataServiceListAPI.getListAll2().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res?.data?.contents ? res?.data?.contents : []
                });
            } else {
                message.error(res.msg || res?.message);
            }
        });
    };

    filterDetail = (params = {}) => {
        const { dispatch } = this.props;
        const { filter, subfilter } = this.state;
        dispatch({
            type: 'provider/getInvokeDetailStat',
            payload: {
                invokeType: 2,
                ...params
            }
        });
        dispatch({
            type: 'provider/getInvokeDetailForDay',
            payload: {
                ...filter,
                ...subfilter,
                ...params
            }
        });
        dispatch({
            type: 'provider/getInvokeRankStat',
            payload: {
                invokeType: 2,
                ...filter,
                ...params
            }
        });
        dispatch({
            type: 'provider/getReasonCodeStatistic',
            payload: {
                invokeType: 2,
                ...filter,
                ...params
            }
        });
        dispatch({
            type: 'provider/getInvokeSourceTypeStat',
            payload: {
                invokeType: 2,
                ...filter,
                ...params
            }
        });
    };

    handleChange = (val) => {
        const { filter } = this.state;
        const startTime = moment(val.startTime).format('YYYY/MM/DD');
        const endTime = moment(val.endTime).format('YYYY/MM/DD');
        const startDate = Number(new Date(`${startTime} 00:00:00`));
        const endDate = Number(new Date(`${endTime} 23:59:59`));
        this.filterDetail({
            ...filter,
            startDate,
            endDate
        });
        this.setState({
            filter: {
                ...filter,
                startDate,
                endDate
            }
        });
    };

    handleSelectChange = (val) => {
        const { filter } = this.state;
        this.filterDetail({
            ...filter,
            serviceCode: val
        });
        this.setState({
            filter: {
                ...filter,
                serviceCode: val
            }
        });
    };
    handleSelectTypeChange = (val) => {
        const { dispatch } = this.props;
        const { filter } = this.state;
        this.filterDetail({
            ...filter,
            serviceType: val,
            serviceCode: null
        });
        this.setState({
            filter: {
                ...filter,
                serviceType: val,
                serviceCode: null
            }
        });
        dispatch({
            type: 'provider/getServiceConfig',
            payload: {
                dataType: val
            }
        });
    };

    handleSelectInvokeFilterChange = (val, key) => {
        const { dispatch } = this.props;
        const { filter, subfilter } = this.state;
        this.setState({
            subfilter: {
                ...subfilter,
                [key]: val
            }
        });
        dispatch({
            type: 'provider/getInvokeDetailForDay',
            payload: {
                ...filter,
                ...subfilter,
                [key]: val
            }
        });
    };

    handleRadioButtonChange = (val) => {
        const value = val.target.value;
        const { dispatch } = this.props;
        const { filter, subfilter } = this.state;

        dispatch({
            type: 'provider/getInvokeDetailMs',
            payload: {
                ...filter,
                ...subfilter,
                ...timeMap[value]
            }
        });

        this.setState({
            subfilter: {
                ...subfilter,
                ...timeMap[value],
                averageTime: value
            }
        });
    };

    // 点击跳转 - 三方调用明细
    handleClick = (errorCode) => {
        const { filter } = this.state;
        const { startDate, endDate, serviceCode, serviceType } = filter;
        let path = '/handle/dataManagement/threeCallDetail';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    date: [startDate, endDate],
                    serviceCode,
                    errorCode,
                    returnResult: 'fail',
                    serviceType
                })
            )}`,
            '_blank'
        );
    };

    // 点击跳转 - 三方调用明细
    goToPage = (invokeSource) => {
        const { filter } = this.state;
        const { startDate, endDate, serviceCode } = filter;
        let path = '/handle/dataManagement/threeCallDetail';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    date: [startDate, endDate],
                    serviceCode,
                    invokeSource
                })
            )}`,
            '_blank'
        );
    };

    // 手动统计
    manual = () => {
        this.setState({ loading: true });
        service
            .statistical()
            .then((res) => {
                if (res && res.success) {
                    setTimeout(() => {
                        const { filter } = this.state;
                        this.filterDetail({
                            ...filter
                        });
                        this.setState({ loading: false });
                    }, 3000);
                } else {
                    message.error(res.msg);
                    this.setState({ loading: false });
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    render() {
        const { store, globalStore } = this.props;
        const { subfilter, filter, loading, threeServiceList } = this.state;
        const { serviceType: serviceType2, serviceCode } = filter;
        const { allMap } = globalStore;
        const {
            overview,
            detail,
            averageTime,
            invokeStatus,
            invokeRank,
            codeData,
            sourceType,
            overviewLoading,
            invokeStatusLoading,
            averageTimeLoading
        } = store;
        const { changeNum, total, providerRanks = [], typeRanks = [] } = overview;
        const {
            successCount = 0,
            successRate = 0,
            failCount = 0,
            succAvgCost = 0,
            failAvgCost = 0,
            searchCount = 0,
            searchRate = 0,
            statTime = 0,
            failRate = 0
        } = detail;

        const serviceType = threeServiceList;

        const successR = (successRate * 100).toFixed(2);
        const searchR = (searchRate * 100).toFixed(2);
        const failR = (failRate * 100).toFixed(2);

        return (
            <div className="p-provider-data">
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2> */}
                {/* 数据源大盘 */}
                {/* {I18N.providerdata.index.shuJuYuanDaPan}
                        </h2>
                    </div>
                </div> */}
                <div className="page-global-body">
                    <div className="realtime section">
                        <h2 className="title">
                            {/* 实时概览 */}
                            {I18N.providerdata.index.shiShiGaiLan}
                        </h2>
                        <div className="container">
                            <div
                                className="header"
                                style={{
                                    backgroundImage: `url(${chartImages.header})`
                                }}>
                                <div className="profile">
                                    {/* 数据源服务接口总数 */}
                                    {I18N.providerdata.index.shuJuYuanFuWu3}
                                    <span className="u-num">{total}</span>
                                    <span className="u-sub">
                                        {/* 个 */}
                                        {I18N.providerdata.index.ge === 'a' ? null : I18N.providerdata.index.ge}
                                    </span>
                                    <i className="line" />
                                    {/* 环比昨天 */}
                                    {I18N.providerdata.index.huanBiZuoTian}
                                    {changeNum >= 0 && <img src={chartImages.arrowUp} className="u-img" />}
                                    {changeNum < 0 && <img src={chartImages.arrowDown} className="u-img" />}
                                    <span className="u-num">{changeNum && Math.abs(changeNum)}</span>
                                    <span className="u-sub">
                                        {/* 个 */}
                                        {I18N.providerdata.index.ge === 'a' ? null : I18N.providerdata.index.ge}
                                    </span>
                                </div>
                                <div
                                    className="tag"
                                    style={{
                                        backgroundImage: `url(${chartImages.tag})`
                                    }}>
                                    {/* 生效中 */}
                                    {I18N.providerdata.index.shengXiaoZhong}
                                </div>
                            </div>
                            <Row>
                                <Col span={12} className="borderR">
                                    <h2 className="title">
                                        {/* 合作方分布 */}
                                        {I18N.providerdata.index.heZuoFangFenBu}
                                    </h2>
                                    <Spin spinning={overviewLoading}>
                                        <PieChart idName="providerRanks" chartData={providerRanks} />
                                    </Spin>
                                </Col>
                                <Col span={12}>
                                    <h2 className="title">
                                        {/* 数据类型分布 */}
                                        {I18N.providerdata.index.shuJuLeiXingFen}
                                    </h2>
                                    <Spin spinning={overviewLoading}>
                                        <PieChart2 idName="typeRanks" chartData={typeRanks} />
                                    </Spin>
                                </Col>
                            </Row>
                        </div>
                    </div>
                    <div className="section">
                        <h2 className="u-title">
                            {/* 调用统计 */}
                            {I18N.providerdata.index.diaoYongTongJi}
                            {/* 统计结果有5~10分钟左右的延迟 */}
                            <Tooltip title={I18N.providerdata.index.tongJiJieGuoYou}>
                                <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                            </Tooltip>
                            <span className="u-time">
                                {/* 上一次统计时间 */}
                                {I18N.providerdata.index.shangYiCiTongJi}：
                                {statTime ? moment(statTime).format('MM-DD HH:mm:ss') : I18N.providerdata.index.wu}
                            </span>
                            <Button size="small" onClick={this.manual} loading={loading}>
                                {/* 手动统计 */}
                                {I18N.providerdata.index.shouDongTongJi}
                            </Button>
                        </h2>
                        <div className="header2">
                            <div style={{ position: 'relative' }} id="property-select2" className="filter-search">
                                <RangeData className="rangedata" handleChange={this.handleChange} />
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '120px' }}
                                    getPopupContainer={() => document.getElementById('property-select2')}
                                    placeholder={I18N.providerdata.index.shuJuLeiXing} // 请选择数据类型
                                    optionFilterProp="children"
                                    value={serviceType2 || undefined}
                                    onChange={this.handleSelectTypeChange}>
                                    {allMap &&
                                        allMap.serviceTypeList &&
                                        allMap.serviceTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.dataType} key={index} title={item.name}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '170px' }}
                                    getPopupContainer={() => document.getElementById('property-select2')}
                                    placeholder={I18N.providerdata.index.shuJuYuanFuWu2} // 请选择三方服务接口
                                    optionFilterProp="children"
                                    value={serviceCode || undefined}
                                    onChange={this.handleSelectChange}>
                                    {serviceType &&
                                        serviceType
                                            .filter(({ dataType }) => {
                                                if (serviceType2 === null) {
                                                    return true;
                                                }

                                                return dataType === serviceType2;
                                            })
                                            .map((item, index) => {
                                                return (
                                                    <Option value={item.name} key={index} title={item.displayName}>
                                                        {item.displayName}
                                                    </Option>
                                                );
                                            })}
                                </Select>
                            </div>
                        </div>
                        <div className="filter-results">
                            <div className="item">
                                <p className="title">
                                    {/* 调用总量/次 */}
                                    {I18N.providerdata.index.diaoYongZongLiangCi}
                                </p>
                                <p className="percent">{detail.total || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart1} />
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 成功率 */}
                                    {I18N.providerdata.index.chengGongLu}
                                    {/* 总成功率，包含查得和未查得 */}
                                    <Tooltip title={I18N.providerdata.index.zongChengGongLuBao}>
                                        <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                                    </Tooltip>
                                </p>
                                <p className="percent">{successR}%</p>
                                <p className="num">
                                    {/* 成功量  */}
                                    {I18N.providerdata.index.chengGongLiang}
                                    {successCount}
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 失败率 */}
                                    {I18N.providerdata.index.shiBaiLu}
                                </p>
                                <p className="percent">{failR}%</p>
                                <p className="num">
                                    {/* 失败量  */}
                                    {I18N.providerdata.index.shiBaiLiang}
                                    {failCount}
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 查得率 */}
                                    {I18N.providerdata.index.chaDeLu}
                                    {/* 查询三方服务接口返回结果属于“查得”状态的比率 */}
                                    <Tooltip title={I18N.providerdata.index.chaXunShuJuYuan}>
                                        <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                                    </Tooltip>
                                </p>
                                <p className="percent">{searchR}%</p>
                                <p className="num">
                                    {/* 成功(三方查得)量  */}
                                    {I18N.providerdata.index.chengGongShuJuYuan}
                                    {searchCount}
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 成功平均耗时 */}
                                    {I18N.providerdata.index.chengGongPingJunHao}/ms
                                </p>
                                <p className="percent">{succAvgCost || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart2} />
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 失败平均耗时 */}
                                    {I18N.providerdata.index.shiBaiPingJunHao}/ms
                                </p>
                                <p className="percent">{failAvgCost || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart3} />
                                </p>
                            </div>
                        </div>
                        {/* 调用状态和耗时统计 */}
                        <Card className="card-container" title={I18N.providerdata.index.diaoYongZhuangTaiHe}>
                            <div className="pd16">
                                <h2 className="title">
                                    {/* 调用状态 */}
                                    {I18N.providerdata.index.diaoYongZhuangTai}
                                </h2>
                                <Spin spinning={invokeStatusLoading}>
                                    <LineBarChart2 idName="invokeStatus" chartData={invokeStatus} />
                                </Spin>
                            </div>
                            <div className="divider-line" />
                            <div className="pd16">
                                <div className="average-time">
                                    <h2 className="title">
                                        {/* 平均耗时 */}
                                        {I18N.providerdata.index.pingJunHaoShi}
                                    </h2>
                                    <div>
                                        <Radio.Group
                                            onChange={this.handleRadioButtonChange}
                                            defaultValue={subfilter.averageTime}
                                            value={subfilter.averageTime}>
                                            <Radio.Button value="200">{I18N.providerdata.index.xiaoYu}200ms</Radio.Button>
                                            <Radio.Button value="500">200ms-500ms</Radio.Button>
                                            <Radio.Button value="1000">500ms-1s</Radio.Button>
                                            <Radio.Button value="5000">1s-5s</Radio.Button>
                                            <Radio.Button value="5000d">{I18N.providerdata.index.daYu}5s</Radio.Button>
                                            <Radio.Button value="all">{I18N.providerdata.index.quanBu}</Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                                <Spin spinning={averageTimeLoading}>
                                    <LineBarChart3 idName="averageTime" chartData={averageTime} />
                                </Spin>
                            </div>
                            <div className="bottom-chart">
                                <Row gutter={16}>
                                    <Col span={8}>
                                        {/* 数据源服务接口调用排行 */}
                                        <Card title={I18N.providerdata.index.shuJuYuanFuWu}>
                                            <TopTable invokeRank={invokeRank} />
                                        </Card>
                                    </Col>
                                    <Col span={8}>
                                        {/* 调用来源类型分布 */}
                                        <Card title={I18N.providerdata.index.diaoYongLaiYuanLei}>
                                            <PieChart3 idName="sourceType" chartData={sourceType} onClick={this.goToPage} />
                                        </Card>
                                    </Col>
                                    <Col span={8}>
                                        {/* 异常状态码分布 */}
                                        <Card title={I18N.providerdata.index.yiChangZhuangTaiMa}>
                                            <CodePieChart
                                                idName="codeStatus"
                                                chartData={codeData}
                                                onClick={this.handleClick}
                                                type="small"
                                            />
                                        </Card>
                                    </Col>
                                </Row>
                            </div>
                        </Card>
                    </div>
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    store: state.provider
}))(PageContainer(ConsumerData));
