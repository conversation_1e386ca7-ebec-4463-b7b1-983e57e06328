import service from './service';

export default {
	namespace: 'provider',
	state: {
		overview: {},
		invokeStatus: [],
		averageTime: [],
		invokeRank: [],
		threeService: null,
		codeData: [],
		detail: {},
		sourceType: [],
		overviewLoading: false,
		invokeStatusLoading: false,
		averageTimeLoading: false
	},
	effects: {
		*getWholeView({ payload }, { call, put }) {
			yield put({
				type: 'updateState',
				payload: {
					overviewLoading: true
				}
			});
			const res = yield call(service.getWholeView, payload);
			yield put({
				type: 'updateState',
				payload: {
					overview: res.data ? res.data : {},
					overviewLoading: false
				}
			});
		},
		*getInvokeDetailForDay({ payload }, { call, put }) {
			yield put({
				type: 'updateState',
				payload: {
					invokeStatusLoading: true
				}
			});
			let averageTime;
			let res;
			if (typeof payload.minCost !== 'undefined') {
				const { minCost, maxCost, ...rest } = payload;
				res = yield call(service.getInvokeDetailForDay, rest);
				averageTime = yield call(service.getInvokeDetailForDay, payload);
			} else {
				res = yield call(service.getInvokeDetailForDay, payload);
				averageTime = res;
			}

			yield put({
				type: 'updateState',
				payload: {
					invokeStatus: res.data ? res.data : [],
					averageTime: averageTime.data ? averageTime.data : [],
					invokeStatusLoading: false
				}
			});
		},
		*getInvokeDetailMs({ payload }, { call, put }) {
			yield put({
				type: 'updateState',
				payload: {
					averageTimeLoading: true
				}
			});
			const res = yield call(service.getInvokeDetailForDay, payload);
			yield put({
				type: 'updateState',
				payload: {
					averageTime: res.data ? res.data : [],
					averageTimeLoading: false
				}
			});
		},
		*getServiceConfig({ payload }, { call, put }) {
			const res = yield call(service.getServiceConfig, payload);
			yield put({
				type: 'updateState',
				payload: {
					threeService: res?.data?.contents
				}
			});
		},
		*getInvokeRankStat({ payload }, { call, put }) {
			const res = yield call(service.getInvokeRankStat, payload);
			yield put({
				type: 'updateState',
				payload: {
					invokeRank: res.data ? res.data : []
				}
			});
		},
		*getReasonCodeStatistic({ payload }, { call, put }) {
			const res = yield call(service.getReasonCodeStatistic, payload);
			yield put({
				type: 'updateState',
				payload: {
					codeData: res.data ? res.data : []
				}
			});
		},
		*getInvokeDetailStat({ payload }, { call, put }) {
			const res = yield call(service.getInvokeDetailStat, payload);
			yield put({
				type: 'updateState',
				payload: {
					detail: res.data ? res.data : {}
				}
			});
		},
		*getInvokeSourceTypeStat({ payload }, { call, put }) {
			const res = yield call(service.getInvokeSourceTypeStat, payload);
			yield put({
				type: 'updateState',
				payload: {
					sourceType: res.data ? res.data : []
				}
			});
		}

	},

	reducers: {
		updateState(state, { payload }) {
			return {
				...state,
				...payload
			};
		}
	}
};
