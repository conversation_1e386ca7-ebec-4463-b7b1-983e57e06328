import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';
import { connect } from 'dva';
class Pie<PERSON>hart extends PureComponent {
    state = {
        defaultHeight: 290
    };
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        const seriesData = chartData.map((res) => {
            return { value: res.count, name: res.sourceName, rate: res.rate, code: res.sourceCode };
        });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: (params) => {
                    const { name, value, percent } = params;
                    const tempStr = I18N.piechart3.index.dianJiTuXingCha;
                    let dom = `<span style="color:#00fcff;font-size: 12px">${tempStr}</span>`; // 点击图形查看详情
                    let str = `${name}：${value}（${percent.toFixed(2)}%）<br />${dom}`;
                    return str;
                }
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                left: '60%',
                align: 'left',
                top: 'middle',
                tooltip: {
                    show: true
                },
                icon: 'rect',
                itemWidth: 6,
                itemHeight: 6,
                borderColor: '#000',
                formatter: (name) => {
                    const cur = seriesData.find((res) => res.name === name);
                    const rate = (cur && (cur.rate * 100).toFixed(2)) || 0;
                    let value = name;
                    if (name.length > 4) {
                        value = `${name.substr(0, 4)}...`;
                    }
                    return `${value} \t ${rate}%`;
                },
                textStyle: {
                    color: '#8B94AB'
                },
                data: seriesData,
                height: 220
            },
            series: [
                {
                    name: I18N.piechart3.index.laiYuanLeiXingFen,
                    type: 'pie',
                    center: ['30%', '50%'],
                    radius: [0, '50%'],
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    itemStyle: {
                        emphasis: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    data: seriesData
                }
            ]
        };
        let dataIndex = 0;
        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
                // myChart.dispatchAction({
                // 	type: "highlight",
                // 	dataIndex: dataIndex
                // });
            });
            myChart.on('click', (params) => {
                const { code } = params.data;
                this.props.onClick(code);
            });
            // setTimeout(() => {
            // 	myChart.on("mouseover", (params) => {
            // 		const index = params.dataIndex;
            // 		const name = params.name;
            // 		const obj = seriesData.find(item => item.name === name);
            // 		if (obj && dataIndex !== index) {
            // 			console.log(obj, dataIndex, index);
            // 			myChart.dispatchAction({
            // 				type: "downplay",
            // 				dataIndex: dataIndex
            // 			});
            // 			myChart.dispatchAction({
            // 				type: "highlight",
            // 				dataIndex: index
            // 			});
            // 			dataIndex = index;
            // 		}
            // 	});

            // 	myChart.on("mouseout", () => {
            // 		myChart.dispatchAction({
            // 			type: "highlight",
            // 			dataIndex: dataIndex
            // 		});
            // 	});
            // 	myChart.dispatchAction({
            // 		type: "highlight",
            // 		dataIndex: dataIndex
            // 	});
            // }, 500);
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData top={90} />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(PieChart);
