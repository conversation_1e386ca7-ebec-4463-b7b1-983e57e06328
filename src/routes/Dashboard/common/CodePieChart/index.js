/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 饼状图表组件 - 采用echarts第三方库 - macarons主题
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../Theme/macarons';
import { connect } from 'dva';

class PieChart extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            defaultHeight: 290,
            dataIndex: 0
        };
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        const { type } = this.props;
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        const seriesData = chartData.map((res) => {
            return { value: res.cnt, name: res.displayName, rate: res.rate, code: res.code };
        });

        const zhanbi = I18N.codepiechart.index.zhanBi;

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: (params) => {
                    const { name, value, percent } = params;
                    const tempStr = I18N.codepiechart.index.dianJiTuXingCha;
                    let dom = `<span style="color:#00fcff;font-size: 12px">${tempStr}</span>`; // 点击图形查看详情
                    let str = `${name}：${value}（${percent.toFixed(2)}%）<br />${dom}`;
                    return params.seriesName === I18N.codepiechart.index.waiBianKuang ? null : str;
                }
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                tooltip: {
                    show: true
                },
                left: '56%',
                align: 'left',
                top: 'middle',
                icon: 'rect',
                itemWidth: 6,
                itemHeight: 6,
                borderColor: '#000',
                formatter: (name) => {
                    const cur = seriesData.find((res) => res.name === name);
                    const rate = (cur.rate * 100).toFixed(2) || 0;
                    let length = type === 'small' ? 12 : 18;
                    let value = name;
                    if (name.length > length) {
                        value = `${name.substr(0, length)}...`;
                    }
                    // return `${value} \t ${cur.value} 个 \t ${rate}%`;
                    return value;
                },
                textStyle: {
                    color: '#8B94AB'
                },
                data: seriesData,
                height: 220
                // selectedMode: false
            },
            series: [
                // 主要展示层的
                {
                    radius: type === 'small' ? ['32%', '48%'] : ['50%', '70%'],
                    center: ['30%', '50%'],
                    avoidLabelOverlap: false,
                    hoverOffset: type === 'small' ? 4 : 10,
                    type: 'pie',
                    label: {
                        normal: {
                            show: false,
                            position: 'center',
                            formatter: `{cha|${zhanbi}}\n{text|{d}%}`, // 占比
                            rich: {
                                text: {
                                    color: '#333',
                                    fontSize: type === 'small' ? 14 : 20,
                                    align: 'center',
                                    verticalAlign: 'middle',
                                    padding: [0, 0, 2, 0]
                                },
                                cha: {
                                    color: '#8b94ab',
                                    fontSize: type === 'small' ? 12 : 16,
                                    align: 'center',
                                    verticalAlign: 'middle'
                                }
                            }
                        },
                        emphasis: {
                            show: true,
                            textStyle: {
                                fontSize: type === 'small' ? 16 : 24
                            }
                        }
                    },
                    name: I18N.codepiechart.index.zhuangTaiMaFenBu,
                    data: seriesData
                },
                // 边框的设置
                {
                    radius: type === 'small' ? ['23%', '23%'] : ['40%', '40%'],
                    center: ['30%', '50%'],
                    clockWise: false, // 顺时加载
                    hoverAnimation: false, // 鼠标移入变大
                    type: 'pie',
                    label: {
                        normal: {
                            show: false
                        },
                        emphasis: {
                            show: false
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        },
                        emphasis: {
                            show: false
                        }
                    },
                    animation: false,
                    tooltip: {
                        show: false
                    },
                    data: [
                        {
                            value: 1,
                            itemStyle: {
                                borderWidth: 1,
                                borderColor: 'rgba(58,132,255,0.16)'
                            }
                        }
                    ]
                },
                {
                    name: I18N.codepiechart.index.waiBianKuang,
                    type: 'pie',
                    clockWise: false, // 顺时加载
                    hoverAnimation: false, // 鼠标移入变大
                    center: ['30%', '50%'],
                    radius: type === 'small' ? ['55%', '55%'] : ['80%', '80%'],
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    data: [
                        {
                            value: 9,
                            name: '',
                            itemStyle: {
                                normal: {
                                    borderWidth: 1,
                                    borderColor: 'rgba(58,132,255,0.16)'
                                }
                            }
                        }
                    ]
                }
            ]
        };

        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
            });
            myChart.on('click', (params) => {
                const { code } = params.data;
                this.props.onClick(code);
            });
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData top={90} />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(PieChart);
