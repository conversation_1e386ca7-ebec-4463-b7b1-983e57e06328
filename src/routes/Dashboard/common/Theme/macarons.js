import echarts from "echarts/lib/echarts";

const colorPalette = [
	"#007AFF", "#727DF5", "#4FD6A7", "#00C5DC", "#58C7FF",
	"#FFB721", "#FF7878", "#FBDB59"
];

const theme = {
	color: colorPalette,

	title: {
		textStyle: {
			fontWeight: "normal",
			color: "#008acd"
		}
	},

	visualMap: {
		itemWidth: 15,
		color: ["#5ab1ef", "#e0ffff"]
	},

	toolbox: {
		iconStyle: {
			normal: {
				borderColor: colorPalette[0]
			}
		}
	},

	tooltip: {
		backgroundColor: "rgba(50,50,50,0.7)",
		axisPointer: {
			type: "line",
			lineStyle: {
				color: "#008acd"
			},
			crossStyle: {
				color: "#008acd"
			},
			shadowStyle: {
				color: "rgba(200,200,200,0.2)"
			}
		}
	},

	dataZoom: {
		dataBackgroundColor: "#efefff",
		fillerColor: "rgba(182,162,222,0.2)",
		handleColor: "#008acd"
	},

	grid: {
		borderColor: "#eee"
	},

	categoryAxis: {
		axisLine: {
			lineStyle: {
				color: "#008acd"
			}
		},
		splitLine: {
			lineStyle: {
				color: ["#eee"]
			}
		}
	},

	valueAxis: {
		axisLine: {
			lineStyle: {
				color: "#008acd"
			}
		},
		splitArea: {
			show: true,
			areaStyle: {
				color: ["rgba(250,250,250,0.1)", "rgba(200,200,200,0.1)"]
			}
		},
		splitLine: {
			lineStyle: {
				color: ["#eee"]
			}
		}
	},

	timeline: {
		lineStyle: {
			color: "#008acd"
		},
		controlStyle: {
			normal: { color: "#008acd" },
			emphasis: { color: "#008acd" }
		},
		symbol: "emptyCircle",
		symbolSize: 3
	},

	line: {
		smooth: true,
		symbol: "emptyCircle",
		symbolSize: 3
	},

	candlestick: {
		itemStyle: {
			normal: {
				color: "#d87a80",
				color0: "#2ec7c9",
				lineStyle: {
					color: "#d87a80",
					color0: "#2ec7c9"
				}
			}
		}
	},

	scatter: {
		symbol: "circle",
		symbolSize: 4
	},

	map: {
		label: {
			normal: {
				textStyle: {
					color: "#d87a80"
				}
			}
		},
		itemStyle: {
			normal: {
				borderColor: "#eee",
				areaColor: "#ddd"
			},
			emphasis: {
				areaColor: "#fe994e"
			}
		}
	},

	graph: {
		color: colorPalette
	},

	gauge: {
		axisLine: {
			lineStyle: {
				color: [[0.2, "#2ec7c9"], [0.8, "#5ab1ef"], [1, "#d87a80"]],
				width: 10
			}
		},
		axisTick: {
			splitNumber: 10,
			length: 15,
			lineStyle: {
				color: "auto"
			}
		},
		splitLine: {
			length: 22,
			lineStyle: {
				color: "auto"
			}
		},
		pointer: {
			width: 5
		}
	}
};

echarts.registerTheme("business", theme);
