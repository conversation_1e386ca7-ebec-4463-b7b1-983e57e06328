import I18N from '@/utils/I18N';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { DatePicker, Icon, Tooltip, message } from 'tntd';
import moment from 'moment';
import './index.less';
import ClassNames from 'classnames';

const { RangePicker } = DatePicker;
const dateFormat = 'YYYY-MM-DD';

import { connect } from 'dva';
import { DateRanges } from '@/constants';

class Search extends Component {
    static propTypes = {
        handleChange: PropTypes.func,
        className: PropTypes.string
    };
    constructor(props) {
        super(props);
        this.state = {
            active: 0,
            rangestarttime: moment(),
            rangeendtime: moment()
        };
    }

    componentDidMount() {
        const { rangestarttime, rangeendtime } = this.state;
        this.props.handleChange({
            startTime: rangestarttime,
            endTime: rangeendtime
        });
    }

    handleChangeDay = (val = 0) => {
        const params = {};

        switch (val) {
            case -1:
                params.startTime = moment().add(val, 'days');
                params.endTime = moment().add(val, 'days');
                break;
            case 0:
                params.startTime = moment();
                params.endTime = moment();
                break;
            default:
                params.startTime = moment().add(-(val - 1), 'days');
                params.endTime = moment();
                break;
        }

        this.setState({
            active: val,
            rangestarttime: params.startTime,
            rangeendtime: params.endTime
        });
        this.props.handleChange(params);
    };

    handleChangeRange = (val) => {
        const params = {};
        if (val.length) {
            params.startTime = val[0];
            params.endTime = val[1];

            const start = val[0].valueOf();
            const end = val[1].valueOf();
            const rangeDay = (end - start) / 1000 / 60 / 60 / 24;
            if (rangeDay > 365) return message.warning(I18N.rangedata.index.xiTongYiCiZui); // 系统一次最多支持查询一年

            this.setState({
                active: '',
                rangestarttime: params.startTime,
                rangeendtime: params.endTime
            });
        } else {
            this.setState({
                active: '',
                rangestarttime: null,
                rangeendtime: null
            });
        }
        this.props.handleChange(params);
    };

    disabledDate = (current) => {
        // Can not select days before today and today
        return (current && current > moment().endOf('day')) || current <= moment().add(-365, 'days');
    };

    render() {
        const { className } = this.props;
        const { active, rangestarttime, rangeendtime } = this.state;

        let defaultValue = [];
        if (rangestarttime) {
            defaultValue = [rangestarttime, rangeendtime];
        }
        return (
            <div className={ClassNames('m-range-data', className)}>
                <span
                    onClick={() => {
                        this.handleChangeDay();
                    }}
                    className={`itemday ${active === 0 && 'active'}`}>
                    {/* 今天 */}
                    {I18N.rangedata.index.jinTian}
                </span>
                <span
                    onClick={() => {
                        this.handleChangeDay(-1);
                    }}
                    className={`itemday ${active === -1 && 'active'}`}>
                    {/* 昨天 */}
                    {I18N.rangedata.index.zuoTian}
                </span>
                <span
                    onClick={() => {
                        this.handleChangeDay(7);
                    }}
                    className={`itemday ${active === 7 && 'active'}`}>
                    7{I18N.rangedata.index.tian}
                </span>
                <span
                    onClick={() => {
                        this.handleChangeDay(30);
                    }}
                    className={`itemday ${active === 30 && 'active'}`}>
                    30{I18N.rangedata.index.tian}
                </span>
                <span
                    onClick={() => {
                        this.handleChangeDay(90);
                    }}
                    className={`itemday ${active === 90 && 'active'}`}>
                    90{I18N.rangedata.index.tian}
                </span>
                <span
                    onClick={() => {
                        this.handleChangeDay(365);
                    }}
                    className={`itemday ${active === 365 && 'active'}`}>
                    {/* 近一年 */}
                    {I18N.rangedata.index.jinYiNian}
                </span>
                <div id="property-rangepicker" className="rangepicker">
                    <RangePicker
                        value={defaultValue}
                        // disabledDate={this.disabledDate}
                        getCalendarContainer={() => document.getElementById('property-rangepicker')}
                        allowClear={false}
                        onChange={(val) => {
                            this.handleChangeRange(val);
                        }}
                        format={dateFormat}
                        ranges={DateRanges}
                    />
                </div>
                {/* 系统一次最多支持查询一年 */}
                <Tooltip title={I18N.rangedata.index.xiTongYiCiZui}>
                    <Icon type="question-circle" />
                </Tooltip>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(Search);
