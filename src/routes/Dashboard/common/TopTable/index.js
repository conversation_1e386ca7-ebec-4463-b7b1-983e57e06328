import I18N from '@/utils/I18N';
import { Component } from 'react';
import { Table, Empty, Tooltip } from 'tntd';
import NoData from '@/components/NoData';
import { connect } from 'dva';

class TopTable extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    componentDidMount() {}

    render() {
        const { invokeRank, widthR = 120 } = this.props;
        const count = (invokeRank[0] && invokeRank[0].count) || 0;

        let columns = [
            {
                title: I18N.toptable.index.paiMing, // 排名
                dataIndex: 'order',
                width: 100,
                render: (text) => {
                    return <div style={{ width: '30px' }}>{text}</div>;
                }
            },
            {
                title: widthR !== 120 ? I18N.toptable.index.fuWuMingCheng : I18N.toptable.index.shuJuYuanFuWu, // 数据源服务接口名称
                dataIndex: 'serviceName',
                width: 160,
                ellipsis: true,
                render: (text) => {
                    return (
                        <Tooltip title={text}>
                            <div className="text-ellipsis" style={{ maxWidth: '120px' }}>
                                {text}
                            </div>
                        </Tooltip>
                    );
                }
            },
            {
                title: I18N.toptable.index.diaoYongZongLiang, // 调用总量
                dataIndex: 'count',
                align: 'center',
                ellipsis: true,
                width: 160,
                render: (item) => {
                    return (
                        <div className="invoke-count">
                            <p className="count">{item}</p>
                            <p className="percent" style={{ width: (item / count) * widthR + 'px' }} />
                        </div>
                    );
                }
            }
        ];
        return (
            <div className="invokeRank">
                {invokeRank && invokeRank.length > 0 && (
                    <Table
                        dataSource={invokeRank}
                        columns={columns}
                        pagination={{
                            total: invokeRank.length,
                            pageSize: 5,
                            size: 'small'
                        }}
                        rowKey="order"
                        scroll={{ x: 500 }}
                    />
                )}
                {!invokeRank.length && <NoData top={90} />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(TopTable);
