import service from "./service";

export default {
	namespace: "consumer",
	state: {
		overview: {},
		invokeStatus: [],
		averageTime: [],
		sourceType: [],
		invokeRank: [],
		codeData: [],
		detail: {},
		overviewLoading: false,
		invokeStatusLoading: false,
		averageTimeLoading: false,
		invokeRankLoading: false,
		codeDataLoading: false
	},
	effects: {
		*getWholeView({ payload }, { call, put }) {
			yield put({
				type: "updateState",
				payload: {
					overviewLoading: true
				}
			});
			const res = yield call(service.getWholeView, payload);
			yield put({
				type: "updateState",
				payload: {
					overview: res.data ? res.data : {},
					overviewLoading: false
				}
			});
		},
		*getInvokeStatistic({ payload }, { call, put }) {
			yield put({
				type: "updateState",
				payload: {
					invokeStatusLoading: true
				}
			});
			let averageTime;
			let res;
			if (typeof payload.minCost !== "undefined") {
				const { minCost, maxCost, ...rest } = payload;
				res = yield call(service.getInvokeStatistic, rest);
				averageTime = yield call(service.getInvokeStatistic, payload);
			} else {
				res = yield call(service.getInvokeStatistic, payload);
				averageTime = res;
			}

			yield put({
				type: "updateState",
				payload: {
					invokeStatus: res.data ? res.data : [],
					averageTime: averageTime.data ? averageTime.data : [],
					invokeStatusLoading: false
				}
			});
		},
		*getFilterInvokeStatistic({ payload }, { call, put }) {
			yield put({
				type: "updateState",
				payload: {
					averageTimeLoading: true
				}
			});
			const res = yield call(service.getInvokeStatistic, payload);
			yield put({
				type: "updateState",
				payload: {
					averageTime: res.data ? res.data : [],
					averageTimeLoading: false
				}
			});
		},
		*getInvokeSourceTypeStat({ payload }, { call, put }) {
			const res = yield call(service.getInvokeSourceTypeStat, payload);
			yield put({
				type: "updateState",
				payload: {
					sourceType: res.data ? res.data : []
				}
			});
		},
		*getInvokeRankStat({ payload }, { call, put }) {
			yield put({
				type: "updateState",
				payload: {
					invokeRankLoading: true
				}
			});
			const res = yield call(service.getInvokeRankStat, payload);
			yield put({
				type: "updateState",
				payload: {
					invokeRank: res.data ? res.data : [],
					invokeRankLoading: false
				}
			});
		},
		*getReasonCodeStatistic({ payload }, { call, put }) {
			yield put({
				type: "updateState",
				payload: {
					codeDataLoading: false
				}
			});
			const res = yield call(service.getReasonCodeStatistic, payload);
			yield put({
				type: "updateState",
				payload: {
					codeData: res.data ? res.data : [],
					codeDataLoading: false
				}
			});
		},
		*getInvokeDetailStat({ payload }, { call, put }) {
			const res = yield call(service.getInvokeDetailStat, payload);
			yield put({
				type: "updateState",
				payload: {
					detail: res.data ? res.data : {}
				}
			});
		}

	},

	reducers: {
		updateState(state, { payload }) {
			return {
				...state,
				...payload
			};
		}
	}
};
