import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';
import { connect } from 'dva';

class LineBarChart extends PureComponent {
    state = {
        defaultHeight: 300
    };
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        const seriesData = chartData.map((res) => {
            return res.count;
        });
        const xAxisData = chartData.map((res) => {
            return res.serviceName;
        });

        const option = {
            color: ['#007AFF'],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    // 坐标轴指示器，坐标轴触发有效
                    type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                },
                formatter: (val) => {
                    const data=val?.[0]?.axisValueLabel||'--'
                    // 图例最大宽度为300px
                    return `<p style="max-width: 300px;overflow:auto;word-break: break-all;
                    word-wrap: break-word;white-space:pre-wrap;">${data}</p>`;
                },
            },
            grid: {
                left: 40,
                right: 130,
                bottom: 30,
                top: 30,
                containLabel: true
            },
            xAxis: {
                name: I18N.linebarchart.index.shuJuYuanFuWu2, // 三方服务接口
                type: 'category',
                data: xAxisData,
                axisLine: {
                    lineStyle: {
                        color: '#EBEEF5'
                    }
                },
                axisLabel: {
                    interval: 0,
                    color: '#909399',
                    rotate: 45,
                    formatter: (value) => {
                        let text = value;
                        if (value.length > 6) {
                            text = `${value.substr(0, 6)}...`;
                        }
                        return text;
                    }
                },
                nameTextStyle: {
                    color: '#909399'
                },
                axisTick: {
                    show: false
                }
            },
            dataZoom: [
                {
                    type: 'slider',
                    show: true,
                    height: 12,
                    xAxisIndex: [0],
                    bottom: 10,
                    start: 0,
                    end: 20,
                    textStyle: {
                        color: '#909399',
                        // width:120,
                        // overflow:'truncate',
                        // ellipsis:'...' //当前版本echarts不支持，若以后升级可以这样写，就可以不用下面的labelFormatter
                    },
                    labelFormatter: (value) => {
                        const res=xAxisData[value]?xAxisData[value]:'--';
                        let text = res;
                        if (res.length > 6) {
                            text = `${res.substr(0, 6)}...`;
                        }
                        return text;
                      },
                }
            ],
            yAxis: {
                name: I18N.linebarchart.index.diaoYongFangFuWu, // 调用方服务数量
                type: 'value',
                nameTextStyle: {
                    padding: [0, 0, 0, 50]
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#909399'
                    }
                },
                axisTick: {
                    show: false
                },
                splitArea: {
                    areaStyle: {
                        color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: ['#EBEEF5', '#EBEEF5'],
                        type: 'solid'
                    }
                },
                minInterval: 1
            },
            series: [
                {
                    name: I18N.linebarchart.index.shuJuYuanFuWu, // 三方服务接口分布
                    type: 'bar',
                    barWidth: 15,
                    data: seriesData
                }
            ]
        };

        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
            });
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(LineBarChart);
