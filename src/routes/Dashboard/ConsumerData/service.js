import { getUrl, deleteEmptyObjItem } from "@/utils/common";
import request from "@/utils/request";

// 实时概览
const getWholeView = async(params) => {
	return request(getUrl("/statistics/channel/getWholeView", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 调用明细统计
const getInvokeDetailStat = async(params) => {
	return request(getUrl("/statistics/channel/getInvokeDetailStat", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 调用状态和耗时明细
const getInvokeStatistic = async(params) => {
	return request(getUrl("/statistics/channel/getInvokeStatistic", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 服务调用排行榜
const getInvokeRankStat = async(params) => {
	return request(getUrl("/statistics/channel/getInvokeRankStat", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 调用来源类型分布明细
const getInvokeSourceTypeStat = async(params) => {
	return request(getUrl("/statistics/channel/getInvokeSourceTypeStat", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 错误码统计
const getReasonCodeStatistic = async(params) => {
	return request(getUrl("/statistics/channel/getReasonCodeStatistic", deleteEmptyObjItem(params)), {
		method: "GET"
	});
};

// 手动统计
const statistical = async() => {
	const params = {
		type: 1
	};
	return request("/statistics/channel/es/statics", {
		method: "POST",
		body: deleteEmptyObjItem(params)
	});
};

export default {
	getWholeView,
	getInvokeStatistic,
	getInvokeRankStat,
	getReasonCodeStatistic,
	getInvokeSourceTypeStat,
	getInvokeDetailStat,
	statistical
};
