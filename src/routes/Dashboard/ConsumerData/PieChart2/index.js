import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';

export default class Pie<PERSON><PERSON> extends PureComponent {
    state = {
        defaultHeight: 280
    };
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        const seriesData = chartData.map((res) => {
            return { value: res.count, name: res.sourceName, rate: res.rate, code: res.sourceCode };
        });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                left: '65%',
                align: 'left',
                top: 'middle',
                icon: 'rect',
                itemWidth: 6,
                itemHeight: 6,
                borderColor: '#000',
                formatter: (name) => {
                    const cur = seriesData.find((res) => res.name === name);
                    const rate = (cur && Math.ceil(cur.rate * 100)) || 0;
                    return `${rate}% \t ${name}`;
                },
                textStyle: {
                    color: '#8B94AB'
                },
                data: seriesData,
                height: 220
            },
            roseType: 'area',
            series: [
                {
                    name: I18N.piechart2.index.laiYuanLeiXingFen,
                    type: 'pie',
                    center: ['30%', '50%'],
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    itemStyle: {
                        emphasis: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },

                    data: seriesData
                }
            ]
        };
        let dataIndex = 0;
        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
                // myChart.dispatchAction({
                // 	type: "highlight",
                // 	dataIndex: dataIndex
                // });
            });
            myChart.on('click', (params) => {
                const { code } = params.data;
                this.props.onClick(code);
            });
            // setTimeout(() => {
            // 	myChart.on("mouseover", (params) => {
            // 		myChart.dispatchAction({
            // 			type: "downplay",
            // 			dataIndex: dataIndex
            // 		});
            // 		dataIndex = params.dataIndex;
            // 	});

            // 	myChart.on("mouseout", () => {
            // 		myChart.dispatchAction({
            // 			type: "highlight",
            // 			dataIndex: dataIndex
            // 		});
            // 	});
            // 	myChart.dispatchAction({
            // 		type: "highlight",
            // 		dataIndex: dataIndex
            // 	});
            // }, 500);
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData />}
            </div>
        );
    }
}
