import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import { Empty } from 'tntd';
import { formatCount } from '@/utils/utils';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';

import { chartImages } from '@/constants/images';
import { connect } from 'dva';

class LineBarChart extends PureComponent {
    state = {
        defaultHeight: 450
    };
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        const successSearchCountData = [];
        const successNoSearchCountData = [];
        const cachedCountData = [];
        const successCountData = [];
        const failCountData = [];
        const successCostData = [];
        const failCostData = [];

        const xAxisData = chartData.map((res) => {
            successSearchCountData.push(res.successSearchCount);
            successNoSearchCountData.push(res.successNoSearchCount);
            cachedCountData.push(res.cachedCount);
            successCountData.push(res.successCount);
            failCountData.push(res.failCount);
            successCostData.push(res.successCost);
            failCostData.push(res.failCost);
            return res.name;
        });

        const legengData = [
            {
                name: I18N.linebarchart3.index.chengGongPingJunHao, // 成功平均耗时
                icon: `image://${chartImages.success}`
            },
            {
                name: I18N.linebarchart3.index.shiBaiPingJunHao, // 失败平均耗时
                icon: `image://${chartImages.failed}`
            },
            {
                name: I18N.linebarchart3.index.chengGongShuJuYuan2 // 成功(三方查得)量
            },
            {
                name: I18N.linebarchart3.index.chengGongShuJuYuan // 成功(三方未查得)量
            },
            {
                name: I18N.linebarchart3.index.chengGongBenDiCha // 成功(缓存查得)量
            },
            {
                name: I18N.linebarchart3.index.shiBaiLiang // 失败量
            }
        ];

        const option = {
            color: ['#ff8080', '#ffba2c', '#727cf5', '#00c5dc', '#ffb722', '#ff7878', '#5ddaaf'],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    },
                    label: {
                        show: true,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                formatter: (params) => {
                    let returnVal = `${params[0].axisValue}<br/>`;
                    let list = '';
                    params.forEach((item, i) => {
                        let isPercent = '';
                        let value = formatCount(item.value);
                        let marker = item.marker;
                        if (item.seriesIndex === 0 || item.seriesIndex === 1) {
                            isPercent = 'ms';
                            let style = 'display:inline-block;margin-right:5px;width:10px;height:10px;';
                            if (item.seriesIndex === 0) marker = `<img src="${chartImages.success}" style=${style} />`;
                            if (item.seriesIndex === 1) marker = `<img src="${chartImages.failed}" style=${style} />`;
                        } else {
                            marker = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${item.color};"></span>`;
                        }

                        if (i === 0) {
                            list = `${marker}${item.seriesName}：${value}${isPercent}`;
                        } else {
                            list = `${list}<br/>${marker}${item.seriesName}：${value}${isPercent}`;
                        }
                    });
                    returnVal = returnVal + list;
                    return returnVal;
                }
            },
            grid: {
                left: 20,
                right: 28,
                bottom: 70,
                top: 40,
                containLabel: true
            },
            dataZoom: [
                {
                    type: 'slider',
                    show: true,
                    height: 12,
                    xAxisIndex: [0],
                    bottom: '12%',
                    start: 0,
                    end: 90,
                    textStyle: {
                        color: '#909399'
                    }
                }
            ],
            legend: {
                data: legengData,
                icon: 'rect',
                itemWidth: 10,
                itemHeight: 10,
                bottom: 0
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#EBEEF5'
                        }
                    },
                    axisLabel: {
                        color: '#909399'
                    },
                    axisTick: {
                        show: false
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: I18N.linebarchart3.index.haoShi, // 耗时
                    min: 0,
                    nameTextStyle: {
                        align: 'right',
                        padding: [0, -30, 0, 0]
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#909399'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitArea: {
                        areaStyle: {
                            color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)'],
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}ms'
                    }
                },
                {
                    type: 'value',
                    name: I18N.linebarchart3.index.diaoYongLiangCi, // 调用量/次
                    nameTextStyle: {
                        padding: [0, 0, 0, -50]
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#909399'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitArea: {
                        areaStyle: {
                            color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: ['#EBEEF5', '#EBEEF5'],
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}'
                    },
                    minInterval: 1
                }
            ],
            series: [
                {
                    name: I18N.linebarchart3.index.chengGongPingJunHao, // 成功平均耗时
                    type: 'line',
                    lineStyle: {
                        color: '#4FD6A7'
                    },
                    data: successCostData
                },
                {
                    name: I18N.linebarchart3.index.shiBaiPingJunHao, // 失败平均耗时
                    type: 'line',
                    lineStyle: {
                        color: '#FF7878'
                    },
                    data: failCostData
                },
                {
                    name: I18N.linebarchart3.index.chengGongShuJuYuan2, // 成功(三方查得)量
                    type: 'bar',
                    barWidth: 8,
                    yAxisIndex: 1,
                    data: successSearchCountData
                },
                {
                    name: I18N.linebarchart3.index.chengGongShuJuYuan, // 成功(三方未查得)量
                    type: 'bar',
                    barWidth: 8,
                    yAxisIndex: 1,
                    data: successNoSearchCountData
                },
                {
                    name: I18N.linebarchart3.index.chengGongBenDiCha, // 成功(缓存查得)量
                    type: 'bar',
                    barWidth: 8,
                    yAxisIndex: 1,
                    data: cachedCountData
                },
                {
                    name: I18N.linebarchart3.index.shiBaiLiang, // 失败量
                    type: 'bar',
                    barWidth: 8,
                    yAxisIndex: 1,
                    data: failCountData
                }
            ]
        };

        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
            });
            myChart.on('legendselectchanged', (obj) => {
                if (obj.name === I18N.linebarchart3.index.chengGongPingJunHao || obj.name === I18N.linebarchart3.index.shiBaiPingJunHao) {
                    const option1 = myChart.getOption();
                    let copyLengend = Object.assign([], legengData);
                    copyLengend.forEach((item) => {
                        if (item.name === obj.name) {
                            let flag;
                            for (let key in obj.selected) {
                                if (item.name === key) {
                                    flag = obj.selected[key];
                                }
                            }
                            if (!flag) {
                                item.icon = `image://${chartImages.gray}`;
                            } else if (obj.name === I18N.linebarchart3.index.chengGongPingJunHao) {
                                item.icon = `image://${chartImages.success}`;
                            } else if (obj.name === I18N.linebarchart3.index.shiBaiPingJunHao) {
                                item.icon = `image://${chartImages.failed}`;
                            }
                        }
                    });
                    const legend = {
                        data: copyLengend,
                        selected: obj.selected,
                        icon: 'rect',
                        itemWidth: 10,
                        itemHeight: 10,
                        bottom: 0
                    };
                    option1.legend = legend;
                    myChart.setOption(option1, true);
                }
            });
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(LineBarChart);
