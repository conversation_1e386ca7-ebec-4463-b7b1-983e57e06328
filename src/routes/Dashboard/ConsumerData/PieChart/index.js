import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import Echarts from 'echarts';
import { Empty } from 'tntd';
import NoData from '@/components/NoData';

import '../../common/Theme/macarons';
import { connect } from 'dva';

class PieChart extends PureComponent {
    state = {
        defaultHeight: 300
    };
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { idName, chartData } = this.props;
        this.draw(idName, chartData);
    }

    componentDidUpdate(prevProps) {
        let preChartData = prevProps.chartData;
        let nextChartData = this.props.chartData;
        if (preChartData !== nextChartData) {
            const { idName, chartData } = this.props;
            this.draw(idName, chartData);
        }
    }

    draw = (idName, chartData) => {
        if (!chartData.length) return;
        const dom = document.getElementById(idName);
        const myChart = Echarts.init(dom, 'business');

        let num = 0;
        const seriesData = chartData.map((res) => {
            num = num + res.count;
            return { value: res.count, name: res.channelName, count: res.count, rate: res.rate };
        });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            title: {
                text: num,
                subtext: I18N.piechart.index.diaoYongFangFuWu, // 调用方服务数量
                left: '29%',
                top: '40%',
                textAlign: 'center',
                textStyle: {
                    color: '#333333',
                    fontSize: 30,
                    fontWeight: 'normal'
                },
                subtextStyle: {
                    color: '#8B94AB',
                    fontSize: 12,
                    fontWeight: 'normal'
                }
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                left: '60%',
                align: 'left',
                top: 'middle',
                icon: 'rect',
                itemWidth: 10,
                itemHeight: 10,
                formatter: (name) => {
                    const cur = seriesData.find((res) => res.name === name);
                    const rate = (cur.rate * 100).toFixed(2) || 0;
                    let value = name;
                    if (name.length > 4) {
                        value = `${name.substr(0, 4)}...`;
                    }
                    let a = I18N.piechart.index.ge === 'a' ? '' : I18N.piechart.index.ge; // 个
                    return `${value} \t ${cur.count} ${a} \t ${rate}%`;
                    // return value;
                },
                textStyle: {
                    color: '#8C8C8C'
                },
                data: seriesData,
                height: 250
            },
            series: [
                {
                    name: I18N.piechart.index.yingYongFenBu,
                    type: 'pie',
                    center: ['30%', '50%'],
                    radius: ['50%', '70%'],
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    itemStyle: {
                        emphasis: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },

                    data: seriesData
                },
                {
                    name: '',
                    type: 'pie',
                    center: ['30%', '50%'],
                    radius: ['45%', '50%'],
                    hoverAnimation: false,
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    color: '#EBEEF5',
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        formatter: 's',
                        textStyle: {
                            color: 'rgba(0, 0, 0, 0)'
                        }
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#EBEEF5'
                        }
                    },
                    data: [{ value: 0, name: '' }]
                },
                {
                    name: '',
                    type: 'pie',
                    color: '#fff',
                    center: ['30%', '50%'],
                    radius: ['0%', '45%'],
                    hoverAnimation: false,
                    label: {
                        normal: {
                            show: false
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        formatter: 's',
                        textStyle: {
                            color: 'rgba(0, 0, 0, 0)'
                        }
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#fff'
                        }
                    },
                    data: [{ value: 0, name: '' }]
                }
            ]
        };
        let dataIndex = 0;
        if (option && typeof option === 'object') {
            myChart.setOption(option, true);
            window.addEventListener('resize', () => {
                myChart.resize();
                // myChart.dispatchAction({
                // 	type: "highlight",
                // 	dataIndex: dataIndex
                // });
            });
            // setTimeout(() => {
            // 	myChart.on("mouseover", (params) => {
            // 		const index = params.dataIndex;
            // 		const name = params.name;
            // 		const obj = seriesData.find(item => item.name === name);
            // 		if (obj && dataIndex !== index) {
            // 			myChart.dispatchAction({
            // 				type: "downplay",
            // 				dataIndex: dataIndex
            // 			});
            // 			myChart.dispatchAction({
            // 				type: "highlight",
            // 				dataIndex: index
            // 			});
            // 			dataIndex = index;
            // 		}
            // 	});

            // 	myChart.on("mouseout", () => {
            // 		myChart.dispatchAction({
            // 			type: "highlight",
            // 			dataIndex: dataIndex
            // 		});
            // 	});
            // 	myChart.dispatchAction({
            // 		type: "highlight",
            // 		dataIndex: dataIndex
            // 	});
            // }, 500);
        }
    };

    render() {
        const { defaultHeight } = this.state;
        const { height, idName, chartData } = this.props;
        const realHeight = height ? `${height}px` : `${defaultHeight}px`;

        return (
            <div style={{ height: realHeight }}>
                {chartData.length ? <div id={idName} style={{ height: realHeight }} /> : null}
                {!chartData.length && <NoData />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(PieChart);
