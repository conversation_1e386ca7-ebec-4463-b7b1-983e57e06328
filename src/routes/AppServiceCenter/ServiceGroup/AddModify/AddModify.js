/*
 * @CreatDate: 2020-11-02 11:41:33
 * @Describe: 新增、修改服务接口
 */

import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Select, message, Modal, InputNumber, Tooltip, Icon } from 'tntd';
import { dataServiceListAPI, appServiceListAPI, serviceGroupAPI } from '@/services';
import RouterConfig from '@/routes/AppServiceCenter/AppServiceList/Inner/RouterConfig';
import { routeDisplayConfigParser } from '@/utils/routeDisplayConfigParser';
const { Option } = Select;
const HookModal = (props) => {
    const { visible, onCancel, globalStore, appServiceListStore, dispatch, onOk, items, modalType, modifyIndex, appName, existData } =
        props;
    const { allMap } = globalStore;
    const { routeConfig, routeDisplayConfig } = appServiceListStore.dialogData.addEditModalData;
    const [data, setData] = useState({
        dataType: null, // 数据类型
        serviceName: null, // 数据源标识
        uuid: null, // 数据源标识
        displayName: null, // 显示名称
        retry: null, // 重试次数
        timeout: null, // 超时时间
        responseType: null, // 返回类型 1原始报文，2解析报文
        switchFlag: null // 异常切换 1是，2否
    });

    const [routerList, setRouterList] = useState([]);
    const [threeServiceList, setThreeServiceList] = useState([]);
    const [allThirdServiceList, setAllThirdServiceList] = useState([]);
    const [outputConfig, setOutPutConfig] = useState('');

    const { dataType, serviceName, retry, timeout, responseType, switchFlag, uuid } = data;
    const handleOk = () => {
        if (!dataType) return message.warning(I18N.addmodify.shuJuLeiXingBi);
        if (!serviceName) return message.warning(I18N.addmodify.shuJuYuanJieKou2);
        if (!responseType) return message.warning(I18N.addmodify.fanHuiLeiXingBi);
        if (!switchFlag) return message.warning(I18N.addmodify.yiChangQieHuanBi);
        if (switchFlag === 1) {
            let flag = false;
            routeConfig.forEach((item) => {
                if (!item) flag = true;
            });
            if (flag) return message.warning(I18N.addmodify.cunZaiBiTianXiang);
        }
        const params = {
            ...data,
            outputConfig,
            routeConfig: JSON.stringify(routeConfig)
        };
        onOk(params, modalType, modifyIndex);
    };
    const afterClose = () => {
        setData({
            dataType: null, // 数据类型
            serviceName: null, // 数据源标识
            displayName: null, // 显示名称
            retry: null, // 重试次数
            timeout: null, // 超时时间
            responseType: null, // 返回类型
            switchFlag: null, // 异常切换
            inquireStatus: 0, // 1查得，0未查得
            status: 2 // 1打开，2关闭
        });
        setOutPutConfig('');
        setRouterList([]);
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        routeConfig: ['']
                    }
                }
            }
        });
    };
    const getDefaultRouteList = (uuid, appName) => {
        appServiceListAPI.findDefaultRouteServiceList({ id: uuid[0], appNames: appName }).then((res) => {
            let arr = [''];
            if (res?.data?.length) {
                arr = res?.data?.slice(0, 3)?.map((v) => v.name);
            }
            dispatch({
                type: 'appServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            routeConfig: arr
                        }
                    }
                }
            });
        });
    };
    const changeField = (e, field, ref) => {
        let obj = {};
        let obj2 = {};
        obj[field] = e;
        // 切换数据类型 - 获取接口列表
        if (field === 'dataType') {
            getThreeServiceList(e);
            obj.uuid = null;
            obj.serviceName = null;
            obj2.routeConfig = [''];
            setRouterList([]);
        }

        // 选择接口，获取路由列表
        if (field === 'serviceName') {
            const item = ref.props.item;
            obj['displayName'] = item.displayName;
            obj['uuid'] = item.uuid;
            getRouterList(item.uuid);
            getOutPutConfig(item.uuid);

            obj2.routeConfig = [''];
            if (switchFlag === 1) {
                getDefaultRouteList([item.uuid], appName);
            }
        }

        // 异常切换
        if (field === 'switchFlag') {
            obj2.routeConfig = [''];
            if (serviceName && e === 1) {
                getDefaultRouteList([uuid], appName);
            }
        }

        setData({
            ...data,
            ...obj
        });

        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...obj2
                    }
                }
            }
        });
    };

    // 获取数据源接口列表
    const getThreeServiceList = (dataType, serviceName) => {
        setThreeServiceList([]);
        dataServiceListAPI.getList({ dataType, appNames: appName, dataSourceType: 'SYNC' }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                let data = [];
                // 过滤掉没有表示的数据
                res.data.contents.forEach((item) => {
                    if (item.name && item.status === 1) {
                        data.push(item);
                    }
                });
                setThreeServiceList(data);
                if (serviceName) {
                    const findObj = data.find((item) => item.name === serviceName);
                    if (findObj) {
                        getRouterList(findObj.uuid);
                    }
                }
            } else {
                message.error(res.msg);
            }
        });
    };

    // 获取路由列表
    const getRouterList = (uuid) => {
        appServiceListAPI.findRouteService({ uuid, appNames: appName, dataSourceType: 'SYNC' }).then((res) => {
            if (res.success) {
                setRouterList(res.data || []);
            } else {
                message.error(res.msg);
            }
        });
    };

    const getOutPutConfig = (uuid) => {
        serviceGroupAPI.getOutPutConfig({ uuid }).then((res) => {
            if (res.success) {
                setOutPutConfig(res.data);
            } else {
                message.error(res.msg);
            }
        });
    };

    useEffect(() => {
        dataServiceListAPI.getList().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setAllThirdServiceList(res.data.contents ? res.data.contents : []);
            } else {
                message.error(res.msg);
            }
        });
    }, []);

    useEffect(() => {
        if (items) {
            const routeConfig = items.routeConfig ? JSON.parse(items.routeConfig) : [];
            const routeDisplayConfig = routeConfig
                .map((route) => {
                    const service = (allThirdServiceList || []).find(({ name }) => name === route);

                    // 生成带状态的配置
                    if (service) {
                        return {
                            name: route,
                            status: service.status
                        };
                    }

                    return {
                        name: route,
                        status: -1
                    };
                })
                .map(routeDisplayConfigParser(allThirdServiceList));

            if (items.dataType) {
                getThreeServiceList(items.dataType, items.serviceName);
            }
            setData({
                ...items
            });
            setOutPutConfig(items.outputConfig);
            dispatch({
                type: 'appServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            routeConfig,
                            routeDisplayConfig
                        }
                    }
                }
            });
        }
    }, [items, allThirdServiceList]);
    const titleText = I18N.addmodify.diaoYongFangFuWu;
    let titleText1 = I18N.addmodify.xinZengFuWuJie;
    if (modalType === 2) {
        titleText1 = I18N.addmodify.xiuGaiFuWuJie;
    }
    return (
        <Modal
            className="m-servicegroup-addservice-modal"
            title={`${titleText}-${titleText1}`}
            visible={visible}
            onOk={handleOk}
            onCancel={onCancel}
            afterClose={afterClose}
            maskClosable={false}>
            <div className="modal-box">
                <span className="u-label">
                    <b>*</b>
                    {I18N.addmodify.shuJuYuanJieKou}
                </span>
                <span className="u-select">
                    <Select
                        showSearch
                        style={{ width: '45%' }}
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        placeholder={I18N.addmodify.shuJuLeiXing}
                        optionFilterProp="children"
                        value={dataType || undefined}
                        onChange={(e) => changeField(e, 'dataType')}>
                        {allMap &&
                            allMap.serviceTypeList.map((item, index) => {
                                return (
                                    <Option value={item.dataType} key={index}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                    </Select>
                    <Select
                        showSearch
                        style={{ width: '55%' }}
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        placeholder={I18N.addmodify.shuJuYuanJieKou}
                        optionFilterProp="children"
                        value={serviceName || undefined}
                        onChange={(val, e) => changeField(val, 'serviceName', e)}>
                        {dataType &&
                            threeServiceList &&
                            threeServiceList.map((item, index) => {
                                let arr = [];
                                existData.map((subItem) => {
                                    if (item.displayName == subItem.displayName) {
                                        arr.push(subItem.displayName);
                                    }
                                });
                                return (
                                    <Option value={item.name} item={item} disabled={arr.includes(item.displayName)} key={item.name}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                    </Select>
                </span>
            </div>
            <div className="modal-box">
                <span className="u-label">{I18N.addmodify.shuJuZhongShiCi}</span>
                <span className="u-input">
                    <InputNumber
                        min={0}
                        max={5}
                        style={{ width: '100%' }}
                        placeholder={I18N.addmodify.moRenZeYuShu2}
                        value={retry}
                        onChange={(e) => changeField(e, 'retry')}
                    />
                </span>
            </div>
            <div className="modal-box">
                <span className="u-label">{I18N.addmodify.shuJuChaoShiShi}</span>
                <span className="u-input">
                    <InputNumber
                        min={1}
                        max={60000}
                        step={1}
                        precision={0}
                        style={{ width: '100%' }}
                        placeholder={I18N.addmodify.moRenZeYuShu}
                        value={timeout}
                        onChange={(e) => changeField(e, 'timeout')}
                    />
                </span>
            </div>
            <div className="modal-box">
                <span className="u-label">
                    <b>*</b>
                    {I18N.addmodify.fanHuiLeiXing}
                </span>
                <span className="u-select">
                    <Select
                        showSearch
                        style={{ width: '100%' }}
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        placeholder={I18N.addmodify.qingXuanZeFanHui}
                        optionFilterProp="children"
                        value={responseType || undefined}
                        onChange={(e) => changeField(e, 'responseType')}>
                        {allMap &&
                            allMap.responseTypeList &&
                            allMap.responseTypeList.map((item, index) => {
                                return (
                                    <Option value={item.type} key={index}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                    </Select>
                </span>
            </div>
            <div className="modal-box">
                <span className="u-label">
                    <b>*</b>
                    {I18N.addmodify.yiChangQieHuan}
                </span>
                <span className="u-input">
                    <Select
                        showSearch
                        style={{ width: '100%' }}
                        placeholder={I18N.addmodify.qingXuanZe}
                        optionFilterProp="children"
                        value={switchFlag || undefined}
                        onChange={(e) => changeField(e, 'switchFlag')}>
                        <Option value={1}>{I18N.addmodify.shi}</Option>
                        <Option value={2}>{I18N.addmodify.fou}</Option>
                    </Select>
                </span>
                <Tooltip title={I18N.addmodify.xiTongZiDongGen}>
                    <Icon className="u-question" type="question-circle" />
                </Tooltip>
            </div>
            {switchFlag === 1 && <RouterConfig config={routeConfig} displayConfig={routeDisplayConfig} routerList={routerList} />}
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global,
    appServiceListStore: state.appServiceList
}))(HookModal);
