.g-servicegroup-addmodify{
    .bread-container{
        height: 40px;
        line-height: 40px;
        .ant-breadcrumb-link {
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
        }

    }
    .m-main{
        background: #fff;
        padding: 40px 60px;
        border-radius: @border-radius-base;
        .tnt-current-v3 & {
            padding: 24px 20px;
        }
        .combine-title {
            display: flex;
            align-items: center;
        }
        .title{
            // font-size: 16px;
            // border-left: rgba(255, 204, 0, 1) solid 5px;
            // text-indent: 20px;
            // font-weight: bold;
            // .tntd-title-text {
            //     >h3 {
            //         font-size: 14px;
            //     }
            // }
        }
        .box{
            margin-bottom: 20px;
            .label{
                display: inline-block;
                width: 100px;
                text-align: right;
                margin-right: 10px;
                b{
                    color: #f00;
                    vertical-align: middle;
                    margin-right: 3px;
                }
            }
            .en_label{
                display: inline-block;
                width: 140px;
                text-align: right;
                margin-right: 10px;
                b{
                    color: #f00;
                    vertical-align: middle;
                    margin-right: 3px;
                }
            }
            .u-width{
                width: 250px;
            }
        }
	}
	.execute-mode {
		display: flex;
		align-items: center;
		.ant-select {
			width: 100px;
			margin-right: 5px;
		}
		.ant-input{
			width: 70px;
		}
	}
}
.m-servicegroup-addservice-modal{
    .modal-box{
        .u-label{
            width: 160px;
        }
        .ant-input-number{
            span{
                display: block;
            }
        }
    }
    .m-router-config{
        position: relative;
        padding-left: 116px;
        margin-bottom: 10px;
        .anticon-plus-circle, .anticon-delete, .anticon-up-circle, .anticon-down-circle{
            font-size: 18px;
            cursor: pointer;
            vertical-align: middle;
            margin-top: -2px;
            margin-right: 10px;
            color: #999;
            &:hover{
                color: #2196F3;
            }
        }
        .u-icon-wrap{
            position: absolute;
            left: 68px;
            top: 5px;
            width: 48px;
            text-align: right;
        }
        .anticon-up-circle, .anticon-down-circle{
            margin-right: 5px;
        }
        .ant-select{
            width: 209px !important;
        }
    }
}
