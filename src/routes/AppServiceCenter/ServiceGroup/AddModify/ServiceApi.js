/*
 * @CreatDate: 2020-11-01 20:40:08
 * @Describe: 服务接口列表
 */

import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Input, Tooltip, message, Switch, Checkbox, Button, Select } from 'tntd';
import { systemFieldsAPI } from '@/services';
import DragSortingTable from '../DragSortingTable';
import AddModify from './AddModify';

import otp from './otp';

const { Option } = Select;

const ServiceApi = (props) => {
    const { globalStore, getData, refreshGroupList, disabled, dataSource = [], executeMode, appName, change } = props;
    const { allMap } = globalStore;
    const [data, setData] = useState([]);
    const [systemList, setSystemList] = useState([]);
    const [visible, setVisible] = useState(false);
    const [modifyData, setModifyData] = useState();
    const [modalType, setModalType] = useState(1); // 1新增，2修改
    const [modifyIndex, setModifyIndex] = useState();
    const commonCol = [
        {
            title: I18N.addmodify.serviceapi.shuJuLeiXing,
            dataIndex: 'dataType',
            key: 'dataType',
            width: 160,
            render: (text) => {
                let str = text;
                if (allMap && allMap.serviceTypeList) {
                    const obj = allMap.serviceTypeList.find((k) => parseInt(k.dataType, 10) === parseInt(text, 10));
                    if (obj) str = obj.name;
                }
                return str;
            }
        },
        {
            title: I18N.addmodify.serviceapi.shuJuYuanFuWu,
            dataIndex: 'displayName',
            key: 'displayName',
            width: 160,
            render: (text) => {
                let dom = text;
                if (text && text.length > 20) {
                    dom = <Tooltip title={text}>{text.substr(0, 20)}...</Tooltip>;
                }
                return dom;
            }
        },
        {
            title: I18N.addmodify.serviceapi.zhongShiCiShu,
            dataIndex: 'retry',
            key: 'retry',
            width: 160,
            render: (text) => {
                return text || text === 0 ? text : I18N.addmodify.serviceapi.moRen;
            }
        },
        {
            title: I18N.addmodify.serviceapi.chaoShiShiJian,
            dataIndex: 'timeout',
            key: 'timeout',
            width: 160,
            render: (text) => {
                return text || text === 0 ? `${text}ms` : I18N.addmodify.serviceapi.moRen;
            }
        },
        {
            title: I18N.addmodify.serviceapi.fanHuiLeiXing,
            dataIndex: 'responseType',
            key: 'responseType',
            width: 160,
            render: (text) => {
                let str = text;
                if (allMap && allMap.responseTypeList) {
                    const obj = allMap.responseTypeList.find((k) => parseInt(k.type, 10) === parseInt(text, 10));
                    if (obj) str = obj.name;
                }
                return str;
            }
        },
        {
            title: I18N.addmodify.serviceapi.yiChangQieHuan,
            dataIndex: 'switchFlag',
            key: 'switchFlag',
            width: 160,
            render: (text) => {
                return text === 1 ? I18N.addmodify.serviceapi.shi : I18N.addmodify.serviceapi.fou;
            }
        }
    ];
    const changeInterruptField = (index, value, field, col) => {
        let arr = Object.assign([], data);
        let tempConfig = arr[index][field] ? JSON.parse(arr[index][field]) : {};
        tempConfig[col] = value;
        arr[index][field] = JSON.stringify(tempConfig);
        setData(arr);
    };

    // 获取所有系统字段
    const getSystemList = (i, type, name) => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setSystemList(res.data);
            } else {
                message.error(res.msg);
            }
        });
    };

    const executeCol = [
        {
            title: I18N.addmodify.serviceapi.zhongDuanTiaoJianPei,
            dataIndex: 'interruptConfig',
            key: 'interruptConfig',
            width: 330,
            render: (val, record, index) => {
                const tempConfig = val ? JSON.parse(val) : {};
                const outputConfig = record.outputConfig ? JSON.parse(record.outputConfig) : [];
                return (
                    <div className="execute-mode">
                        <Select
                            dropdownMatchSelectWidth={false}
                            disabled={disabled}
                            allowClear
                            placeholder={I18N.addmodify.serviceapi.piPeiZiDuan}
                            onChange={(val) => {
                                changeInterruptField(index, val, 'interruptConfig', 'field');
                            }}
                            value={tempConfig.field}>
                            {outputConfig.map((item, index) => {
                                const sysObj = systemList.find((res) => res.name === item.field) || {};
                                return (
                                    <Option key={index} value={item.field} title={sysObj.displayName || item.displayName}>
                                        {sysObj.displayName || item.displayName}
                                    </Option>
                                );
                            })}
                        </Select>
                        <Select
                            dropdownMatchSelectWidth={false}
                            disabled={disabled}
                            allowClear
                            placeholder={I18N.addmodify.serviceapi.piPeiFangShi}
                            onChange={(val) => {
                                changeInterruptField(index, val, 'interruptConfig', 'method');
                            }}
                            value={tempConfig.method}>
                            <Option value="1">{I18N.addmodify.serviceapi.dengYu}</Option>
                            <Option value="2">{I18N.addmodify.serviceapi.buDengYu}</Option>
                        </Select>

                        <Tooltip title={tempConfig.value}>
                            <Input
                                value={tempConfig.value}
                                disabled={disabled}
                                onChange={(e) => {
                                    const val = e.currentTarget.value;
                                    changeInterruptField(index, val === '' ? undefined : val, 'interruptConfig', 'value');
                                }}
                                placeholder={I18N.addmodify.serviceapi.panDingZhi}
                            />
                        </Tooltip>
                    </div>
                );
            }
        }
    ];

    const columns = [
        ...commonCol,
        ...(executeMode === 2 ? executeCol : []),
        {
            title: I18N.addmodify.serviceapi.chaDeJieKou,
            dataIndex: 'inquireStatus',
            key: 'inquireStatus',
            width: otp.inquireStatus,
            render: (text, record, index) => {
                return (
                    <Switch
                        disabled={disabled}
                        checkedChildren={I18N.addmodify.serviceapi.shi}
                        unCheckedChildren={I18N.addmodify.serviceapi.fou}
                        checked={text === 1}
                        onChange={(checked) => {
                            changeField(index, checked ? 1 : 0, 'inquireStatus');
                        }}
                    />
                );
            }
        },
        {
            title: I18N.addmodify.serviceapi.zhuangTai,
            dataIndex: 'status',
            key: 'status',
            width: otp.status,
            render: (text, record, index) => {
                return (
                    <Switch
                        disabled={disabled}
                        checkedChildren={I18N.addmodify.serviceapi.kai}
                        unCheckedChildren={I18N.addmodify.serviceapi.guan}
                        checked={text === 1}
                        onChange={(checked) => {
                            changeField(index, checked ? 1 : 2, 'status');
                        }}
                    />
                );
            }
        },
        {
            title: I18N.addmodify.serviceapi.caoZuo,
            dataIndex: 'operate',
            key: 'operate',
            width: otp.action,
            fixed: 'right',
            render: (text, record, index) => {
                let dom = (
                    <>
                        <a className="mr10" onClick={() => modifyHandle(record, index)}>
                            {I18N.addmodify.serviceapi.xiuGai}
                        </a>
                        <a onClick={() => deleteHandle(index)}>{I18N.addmodify.serviceapi.shanChu}</a>
                    </>
                );
                return disabled ? '--' : dom;
            }
        }
    ];

    useEffect(() => {
        getSystemList();
    }, []);

    useEffect(() => {
        if (dataSource.length) {
            setData(dataSource);
        }
    }, [dataSource]);

    const modifyHandle = (record, index) => {
        setModifyData(record);
        setVisible(true);
        setModalType(2);
        setModifyIndex(index);
    };
    useEffect(() => {
        setData([]);
    }, [change]);

    const deleteHandle = (index) => {
        let arr = Object.assign([], data);
        arr.splice(index, 1);
        setData(arr);
        refreshGroupList(arr);
    };

    const addHandle = (params, type, index) => {
        if (type === 1) {
            // 新增
            let arr = [
                {
                    ...params,
                    inquireStatus: 0, // 1查得，0未查得
                    status: 2 // 1打开，2关闭
                }
            ];
            setData(data.concat(arr));
            refreshGroupList(data.concat(arr));
        } else {
            let arr = data.concat([]);
            arr[index] = {
                ...arr[index],
                ...params
            };
            setData(arr);
            refreshGroupList(arr);
        }
    };

    const changeField = (index, value, field) => {
        let arr = Object.assign([], data);
        let val = value;

        arr[index][field] = val;
        setData(arr);
    };

    getData(data);

    let openStatus = 0;
    for (let i = 0; i < data.length; i++) {
        if (data[i].status === 1) openStatus++;
    }

    return (
        <div className="mt20 mb40">
            <div className="mb15">
                {I18N.addmodify.serviceapi.gong} {data.length} {I18N.addmodify.serviceapi.geJieKou}
                {openStatus} {I18N.addmodify.serviceapi.geKaiQi} {data.length - openStatus} {I18N.addmodify.serviceapi.geGuanBi}
                {!disabled && (
                    <Button
                        type="primary"
                        className="fr"
                        onClick={() => {
                            if (!appName) return message.warning(I18N.addmodify.serviceapi.qingXianXuanZeQu);
                            setModalType(1);
                            setVisible(true);
                        }}>
                        {I18N.addmodify.serviceapi.xinZeng}
                    </Button>
                )}
            </div>
            <DragSortingTable
                dataSource={data}
                onChange={(val) => {
                    setData(val);
                }}
                columns={columns}
            />
            <AddModify
                modifyIndex={modifyIndex}
                modalType={modalType}
                appName={appName}
                items={modifyData}
                visible={visible}
                existData={data}
                onCancel={() => {
                    setVisible(false);
                    setModalType(1);
                    setModifyIndex(null);
                    setModifyData(null);
                }}
                onOk={(params, type, modifyIndex) => {
                    addHandle(params, type, modifyIndex);
                    setVisible(false);
                }}
            />
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ServiceApi);
