/*
 * @CreatDate: 2020-11-01 17:17:26
 * @Describe: 新增1、修改2、查看3
 */

import I18N, { getLang } from '@/utils/I18N';
import './index.less';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Input, Select, message, Breadcrumb, Button, Spin, Radio, PageContainer, Icon, Title } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import { serviceGroupAPI, systemFieldsAPI } from '@/services';
import ServiceApi from './ServiceApi';
import InputList from './InputList';

const { Option } = Select;
const AddModify = (props) => {
    const { history, globalStore } = props;
    const { currentUser, appList, allMap } = globalStore;
    const [fieldInfo, setFieldInfo] = useState({
        name: null,
        groupName: null,
        appName: null
    });
    const [data, setData] = useState([]);
    const [inputData, setInputData] = useState([]);
    const [executeMode, setExecuteMode] = useState(1);
    const [serviceConfigInfo, setServiceConfigInfo] = useState([]);
    const [inputLoading, setInputLoading] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [change, setChange] = useState();

    const type = getUrlKey('type');
    const serviceGroupName = getUrlKey('name');
    let disabled = false;
    let modifyDisabled = false;
    let breadName = I18N.addmodify.index.xinZeng;
    if (type === '2') {
        breadName = I18N.addmodify.index.xiuGai;
        modifyDisabled = true;
    }
    if (type === '3') {
        breadName = I18N.addmodify.index.chaKan;
        disabled = true;
    }

    const [fieldAll, setFieldAll] = useState([]);
    const getFieldAll = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (!res) return;
            if (res.success) {
                setFieldAll(res.data || []);
            } else {
                message.error(res.msg);
            }
        });
    };

    useEffect(() => {
        if (type === '2' || type === '3') {
            serviceGroupAPI.geServiceGroupInfo({ name: serviceGroupName }).then((res) => {
                if (!res) return;
                if (res.success) {
                    if (!res.data) return;
                    const data = res.data;
                    setFieldInfo({
                        name: data.serviceGroupInfo.name,
                        groupName: data.serviceGroupInfo.groupName,
                        appName: data.serviceGroupInfo.appName
                    });
                    setExecuteMode(data.serviceGroupInfo.executeMode || 1);
                    setInputData(data.inputConfigInfo || []);
                    setServiceConfigInfo(data.serviceConfigInfo || []);
                } else {
                    message.error(res.msg);
                }
            });
        }
        getFieldAll();
    }, []);

    const goBack = () => {
        let path = '/handle/appServiceCenter/serviceGroup';
        history.push(path);
    };

    const comfirmOk = () => {
        const { name, groupName, appName } = fieldInfo;
        if (!groupName) return message.warning(I18N.addmodify.index.fuWuZuMingCheng3);
        if (groupName.length > 200) return message.warning(I18N.addmodify.index.fuWuZuMingCheng2);
        if (!name) return message.warning(I18N.addmodify.index.fuWuZuBiaoZhi3);
        if (name.length > 200) return message.warning(I18N.addmodify.index.fuWuZuBiaoZhi2);
        const reg = /^[0-9a-zA-Z_]{1,}$/;
        if (!reg.test(name)) {
            return message.warning(I18N.addmodify.index.fuWuBiaoZhiGe);
        }
        if (!appName) return message.warning(I18N.addmodify.index.quDaoBuNengWei);
        if (data.length === 0) return message.warning(I18N.addmodify.index.fuWuJieKouLie2);
        if (executeMode === 2) {
            let flag = false;
            for (let index = 0; index < data.length; index++) {
                const element = data[index];
                const interruptConfig = element.interruptConfig ? JSON.parse(element.interruptConfig) : {};
                if (interruptConfig.field && (!interruptConfig.method || !interruptConfig.value)) {
                    flag = true;
                    break;
                }
                if (interruptConfig.method && (!interruptConfig.field || !interruptConfig.value)) {
                    flag = true;
                    break;
                }
                if (interruptConfig.value && (!interruptConfig.field || !interruptConfig.method)) {
                    flag = true;
                    break;
                }
            }
            if (flag) return message.warning(I18N.addmodify.index.zhongDuanTiaoJianPei);
        }

        const params = {
            name,
            groupName,
            appNames: appName,
            executeMode,
            userName: currentUser.account,
            serviceInfo: JSON.stringify(data)
        };
        setConfirmLoading(true);
        if (type === '1') {
            // 新增
            serviceGroupAPI
                .addData(params)
                .then((res) => {
                    setConfirmLoading(false);
                    if (!res) return;
                    if (res.success) {
                        message.success(res.msg);
                        goBack();
                    } else {
                        message.error(res.msg);
                    }
                })
                .catch(() => {
                    setConfirmLoading(false);
                });
        } else if (type === '2') {
            // 修改
            serviceGroupAPI
                .updateData(params)
                .then((res) => {
                    setConfirmLoading(false);
                    if (!res) return;
                    if (res.success) {
                        message.success(res.msg);
                        goBack();
                    } else {
                        message.error(res.msg);
                    }
                })
                .catch(() => {
                    setConfirmLoading(false);
                });
        }
    };

    const onCancel = () => {
        let path = '/handle/AppServiceCenter/ServiceGroup';
        history.push(path);
    };
    const changeField = (e, field, change) => {
        let obj = {};
        obj[field] = e;
        setFieldInfo({
            ...fieldInfo,
            ...obj
        });
        if (change == 'change') {
            setChange(e);
        }
    };

    const getData = (data) => {
        setData(data);
    };

    // 服务接口列表新增和删除，需要同步更新服务组入参列表
    const refreshGroupList = (data) => {
        if (data && data.length === 0) {
            setInputData([]);
            return;
        }
        let arr = [];
        data.forEach((item) => {
            arr.push(item.serviceName);
        });
        const params = {
            serviceNameList: JSON.stringify(arr)
        };
        setInputLoading(true);
        serviceGroupAPI
            .getServiceInfo(params)
            .then((res) => {
                setInputLoading(false);
                if (!res) return;
                if (res.success) {
                    setInputData(res.data || []);
                } else {
                    message.error(res.msg);
                }
            })
            .catch(() => {
                setInputLoading(false);
            });
    };

    const onChangeExecuteMode = (e) => {
        setExecuteMode(e.target.value);
    };

    return (
        <div className="g-servicegroup-addmodify">
            <div className="page-global-header">
                <div className="left-info">
                    <Breadcrumb className="bread-container">
                        <Breadcrumb.Item>
                            <Icon type="left" />
                            <a onClick={goBack}>{I18N.addmodify.index.fanHui}</a>
                        </Breadcrumb.Item>
                        {/* <Breadcrumb.Item>
							{I18N.template(I18N.addmodify.index.bREAD, { val1: breadName })}
						</Breadcrumb.Item> */}
                    </Breadcrumb>
                </div>
            </div>
            <div className="page-global-body">
                <div className="m-main">
                    <Title className="title" bold size="small" title={I18N.addmodify.index.jiBenXinXi} />
                    <div className="box mt20">
                        <span className={getLang() === 'en' ? 'en_label' : 'label'}>
                            <b>*</b>
                            {I18N.addmodify.index.fuWuZuMingCheng}
                        </span>
                        <Input
                            disabled={disabled}
                            className="u-width"
                            placeholder={I18N.addmodify.index.shuRuFuWuZu}
                            value={fieldInfo.groupName}
                            onChange={(e) => changeField(e.target.value, 'groupName')}
                        />
                        <span className={getLang() === 'en' ? 'en_label' : 'label'}>
                            <b>*</b>
                            {I18N.addmodify.index.fuWuZuBiaoZhi}
                        </span>
                        <Input
                            disabled={modifyDisabled ? modifyDisabled : disabled}
                            className="u-width"
                            placeholder={I18N.addmodify.index.yongYuDiaoYongFang}
                            value={fieldInfo.name}
                            onChange={(e) => changeField(e.target.value, 'name')}
                        />
                    </div>
                    <div className="box">
                        <span className={getLang() === 'en' ? 'en_label' : 'label'}>
                            <b>*</b>
                            {I18N.addmodify.index.quDao}
                        </span>
                        <Select
                            disabled={modifyDisabled ? modifyDisabled : disabled}
                            showSearch
                            dropdownMatchSelectWidth={false}
                            dropdownStyle={{ width: 350 }}
                            className="u-width"
                            placeholder={I18N.addmodify.index.xuanZeQuDao}
                            optionFilterProp="children"
                            value={fieldInfo.appName || undefined}
                            onChange={(e) => changeField(e, 'appName', 'change')}>
                            {appList &&
                                appList.map((item, i) => {
                                    let dom = (
                                        <Option value={item.key} key={i}>
                                            {item.name}
                                        </Option>
                                    );
                                    return i !== 0 ? dom : null;
                                })}
                        </Select>
                    </div>
                    <div className="mt40 combine-title">
                        <Title className="title" bold size="small" title={I18N.addmodify.index.fuWuJieKouLie} />
                        <Radio.Group onChange={onChangeExecuteMode} disabled={disabled} value={executeMode}>
                            <Radio value={1}>{I18N.addmodify.index.bingXingMoShi}</Radio>
                            <Radio value={2}>{I18N.addmodify.index.chuanXingMoShi}</Radio>
                        </Radio.Group>
                    </div>
                    <ServiceApi
                        dataSource={serviceConfigInfo}
                        disabled={disabled}
                        executeMode={executeMode}
                        getData={getData}
                        appName={fieldInfo.appName}
                        refreshGroupList={refreshGroupList}
                        change={change}
                    />
                    <Title className="title" bold size="small" title={I18N.addmodify.index.diaoYongFuWuZu} />
                    <Spin spinning={inputLoading}>
                        <InputList
                            fieldAll={fieldAll}
                            dataTypeList={allMap ? allMap.dataTypeList : []}
                            serviceTypeList={allMap ? allMap.serviceTypeList : []}
                            data={inputData}
                        />
                    </Spin>
                    {!disabled && (
                        <div className="tr">
                            <Button onClick={onCancel} loading={confirmLoading} style={{ marginRight: '15px' }}>
                                {I18N.addmodify.index.quXiao}
                            </Button>
                            <Button type="primary" onClick={comfirmOk} loading={confirmLoading}>
                                {I18N.addmodify.index.queRenTiJiao}
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(PageContainer(AddModify));
