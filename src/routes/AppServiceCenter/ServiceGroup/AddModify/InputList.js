/*
 * @CreatDate: 2020-11-01 20:40:08
 * @Describe: 调用服务组入参列表
 */

import I18N from '@/utils/I18N';
import { useState } from 'react';
import { Table, Checkbox, Popover, Ellipsis } from 'tntd';

export default (props) => {
    const { data, dataTypeList, fieldAll, serviceTypeList } = props;

    const columns = [
        {
            title: I18N.addmodify.inputlist.xiTongZiDuan,
            dataIndex: 'field',
            key: 'field',
            width: 300,
            render: (text, record) => {
                // 常量特殊处理
                if (record.paramType === 'constant') {
                    return text.replace('[constant]', '');
                }

                const obj = fieldAll.find((k) => k.name === text);
                // return obj ? obj.displayName : text;
                return <Ellipsis title={obj ? obj.displayName : text} widthLimit={280} />;
            }
        },
        {
            title: I18N.addmodify.inputlist.ziDuanLeiXing,
            dataIndex: 'dataType',
            key: 'dataType',
            width: 160,
            render: (text, record) => {
                // 常量特殊处理
                if (record.paramType === 'constant') {
                    return '--';
                }

                const obj = dataTypeList.find((k) => k.code === text);
                return obj ? obj.name : text;
            }
        },
        {
            title: I18N.addmodify.inputlist.shiFouBiTian,
            dataIndex: 'mustInput',
            key: 'mustInput',
            width: 160,
            render: (text) => {
                return <Checkbox checked={text} disabled />;
            }
        },
        {
            title: I18N.addmodify.inputlist.suoXuFuWuJie,
            dataIndex: 'name',
            key: 'name',
            width: 340,
            render: (text, record) => {
                let str = '';
                if (record.serviceInfo && record.serviceInfo.length > 0) {
                    const obj = serviceTypeList.find((k) => k.dataType === record.serviceInfo[0].type);
                    str = `[${obj ? obj.name : ''}]${record.serviceInfo[0].displayName}`;
                    for (let i = 1; i < record.serviceInfo.length; i++) {
                        const obj2 = serviceTypeList.find((k) => k.dataType === record.serviceInfo[i].type);
                        str = str + `[${obj2 ? obj2.name : ''}]${record.serviceInfo[i].displayName}`;
                    }
                }
                let dom;
                let content = (
                    <div>
                        {record.serviceInfo &&
                            record.serviceInfo.map((item, i) => {
                                const obj = serviceTypeList.find((k) => k.dataType === parseInt(item.type, 10));
                                const dName = obj ? obj.name : item.dataType;
                                let dom = (
                                    <p key={i}>
                                        {dName ? `[${dName}]` : ''}
                                        {item.displayName}
                                    </p>
                                );
                                return dom;
                            })}
                    </div>
                );
                if (str.length > 20) {
                    dom = (
                        <Popover placement="left" content={content} title={I18N.addmodify.inputlist.fuWuJieKouLie}>
                            {str.substr(0, 20)}...
                        </Popover>
                    );
                } else {
                    dom = str;
                }
                return dom;
            }
        }
    ];

    return (
        <div className="mt20 mb40">
            <Table dataSource={data} columns={columns} pagination={false} rowKey="field" />
        </div>
    );
};
