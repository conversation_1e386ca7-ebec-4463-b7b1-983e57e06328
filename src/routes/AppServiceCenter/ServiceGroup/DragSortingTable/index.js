import React from "react";
import { Table } from "tntd";
import { DragDropContext, DragSource, DropTarget } from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import update from "immutability-helper";

import "./index.less";

let dragingIndex = -1;

class BodyRow extends React.Component {
	render() {
		const {
			isOver,
			connectDragSource,
			connectDropTarget,
			moveRow,
			...restProps
		} = this.props;
		const style = { ...restProps.style, cursor: "move" };

		let { className } = restProps;
		if (isOver) {
			if (restProps.index > dragingIndex) {
				className += " drop-over-downward";
			}
			if (restProps.index < dragingIndex) {
				className += " drop-over-upward";
			}
		}

		return connectDragSource(
			connectDropTarget(
				<tr {...restProps} className={className} style={style} />
			)
		);
	}
}

const rowTarget = {
	drop(props, monitor) {
		const dragIndex = monitor.getItem().index;
		const hoverIndex = props.index;

		if (dragIndex === hoverIndex) {
			return;
		}

		props.moveRow(dragIndex, hoverIndex);

		monitor.getItem().index = hoverIndex;
	}
};

const dataSource = {
	beginDrag(props) {
		return { ...props };
	}
};

const DragableBodyRow = DropTarget("row", rowTarget, (connect, monitor) => ({
	connectDropTarget: connect.dropTarget(),
	isOver: monitor.isOver()
}))(
	DragSource("row", dataSource, connect => ({
		connectDragSource: connect.dragSource()
	}))(BodyRow)
);

@DragDropContext(HTML5Backend)
class DragSortingTable extends React.Component {
	state = {
		data: []
	};

	components = {
		body: {
			row: DragableBodyRow
		}
	};

	componentDidMount() {
		const { dataSource } = this.props;
		this.setState({
			data: dataSource
		});
	}

	componentDidUpdate(prevProps) {
		const { dataSource } = this.props;
		if (prevProps.dataSource !== dataSource) {
			this.setState({
				data: dataSource
			});
		}
	}

	moveRow = (dragIndex, hoverIndex) => {
		const { data } = this.state;
		const { onChange } = this.props;
		const dragRow = data[dragIndex];

		this.setState(
			update(this.state, {
				data: {
					$splice: [
						[dragIndex, 1],
						[hoverIndex, 0, dragRow]
					]
				}
			}),
			() => {
				onChange && onChange([...this.state.data]);
			}
		);
	};

	render() {
		const { columns } = this.props;
		return (
			<Table
				rowKey="name"
				className="components-table-demo-drag-sorting"
				columns={columns}
				dataSource={this.state.data}
				components={this.components}
				pagination={false}
				scroll={{ x: 1400 }}
				onRow={(record, index) => ({
					index,
					moveRow: this.moveRow
				})}
			/>
		);
	}
}

export default DragSortingTable;
