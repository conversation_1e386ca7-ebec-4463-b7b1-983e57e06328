/*
 * @CreatDate: 2020-10-30 17:18:16
 * @Describe: 调用方服务组
 */

import I18N from '@/utils/I18N';
import { useCallback, useEffect, useRef, useState } from 'react';
import { connect } from 'dva';
import copy from 'copy-to-clipboard';
import {
    Button,
    Icon,
    Ellipsis,
    Input,
    message,
    Modal,
    Popconfirm,
    Popover,
    Select,
    Switch,
    Table,
    Tooltip,
    HandleIcon,
    TableContainer
} from 'tntd';
import { useToggle } from '@tntd/hooks';
import SimpleList from '@/components/SimpleList';
import { appServiceListAPI, dataServiceListAPI, serviceGroupAPI } from '@/services';
import { checkFunctionHasPermission } from '@/utils/permission';
import { sortableColumnTitleRenderer } from '@/utils/sortableColumnTitleRenderer';
import { getUrlKey } from '@/utils/utils';

const { Option } = Select;
const ServiceGroup = (props) => {
    const { globalStore, history } = props;
    const { currentUser, currentApp } = globalStore;
    const downloadRecord = useRef();
    const [relationOrgList, setRelationOrgList] = useState([]);

    const [downloadOrg, setDownloadOrg] = useState();
    const [threeServiceList, setThreeServiceList] = useState([]);
    const [downloadDialogVisible, setDownloadDialogVisible] = useState(false);
    const [resetUuid, setResetUuid] = useState(null);

    const [tableInfo, setTableInfo] = useState({
        curPage: 1,
        pageSize: 10,
        total: 0,
        tableData: [],
        groupName: getUrlKey('groupName') || '', // 调用方服务组名称
        name: '', // 调用方服务组标识
        serviceName: '', // 数据源标识
        status: getUrlKey('status') || '', // 服务组状态
        currentSorter: undefined
    });
    const { tableData, total, curPage, pageSize, name, groupName, serviceName, status, currentSorter } = tableInfo;
    const [loading, { toggle }] = useToggle();
    const getServiceList = () => {
        dataServiceListAPI.getListAll({ queryType: 3 }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setThreeServiceList(res.data.contents ? res.data.contents : []);
            } else {
                message.error(res.msg);
            }
        });
    };
    const getList = (page, size) => {
        const params = {
            curPage: page || curPage,
            pageSize: size || pageSize,
            groupName,
            name,
            serviceName,
            status,
            appNames: currentApp.name
        };

        if (currentSorter) {
            params.sortField = (() => {
                if (currentSorter.field === 'groupName') {
                    return 'displayName';
                }

                if (currentSorter.field === 'appDisplayName') {
                    return 'appName';
                }

                return currentSorter.field;
            })();
            params.sortRule = currentSorter.order === 'ascend' ? 'asc' : 'desc';
        }

        toggle(true);
        serviceGroupAPI.getList(params).then((res) => {
            toggle(false);
            if (!res) return;
            if (res && res.success) {
                if (!res.data) return;
                const data = res.data;
                setTableInfo({
                    ...tableInfo,
                    tableData: data.contents || [],
                    curPage: data.curPage,
                    pageSize: data.pageSize,
                    total: data.total
                });
            } else {
                message.error(res.msg);
            }
        });
    };
    const showDownloadFileDialog = (record) => {
        serviceGroupAPI.getRelationOrgList({ uuid: record.uuid }).then((res) => {
            setRelationOrgList(res?.data || []);
            setDownloadOrg(res?.data[0]?.code);
        });
        downloadRecord.current = record;
        setDownloadDialogVisible(true);
    };

    const handleDownloadDialogConfirm = () => {
        if (downloadOrg) {
            downloadFile(downloadRecord.current, downloadOrg);
            setDownloadDialogVisible(false);
        }
    };

    const handleDownloadDialogHide = () => {
        setRelationOrgList([]);
        setDownloadDialogVisible(false);
    };

    const downloadFile = useCallback((record, organizationCode) => {
        const { uuid, groupName, appName } = record;

        serviceGroupAPI.downloadDoc(
            { uuid, appNames: appName, organizationCode },
            I18N.template(I18N.servicegroup.index.gROUP, { val1: groupName })
        );
    }, []);

    // 重新获取密钥
    const refreshKey = (record) => {
        const { uuid, name } = record || {};

        appServiceListAPI
            .reBuildGroupSecretKey({
                uuid,
                name
            })
            .then((res) => {
                if (res.success) {
                    message.success(I18N.servicegroup.index.zhongZhiMiYaoCheng);
                    getList();
                } else {
                    message.error(res.message || I18N.servicegroup.index.zhongZhiMiYaoShi);
                }
            })
            .catch((e) => {
                message.error(e.message || I18N.servicegroup.index.zhongZhiMiYaoShi);
            })
            .finally(() => {
                setResetUuid(null);
            });
    };

    const columns = [
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.fuWuZuMingCheng),
            dataIndex: 'groupName',
            width: 160,
            sorter: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.fuWuZuBiaoZhi),
            dataIndex: 'name',
            width: 160,
            sorter: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.quDao),
            dataIndex: 'appDisplayName',
            width: 160,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.servicegroup.index.miYao,
            dataIndex: 'secretKey',
            width: 120,
            render: (secretKey, record) => {
                return (
                    <div className="secret-key">
                        <Tooltip title={I18N.servicegroup.index.fuZhiMiYao}>
                            <span
                                className="key"
                                onClick={() => {
                                    copy(secretKey);
                                    message.success(I18N.servicegroup.index.fuZhiChengGong);
                                }}
                            />
                        </Tooltip>
                        <Tooltip title={I18N.servicegroup.index.zhongZhiMiYao}>
                            <span
                                className={`reset-key ${resetUuid && record.uuid === resetUuid ? 'disabled' : ''}`}
                                onClick={() => {
                                    if (resetUuid) {
                                        return;
                                    }
                                    setResetUuid(record.uuid);
                                    refreshKey(record);
                                }}
                            />
                        </Tooltip>
                    </div>
                );
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.jieKouShangXianGe),
            dataIndex: 'sum',
            width: 190,
            render: (text, record) => {
                return (
                    <Popover
                        content={
                            <Table
                                size="middle"
                                bordered
                                scroll={{ x: 800 }}
                                columns={[
                                    {
                                        dataIndex: 'displayName',
                                        title: I18N.servicegroup.index.shuJuYuanJieKou2
                                    },
                                    {
                                        dataIndex: 'serviceName',
                                        title: I18N.servicegroup.index.shuJuYuanJieKou
                                    },
                                    {
                                        dataIndex: 'status',
                                        title: I18N.servicegroup.index.zhuangTai,
                                        width: 90,
                                        render(text) {
                                            const mapper = {
                                                1: I18N.servicegroup.index.shangXian,
                                                2: I18N.servicegroup.index.xiaXian
                                            };

                                            return mapper[text];
                                        }
                                    }
                                ]}
                                dataSource={record.middleInfoList.map((middleInfo) => {
                                    const targetService = threeServiceList.find(({ name }) => name === middleInfo.serviceName);

                                    return {
                                        ...middleInfo,
                                        displayName: targetService ? targetService.displayName : ''
                                    };
                                })}
                                pagination={false}
                                rowKey="serviceName"
                            />
                        }
                        placement="left">
                        <a style={{ cursor: 'default' }}>{`${record.count} / ${text}`}</a>
                    </Popover>
                );
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.zhuangTai),
            dataIndex: 'status',
            sorter: true,
            width: 160,
            render: (text, record) => {
                return (
                    <Switch
                        disabled={!checkFunctionHasPermission('TZ0402', 'enable')}
                        checkedChildren={I18N.servicegroup.index.shangXian}
                        unCheckedChildren={I18N.servicegroup.index.xiaXian}
                        checked={text === 1}
                        onChange={(checked) => {
                            changeStatus(record.name, checked);
                        }}
                    />
                );
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.chuangJianShiJian),
            dataIndex: 'gmtCreate',
            sorter: true,
            width: 190
        },
        {
            title: I18N.servicegroup.index.chuangJianRen,
            dataIndex: 'creator',
            width: 120,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.servicegroup.index.xiuGaiShiJian),
            width: 190,
            dataIndex: 'gmtModify',
            sorter: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.servicegroup.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 120,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.servicegroup.index.caoZuo,
            dataIndex: 'operate',
            width: 160,
            fixed: 'right',
            render: (text, record) => (
                <HandleIcon>
                    {record.status == 1
                        ? null
                        : checkFunctionHasPermission('TZ0402', 'modify') && (
                              <HandleIcon.Item title={I18N.servicegroup.index.xiuGai}>
                                  <Icon
                                      type="form"
                                      onClick={() => {
                                          addModify(2, record);
                                      }}
                                  />
                              </HandleIcon.Item>
                          )}
                    {checkFunctionHasPermission('TZ0402', 'look') && (
                        <HandleIcon.Item title={I18N.servicegroup.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    addModify(3, record);
                                }}
                            />
                        </HandleIcon.Item>
                    )}
                    {record.status == 1
                        ? null
                        : checkFunctionHasPermission('TZ0402', 'delete') && (
                              <HandleIcon.Item title={I18N.servicegroup.index.shanChu}>
                                  <Popconfirm title={I18N.servicegroup.index.queRenShanChu} onConfirm={() => deleteHandle(record.name)}>
                                      <Icon type="delete" />
                                  </Popconfirm>
                              </HandleIcon.Item>
                          )}
                    <HandleIcon.Item title={I18N.servicegroup.index.xiaZai}>
                        <Icon type="download-file" onClick={() => showDownloadFileDialog(record)} />
                    </HandleIcon.Item>
                </HandleIcon>
            )
        }
    ];

    const searchParams = [
        {
            render: (
                <Input
                    value={tableInfo.groupName}
                    placeholder={I18N.servicegroup.index.qingShuRuFuWu}
                    onChange={(e) => {
                        setTableInfo({
                            ...tableInfo,
                            groupName: e.target.value
                        });
                    }}
                />
            )
        },
        // {
        // 	render: (
        // 		<Input
        // 			placeholder="服务组标识"
        // 			onChange={(e) => {
        // 				setTableInfo({
        // 					...tableInfo,
        // 					name: e.target.value
        // 				});
        // 			}}
        // 		/>
        // 	)
        // },
        {
            render: (
                <Select
                    showSearch
                    allowClear
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 240 }}
                    style={{ width: '240px' }}
                    placeholder={I18N.servicegroup.index.qingXuanZeShuJu}
                    optionFilterProp="children"
                    onChange={(e) => {
                        setTableInfo({
                            ...tableInfo,
                            serviceName: e
                        });
                    }}>
                    {threeServiceList &&
                        threeServiceList.map((item, index) => {
                            return (
                                <Option value={item.name} key={index}>
                                    {item.displayName}
                                </Option>
                            );
                        })}
                </Select>
            )
        },
        {
            render: (
                <Select
                    showSearch
                    allowClear
                    dropdownMatchSelectWidth={false}
                    style={{ width: '200px' }}
                    placeholder={I18N.servicegroup.index.qingXuanZeFuWu}
                    optionFilterProp="children"
                    defaultValue={getUrlKey('status') == 1 ? I18N.servicegroup.index.shangXian : undefined}
                    onChange={(e) => {
                        setTableInfo({
                            ...tableInfo,
                            status: e
                        });
                    }}>
                    <Option value={2}>{I18N.servicegroup.index.xiaXian}</Option>
                    <Option value={1}>{I18N.servicegroup.index.shangXian}</Option>
                </Select>
            )
        },
        {
            render: (
                <Button onClick={() => getList(1)} type="primary">
                    {I18N.servicegroup.index.chaXun}
                </Button>
            )
        }
    ];

    let tableParams = [
        {
            position: 'right',
            render: (
                <Button onClick={() => addModify(1)} icon="plus" type="primary">
                    {I18N.servicegroup.index.xinZeng}
                </Button>
            )
        }
    ];

    const changeStatus = (name, checked) => {
        const params = {
            name,
            status: checked ? 1 : 2,
            userName: currentUser.account
        };
        serviceGroupAPI.setOnline(params).then((res) => {
            if (!res) return;
            if (res && res.success) {
                message.success(res.msg);
                getList();
            } else {
                message.error(res.msg);
            }
        });
    };

    const deleteHandle = (name) => {
        const params = {
            name
        };
        serviceGroupAPI.deleteData(params).then((res) => {
            if (!res) return;
            if (res && res.success) {
                message.success(res.msg);
                let page = curPage;
                if (tableData.length === 1 && curPage > 1) page = curPage - 1;
                getList(page);
            } else {
                message.error(res.msg);
            }
        });
    };

    const addModify = (type, record) => {
        let name = type === 1 ? '' : record.name;
        let path = `/handle/appServiceCenter/serviceGroup/detail?type=${type}&name=${name}`;
        history.push(path);
    };

    const handleTableSort = (pagination, filters, sorter) => {
        if (sorter && sorter.order) {
            setTableInfo({
                ...tableInfo,
                currentSorter: sorter
            });
        } else {
            setTableInfo({
                ...tableInfo,
                currentSorter: undefined
            });
        }
    };

    useEffect(() => {
        getList();
        getServiceList();
    }, [currentApp]);

    useEffect(() => {
        getList(1);
    }, [currentSorter]);

    // 权限校验
    if (!checkFunctionHasPermission('TZ0402', 'add')) {
        tableParams = [];
    }

    return (
        <>
            <Modal
                title={I18N.servicegroup.index.xuanZeJiGouBing}
                visible={downloadDialogVisible}
                onOk={handleDownloadDialogConfirm}
                onCancel={handleDownloadDialogHide}>
                <div className="item">
                    <span>{I18N.servicegroup.index.jiGou}</span>
                    <Select
                        value={downloadOrg}
                        style={{
                            width: '200px'
                        }}
                        onChange={(value) => {
                            setDownloadOrg(value);
                        }}>
                        {relationOrgList.map((org) => (
                            <Select.Option key={org.code} value={org.code}>
                                {org.name}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
            </Modal>
            <SimpleList
                hasPermission={checkFunctionHasPermission('TZ0402', 'query')}
                tableParams={tableParams}
                searchParams={searchParams}
                columns={columns}
                dataSource={tableData}
                total={total}
                curPage={curPage}
                pageSize={pageSize}
                loading={loading}
                rowKey="name"
                scroll={{ x: 1800 }}
                // title="调用方服务组列表"
                onSort={handleTableSort}
                onChange={(curPage, pageSize) => getList(curPage, pageSize)}
            />
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(TableContainer(ServiceGroup));
