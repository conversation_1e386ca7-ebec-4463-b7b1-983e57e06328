/*
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: Modal弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, Input, Select, message, Button, Tooltip, Icon, InputNumber } from 'tntd';
import { cloneDeep, get } from 'lodash';
import { dataServiceListAPI, appServiceListAPI } from '@/services';
import RouterConfig from './RouterConfig';
import '../index.less';
import cn from 'classnames';

const Option = Select.Option;

class AddModifyModal extends PureComponent {
    state = {
        loading: false,
        threeServiceList: [], // 根据供应商筛选的三方数据
        nextThreeServiceList: {},
        fields: [{ serviceName: undefined, proportion: 100, serviceType: undefined, threeServiceList: [] }],
        routerList: [],
        typeList: [],
        serviceInfoList: []
    };

    componentDidMount() {
        const { appServiceListStore } = this.props;
        const { dialogData, modalType } = appServiceListStore;
        const { serviceProportion } = dialogData.addEditModalData;

        if (modalType !== 1) {
            const ids = serviceProportion.map(({ serviceId }) => serviceId);
            this.getServiceList(serviceProportion[0].serviceType, ids)
                .then((data) => {
                    if (data) {
                        const { typeList, serviceInfoList } = data;
                        const mapper = ({ displayName, name, uuid }) => ({
                            uuid,
                            displayName,
                            name
                        });

                        serviceProportion.forEach((proportion, index) => {
                            if (index === 0) {
                                proportion.threeServiceList = JSON.stringify(typeList.map(mapper));
                            } else {
                                proportion.threeServiceList = JSON.stringify(
                                    get(serviceInfoList, `[${index - 1}].serviceMap.${proportion.serviceType}`, []).map(mapper)
                                );
                            }
                        });

                        this.setState({
                            fields: serviceProportion
                        });
                    }
                })
                .catch(() => {
                    this.setState({
                        fields: serviceProportion
                    });
                });

            this.getRouterList(ids);
        }
    }

    getServiceList = (dataType, ids) => {
        const { appServiceListStore } = this.props;
        const { dialogData } = appServiceListStore;
        const { addEditModalData } = dialogData;
        const { appName } = addEditModalData;
        return dataServiceListAPI.getUpdateServiceInfo({ dataType, ids, appNames: appName }).then((res) => {
            if (res) {
                if (res.success) {
                    if (!res.data) {
                        return;
                    }

                    const { typeList, serviceInfoList } = res.data;

                    return {
                        typeList,
                        serviceInfoList
                    };
                }
                message.error(res.msg || res.message);
            } else {
                message.error(res.msg || res.message);
            }
        });
    };

    // 点击确定
    handleOk = () => {
        const { appServiceListStore, globalStore } = this.props;
        const { fields } = this.state;
        const { account } = globalStore.currentUser;
        const { dialogData, modalType } = appServiceListStore;
        let { addEditModalData } = dialogData;

        let { displayName, name, appName, responseType, switchFlag, routeConfig } = addEditModalData;
        if (!displayName || !name || !appName || !responseType) {
            return message.warning(I18N.inner.addmodifymodal.youBiTianXiangWei); // 有必填项未填
        }
        if (displayName.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.fuWuMingChengBu);
        }
        if (name.length > 200) {
            return message.warning(I18N.inner.addmodifymodal.fuWuBiaoShigBu);
        }
        let flag = false;
        let proportion = 0;
        for (let index = 0; index < fields.length; index++) {
            const element = fields[index];
            proportion = proportion + element.proportion;
            if (!element.serviceName) {
                flag = true;
                break;
            }
            if (!element.proportion) {
                flag = true;
                break;
            }
            if (!element.serviceType) {
                flag = true;
                break;
            }
        }
        if (flag) {
            if (fields.some((field) => field.proportion === 0)) {
                return message.warning(I18N.inner.addmodifymodal.cunZaiFenLiuBi);
            }
            return message.warning(I18N.inner.addmodifymodal.youBiTianXiangWei); // 有必填项未填
        }
        if (proportion !== 100) {
            return message.warning(I18N.inner.addmodifymodal.fenLiuBiLiZong); // 有必填项未填
        }
        let copyData = Object.assign({}, addEditModalData);
        copyData.appNames = copyData.appName;
        if (switchFlag === 1) {
            let flag = false;
            routeConfig.forEach((item) => {
                if (!item) flag = true;
            });
            if (flag) return message.warning(I18N.inner.addmodifymodal.youBiTianXiangWei); // 有必填项未填
            copyData.routeConfig = JSON.stringify(routeConfig);
        } else {
            copyData.routeConfig = JSON.stringify([]);
        }
        if (modalType === 1) {
            // 新增
            copyData.createUser = account;
            copyData.serviceProportion = JSON.stringify(fields);
            this.addData(copyData);
        } else if (modalType === 2) {
            // 修改
            copyData.serviceProportion = JSON.stringify(fields);
            const params = {
                ...copyData,
                modifyUser: account
            };
            this.updateData(params);
        }
    };

    // 添加数据
    addData = (params) => {
        this.setState({ loading: true });
        appServiceListAPI
            .addData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message || res.msg);
                    const { dispatch, appServiceListStore } = this.props;
                    const { searchParams } = appServiceListStore;
                    const { curPage, pageSize } = searchParams;
                    dispatch({
                        type: 'appServiceList/getList',
                        payload: {
                            curPage,
                            pageSize,
                            sortField: searchParams.sortField,
                            sortRule: searchParams.sortRule
                        }
                    });
                } else {
                    message.error(res.msg || res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 更新数据
    updateData = (params) => {
        this.setState({ loading: true });
        appServiceListAPI
            .updateData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.msg || res.message);
                    const { dispatch, appServiceListStore } = this.props;
                    const { searchParams } = appServiceListStore;
                    const { curPage, pageSize } = searchParams;
                    dispatch({
                        type: 'appServiceList/getList',
                        payload: {
                            curPage,
                            pageSize,
                            sortField: searchParams.sortField,
                            sortRule: searchParams.sortRule
                        }
                    });
                } else {
                    message.error(res.msg || res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 点击取消
    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: false
                }
            }
        });
        setTimeout(() => {
            dispatch({
                type: 'appServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            uuid: null,
                            name: null, // 业务系统服务标识
                            displayName: null, // 业务系统服务名称
                            serviceName: null, // 三方服务接口名称
                            channelName: null, // 业务系统标识
                            appName: null, // 应用名称
                            cacheDay: null, // 数据缓存天数
                            retry: null, // 重试次数
                            serviceProportion: [{ serviceName: undefined, proportion: 100, serviceType: undefined }], // 重试次数
                            timeout: null, // 超时时间
                            switchFlag: 2, // 调用异常是否切换 1是，2否
                            providerName: null, // 供应商名称
                            dataType: null, // 数据类型
                            responseType: null // 返回类型(1原始报文/2解析报文)
                        }
                    }
                }
            });
        }, 300);
    };

    // 改变参数
    changeField(e, type, field) {
        let { fields } = this.state;
        const { dispatch, appServiceListStore } = this.props;
        const {
            dialogData: {
                addEditModalData: { routeConfig, appName }
            }
        } = appServiceListStore;
        let val = null;
        let obj = {};
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        obj[field] = val;

        if (field === 'appName') {
            fields = [{ serviceName: undefined, proportion: 100, serviceType: undefined, threeServiceList: [] }];
            obj.routeConfig = [''];
        }

        // 异常切换
        if (field === 'switchFlag') {
            if (e === 1 && fields[0].serviceName) {
                let ids = [];
                fields?.forEach((field) => {
                    const fieldItem = JSON.parse(field['threeServiceList']);
                    const tempObj = fieldItem.find((item) => item.name === field.serviceName);
                    if (tempObj) {
                        ids.push(tempObj.uuid);
                    }
                });
                this.getDefaultRouteList(ids, appName);
            }
        }

        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...obj
                    }
                }
            }
        });
        this.setState({
            fields: [...fields]
        });
    }

    // 获取路由列表
    getRouterList = (uuid) => {
        const { appServiceListStore } = this.props;
        const { dialogData } = appServiceListStore;
        const { addEditModalData } = dialogData;
        const { appName } = addEditModalData;
        appServiceListAPI.findDefaultRouteServiceList({ id: uuid, appNames: appName }).then((res) => {
            if (res.success) {
                this.setState({
                    routerList: res.data ? res.data : []
                });
            } else {
                message.error(res.msg || res.message);
            }
        });
    };

    // 获取默认路由
    getDefaultRouteList = (uuid, appName) => {
        const { dispatch } = this.props;
        appServiceListAPI.findDefaultRouteServiceList({ id: uuid[0], appNames: appName }).then((res) => {
            let arr = [''];
            if (res?.data?.length) {
                arr = res?.data?.slice(0, 3)?.map((v) => v.name);
            }
            dispatch({
                type: 'appServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            routeConfig: arr
                        }
                    }
                }
            });
        });
    };

    addItem = (index) => {
        const { fields } = this.state;
        const curItem = fields[0];
        const tempItem = JSON.parse(curItem['threeServiceList']);

        if (tempItem.length === 0) {
            return message.error(I18N.inner.addmodifymodal.qingXianWanShanDang);
        }

        const tempObj = tempItem.find((item) => item.name === curItem.serviceName) || {};

        this.getAvailableService(tempObj.uuid, (data) => {
            fields.splice(index + 1, 0, {
                serviceName: undefined,
                proportion: 0,
                serviceType: undefined,
                disabledType: JSON.stringify(data?.list),
                threeServiceList: '[]'
            });
            this.setState({
                fields: [...fields]
            });
        });
    };

    deleteItem = (index) => {
        const { dispatch, appServiceListStore } = this.props;
        const { dialogData } = appServiceListStore;
        const { addEditModalData } = dialogData;
        const { fields } = this.state;
        let obj = {};
        let flag = false;
        let tempFields = fields.filter((field, idx) => {
            if (index < idx) {
                field.serviceName = undefined;
                field.serviceType = undefined;
            }
            if (index === 0 && index + 1 === idx) {
                field.proportion = 100;
            }
            if (field.serviceName && index - 1 === idx) {
                flag = field;
            }
            return field.serviceType;
        });

        tempFields.splice(index, 1);

        if (flag) {
            const tempItem = JSON.parse(flag['threeServiceList']);
            const tempObj = tempItem.find((item) => item.name === flag.serviceName) || {};
            this.getAvailableService(tempObj.uuid, (data) => {
                this.setState({
                    fields: [...tempFields]
                });
            });
        } else {
            tempFields.push({
                serviceName: undefined,
                proportion: 100,
                serviceType: undefined,
                disabledType: undefined,
                threeServiceList: '[]'
            });
            this.setState({
                fields: [...tempFields],
                routerList: []
            });
            obj.routeConfig = [''];
        }

        const ids = [];

        tempFields.forEach((field) => {
            const fieldItem = JSON.parse(field['threeServiceList']);
            const tempObj = fieldItem.find((item) => item.name === field.serviceName);

            if (tempObj) {
                ids.push(tempObj.uuid);
            }
        });
        this.getRouterList(ids.splice(0, 1)); // 只传第一个uuid

        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...addEditModalData,
                        ...obj
                    }
                }
            }
        });
    };

    getAvailableService = (uuid, callback) => {
        appServiceListAPI.getAvailableService({ uuid }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    nextThreeServiceList: res.data
                });
            }
            callback && callback(res.data);
        });
    };

    changeItem = async (index, val, key, e) => {
        let { fields, nextThreeServiceList } = this.state;
        const { map = {} } = nextThreeServiceList;
        const { dispatch, appServiceListStore } = this.props;
        const { dialogData } = appServiceListStore;
        const { addEditModalData } = dialogData;
        const { appName, switchFlag } = addEditModalData;
        let obj = {};
        const curItem = fields[index];
        curItem[key] = val;
        if (key === 'serviceType') {
            if (index === 0) {
                const res = await dataServiceListAPI.getList({ dataType: val, appNames: appName });
                let data = [];
                if (res && res.success) {
                    if (!res.data) return;
                    // 过滤掉没有表示的数据
                    res.data.contents.forEach((item) => {
                        if (item.name && item.status === 1) {
                            data.push({
                                uuid: item.uuid,
                                name: item.name,
                                displayName: item.displayName
                            });
                        }
                    });
                    obj.routeConfig = [''];
                } else {
                    message.error(res.msg || res.message);
                    return;
                }
                curItem['threeServiceList'] = JSON.stringify(data);
            } else {
                if (map[val]) {
                    curItem['threeServiceList'] = JSON.stringify(
                        Object.assign(
                            [],
                            map[val].map((item) => {
                                return {
                                    uuid: item.uuid,
                                    name: item.name,
                                    displayName: item.displayName
                                };
                            })
                        )
                    );
                } else {
                    const preObj = fields.find((field, idx) => {
                        return index - 1 === idx;
                    });
                    if (preObj) {
                        const tempItem = JSON.parse(preObj['threeServiceList']);
                        const tempObj = tempItem.find((item) => item.name === preObj.serviceName) || {};
                        const resList = await appServiceListAPI.getAvailableService({ uuid: tempObj.uuid });
                        let curMap = {};
                        if (resList && resList.success) {
                            if (!resList.data) return;
                            this.setState({
                                nextThreeServiceList: resList.data
                            });
                            curMap = resList.data.map || {};
                        } else {
                            message.error(resList.msg);
                            return;
                        }
                        curItem['threeServiceList'] = JSON.stringify(
                            Object.assign(
                                [],
                                curMap[val].map((item) => {
                                    return {
                                        uuid: item.uuid,
                                        name: item.name,
                                        displayName: item.displayName
                                    };
                                })
                            )
                        );
                    }
                }
            }
            curItem['serviceName'] = undefined;
        }
        // 选择接口，获取路由列表
        if (key === 'serviceName') {
            let ids = [];
            fields = fields.filter((field, idx) => {
                const fieldItem = JSON.parse(field['threeServiceList']);
                const tempObj = fieldItem.find((item) => item.name === field.serviceName);
                if (tempObj) {
                    ids.push(tempObj.uuid);
                }
                if (index < idx) {
                    field.serviceName = undefined;
                    field.serviceType = undefined;
                }
                return field.serviceType;
            });
            this.getRouterList(cloneDeep(ids).splice(0, 1)); // 只传第一个uuid
            // this.getRouterList(ids);
            if (index === 0 && switchFlag === 1) {
                this.getDefaultRouteList(ids, appName);
            }
        }
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...addEditModalData,
                        ...obj
                    }
                }
            }
        });
        this.setState({
            fields: [...fields]
        });
    };

    render() {
        const { loading, routerList, fields } = this.state;
        const { appServiceListStore, globalStore } = this.props;
        const { allMap, appList } = globalStore;
        const { dialogShow, modalType, dialogData } = appServiceListStore;
        const { addEditModalData } = dialogData;
        const { displayName, name, retry, switchFlag, responseType, routeConfig, routeDisplayConfig, appName, timeout } = addEditModalData;

        let title = I18N.inner.addmodifymodal.diaoYongFangFuWu; // 三方渠道
        let disabled = false;
        let modifyDisabled = false;
        let text = I18N.inner.addmodifymodal.xinZeng;
        if (modalType === 1) title = `${text}-${title}`; // 新增
        if (modalType === 2) {
            text = I18N.inner.addmodifymodal.xiuGai;
            title = `${text}-${title}`; // 修改
            modifyDisabled = true;
        }
        if (modalType === 3) disabled = true;

        const footerDom = [
            <Button onClick={this.handleCancel} key="cancel">
                {I18N.inner.addmodifymodal.quXiao}
            </Button>,
            <Button type="primary" onClick={this.handleOk} key="ok" loading={loading}>
                {I18N.inner.addmodifymodal.queDing}
            </Button>
        ];

        return (
            <Modal
                title={title}
                width={970}
                maskClosable={false}
                visible={dialogShow.addEditModal}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={disabled ? null : footerDom}
                className="m-appservicelist-modal">
                <div className="u-title">{I18N.inner.addmodifymodal.jiBenXinXi}</div>
                <div className="modal-box">
                    <span className="u-label">
                        <b>*</b>
                        {I18N.inner.addmodifymodal.fuWuMingCheng}
                    </span>
                    <span className={cn('u-input', 'u-input-280')}>
                        <Input
                            placeholder={I18N.inner.addmodifymodal.qingShuRuFuWu}
                            disabled={disabled}
                            value={displayName}
                            onChange={(e) => this.changeField(e, 'input', 'displayName')}
                        />
                    </span>
                    <span className="u-label">
                        <b>*</b>
                        {I18N.inner.addmodifymodal.fuWuBiaoZhi}
                    </span>
                    <span className={cn('u-input', 'u-input-280')}>
                        <Input
                            placeholder={I18N.inner.addmodifymodal.qingTianXieYeWu}
                            disabled={disabled ? disabled : modifyDisabled}
                            value={name}
                            onChange={(e) => this.changeField(e, 'input', 'name')}
                        />
                    </span>
                </div>
                <div className="modal-box">
                    <span className="u-label">
                        <b>*</b>
                        {I18N.inner.addmodifymodal.quDao}
                    </span>
                    <span className="u-select">
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled ? disabled : modifyDisabled}
                            dropdownMatchSelectWidth={false}
                            dropdownStyle={{ width: 350 }}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZeQuDao}
                            optionFilterProp="children"
                            value={appName ? appName : undefined}
                            onChange={(e) => this.changeField(e, 'select', 'appName')}>
                            {appList &&
                                appList.map((item, i) => {
                                    let dom = (
                                        <Option value={item.key} key={i}>
                                            {item.name}
                                        </Option>
                                    );
                                    return i !== 0 ? dom : null;
                                })}
                        </Select>
                    </span>
                    {/* 需提前在“系统管理-字典配置”中注册，若没有，请联系系统管理员添加 */}
                    {/* <Tooltip title={'需提前在“系统管理-字典配置”中注册，若没有，请联系系统管理员添加'}>
						<Icon className="u-question" type="question-circle" />
					</Tooltip> */}
                </div>
                <div className="modal-box">
                    <span className="u-label u-label-group">
                        <b>*</b>
                        {I18N.inner.addmodifymodal.shuJuYuanFuWu}
                    </span>
                    <span className="u-select" style={{ width: '600px' }}>
                        {fields.map((item, index) => {
                            let { threeServiceList = [], disabledType } = item;
                            try {
                                threeServiceList = threeServiceList ? JSON.parse(threeServiceList) : [];
                                disabledType = disabledType ? JSON.parse(disabledType) : [];
                            } catch (error) {
                                threeServiceList = [];
                                disabledType = [];
                            }
                            return (
                                <div className="u-group" key={index}>
                                    <Select
                                        showSearch
                                        style={{ width: '150px' }}
                                        disabled={disabled}
                                        dropdownMatchSelectWidth={false}
                                        dropdownStyle={{ width: 350 }}
                                        placeholder={I18N.inner.addmodifymodal.qingXuanZeLeiXing}
                                        optionFilterProp="children"
                                        value={item.serviceType}
                                        onChange={(val, e) => {
                                            if (!appName) return message.warning(I18N.inner.addmodifymodal.qingXianXuanZeQu);
                                            this.changeItem(index, val, 'serviceType');
                                        }}>
                                        {allMap &&
                                            allMap.serviceTypeList.map((it, idx) => {
                                                let disabled = false;
                                                if (index !== 0 && !disabledType.includes(it.dataType)) {
                                                    disabled = true;
                                                }
                                                return (
                                                    <Option disabled={disabled} value={it.dataType} key={idx}>
                                                        {it.name}
                                                    </Option>
                                                );
                                            })}
                                    </Select>
                                    <Select
                                        showSearch
                                        style={{ width: '150px' }}
                                        disabled={disabled}
                                        dropdownMatchSelectWidth={false}
                                        dropdownStyle={{ width: 350 }}
                                        placeholder={I18N.inner.addmodifymodal.qingXuanZeJieKou} // 请选择接口
                                        optionFilterProp="children"
                                        value={item.serviceName}
                                        onChange={(val, e) => {
                                            if (fields.some(({ serviceName }) => serviceName === val)) {
                                                return message.warning(I18N.inner.addmodifymodal.buNengChongFuXuan); // 不能重复选择
                                            }

                                            this.changeItem(index, val, 'serviceName', e);
                                        }}>
                                        {threeServiceList.map((item, index) => {
                                            let disabled = false;
                                            fields.map((subItem) => {
                                                if (subItem.serviceName == item.name) {
                                                    disabled = true;
                                                }
                                            });
                                            return (
                                                <Option value={item.name} key={index} disabled={disabled}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                    <InputNumber
                                        style={{ width: '150px' }}
                                        placeholder={I18N.inner.addmodifymodal.qingShuRuFenLiu}
                                        value={item.proportion}
                                        disabled={disabled}
                                        min={0}
                                        max={100}
                                        formatter={(value) => `${value}%`}
                                        parser={(value) => value.replace('%', '')}
                                        onChange={(val) => {
                                            this.changeItem(index, val, 'proportion');
                                        }}
                                    />
                                    {!disabled && (
                                        <div className="right">
                                            {fields.length - 1 === index && (
                                                <Tooltip title={I18N.inner.addmodifymodal.tianJiaYiXiang} placement="left">
                                                    <Icon
                                                        className="add"
                                                        type="plus-circle-o"
                                                        onClick={() => {
                                                            this.addItem(index);
                                                        }}
                                                    />
                                                </Tooltip>
                                            )}
                                            {fields.length > 1 && (
                                                <Tooltip title={I18N.inner.addmodifymodal.yiChuDangQianXing} placement="right">
                                                    <Icon
                                                        className="delete"
                                                        type="delete"
                                                        onClick={() => {
                                                            this.deleteItem(index);
                                                        }}
                                                    />
                                                </Tooltip>
                                            )}
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </span>
                    {/* 需提前在“系统管理-字典配置”中注册，若没有，请联系系统管理员添加 */}
                </div>
                <div className="u-title">
                    {/* 接口调用机制 */}
                    {I18N.inner.addmodifymodal.jieKouDiaoYongJi}
                </div>
                <div className="modal-box" style={{ position: 'relative' }}>
                    <span className="u-label">
                        {/* 数据重试次数 */}
                        {I18N.inner.addmodifymodal.shuJuZhongShiCi}
                    </span>
                    <span className={cn('u-input', 'u-input-280')}>
                        <InputNumber
                            min={0}
                            max={5}
                            style={{ width: '100%' }}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 默认3次
                            disabled={disabled}
                            value={retry}
                            onChange={(e) => this.changeField(e, 'select', 'retry')}
                        />
                    </span>

                    <Tooltip title={I18N.inner.addmodifymodal.moRenZeYuShu2}>
                        <Icon className="u-question" type="question-circle" style={{ position: 'absolute', top: '7px' }} />
                    </Tooltip>
                    <span className="u-label">
                        <b>*</b>
                        {/* 返回类型 */}
                        {I18N.inner.addmodifymodal.fanHuiLeiXing}
                    </span>
                    <span className={cn('u-select', 'u-select-280')}>
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            dropdownMatchSelectWidth={false}
                            dropdownStyle={{ width: 350 }}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 请选择返回类型
                            optionFilterProp="children"
                            value={responseType ? responseType : undefined}
                            onChange={(e) => this.changeField(e, 'select', 'responseType')}>
                            {allMap &&
                                allMap.responseTypeList &&
                                allMap.responseTypeList.map((item, index) => {
                                    return (
                                        <Option value={item.type} key={index}>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    </span>
                </div>
                <div className="modal-box" style={{ position: 'relative' }}>
                    <span className="u-label">
                        {/* 异常切换 */}
                        {I18N.inner.addmodifymodal.yiChangQieHuan}
                    </span>
                    <span className={cn('u-input', 'u-input-280')}>
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 请选择
                            optionFilterProp="children"
                            value={switchFlag ? switchFlag : undefined}
                            onChange={(e) => this.changeField(e, 'select', 'switchFlag')}>
                            <Option value={1}>{I18N.inner.addmodifymodal.shi}</Option>
                            <Option value={2}>{I18N.inner.addmodifymodal.fou}</Option>
                        </Select>
                    </span>
                    {/* 系统自动根据输出入参匹配符合条件的三方服务接口列表供选择配置，可指定多个，设置切换优先级。
						以下两种情况触发异常切换：
						1.已达到配置重试的次数后，仍然失败
						2.当前接口已达到设置的流量上限控制
					*/}
                    <Tooltip title={I18N.inner.addmodifymodal.xiTongZiDongGen}>
                        <Icon className="u-question" type="question-circle" style={{ position: 'absolute', top: '7px' }} />
                    </Tooltip>
                    <span className="u-label">{I18N.inner.addmodifymodal.shuJuChaoShiShi}</span>
                    <span className={cn('u-input', 'u-input-280')}>
                        <InputNumber
                            min={1}
                            step={1}
                            precision={0}
                            style={{ width: '100%' }}
                            placeholder={I18N.inner.addmodifymodal.qingXuanZe} // 默认1000ms
                            disabled={disabled}
                            value={timeout}
                            onChange={(e) => this.changeField(e, 'select', 'timeout')}
                        />
                    </span>
                    <Tooltip title={I18N.inner.addmodifymodal.moRenZeYuShu2}>
                        <Icon className="u-question" type="question-circle" style={{ position: 'absolute', top: '7px' }} />
                    </Tooltip>
                </div>
                {switchFlag === 1 && (
                    <RouterConfig disabled={disabled} config={routeConfig} displayConfig={routeDisplayConfig} routerList={routerList} />
                )}
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    appServiceListStore: state.appServiceList
}))(AddModifyModal);
