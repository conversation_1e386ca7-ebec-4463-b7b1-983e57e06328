import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Select, Icon, Button, message, Tooltip } from 'tntd';

const Option = Select.Option;

const isConfigUnavailable = (routeList = [], name) => {
	return !routeList.some(route => route.name === name);
};

class RouterConfig extends PureComponent {

	add = () => {
		const { config, dispatch, } = this.props;
		let copyConfig = Object.assign([], config);
		copyConfig.push('');
		dispatch({
			type: 'appServiceList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						routeConfig: copyConfig
					}
				}
			}
		});
	}

	handleDelete = (i) => {
		const { config, dispatch } = this.props;
		let copyConfig = Object.assign([], config);
		copyConfig.splice(i, 1);
		dispatch({
			type: 'appServiceList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						routeConfig: copyConfig
					}
				}
			}
		});
	}

	changeField = (e, i) => {
		const { config, dispatch } = this.props;
		let copyConfig = Object.assign([], config);

		const findObj = config.find(item => item === e);
		if (findObj) return message.warning(I18N.inner.routerconfig.buNengChongFuXuan); // 不能重复选择
		copyConfig[i] = e;
		dispatch({
			type: 'appServiceList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						routeConfig: copyConfig
					}
				}
			}
		});
	}

	handleMove(type, i) {
		const { config, dispatch } = this.props;
		let copyConfig = Object.assign([], config);

		let temp;
		if (type === 'up') {
			temp = copyConfig[i];
			copyConfig[i] = copyConfig[i - 1];
			copyConfig[i - 1] = temp;
		} else {
			temp = copyConfig[i];
			copyConfig[i] = copyConfig[i + 1];
			copyConfig[i + 1] = temp;
		}

		dispatch({
			type: 'appServiceList/setAttrValue',
			payload: {
				dialogData: {
					addEditModalData: {
						routeConfig: copyConfig
					}
				}
			}
		});
	}
	render() {
		const { config, disabled, routerList, displayConfig = [] } = this.props;

		return (
			<Fragment>
				{
					config &&
					config.map((item, index) => {
						const value = (() => {
							if (item) {
								if (isConfigUnavailable(routerList, item)) {
									const display = displayConfig.find(({ name }) => item);

									return display ? display.displayName : undefined;
								}

								return item;
							}

							return undefined;
						})();

						return (
							<div key={index} className="m-router-config">
								<div className="u-icon-wrap">
									{
										index !== 0 &&
										!disabled &&
										// 上移
										<Tooltip title={I18N.inner.routerconfig.shangYi}>
											<Icon type="up-circle" onClick={() => this.handleMove('up', index)} />
										</Tooltip>
									}
									{
										(index + 1 !== config.length || index === 0) &&
										!disabled &&
										// 下移
										<Tooltip title={I18N.inner.routerconfig.xiaYi}>
											<Icon type="down-circle" onClick={() => this.handleMove('down', index)} />
										</Tooltip>
									}
								</div>
								<Button className="mr10">
									{/* 优先级 */}
									{I18N.inner.routerconfig.youXianJi}
									{index + 1}
								</Button>
								<Select
									style={{ width: 200, marginRight: '10px' }}
									showSearch
									disabled={disabled}
									optionFilterProp="children"
									placeholder={I18N.inner.routerconfig.qingXuanZe} // 请选择
									dropdownMatchSelectWidth={false}
									value={value}
									onChange={(e) => this.changeField(e, index)}
								>
									{
										routerList &&
										routerList.map((item, index) => {
											let disabled = false;
											disabled = config.includes(item.name)
											const map = {
												1: I18N.inner.routerconfig.zhiXinDuGao, // 置信度高
												2: I18N.inner.routerconfig.zhiXinDuZhong, // 置信度中
												3: I18N.inner.routerconfig.zhiXinDuDi // 置信度低
											};
											const map2 = {
												1: I18N.inner.routerconfig.chengBenGao, // 成本高
												2: I18N.inner.routerconfig.chengBenZhong, // 成本中
												3: I18N.inner.routerconfig.chengBenDi // 成本低
											};
											return (
												<Option value={item.name} key={index} disabled={disabled}>
													{item.displayName} {map[item.confidence]} {map2[item.costLevel]}
												</Option>
											);
										})
									}
								</Select>
								{
									config.length === index + 1 &&
									!disabled &&
									<Icon type="plus-circle" onClick={this.add} />
								}
								{
									config.length !== 1 &&
									!disabled &&
									<Icon type="delete" onClick={() => this.handleDelete(index)} />
								}
							</div>
						);
					})
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	appServiceListStore: state.appServiceList
}))(RouterConfig);
