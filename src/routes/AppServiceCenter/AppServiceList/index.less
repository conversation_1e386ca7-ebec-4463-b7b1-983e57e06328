:global{
    .p-app-service{
        .u-key, .u-key2{
            width: 20px;
            cursor: pointer;
        }
        .u-key2{
            width: 18px;
            margin-left: 10px;
        }
    }
    .m-appservicelist-modal{
        .u-title{
            border-left: 4px solid #1890ff;
            padding-left: 10px;
            font-weight: bold;
            line-height: 14px;
            margin-bottom: 20px;
            margin-top: 30px;
            &:first-child{
                margin-top: 10px;
            }
        }
        .modal-box{
            .u-label{
				width: 160px;
			}
			.u-label-group {
				vertical-align: top;
				margin-top: 0;
			}
            .ant-input-number{
                span{
                    display: block;
                }
			}
			.u-group {
				display: flex;
				margin-bottom: 6px;
				.ant-select {
					margin-right: 5px;
				}
				.right {
					display: flex;
					align-items: center;
					width: 40px;
					.anticon {
						margin-left: 5px;
					}
				}
			}
        }
        .m-router-config{
            position: relative;
            padding-left: 116px;
            margin-bottom: 10px;
            .anticon-plus-circle, .anticon-delete, .anticon-up-circle, .anticon-down-circle{
                font-size: 18px;
                cursor: pointer;
                vertical-align: middle;
                margin-top: -2px;
                margin-right: 10px;
                color: #999;
                &:hover{
                    color: #2196F3;
                }
            }
            .u-icon-wrap{
                position: absolute;
                left: 68px;
                top: 5px;
                width: 48px;
                text-align: right;
            }
            .anticon-up-circle, .anticon-down-circle{
                margin-right: 5px;
            }
        }
    }
    .secret-key{
        > span{
            display: inline-block;
            width: 16px;
            height: 16px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            vertical-align: middle;
            cursor: pointer;
            &:not(:first-of-type){
                margin-left: 10px;
            }
            &.disabled{
                opacity: 0.2;
            }
        }
        .key{
            background-image: url(../../../sources/images/application/key.png);
        }
        .reset-key{
            background-image: url(../../../sources/images/application/reset-key.png);
        }
    }
    .u-input-280 {
        width: 280px !important;
    }

    .u-select-280 {
        width: 280px !important;
    }
    
}
