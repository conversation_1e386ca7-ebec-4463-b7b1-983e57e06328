import I18N from '@/utils/I18N';
import './index.less';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Button, Select, Input, Badge, Drawer } from 'tntd';
import { systemFieldsAPI } from '@/services';

import TooltipSelect from '@tntd/tooltip-select';

const { Option } = Select;

class SearchDrawer extends PureComponent {
    state = {
        systemList: []
    };

    componentDidMount() {
        document.body.addEventListener('click', this.listener);
        this.getSystemListAll();
    }

    getSystemListAll = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res && res.success) {
                this.setState({
                    systemList: res.data ? res.data : []
                });
            }
        });
    };

    componentWillUnmount() {
        document.body.removeEventListener('click', this.listener);
    }

    listener = (e) => {
        const { dialogShow } = this.props.threeCallDetailStore;
        const clientWidth = document.body.clientWidth;
        if (e.clientY <= 65 || e.clientX <= clientWidth - 300) {
            if (dialogShow && dialogShow.searchDrawer) {
                this.handleClose();
            }
        }
    };

    handleClose = () => {
        const { dispatch } = this.props;

        dispatch({
            type: 'threeCallDetail/setAttrValue',
            payload: {
                dialogShow: {
                    searchDrawer: false
                }
            }
        });
    };

    async changeField(e, field) {
        const { getContractList, dispatch, threeCallDetailStore } = this.props;
        let { searchParams } = threeCallDetailStore;
        let obj = {};

        if (field === 'partnerId') {
            getContractList(e);
            obj['contractCode'] = null;
        }

        if (field === 'returnResult') {
            if (e === 'success') {
                const { allMap } = this.props.globalStore;
                let errorCodeList = [];
                if (allMap && allMap.responseResult) {
                    const obj = allMap.responseResult.find((item) => item.code === e);
                    if (obj) errorCodeList = obj.reasonCode;
                }
                searchParams.errorCode = errorCodeList[0].code;
            } else {
                searchParams.errorCode = null;
            }
        }
        searchParams[field] = e || e === 0 ? e : null;

        await dispatch({
            type: 'threeCallDetail/setAttrValue',
            payload: {
                searchParams: {
                    ...searchParams,
                    ...obj
                }
            }
        });
    }

    render() {
        const { systemList } = this.state;
        const { innerCount, partnerList, contractList, dispatch, threeCallDetailStore, globalStore } = this.props;
        const { allMap } = globalStore;
        const { dialogShow, searchParams } = threeCallDetailStore;
        const {
            returnResult,
            partnerId,
            contractCode,
            searchResult,
            errorCode,
            sequenceId,
            channelSequenceId,
            searchWhere,
            searchValue,
            invokeSource
        } = searchParams;

        let returnResultList = [];
        allMap &&
            allMap.responseResult &&
            allMap.responseResult.forEach((item) => {
                returnResultList.push({
                    name: item.code,
                    dName: item.name
                });
            });

        let searchResultList = [];
        allMap &&
            allMap.searchResult &&
            allMap.searchResult.forEach((item) => {
                searchResultList.push({
                    name: item.code,
                    dName: item.name
                });
            });

        let errorCodeList = [];
        if (returnResult && allMap && allMap.responseResult) {
            const obj = allMap.responseResult.find((item) => item.code === returnResult);
            if (obj) errorCodeList = obj.reasonCode;
        }

        let invokeSourcelist = [];
        allMap &&
            allMap.invokeSourcelist &&
            allMap.invokeSourcelist.forEach((item) => {
                invokeSourcelist.push({
                    name: item.type,
                    dName: item.name
                });
            });

        return (
            <Fragment>
                <Badge count={innerCount !== 0 ? innerCount : null}>
                    <Button
                        icon="filter"
                        onClick={() => {
                            dispatch({
                                type: 'threeCallDetail/setAttrValue',
                                payload: {
                                    dialogShow: {
                                        searchDrawer: true
                                    }
                                }
                            });
                        }}>
                        {/* 更多过滤 */}
                        {I18N.searchdrawer.index.gengDuoGuoLu}
                    </Button>
                </Badge>
                <Drawer
                    title={I18N.searchdrawer.index.gengDuoShaiXuan} // 更多筛选
                    width={300}
                    className="page-search-drawer-wrap"
                    placement="right"
                    closable={true}
                    onClose={this.handleClose}
                    visible={dialogShow.searchDrawer}
                    footer={
                        <>
                            <Button type="primary" onClick={this.props.onSearch}>
                                {I18N.searchdrawer.index.chaXun}
                            </Button>
                            <Button onClick={this.props.onClear}>{I18N.searchdrawer.index.qingKong}</Button>
                        </>
                    }
                    mask={false}>
                    <div className="search-drawer-detail">
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 返回结果 */}
                                {I18N.searchdrawer.index.fanHuiJieGuo}
                            </div>
                            <Select
                                allowClear
                                style={{ marginBottom: '8px' }}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeFanHui} // 请选择返回结果
                                value={returnResult || undefined}
                                onChange={(e) => this.changeField(e, 'returnResult')}>
                                {returnResultList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                            <Select
                                allowClear
                                disabled={returnResult === 'success'}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeZhuangTai} // 请选择状态码
                                value={errorCode || undefined}
                                onChange={(e) => this.changeField(e, 'errorCode')}>
                                {errorCodeList.map((item, index) => {
                                    return (
                                        <Option value={item.code} key={index}>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        {!allMap.isIntegrationTG &&
                            (
                                <div className="filter-item">
                                    <div className="label-text">{I18N.searchdrawer.index.heZuoFangMingCheng}</div>
                                    <Select
                                        allowClear
                                        showSearch
                                        optionFilterProp="children"
                                        placeholder={I18N.searchdrawer.index.qingXuanZeHeZuo}
                                        value={partnerId || undefined}
                                        onChange={(e) => this.changeField(e, 'partnerId')}>
                                        {partnerList?.map((v) => (
                                            <Option key={v.uuid} value={v.uuid}>
                                                {v.displayName}
                                            </Option>
                                        ))}
                                    </Select>
                                </div>
                            )
                        }
                        {!allMap.isIntegrationTG &&
                            (
                                <div className="filter-item">
                                    <div className="label-text">{I18N.searchdrawer.index.heTongMingCheng}</div>
                                    <Select
                                        allowClear
                                        showSearch
                                        optionFilterProp="children"
                                        placeholder={I18N.searchdrawer.index.qingXuanZeHeTong}
                                        value={contractCode || undefined}
                                        onChange={(e) => this.changeField(e, 'contractCode')}>
                                        {contractList?.map((v) => (
                                            <Option key={v.code} value={v.code}>
                                                {v.name}
                                            </Option>
                                        ))}
                                    </Select>
                                </div>
                            )
                        }
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 查询结果 */}
                                {I18N.searchdrawer.index.chaXunJieGuo}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeChaXun} // 请选择查询结果
                                value={searchResult || undefined}
                                onChange={(e) => this.changeField(e, 'searchResult')}>
                                {searchResultList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        <div className="filter-item">
                            <div className="label-text">{I18N.searchdrawer.index.yeWuWeiYiLiu}</div>
                            <Input
                                placeholder={I18N.searchdrawer.index.qingShuRuYeWu}
                                value={channelSequenceId || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'channelSequenceId')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 调用唯一标识码 */}
                                {I18N.searchdrawer.index.diaoYongWeiYiBiao}
                            </div>
                            <Input
                                placeholder={I18N.searchdrawer.index.shuRuDiaoYongWei} // 输入调用唯一标识码（sequenceID）
                                value={sequenceId || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'sequenceId')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 入参字段 */}
                                {I18N.searchdrawer.index.ruCanZiDuan}
                            </div>
                            <TooltipSelect
                                isVirtual
                                allowClear
                                style={{ marginBottom: '8px' }}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeRuCan} // 请选择入参字段
                                value={searchWhere || undefined}
                                onChange={(e) => this.changeField(e, 'searchWhere')}>
                                {systemList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.displayName}
                                        </Option>
                                    );
                                })}
                            </TooltipSelect>
                            <Input
                                placeholder={I18N.searchdrawer.index.shuRuZhiQianZhui} // 输入值（前缀匹配）
                                value={searchValue || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'searchValue')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 数据类型来源 */}
                                {I18N.searchdrawer.index.shuJuLeiXingLai}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZe} // 请选择
                                value={invokeSource || undefined}
                                onChange={(e) => this.changeField(e, 'invokeSource')}>
                                {invokeSourcelist.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                    </div>
                </Drawer>
            </Fragment>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    threeCallDetailStore: state.threeCallDetail
}))(SearchDrawer);
