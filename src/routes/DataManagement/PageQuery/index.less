:global {
    .p-page-query {
        .m-result-query {
            position: relative;
            height: ~"calc(100vh - 165px)";
            background: #fff;
            overflow: hidden;
            border-radius: 16px;
            .middle,
            .right {
                float: left;
                height: 100%;
                border-radius: 0 @border-radius-base @border-radius-base 0;
                .header {
                    // border-bottom: 1px solid #e6e6e6;
                    height: 48px;
                    line-height: 48px;
                    color: #17233d;
                    padding: 0px 16px;
                    .s1 {
                        display: inline-block;
                        width: 100px;
                        height: 100%;
                        text-align: center;
                        margin-top: 1px;
                        cursor: pointer;
                    }
                    .active {
                        color: #126bfb;
                        border-bottom: 2px solid #126bfb;
                    }
                }
                .body {
                    height: ~"calc(100vh - 260px)";
                    overflow-x: hidden;
                    overflow-y: auto;
                    .ul {
                        margin: 0;
                        padding: 0;
                        li {
                            list-style: none;
                            min-height: 56px;
                            line-height: 56px;
                            cursor: pointer;
                            padding: 0 20px;
                            border-bottom: 1px solid #e6e6e6;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .active {
                            color: #222;
                            background: #f0f0f0;
                        }
                    }
                    .box {
                        position: relative;
                        .lable {
                            // display: inline-block;
                            display: flex;
                            margin-bottom: 5px;
                            width: 100%;
                            b {
                                color: #f00;
                                vertical-align: middle;
                                margin-right: 3px;
                            }
                        }
                        input {
                            margin-bottom: 25px;
                        }
                        .red-border {
                            border: #f5222d solid 1px;
                        }
                        .tip {
                            position: absolute;
                            left: 0;
                            bottom: 5px;
                            font-size: 12px;
                            color: #f5222d;
                        }
                    }
                    .btn {
                        width: 100%;
                    }
                    .result {
                        margin-bottom: 30px;
                        .result-header {
                            position: relative;
                            font-size: 17px;
                            padding-left: 12px;
                            margin-bottom: 10px;
                            &::before {
                                position: absolute;
                                left: 0;
                                top: 6px;
                                width: 4px;
                                height: 16px;
                                background: #126bfb;
                                content: "";
                                border-radius: 8px;
                            }
                        }
                        .result-body {
                            padding: 20px;
                            // border: 1px dashed #dcdcdc;
                            background: #f5f5f5;
                            .ant-row {
                                padding: 5px 0;
                                border-bottom: 1px #dcdada dashed;
                                &:last-child {
                                    border: none;
                                }
                                .ant-col-12 {
                                    word-break: break-all;
                                }
                            }
                        }
                    }
                    // .ant-btn {
                    //     font-size: 12px;
                    //     i {
                    //         font-size: 18px;
                    //         padding-top: 2px;
                    //     }
                    //     span {
                    //         vertical-align: text-bottom;
                    //     }
                    // }
                }
            }
            .middle {
                position: absolute;
                left: 0;
                top: 0;
                z-index: 2;
                width: 40%;
                box-shadow: 0 0 10px 0 rgba(213, 213, 213, 0.5);
                border-radius: @border-radius-base 0 0 @border-radius-base;
                .body {
                    padding: 20px;
                    .ant-form-item-label {
                        text-align: left;
                    }
                    .u-tip {
                        font-size: 12px;
                        color: #ff9800;
                        i {
                            font-size: 14px;
                            margin: 0 2px 0 4px;
                        }
                    }
                }
                .m-pagequery-table {
                    border: 1px solid #e1e6ee;
                    border-radius: 2px;
                    margin-top: 15px;
                    table {
                        width: 100%;
                        tr {
                            border-bottom: 1px solid #e1e6ee;
                            th,
                            td {
                                padding: 10px;
                                font-size: 12px;
                                .s1,
                                .s2 {
                                    color: #1890ff;
                                    cursor: pointer;
                                    display: inline-block;
                                }
                                .s1 {
                                    margin-right: 8px;
                                }
                            }
                        }
                        thead {
                            tr {
                                background: #fafafa;
                            }
                        }
                        tbody {
                            tr {
                                &:hover {
                                    box-shadow: 0 0 6px 0 rgba(23, 35, 61, 0.2);
                                }
                                &:last-child {
                                    border: none;
                                }
                            }
                            .active {
                                box-shadow: 0 0 6px 0 rgba(23, 35, 61, 0.2);
                            }
                        }
                    }
                }
                .ant-pagination {
                    text-align: right;
                    margin-top: 10px;
                    .ant-pagination-item-active {
                        border-color: #fff;
                        &:hover {
                            border-color: #fff;
                        }
                    }
                }
            }
            .right {
                position: absolute;
                right: 0;
                top: 0;
                width: 60%;
                background: #fcfcfc;
                box-shadow: 6px 0 10px 0 rgba(213, 213, 213, 0.5);
                .body {
                    padding: 20px;
                    .u-data {
                        background: #f3f3f3;
                        font-size: 12px;
                        padding: 8px 12px;
                        border-radius: 2px;
                        .u-p1 {
                            margin: 6px 0;
                            .ant-progress-line {
                                width: 160px;
                                margin-left: 8px;
                                .ant-progress-inner {
                                    background-color: #dcdcdc;
                                }
                            }
                            .s1 {
                                color: #00b794;
                            }
                            .s2 {
                                color: #ec5046;
                            }
                            .s3 {
                                color: #00b794;
                                margin-right: 15px;
                            }
                        }
                    }
                }
                .header {
                    padding: 0 20px;
                }
                .m-result-table {
                    margin-top: 12px;
                    overflow: auto;
                    table {
                        min-width: 100%;
                    }
                    .th1 {
                        background: #f3f3f3;
                        b {
                            color: #f00;
                            vertical-align: middle;
                            margin-right: 2px;
                        }
                    }
                    .th2 {
                        background: #dfe3f5;
                    }
                    .th3 {
                        background: #f8f8f8;
                    }
                    .th4 {
                        background: #ebeefb;
                    }
                    th,
                    td {
                        padding: 10px 20px;
                        font-size: 12px;
                        border-right: 1px solid #fff;
                    }
                    tbody {
                        tr {
                            border-top: 1px solid #fff;
                        }
                    }
                }
                .pagination-bg {
                    text-align: right;
                    background: #f8f8f8;
                    border-top: 1px solid #fff;
                    padding: 10px 8px;
                    font-size: 12px;
                }
            }
            .title-tabs {
                margin-top: 30px;
                margin: 0 auto;
                display: flex;
                // justify-content: space-between;
                align-items: center;
                text-align: center;
                height: 40px;
                width: 97%;
                background: #f1f2f6;
                div {
                    cursor: pointer !important;
                    margin-right: 30px; //50px;
                    width: 135px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    &:hover {
                        background: #126bfb;
                        color: white;
                    }
                }
                div:first-of-type {
                    margin-left: 20px !important;
                }
                div:last-of-type {
                    margin-right: 0px !important;
                }
            }
        }
    }
}

.m-batch-query-modal {
    .ant-modal-content {
        padding: 12px 32px 18px;
        .ant-modal-close {
            display: none;
        }
        .anticon-exclamation-circle {
            color: #faad14;
            float: left;
            margin-right: 16px;
            font-size: 22px;
        }
        .ant-modal-body {
            font-size: 16px;
            span {
                color: #f44336;
            }
        }
    }
    .ant-modal-footer {
        border: none;
    }
}
