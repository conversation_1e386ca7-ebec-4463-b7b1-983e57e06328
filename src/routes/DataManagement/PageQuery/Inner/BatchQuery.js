import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment } from 'react';
import NoData from '@/components/NoData';
import { pageQueryAPI } from '@/services';
import { message, Button, Upload, Pagination, Spin, Modal, Icon } from 'tntd';
import fetch from 'dva/fetch';
import { connect } from 'dva';
import { getHeader } from '@/utils/common';

class BatchQuery extends PureComponent {
    state = {
        curPage: 1,
        pageSize: 10,
        total: 0,
        list: [],
        btnLoading: false,
        tableLoading: false,
        active: null,
        visible: false,
        batchId: null,
        modalData: null
    };

    componentDidMount() {
        const { threeValue } = this.props;
        if (threeValue) {
            this.getList(threeValue);
        }
    }

    componentDidUpdate(preProps) {
        const preValue = preProps.threeValue;
        const nextValue = this.props.threeValue;
        if (preValue !== nextValue && nextValue) {
            this.getList(nextValue);
        }
        const preId = preProps.batchId;
        const nextId = this.props.batchId;
        if (preId !== nextId && !nextId) {
            this.setState({ active: null });
        }
    }

    getList = (uuid) => {
        const { curPage, pageSize } = this.state;
        const { account } = this.props;

        const params = {
            uuid,
            curPage,
            pageSize,
            creator: account
        };
        this.setState({ tableLoading: true });
        pageQueryAPI
            .batchList(params)
            .then((res) => {
                this.setState({ tableLoading: false });
                if (res.success) {
                    if (!res.data && !res.data.comtents) return;
                    this.setState({
                        curPage: res.data.curPage,
                        pageSize: res.data.pageSize,
                        total: res.data.total,
                        list: res.data.contents ? res.data.contents : []
                    });
                } else {
                    message.error(res.msg);
                }
            })
            .catch(() => {
                this.setState({ tableLoading: false });
            });
    };

    downloadFile = () => {
        const { threeValue, threeServiceName } = this.props;
        const tempStr = I18N.inner.batchquery.piLiangChaXunMo;
        pageQueryAPI.exportTemplate({ uuid: threeValue }, `${threeServiceName}_${tempStr}`, 'xlsx'); // 批量查询模板
    };

    upLoadFile = (uuid, file) => {
        const formData = new FormData();
        formData.append('uuid', uuid);
        formData.append('file', file);
        this.setState({ btnLoading: true });
        fetch('/handleApi/invokeCommon/importInvoke', {
            method: 'POST',
            headers: getHeader(),
            body: formData
        })
            .then((res) => {
                return res.json();
            })
            .then((res) => {
                this.setState({ btnLoading: false });
                if (res && res.success) {
                    message.success(I18N.inner.batchquery.shangChuanChengGong); // 上传成功
                    this.getList(uuid);
                } else {
                    message.error(res.msg);
                }
            })
            .catch((err) => {
                this.setState({ btnLoading: false });
            });
    };

    changePage = async (curPage, pageSize) => {
        const { threeValue } = this.props;
        await this.setState({
            curPage,
            pageSize
        });
        this.getList(threeValue);
    };

    activeTr = (i) => {
        this.setState({ active: i });
    };

    // 执行查询
    handleInvoke(item) {
        pageQueryAPI.getBatchDetail({ uuid: item.uuid }).then((res) => {
            if (res.success) {
                if (!res.data) return;
                if (!res.data.searchedNum || res.data.searchedNum === 0) {
                    // 直接查询
                    this.submitInvoke(item.uuid, 1);
                } else {
                    // 已经存在查询结果时，“需要有二次确认框 ”
                    this.setState({
                        visible: true,
                        batchId: item.uuid,
                        modalData: res.data
                    });
                }
            } else {
                message.error(res.msg);
            }
        });
    }

    submitInvoke = (uuid, queryType) => {
        const params = {
            batchId: uuid,
            queryType // 1全量查询/2失败记录查询
        };
        this.setState({
            visible: false,
            batchId: null
        });
        pageQueryAPI.submitInvoke(params).then((res) => {
            if (res.success) {
                message.success(
                    <span style={{ textAlign: 'left', verticalAlign: 'top', display: 'inline-block' }}>
                        <span>
                            {/* 提交查询成功 */}
                            {I18N.inner.batchquery.tiJiaoChaXunCheng}
                        </span>
                        <br />
                        <span>
                            {/* 查询需要一些时间，请稍后再点击“预览数据”查询进度和结果 */}
                            {I18N.inner.batchquery.chaXunXuYaoYi}
                        </span>
                    </span>
                );
                this.props.viewData(uuid);
            } else {
                message.error(res.msg);
            }
        });
    };

    handleCancel = () => {
        this.setState({ visible: false });
    };

    render() {
        const { threeValue } = this.props;
        const { total, list, btnLoading, tableLoading, active, visible, batchId, modalData } = this.state;

        const uploadProps = {
            showUploadList: false,
            accept: '.xlsx,.xls',
            beforeUpload: (file) => {
                this.upLoadFile(threeValue, file);
                return false;
            }
        };

        return (
            <div className="body">
                {threeValue && (
                    <Fragment>
                        <Button icon="cloud-download" onClick={this.downloadFile}>
                            {/* 模板下载 */}
                            {I18N.inner.batchquery.muBanXiaZai}
                        </Button>
                        <Upload {...uploadProps}>
                            <Button style={{ marginLeft: '10px' }} icon="cloud-upload" loading={btnLoading}>
                                {/* 上传数据 */}
                                {I18N.inner.batchquery.shangChuanShuJu}
                            </Button>
                        </Upload>
                        <span className="u-tip">
                            <Icon type="exclamation-circle" />
                            {/* 最大支持1万条数据 */}
                            {I18N.inner.batchquery.zuiDaZhiChiWan}
                        </span>
                        {list && list.length > 0 && (
                            <Fragment>
                                <Spin spinning={tableLoading}>
                                    <div className="m-pagequery-table">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>
                                                        {/* 序号 */}
                                                        {I18N.inner.batchquery.xuHao}
                                                    </th>
                                                    <th>
                                                        {/* 上传时间 */}
                                                        {I18N.inner.batchquery.shangChuanShiJian}
                                                    </th>
                                                    <th>
                                                        {/* 上传人 */}
                                                        {I18N.inner.batchquery.shangChuanRen}
                                                    </th>
                                                    <th>
                                                        {/* 操作 */}
                                                        {I18N.inner.batchquery.caoZuo}
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {list.map((item, index) => {
                                                    return (
                                                        <tr
                                                            key={index}
                                                            onClick={() => this.activeTr(index)}
                                                            className={active === index ? 'active' : ''}>
                                                            <td>{item.orderNum}</td>
                                                            <td>{item.gmtCreate}</td>
                                                            <td>{item.creator}</td>
                                                            <td>
                                                                <span className="s1" onClick={() => this.props.viewData(item.uuid)}>
                                                                    {/* 预览数据 */}
                                                                    {I18N.inner.batchquery.yuLanShuJu}
                                                                </span>
                                                                <span className="s2" onClick={() => this.handleInvoke(item)}>
                                                                    {/* 执行查询 */}
                                                                    {I18N.inner.batchquery.zhiXingChaXun}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                </Spin>
                                <Pagination size="small" total={total} onChange={this.changePage} />
                            </Fragment>
                        )}
                    </Fragment>
                )}
                {list.length === 0 && threeValue && <NoData />}
                {!threeValue && <NoData content={I18N.inner.batchquery.qingXuanZeShuJu} />}
                <Modal
                    className="m-batch-query-modal"
                    visible={visible}
                    maskClosable={false}
                    footer={[
                        <Button key="back" onClick={this.handleCancel}>
                            {/* 取消 */}
                            {I18N.inner.batchquery.quXiao}
                        </Button>,
                        <Button key="submit" type="primary" onClick={() => this.submitInvoke(batchId, 2)}>
                            {/* 重查失败 */}
                            {I18N.inner.batchquery.zhongChaShiBai}
                        </Button>,
                        <Button key="submit" type="primary" onClick={() => this.submitInvoke(batchId, 1)}>
                            {/* 重查全部 */}
                            {I18N.inner.batchquery.zhongChaQuanBu}
                        </Button>
                    ]}>
                    {modalData && (
                        <div>
                            <Icon type="exclamation-circle" />
                            {/* 您已存在<span> 查询结果，重复查询会产生费用 </span> */}
                            {I18N.inner.batchquery.ninYiCunZai}
                            <span> {I18N.inner.batchquery.chaXunJieGuoZhong} </span>，
                            {I18N.template(I18N.inner.batchquery.dangQianJieGuoGong, {
                                val1: modalData.totalNum,
                                val2: modalData.successNum,
                                val3: modalData.failedNum
                            })}
                        </div>
                    )}
                </Modal>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(BatchQuery);
