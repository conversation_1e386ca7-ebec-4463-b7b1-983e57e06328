import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment } from 'react';
import { pageQueryAPI } from '@/services';
import { message, Button, Upload, Spin, Progress, Tooltip } from 'tntd';
import Pagination from '@/components/Pagination';
import { connect } from 'dva';
class BatchResult extends PureComponent {
    state = {};

    downloadFile = () => {
        const { batchId, threeServiceName } = this.props;
        const tempStr = I18N.inner.batchresult.chaXunJieGuo;
        pageQueryAPI.exportResult({ id: batchId }, `${threeServiceName}_${tempStr}`, 'xlsx'); // 查询结果
    };

    changePagination = (curPage, pageSize) => {
        this.props.onChange(curPage, pageSize);
    };

    render() {
        const { loading, pageSize, curPage, total, tableList, batchId, progressInfo } = this.props;

        return (
            <div className="body">
                <div>
                    {/* <Button
						icon="cloud-download"
						className="mb10"
						onClick={this.downloadFile}
					>
						{'下载结果'}
					</Button> */}
                    {progressInfo && progressInfo.percnet !== 100 && (
                        <Button className="mb10" onClick={() => this.props.onRefresh(batchId)}>
                            {/* 刷新 */}
                            {I18N.inner.batchresult.shuaXin}
                        </Button>
                    )}
                    {progressInfo && (
                        <Fragment>
                            <div className="u-data">
                                <p className="u-p1">
                                    {/* 已耗时: */}
                                    {I18N.inner.batchresult.yiHaoShi}:<span className="s3">{progressInfo.consuming}</span>
                                    <span className="mr15">
                                        {/* 结果生成时间: */}
                                        {I18N.inner.batchresult.jieGuoShengChengShi}:{progressInfo.endTime}
                                    </span>
                                    <span style={{ display: 'inline-block' }}>
                                        {/* 查询进度 */}
                                        {I18N.inner.batchresult.chaXunJinDu}
                                        <Progress percent={progressInfo.percnet} size="small" status="active" />
                                    </span>
                                </p>
                                <p className="u-p1">
                                    {/* 总条数 */}
                                    {I18N.inner.batchresult.zongTiaoShu}
                                    <span className="s1"> {progressInfo.totalNum} </span>，{/* 待查询 */}
                                    {I18N.inner.batchresult.daiZhaXun}
                                    <span className="s1"> {progressInfo.surplusNum} </span>
                                    {I18N.inner.batchresult.tiao /** 条 */}，{/* 查询完成 */}
                                    {I18N.inner.batchresult.chaXunWanCheng}
                                    <span className="s1"> {progressInfo.searchedNum} </span>
                                    {I18N.inner.batchresult.tiao /** 条 */}，{/* 成功 */}
                                    {I18N.inner.batchresult.chengGong}
                                    <span className="s1"> {progressInfo.successNum} </span>
                                    {I18N.inner.batchresult.tiao /** 条 */}，{/* 失败 */}
                                    {I18N.inner.batchresult.shiBai}
                                    <span className="s2"> {progressInfo.failedNum} </span>
                                    {I18N.inner.batchresult.tiao /** 条 */}
                                </p>
                            </div>
                            <Spin spinning={loading}>
                                <div className="m-result-table">
                                    <table style={{ width: `${progressInfo.columns.length * 120}px` }}>
                                        <thead>
                                            <tr>
                                                {progressInfo.columns &&
                                                    progressInfo.columns.map((item, index) => {
                                                        return (
                                                            <th
                                                                key={index}
                                                                className={item.type === 'input' ? 'th1' : 'th2'}
                                                                style={{ minWidth: '66px' }}>
                                                                {item.mustInput && <b>*</b>}
                                                                {item.displayName}
                                                            </th>
                                                        );
                                                    })}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {tableList &&
                                                tableList.map((item, index) => {
                                                    return (
                                                        <tr key={index}>
                                                            {progressInfo.columns &&
                                                                progressInfo.columns.map((sItem, sIndex) => {
                                                                    let type = 'th3';
                                                                    let name = '';
                                                                    if (sIndex === 0) {
                                                                        // 序号
                                                                        name = item.orderNum;
                                                                    } else if (sIndex === 1) {
                                                                        // 状态
                                                                        if (item.success === 1) {
                                                                            name = (
                                                                                <span style={{ color: '#00B794' }}>
                                                                                    {/* 成功 */}
                                                                                    {I18N.inner.batchresult.chengGong}
                                                                                </span>
                                                                            );
                                                                        } else if (item.success === 0) {
                                                                            name = (
                                                                                <span style={{ color: '#EC5046' }}>
                                                                                    {/* 失败 */}
                                                                                    {I18N.inner.batchresult.shiBai}
                                                                                </span>
                                                                            );
                                                                        } else {
                                                                            name = '--';
                                                                        }
                                                                    } else {
                                                                        let key =
                                                                            sItem.type === 'output' ? `output-${sItem.name}` : sItem.name;
                                                                        if (sItem.type === 'output') type = 'th4';
                                                                        let value = item[key];
                                                                        if (value && value.length < 20) {
                                                                            name = value;
                                                                        } else if (value) {
                                                                            name = (
                                                                                <Tooltip title={value}>{value.substr(0, 20)}...</Tooltip>
                                                                            );
                                                                        }
                                                                    }
                                                                    return (
                                                                        <td key={sIndex} className={type}>
                                                                            {name}
                                                                        </td>
                                                                    );
                                                                })}
                                                        </tr>
                                                    );
                                                })}
                                        </tbody>
                                    </table>
                                </div>
                            </Spin>
                            <div className="pagination-bg">
                                <Pagination
                                    total={total}
                                    pageSize={pageSize}
                                    curPage={curPage}
                                    onChange={this.changePagination}
                                    onShowSizeChange={(current, size) => this.changePagination(1, size)}
                                />
                            </div>
                        </Fragment>
                    )}
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(BatchResult);
