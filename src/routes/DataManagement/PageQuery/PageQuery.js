/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 页面查询
 */

import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import cloneDeep from 'lodash.clonedeep';
import { Button, Tooltip, Input, message, Spin, PageContainer, Row, Col, Select, Ellipsis } from 'tntd';
import { dataServiceListAPI, pageQueryAPI, systemFieldsAPI } from '@/services';
import { isJSON } from '@/utils/isJSON';
import NoData from '@/components/NoData';
import BatchQuery from './Inner/BatchQuery';
import BatchResult from './Inner/BatchResult';
import moment from 'moment';
import { checkFunctionHasPermission } from '@/utils/permission';

const Option = Select.Option;

class PageQuery extends PureComponent {
    state = {
        loading: false,
        inputConfig: [],
        threeServiceList: [],
        data: null,
        btnLoading: false,
        outputConfig: [], // 返回字段描述
        threeValue: null,
        threeServiceName: null,
        activeKey: 1, // 1单条查询，2批量查询
        curPage: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        tableLoading: false,
        batchId: null,
        progressInfo: null,
        systemList: []
    };

    componentDidMount() {
        this.getSystemListAll();
        this.getServiceList();
    }

    // 获取所有系统字段
    getSystemListAll = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res && res.success) {
                this.setState({
                    systemList: res.data ? res.data : []
                });
            }
        });
    };

    getServiceList = (type) => {
        dataServiceListAPI.getListAll2({ type, status: 1 }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res.data.contents ? res.data.contents : []
                });
            } else {
                message.error(res.msg || res?.message);
            }
        });
    };

    // 根据ID获取数据服务
    getDetail = (uuid) => {
        this.setState({ inputConfig: [], outputConfig: [] });
        dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success && res.data) {
                const inputConfig = res.data.inputConfig;
                const outputConfig = res.data.outputConfig;
                this.setState({
                    inputConfig: isJSON(inputConfig) ? JSON.parse(inputConfig) : [],
                    outputConfig: isJSON(outputConfig) ? JSON.parse(outputConfig) : []
                });
            } else {
                message.error(res.msg);
            }
        });
    };

    // 点击选项
    clickItem(uuid) {
        this.getDetail(uuid);
        const { threeServiceList } = this.state;
        const obj = threeServiceList.find((item) => item.uuid === uuid);
        this.setState({
            data: null,
            threeValue: uuid,
            threeServiceName: obj ? obj.displayName : null,
            curPage: 1,
            pageSize: 10,
            total: 0,
            tableList: [],
            tableLoading: false,
            batchId: null
        });
    }

    // 改变参数
    changeField(e, index) {
        const { inputConfig } = this.state;
        let copyInputConfig = cloneDeep(inputConfig);
        copyInputConfig[index].value = e.target.value;
        if (e.target.value) {
            copyInputConfig[index].errMsg = false;
        }
        this.setState({
            inputConfig: copyInputConfig
        });
    }

    // 查询
    search = () => {
        const { inputConfig, threeValue, threeServiceList } = this.state;
        let copyInputConfig = cloneDeep(inputConfig);
        let flag = false;
        copyInputConfig.forEach((item) => {
            if (item.mustInput && !item.value) {
                item.errMsg = true;
                flag = true;
            }
        });
        if (flag) {
            this.setState({
                inputConfig: copyInputConfig
            });
        } else {
            let serviceName = null;
            let arr = [];
            copyInputConfig.forEach((item) => {
                if (item.type !== 'constant' && item.value) {
                    arr.push({
                        paramName: item.field,
                        paramValue: item.value
                    });
                }
            });
            let params = {
                inputConfig: JSON.stringify(arr)
            };
            const obj = threeServiceList.find((item) => item.uuid === threeValue);
            if (obj) serviceName = obj.name;
            params.serviceName = serviceName;
            params.type = 1;
            this.getThreeServiceSearch(params);
        }
    };

    // 三方服务查询
    getThreeServiceSearch = (params) => {
        this.setState({ btnLoading: true });
        pageQueryAPI
            .getThreeServiceSearch(params, 'query')
            .then((res) => {
                const { outputConfig } = this.state;
                res.outputConfig = outputConfig;
                this.setState({ data: res, btnLoading: false });
            })
            .catch(() => {
                this.setState({ btnLoading: false });
            });
    };

    changeTab(key) {
        this.setState({ activeKey: key });
    }

    // 预览数据
    preViewData = async (uuid) => {
        const { curPage, pageSize } = this.state;
        await this.getProgress(uuid);
        const params = {
            batchId: uuid,
            curPage,
            pageSize
        };
        this.setState({ tableLoading: true, batchId: uuid });
        pageQueryAPI
            .batchDetails(params)
            .then((res) => {
                this.setState({ tableLoading: false });
                if (res.success) {
                    if (!res.data) return;
                    const data = res.data;
                    let contents = data.contents ? data.contents : [];
                    let tableList = [];

                    contents.forEach((item) => {
                        let obj = item.inputParam ? JSON.parse(item.inputParam) : {};
                        let outputObj = item.output ? item.output : {};
                        let out = {};
                        for (let key in outputObj) {
                            out[`output-${key}`] = outputObj[key];
                        }
                        Object.assign(obj, out);
                        obj.orderNum = item.orderNum;
                        obj.sequenceId = item.sequenceId;
                        obj.success = item.success;
                        tableList.push(obj);
                    });
                    this.setState({
                        curPage: data.curPage,
                        pageSize: data.pageSize,
                        total: data.total,
                        tableList
                    });
                } else {
                    message.error(res.msg);
                }
            })
            .catch(() => {
                this.setState({ tableLoading: false });
            });
    };

    // 查询进度信息
    getProgress = (uuid) => {
        pageQueryAPI.getBatchDetail({ uuid }).then(async (res) => {
            if (res.success) {
                if (!res.data) return;
                const { systemList } = this.state;
                let data = res.data;
                const currentDate = data.endTime ? data.endTime : moment().format('YYYY-MM-DD HH:mm:ss');
                const startDate = data.startTime;
                let durationDate = 0;
                if (startDate) {
                    durationDate = moment(currentDate).valueOf() - moment(startDate).valueOf();
                }
                let percnet = 0;
                if (data.totalNum && data.searchedNum) {
                    percnet = ((data.searchedNum / data.totalNum) * 100).toFixed(2);
                }
                data.totalNum = data.totalNum ? data.totalNum : 0;
                data.surplusNum = data.surplusNum ? data.surplusNum : 0;
                data.successNum = data.successNum ? data.successNum : 0;
                data.failedNum = data.failedNum ? data.failedNum : 0;
                data.searchedNum = data.searchedNum ? data.searchedNum : 0;
                data.endTime = currentDate;
                data.consuming = this.formatDuring(durationDate);
                data.percnet = percnet;

                // 获取表头
                let arry = [
                    { name: 'number', type: 'input', displayName: I18N.pagequery.xuHao }, // 序号
                    { name: 'success', type: 'input', displayName: I18N.pagequery.zhuangTai }, // 状态
                    { name: 'sequenceId', type: 'input', displayName: 'sequenceId' }
                ];
                const inputConfig = data.inputConfig ? JSON.parse(data.inputConfig) : [];
                const outputConfig = data.outputConfig ? JSON.parse(data.outputConfig) : [];
                inputConfig.forEach((item) => {
                    if (item.type === 'variable') {
                        const obj = systemList.find((k) => k.name === item.field);
                        arry.push({
                            name: item.field,
                            type: 'input',
                            mustInput: item.mustInput,
                            displayName: obj ? obj.displayName : null
                        });
                    }
                });

                outputConfig.forEach((item) => {
                    const obj = systemList.find((k) => k.name === item.field);
                    arry.push({
                        name: item.field,
                        type: 'output',
                        displayName: obj ? obj.displayName : null
                    });
                });
                data.columns = arry;

                await this.setState({
                    progressInfo: data
                });
            } else {
                message.error(res.msg);
            }
        });
    };

    changeBatchResult = async (curPage, pageSize) => {
        const { batchId } = this.state;
        await this.setState({
            curPage,
            pageSize
        });
        this.preViewData(batchId);
    };

    formatDuring = (mss) => {
        const days = parseInt(mss / (1000 * 60 * 60 * 24), 10);
        const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60), 10);
        const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60), 10);
        const seconds = (mss % (1000 * 60)) / 1000;
        let data;
        const secondsStr = I18N.pagequery.miao;
        const minutesStr = I18N.pagequery.fenZhong;
        const hoursStr = I18N.pagequery.xiaoShi;
        const daysStr = I18N.pagequery.tian;
        if (days === 0 && hours === 0 && minutes === 0) data = `${seconds}${secondsStr}`; // 秒
        if (days === 0 && hours === 0 && minutes !== 0) data = `${minutes}${minutesStr}${seconds}${secondsStr}`; // ${minutes}分钟${seconds}秒
        if (days === 0 && hours !== 0 && minutes !== 0) data = `${hours}${hoursStr}${minutes}${minutesStr}${seconds}${secondsStr}`;
        if (days !== 0 && hours !== 0 && minutes !== 0) {
            data = `${days}${daysStr}${hours}${hoursStr}${minutes}${minutesStr}${seconds}${secondsStr}`;
        }
        return data;
    };

    render() {
        const {
            loading,
            inputConfig,
            data,
            btnLoading,
            activeKey,
            threeValue,
            tableList,
            total,
            curPage,
            pageSize,
            tableLoading,
            batchId,
            progressInfo,
            threeServiceName,
            systemList,
            threeServiceList
        } = this.state;
        const { account } = this.props.globalStore.currentUser;
        const { personalMode, menuTreeReady } = this.props.globalStore;
        const { lang } = personalMode;

        return (
            <div className="p-page-query">
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2> */}
                {/* 三方手工调用 */}
                {/* {I18N.pagequery.shouGongChaXun}
                        </h2>
                    </div>
                </div> */}
                <div className="page-global-body">
                    <Select
                        showSearch
                        style={{ width: '380px', marginBottom: '15px' }}
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        placeholder={I18N.pagequery.qingXuanZeShuJu} // 请选择三方服务
                        optionFilterProp="children"
                        value={threeValue ? threeValue : undefined}
                        onChange={(e) => this.clickItem(e)}>
                        {threeServiceList &&
                            threeServiceList.map((item, index) => {
                                let dom = (
                                    <Option value={item.uuid} key={index}>
                                        {item.displayName}
                                    </Option>
                                );
                                return item.status === -1 ? null : dom;
                            })}
                    </Select>
                    <div className="m-result-query">
                        <div className="middle">
                            <div className="header">
                                <span className={activeKey === 1 ? 'active s1' : 's1'} onClick={() => this.changeTab(1)}>
                                    {/* 单条查询 */}
                                    {I18N.pagequery.danTiaoChaXun}
                                </span>
                                <span className={activeKey === 2 ? 'active s1' : 's1'} onClick={() => this.changeTab(2)}>
                                    {/* 批量查询 */}
                                    {I18N.pagequery.piLiangChaXun}
                                </span>
                            </div>
                            <div className="body" style={activeKey === 1 ? {} : { display: 'none' }}>
                                {menuTreeReady && checkFunctionHasPermission('TZ0504', 'singleQuery') && (
                                    <Fragment>
                                        {inputConfig.map((item, index) => {
                                            let dName = systemList.find((k) => k.name === item.field);
                                            let disName = dName ? dName.displayName : item.displayName;
                                            return (
                                                <div className="box" key={index}>
                                                    <span className="lable">
                                                        {item.mustInput && <b>*</b>}
                                                        <Ellipsis widthLimit={'100%'} title={disName} key={disName} />
                                                    </span>
                                                    <Input
                                                        placeholder={I18N.template(I18N.pagequery.qingShuRuDI, { val1: disName })} // 请输入
                                                        value={item.value}
                                                        className={item.errMsg ? 'red-border' : ''}
                                                        disabled={item.type === 'constant'}
                                                        onChange={(e) => this.changeField(e, index)}
                                                    />
                                                    {item.errMsg && (
                                                        <span className="tip">
                                                            {disName} &nbsp;
                                                            {/* 必填 */}
                                                            {I18N.pagequery.biTian}
                                                        </span>
                                                    )}
                                                </div>
                                            );
                                        })}
                                        {threeValue && (
                                            <Button type="primary" className="btn" onClick={this.search} loading={btnLoading}>
                                                {/* 查询 */}
                                                {I18N.pagequery.chaXun}
                                            </Button>
                                        )}
                                        {!threeValue && activeKey === 1 && <NoData content={I18N.pagequery.qingXuanZeShuJu} />}
                                    </Fragment>
                                )}
                                {menuTreeReady && !checkFunctionHasPermission('TZ0504', 'singleQuery') && activeKey === 1 && (
                                    <NoData content={I18N.pagequery.zanWuQuanXian} type="no-permission" />
                                )}
                            </div>
                            <div style={activeKey === 2 ? {} : { display: 'none' }}>
                                {menuTreeReady && checkFunctionHasPermission('TZ0504', 'batchQuery') && (
                                    <BatchQuery
                                        account={account}
                                        batchId={batchId}
                                        threeValue={threeValue}
                                        viewData={this.preViewData}
                                        threeServiceName={threeServiceName}
                                    />
                                )}
                                {menuTreeReady && !checkFunctionHasPermission('TZ0504', 'batchQuery') && activeKey === 2 && (
                                    <NoData content={I18N.pagequery.zanWuQuanXian} type="no-permission" />
                                )}
                            </div>
                        </div>
                        <div className="right">
                            <div className="header">
                                {/* 查询结果 */}
                                {I18N.pagequery.chaXunJieGuo}
                            </div>
                            <div className="body" style={activeKey === 1 ? {} : { display: 'none' }}>
                                {data && (
                                    <Fragment>
                                        <div className="result">
                                            <div className="result-header">{I18N.pagequery.jieGuoXiangQing}</div>
                                            <div className="result-body">
                                                {I18N.pagequery.chaXunJieGuo}
                                                {data.success
                                                    ? I18N.pagequery.chengGong
                                                    : I18N.template(I18N.pagequery.shiBaiDAT, { val1: data.msg })}
                                            </div>
                                        </div>
                                        <div className="result">
                                            <div className="result-header">
                                                {/* 返回报文 */}
                                                {I18N.pagequery.fanHuiBaoWen}
                                            </div>
                                            <div className="result-body">
                                                <pre>{data.data ? JSON.stringify(data.data, null, 4) : []}</pre>
                                            </div>
                                        </div>
                                        <div className="result">
                                            <div className="result-header">
                                                {/* 返回字段描述 */}
                                                {I18N.pagequery.fanHuiZiDuanMiao}
                                            </div>
                                            <div className="result-body">
                                                {data.outputConfig &&
                                                    data.outputConfig.length > 0 &&
                                                    data.outputConfig.map((item, index) => {
                                                        return (
                                                            <Row key={index}>
                                                                <Col span={12}>{item.field}</Col>
                                                                <Col span={12}>{item.displayName}</Col>
                                                            </Row>
                                                        );
                                                    })}
                                            </div>
                                        </div>
                                    </Fragment>
                                )}
                                {!data && <NoData />}
                            </div>
                            <div style={activeKey === 2 ? {} : { display: 'none' }}>
                                {tableList.length > 0 && (
                                    <BatchResult
                                        progressInfo={progressInfo}
                                        batchId={batchId}
                                        loading={tableLoading}
                                        pageSize={pageSize}
                                        curPage={curPage}
                                        total={total}
                                        tableList={tableList}
                                        onChange={this.changeBatchResult}
                                        onRefresh={this.preViewData}
                                        threeServiceName={threeServiceName}
                                    />
                                )}
                                {tableList.length === 0 && (
                                    <div className="body">
                                        <NoData />
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    pageQueryStore: state.pageQuery
}))(PageContainer(PageQuery));
