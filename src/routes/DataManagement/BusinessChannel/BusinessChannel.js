/*
 * @Author: l<PERSON><PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 业务渠道查询明细
 */

import I18N from '@/utils/I18N';
import React, { PureComponent, Suspense } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { TableContainer, Table, Pagination, DatePicker, Select, Tooltip, message, Icon, Input, Modal, Ellipsis } from 'tntd';
import { parse } from 'query-string';
import { isJSON } from '@/utils/isJSON';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { businessChannelAPI } from '@/services';
import { DateRanges } from '@/constants';
import SearchDrawer from './SearchDrawer';

const { RangePicker } = DatePicker;
const Option = Select.Option;
const TextArea = Input.TextArea;
import './index.less';
class BusinessChannel extends PureComponent {
    state = {
        threeServiceList: [], // 所有三方数据
        allServiceList: []
    };

    constructor(props) {
        super(props);
        // 外部跳转带参数进入
        const { dispatch, location } = props;
        let { state, search } = location;

        // url 传入状态
        if (search) {
            const parsedSearch = parse(search);

            if (parsedSearch.state) {
                try {
                    state = JSON.parse(parsedSearch.state);
                } catch (error) {
                    console.error(error);
                }
            }
        }

        if (state) {
            let obj = {};
            if (state.serviceCode) obj['serviceCode'] = state.serviceCode; // 三方服务接口名称
            if (state.channelCode) obj['channelCode'] = state.channelCode; // 业务系统
            if (state.organizeCode) obj['organizeCode'] = state.organizeCode; // 机构
            if (state.date) {
                obj['date'] = state.date; // 时间戳
                const st = state.date[0];
                const et = state.date[1];
                if ((et - st) / (1000 * 60 * 60 * 24) > 90) {
                    // 系统一次最多支持查询90天，已自动帮您截取所选范围的近90天
                    message.warning(I18N.businesschannel.xiTongYiCiZui2);
                    obj['date'] = [moment(et).add(-89, 'days').valueOf(), moment(et).valueOf()];
                }
            }
            if (state.returnResult) obj['returnResult'] = state.returnResult; // 返回结果
            if (state.searchResult) obj['searchResult'] = state.searchResult; // 查询结果
            if (state.sequenceId) obj['sequenceId'] = state.sequenceId; // 流水号
            if (state.searchWhere) obj['searchWhere'] = state.searchWhere; // 入参系统字段名称
            if (state.searchValue) obj['searchValue'] = state.searchValue; // 入参系统字段的值
            if (state.errorCode) obj['errorCode'] = state.errorCode; // 错误码
            if (state.invokeSource) obj['invokeSource'] = state.invokeSource; // 数据来源
            if (state.cacheFlag) obj['cacheFlag'] = state.cacheFlag; // 是否缓存
            dispatch({
                type: 'businessChannel/setAttrValue',
                payload: {
                    searchParams: {
                        ...obj
                    }
                }
            });
        }
        setTimeout(() => {
            dispatch({
                type: 'global/getAllProvider',
                payload: {}
            });
        }, 200);
    }

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0502', 'query')) {
                    this.search();
                    this.getListAll();
                }
            }
        }, 100);
    }

    componentDidUpdate(preProps) {
        const appName = preProps.globalStore.currentApp.name;
        const nextAppName = this.props.globalStore.currentApp.name;
        if (appName !== nextAppName) {
            this.search();
        }
    }

    componentWillUnmount() {
        this.clearSearchParam();
    }

    // 获取所有调用方服务
    getListAll = () => {
        businessChannelAPI.getListAll().then((res) => {
            if (!res) return;
            if (res && res.success) {
                let data = res.data ? res.data : [];
                // 数组去重
                let obj = {};
                data = data.reduce((prev, cur) => {
                    obj[cur.name] ? '' : (obj[cur.name] = true && prev.push(cur));
                    return prev;
                }, []);
                this.setState({
                    allServiceList: data
                });
            }
        });
    };

    showModal(record) {
        businessChannelAPI.getDetail({ sequenceId: record.sequenceId, recordTime: record.recordTime }).then((res) => {
            if (res && res.success) {
                var inputParams = '';
                var outputParams = '';
                if (res.data.inputParams) {
                    inputParams = (' ' + res.data.inputParams).slice(1);
                    if (isJSON(inputParams)) {
                        inputParams = JSON.parse(inputParams);
                        inputParams = JSON.stringify(inputParams, null, 2);
                    }
                }

                if (res.data.outputParams) {
                    outputParams = (' ' + res.data.outputParams).slice(1);
                    if (isJSON(outputParams)) {
                        outputParams = JSON.parse(outputParams);
                        outputParams = JSON.stringify(outputParams, null, 2);
                    }
                }

                Modal.info({
                    title: I18N.businesschannel.mingXiXinXi,
                    content: (
                        <div className="modal-detail">
                            <div className="modal-textarea-top modal-textarea">
                                <span>{I18N.businesschannel.ruCan}</span>
                                <TextArea value={inputParams} disabled={true} />
                            </div>
                            <div className="modal-textarea">
                                <span>{I18N.businesschannel.chuCan}</span>
                                <TextArea value={outputParams} disabled={true} />
                            </div>
                        </div>
                    ),
                    width: 500,
                    okText: I18N.businesschannel.guanBi,
                    onOk() {}
                });
            }
        });
    }
    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, businessChannelStore } = this.props;
        const { searchParams } = businessChannelStore;
        dispatch({
            type: 'businessChannel/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            const st = e[0].valueOf();
            const et = e[1].valueOf();
            const rangeDay = (et - st) / 1000 / 60 / 60 / 24;
            if (rangeDay > 89) return message.warning(I18N.businesschannel.xiTongYiCiZui); // 系统一次最多支持查询90天
            val = [e[0].valueOf(), e[1].valueOf()];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        obj[field] = val;
        dispatch({
            type: 'businessChannel/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });

        this.search();
    }

    clearSearchParam = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'businessChannel/setAttrValue',
            payload: {
                searchParams: {
                    curPage: 1,
                    pageSize: 10,
                    date: [moment().valueOf(), moment().valueOf()],
                    serviceCode: null, // 三方服务接口名称
                    returnResult: null, // 返回结果
                    searchResult: null, // 查询结果
                    channelCode: null, // 业务系统
                    organizeCode: null, // 机构
                    sequenceId: null, // 流水号
                    searchWhere: null, // 入参系统字段名称
                    searchValue: null, // 入参系统字段的值
                    errorCode: null, // 错误码
                    invokeSource: null, // 数据来源
                    cacheFlag: null, // 是否缓存
                    channelServiceCode: null, // 服务名称
                    channelSequenceId: null // 业务唯一流水号
                }
            }
        });
    };

    render() {
        const { allServiceList } = this.state;
        const { businessChannelStore, dispatch, globalStore } = this.props;
        const { allMap, threeServiceList, menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading, dialogShow } = businessChannelStore;
        const { date, organizeCode, channelServiceCode } = searchParams;

        const columns = [
            {
                title: I18N.businesschannel.diaoYongWeiYiBiao, // 调用唯一标识码
                dataIndex: 'sequenceId',
                key: 'sequenceId',
                width: 200,
                render: (text) => {
                    return <Ellipsis title={text || '--'} />;
                }
            },
            {
                title: I18N.businesschannel.fuWuMingCheng, // 服务名称
                dataIndex: 'channelServiceDisplayName',
                width: 120,
                key: 'channelServiceDisplayName',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.businesschannel.quDao,
                dataIndex: 'appDisplayName',
                width: 120,
                key: 'appDisplayName',
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.businesschannel.jiGou, // 机构
                dataIndex: 'organizeName',
                key: 'organizeName',
                width: 120,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.businesschannel.shuJuYuanFuWu, // 三方服务接口名称
                dataIndex: 'serviceName',
                key: 'serviceName',
                width: 180,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.businesschannel.shuJuLeiXingLai, // 数据来源类型
                dataIndex: 'invokeSource',
                width: 160,
                key: 'invokeSource'
            },
            {
                title: I18N.businesschannel.shuJuLeiXing, // 数据类型
                dataIndex: 'serviceTypeName',
                width: 120,
                key: 'serviceTypeName'
            },
            // {
            // 	title: "入参",
            // 	dataIndex: "inputParams",
            // 	key: "inputParams",
            // 	render: (text) => {
            // 		let dom = (
            // 			text
            // 				? <MessageBox content={text} placement="left">
            // 					<span className="u-ellipsis">{text}</span>
            // 				</MessageBox>
            // 				: "--"
            // 		);
            // 		return dom;
            // 	}
            // },
            // {
            // 	title: "出参",
            // 	dataIndex: "outputParams",
            // 	key: "outputParams",
            // 	render: (text) => {
            // 		let dom = (
            // 			text
            // 				? <MessageBox content={text} placement="left">
            // 					<span className="u-ellipsis">{text}</span>
            // 				</MessageBox>
            // 				: "--"
            // 		);
            // 		return dom;
            // 	}
            // },
            {
                title: I18N.businesschannel.shiFouBenDi, // 是否缓存
                dataIndex: 'cachedName',
                key: 'cachedName',
                width: 120
            },
            {
                title: I18N.businesschannel.fanHuiJieGuo, // 返回结果
                dataIndex: 'responseResult',
                key: 'responseResult',
                width: 120,
                render: (text, record) => {
                    let dom = (
                        <Tooltip title={record.msg}>
                            <a>{text}</a>
                        </Tooltip>
                    );
                    return text !== I18N.businesschannel.shiBai && text !== 'fail' ? text : dom;
                }
            },
            {
                title: I18N.businesschannel.chaXunJieGuo, // 查询结果
                dataIndex: 'searchResult',
                key: 'searchResult',
                width: 120
            },
            {
                title: I18N.businesschannel.qingQiuShiJian, // 请求时间
                dataIndex: 'requestTime',
                key: 'requestTime',
                width: 185,
                render: (text) => {
                    let dom = (
                        <div>
                            <div>{text ? text : '--'}</div>
                        </div>
                    );
                    return dom;
                }
            },
            {
                title: I18N.businesschannel.haoShiMS, // 耗时(ms)
                dataIndex: 'cost',
                width: 100,
                key: 'cost'
            },
            {
                title: I18N.businesschannel.jieGuoLeiXing, // 结果类型
                dataIndex: 'responseType',
                key: 'responseType',
                width: 100,
                render: (text) => {
                    const map = {
                        1: I18N.businesschannel.yuanShiBaoWen, // 原始报文
                        2: I18N.businesschannel.jieXiBaoWen // 解析报文
                    };
                    return map[text];
                }
            },
            {
                title: I18N.businesschannel.caoZuo, // 耗时(ms)
                dataIndex: 'action',
                key: 'action',
                fixed: 'right',
                width: 120,
                render: (text, record) => {
                    return <a onClick={() => this.showModal(record)}>{I18N.businesschannel.chaKanMingXi}</a>;
                }
            }
        ];

        let innerCount = 0;
        let list = [
            'returnResult',
            'searchResult',
            'serviceCode',
            'sequenceId',
            'searchWhere',
            'searchValue',
            'errorCode',
            'invokeSource',
            'cacheFlag',
            'channelSequenceId'
        ];
        list.forEach((item) => {
            if (searchParams[item]) innerCount += 1;
        });

        return (
            <div>
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2 style={{ marginRight: '0' }}> */}
                {/* 渠道查询明细 */}
                {/* {I18N.businesschannel.diaoYongFangDiaoYong}
                        </h2>
                    </div>
                </div> */}
                {menuTreeReady && checkFunctionHasPermission('TZ0502', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <RangePicker
                                    className="middle-calendar-picker"
                                    allowClear={false}
                                    value={searchParams.date ? [moment(searchParams.date[0]), moment(searchParams.date[1])] : null}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'date')}
                                    ranges={DateRanges}
                                />
                                {/* 系统一次最多支持查询90天 */}
                                <Tooltip title={I18N.businesschannel.xiTongYiCiZui}>
                                    <Icon type="question-circle" style={{ fontSize: '16px', marginLeft: '5px' }} />
                                </Tooltip>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '190px' }}
                                    placeholder={I18N.businesschannel.qingShuRuDiaoYong} // 请输入调用方服务名称
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={channelServiceCode ? channelServiceCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'channelServiceCode')}>
                                    {allServiceList &&
                                        allServiceList.map((item, index) => {
                                            return (
                                                <Option value={item.name} key={index}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    placeholder={I18N.businesschannel.jiGou} // 业务机构
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={organizeCode ? organizeCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'organizeCode')}>
                                    {allMap &&
                                        allMap.organizeList &&
                                        allMap.organizeList.map((item, index) => {
                                            return (
                                                <Option value={item.code} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="item">
                                <SearchDrawer
                                    innerCount={innerCount}
                                    onClear={() => this.clearSearchParam()}
                                    onSearch={() => this.search()}
                                />
                            </div>
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.sequenceId}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 1900 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.businesschannel.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0502', 'query') && <NoPermission />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    businessChannelStore: state.businessChannel
}))(TableContainer(BusinessChannel));
