import I18N from '@/utils/I18N';
import './index.less';
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Button, Select, Input, Tooltip, Badge, Drawer } from 'tntd';
import { systemFieldsAPI } from '@/services';
import TooltipSelect from '@tntd/tooltip-select';

const { Option } = Select;

class SearchDrawer extends PureComponent {
    state = {
        systemList: []
    };

    componentDidMount() {
        document.body.addEventListener('click', this.listener);
        this.getSystemListAll();
    }

    getSystemListAll = () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res && res.success) {
                this.setState({
                    systemList: res.data ? res.data : []
                });
            }
        });
    };

    componentWillUnmount() {
        document.body.removeEventListener('click', this.listener);
    }

    listener = (e) => {
        const { dialogShow } = this.props.businessChannelStore;
        const clientWidth = document.body.clientWidth;
        if (e.clientY <= 65 || e.clientX <= clientWidth - 300) {
            if (dialogShow && dialogShow.searchDrawer) {
                this.handleClose();
            }
        }
    };

    handleClose = () => {
        const { dispatch } = this.props;

        dispatch({
            type: 'businessChannel/setAttrValue',
            payload: {
                dialogShow: {
                    searchDrawer: false
                }
            }
        });
    };

    async changeField(e, field) {
        const { dispatch, businessChannelStore } = this.props;
        let { searchParams } = businessChannelStore;

        if (field === 'returnResult') {
            if (e === 'success') {
                const { allMap } = this.props.globalStore;
                let errorCodeList = [];
                if (allMap && allMap.responseResult) {
                    const obj = allMap.responseResult.find((item) => item.code === e);
                    if (obj) errorCodeList = obj.reasonCode;
                }
                searchParams.errorCode = errorCodeList[0].code;
            } else {
                searchParams.errorCode = null;
            }
        }
        searchParams[field] = e || e === 0 ? e : null;

        await dispatch({
            type: 'businessChannel/setAttrValue',
            payload: {
                searchParams: {
                    ...searchParams
                }
            }
        });
    }

    render() {
        const { systemList } = this.state;
        const { innerCount, dispatch, businessChannelStore, globalStore } = this.props;
        const { allMap, threeServiceList } = globalStore;
        const { dialogShow, searchParams } = businessChannelStore;
        const {
            returnResult,
            searchResult,
            errorCode,
            sequenceId,
            searchWhere,
            searchValue,
            channelCode,
            channelSequenceId,
            serviceCode,
            cacheFlag,
            invokeSource
        } = searchParams;

        let returnResultList = [];
        allMap &&
            allMap.responseResult &&
            allMap.responseResult.forEach((item) => {
                returnResultList.push({
                    name: item.code,
                    dName: item.name
                });
            });

        let searchResultList = [];
        allMap &&
            allMap.searchResult &&
            allMap.searchResult.forEach((item) => {
                searchResultList.push({
                    name: item.code,
                    dName: item.name
                });
            });

        let channelCodeList = [];
        allMap &&
            allMap.channelList &&
            allMap.channelList.forEach((item) => {
                channelCodeList.push({
                    name: item.name,
                    dName: item.displayName
                });
            });

        let serviceCodeList = [];
        threeServiceList.forEach((item) => {
            serviceCodeList.push({
                name: item.name,
                dName: item.displayName
            });
        });

        let invokeSourcelist = [];
        allMap &&
            allMap.invokeSourcelist &&
            allMap.invokeSourcelist.forEach((item) => {
                invokeSourcelist.push({
                    name: item.type,
                    dName: item.name
                });
            });

        let errorCodeList = [];
        if (returnResult && allMap && allMap.responseResult) {
            const obj = allMap.responseResult.find((item) => item.code === returnResult);
            if (obj) errorCodeList = obj.reasonCode;
        }

        return (
            <Fragment>
                <Badge count={innerCount !== 0 ? innerCount : null}>
                    <Button
                        icon="filter"
                        onClick={() => {
                            dispatch({
                                type: 'businessChannel/setAttrValue',
                                payload: {
                                    dialogShow: {
                                        searchDrawer: true
                                    }
                                }
                            });
                        }}>
                        {/* 更多过滤 */}
                        {I18N.searchdrawer.index.gengDuoGuoLu}
                    </Button>
                </Badge>
                <Drawer
                    title={I18N.searchdrawer.index.gengDuoShaiXuan} // 更多筛选
                    width={300}
                    className="page-search-drawer-wrap"
                    placement="right"
                    closable={true}
                    onClose={this.handleClose}
                    visible={dialogShow.searchDrawer}
                    mask={false}>
                    <div className="search-drawer-detail">
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 三方服务接口名称 */}
                                {I18N.searchdrawer.index.shuJuYuanFuWu}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeShuJu} // 请选择三方服务接口名称
                                value={serviceCode || undefined}
                                onChange={(e) => this.changeField(e, 'serviceCode')}>
                                {serviceCodeList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 返回结果 */}
                                {I18N.searchdrawer.index.fanHuiJieGuo}
                            </div>
                            <Select
                                allowClear
                                style={{ marginBottom: '8px' }}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeFanHui} // 请选择返回结果
                                value={returnResult || undefined}
                                onChange={(e) => this.changeField(e, 'returnResult')}>
                                {returnResultList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                            <Select
                                allowClear
                                disabled={returnResult === 'success'}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeZhuangTai} // 请选择状态码
                                value={errorCode || undefined}
                                onChange={(e) => this.changeField(e, 'errorCode')}>
                                {errorCodeList.map((item, index) => {
                                    return (
                                        <Option value={item.code} key={index}>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 查询结果 */}
                                {I18N.searchdrawer.index.chaXunJieGuo}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeChaXun} // 请选择查询结果
                                value={searchResult || undefined}
                                onChange={(e) => this.changeField(e, 'searchResult')}>
                                {searchResultList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        <div className="filter-item">
                            <div className="label-text">{I18N.searchdrawer.index.yeWuWeiYiLiu}</div>
                            <Input
                                placeholder={I18N.searchdrawer.index.shuRuYeWuWei}
                                value={channelSequenceId || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'channelSequenceId')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 调用唯一标识码 */}
                                {I18N.searchdrawer.index.diaoYongWeiYiBiao}
                            </div>
                            <Input
                                placeholder={I18N.searchdrawer.index.shuRuDiaoYongWei} // 输入调用唯一标识码（sequenceID）
                                value={sequenceId || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'sequenceId')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 入参字段 */}
                                {I18N.searchdrawer.index.ruCanZiDuan}
                            </div>
                            <TooltipSelect
                                isVirtual
                                allowClear
                                style={{ marginBottom: '8px' }}
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZeRuCan} // 请选择入参字段
                                value={searchWhere || undefined}
                                onChange={(e) => this.changeField(e, 'searchWhere')}>
                                {systemList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.displayName}
                                        </Option>
                                    );
                                })}
                            </TooltipSelect>
                            <Input
                                placeholder={I18N.searchdrawer.index.shuRuZhiQianZhui} // 输入值（前缀匹配）
                                value={searchValue || undefined}
                                onChange={(e) => this.changeField(e.target.value, 'searchValue')}
                            />
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 是否缓存 */}
                                {I18N.searchdrawer.index.shiFouBenDi}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZe} // 请选择
                                value={cacheFlag || undefined}
                                onChange={(e) => this.changeField(e, 'cacheFlag')}>
                                <Option value={'true'}>{I18N.searchdrawer.index.shi}</Option>
                                <Option value={'false'}>{I18N.searchdrawer.index.fou}</Option>
                            </Select>
                        </div>
                        <div className="filter-item">
                            <div className="label-text">
                                {/* 数据类型来源 */}
                                {I18N.searchdrawer.index.shuJuLeiXingLai}
                            </div>
                            <Select
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                placeholder={I18N.searchdrawer.index.qingXuanZe} // 请选择
                                value={invokeSource || undefined}
                                onChange={(e) => this.changeField(e, 'invokeSource')}>
                                {invokeSourcelist.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index}>
                                            {item.dName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                        <div className="filter-item" style={{ marginBottom: '10px' }}>
                            <Button type="primary" onClick={this.props.onSearch} block>
                                {I18N.searchdrawer.index.chaXun}
                            </Button>
                        </div>
                        <div className="filter-item">
                            <Button onClick={this.props.onClear} block>
                                {I18N.searchdrawer.index.qingKong}
                            </Button>
                        </div>
                    </div>
                </Drawer>
            </Fragment>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    businessChannelStore: state.businessChannel
}))(SearchDrawer);
