import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment, Suspense } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Table, Pagination, Select, DatePicker, message, Tooltip, Icon, Button, Ellipsis, TableContainer, HandleIcon } from 'tntd';
import { billingDetailAPI, contractListAPI } from '@/services';
import { formatStartTime, formatEndTime, formatMoney } from '@/utils/utils';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import LadderPriceTip from '@/routes/AccountManagement/Components/LadderPriceTip';
import AllflowTip from '@/routes/AccountManagement/Components/AllflowTip';
import { DateRanges } from '@/constants';

const { RangePicker } = DatePicker;
const Option = Select.Option;
const ListModal = React.lazy(() => import('./Inner/ListModal'));

class ContractBillingDetail extends PureComponent {
    state = {
        exportLoading: false,
        contractList: [] // 合同列表
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0302', 'query')) {
                    this.search();
                }
            }
        }, 100);
    }

    componentDidUpdate(preProps) {
        const appName = preProps.globalStore.currentApp.name;
        const nextAppName = this.props.globalStore.currentApp.name;
        if (appName !== nextAppName) {
            this.search();
        }
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'billingDetail/reset'
        });
    }

    getContractList = (e) => {
        contractListAPI.getListAll({ providerUuid: e }).then((res) => {
            this.setState({
                contractList: res?.data || []
            });
        });
    };

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, billingDetailStore } = this.props;
        const { searchParams } = billingDetailStore;
        dispatch({
            type: 'billingDetail/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            const st = e[0].valueOf();
            const et = e[1].valueOf();
            const rangeDay = (et - st) / 1000 / 60 / 60 / 24;
            if (rangeDay > 365) return message.warning(I18N.contractbillingdetail.xiTongYiCiZui); // 系统一次最多支持查询一年
            val = [st, et];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        obj[field] = val;
        if (field === 'providerId') {
            this.getContractList(val);
            obj['contractCode'] = null;
        }
        dispatch({
            type: 'billingDetail/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });

        this.search();
    }

    // 导出
    export = () => {
        const { contractCode, date, providerId } = this.props.billingDetailStore.searchParams;
        const { currentApp } = this.props.globalStore;
        const params = {
            providerId,
            contractCode,
            startTime: date ? formatStartTime(date[0]) : null,
            endTime: date ? formatEndTime(date[1]) : null,
            appNames: currentApp?.name
        };
        this.setState({ exportLoading: true });
        // "外部计费报表"
        billingDetailAPI
            .exportExternal(params, I18N.contractbillingdetail.heTongJiFeiBao, 'csv')
            .then(() => {
                this.setState({ exportLoading: false });
            })
            .catch(() => {
                this.setState({ exportLoading: false });
            });
    };

    // 查看
    async lookDetail(record) {
        const { dispatch, billingDetailStore } = this.props;
        const { date } = billingDetailStore.searchParams;
        await dispatch({
            type: 'billingDetail/setAttrValue',
            payload: {
                dialogShow: {
                    lookModal: true
                },
                lookId: record.id,
                modalSearchParams: {
                    date,
                    contractCode: record.contractCode,
                    providerId: record.supplierCode,
                    contractId: record.contractId,
                    contractVersion: record.contractVersion
                }
            }
        });
    }

    getColumns = () => {
        const tipComtent = (
            <Fragment>
                <div>{I18N.contractbillingdetail.canYuJiFeiLiu2}</div>
                <div>{I18N.contractbillingdetail.chaDeLiuLiangShu}</div>
                <div>{I18N.contractbillingdetail.chaXunLiuLiangShu}</div>
            </Fragment>
        );

        const tipComtent2 = (
            <Fragment>
                <div>
                    {/* 按照计费方式进行估算，估算方法： */}
                    {I18N.contractbillingdetail.anZhaoJiFeiFang}
                </div>
                <div>
                    {/* 1.按次计费：估算总价= 单价 * 参与计费流量 */}
                    {I18N.contractbillingdetail.anCiJiFeiGu}
                </div>
                <div>
                    {/* 2.月包、年包、季包：估算总价=每天平均价格* 天数，月包 按30天计算，季包 按90天计算，年包按365天计算 */}
                    {I18N.contractbillingdetail.yueBaoNianBaoJi}
                </div>
                <div>
                    {/* 3.阶梯计费：估算总价=阶梯区间的单价* 参与计费流量 */}
                    {I18N.contractbillingdetail.jieTiJiFeiGu}
                </div>
            </Fragment>
        );

        const columns = [
            {
                title: I18N.contractbillingdetail.heZuoFangMingCheng, // 合作方名称
                dataIndex: 'supplierName',
                key: 'supplierName',
                width: 140,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                }
            },
            {
                title: I18N.contractbillingdetail.heTongMingCheng,
                dataIndex: 'contractName',
                width: 120,
                render: (text) => <Ellipsis title={text || '- -'} widthLimit={100} lines={2} />
            },
            {
                title: I18N.contractbillingdetail.heTongBianHao,
                width: 160,
                dataIndex: 'contractCode',
                render: (text) => <Ellipsis title={text || '- -'} widthLimit={100} lines={2} />
            },
            {
                title: I18N.contractbillingdetail.heTongBanBen,
                dataIndex: 'contractVersion',
                width: 140,
                render: (text) => 'V' + Number(text)
            },
            {
                title: I18N.contractbillingdetail.caiGouKaiShiRi, // 采购开始日期
                dataIndex: 'purchaseStartDate',
                key: 'purchaseStartDate',
                width: 190
            },
            {
                title: I18N.contractbillingdetail.caiGouJieShuRi, // 采购结束日期
                dataIndex: 'purchaseEndDate',
                key: 'purchaseEndDate',
                width: 190
            },
            {
                title: I18N.contractbillingdetail.jiFeiFangShi, // 计费方式
                dataIndex: 'chargeTypeName',
                key: 'chargeTypeName',
                width: 260,
                render: (text, record) => {
                    return <LadderPriceTip data={record.chargeConfig} name={text} chargeType={record.chargeType} />;
                }
            },
            {
                title: (
                    <Fragment>
                        {I18N.contractbillingdetail.pingJunDanJia}
                        <Tooltip title={I18N.contractbillingdetail.guSuanZongJiaChan}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                width: 120,
                dataIndex: 'avgUnitPrice',
                render: (text) => {
                    return text || text === 0 ? text : '--';
                }
            },
            {
                title: (
                    <Fragment>
                        {I18N.contractbillingdetail.pingJunJiFeiDan}
                        {/* 估算总价/参与计费流量 */}
                        <Tooltip title={I18N.contractbillingdetail.guSuanZongJiaCan}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'avgPrice',
                key: 'avgPrice',
                width: 200,
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '--';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 估算总价 */}
                        {I18N.contractbillingdetail.guSuanZongJia}
                        <Tooltip title={tipComtent2} placement="leftTop">
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                width: 120,
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '--';
                }
            },
            {
                title: I18N.contractbillingdetail.chanShengZongLiuLiang, // 产生总流量,
                dataIndex: 'totalFlow',
                key: 'totalFlow',
                width: 120,
                render: (text, record) => {
                    return <AllflowTip data={record} type="external" />;
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 成功流量 */}
                        {I18N.contractbillingdetail.chengGongLiuLiang}
                        {/* 调用成功的流量数量，参与流量上限的统计和预警 */}
                        <Tooltip title={I18N.contractbillingdetail.diaoYongChengGongDe}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                width: 120,
                dataIndex: 'successFlow',
                key: 'successFlow'
            },
            {
                title: (
                    <Fragment>
                        {/* 参与计费流量 */}
                        {I18N.contractbillingdetail.canYuJiFeiLiu}
                        <Tooltip title={tipComtent}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'chargeFlow',
                key: 'chargeFlow',
                render: (text) => {
                    return text || text === 0 ? text : '--';
                }
            },
            // {
            // 	title: (
            // 		<Fragment>
            // 			{/* 剩余流量 */}
            // 			{'剩余流量'}
            // 			{/* 假设三方服务接口设置总流量上限为N，剩余流量=N-成功流量；若没有设置，则显示“-—” */}
            // 			<Tooltip title={'假设数据源服务接口设置总流量上限为N，剩余流量=N-成功流量；若没有设置，则显示“-—”'}>
            // 				<Icon type="question-circle" className="ml5" />
            // 			</Tooltip>
            // 		</Fragment>
            // 	),
            // 	dataIndex: "remainingCount",
            // 	key: "remainingCount",
            // 	render: (text) => {
            // 		return (text || text === 0) ? text : "--";
            // 	}
            // },
            {
                title: I18N.contractbillingdetail.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 90,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = checkFunctionHasPermission('TZ0302', 'look') && (
                        <HandleIcon>
                            <HandleIcon.Item title={I18N.contractbillingdetail.chaKan}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        this.lookDetail(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];
        return columns;
    };

    disabledDate = (current) => {
        // Can not select days before today and today
        return (current && current > moment().endOf('day')) || current <= moment().add(-365, 'days');
    };

    render() {
        const { exportLoading, contractList } = this.state;
        const { billingDetailStore, globalStore } = this.props;
        const { menuTreeReady, providerList } = globalStore;
        let { tableList, total, searchParams, loading, dialogShow } = billingDetailStore;

        const columns = this.getColumns();

        return (
            <div>
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2>合同计费</h2>
                    </div>
                </div> */}
                {menuTreeReady && checkFunctionHasPermission('TZ0302', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <RangePicker
                                    allowClear={false}
                                    className="middle-calendar-picker"
                                    value={searchParams.date ? [moment(searchParams.date[0]), moment(searchParams.date[1])] : null}
                                    // disabledDate={this.disabledDate}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'date')}
                                    ranges={DateRanges}
                                />
                                {/* 系统一次最多支持查询一年 */}
                                <Tooltip title={I18N.contractbillingdetail.xiTongYiCiZui}>
                                    <Icon type="question-circle" style={{ fontSize: '16px', marginLeft: '5px' }} />
                                </Tooltip>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    placeholder={I18N.contractbillingdetail.qingXuanZeHeZuo} // 请选择合作方
                                    optionFilterProp="children"
                                    value={searchParams.providerId ? searchParams.providerId : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'providerId')}>
                                    {providerList &&
                                        providerList.map((item, index) => {
                                            return (
                                                <Option value={item.uuid} key={index}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    style={{ width: '220px' }}
                                    placeholder={I18N.contractbillingdetail.qingXuanZeHeTong}
                                    optionFilterProp="children"
                                    value={searchParams.contractCode ? searchParams.contractCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'contractCode')}>
                                    {contractList?.map((v) => (
                                        <Option key={v.code} value={v.code}>
                                            {v.name}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                            {checkFunctionHasPermission('TZ0302', 'export') && (
                                <div className="item right">
                                    <Tooltip title={I18N.contractbillingdetail.daoChu}>
                                        <Button loading={exportLoading} onClick={this.export}>
                                            <Icon type="export" />
                                        </Button>
                                    </Tooltip>
                                </div>
                            )}
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 2150 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.contractbillingdetail.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0302', 'query') && <NoPermission />}
                {dialogShow.lookModal && (
                    <Suspense fallback={null}>
                        <ListModal history={history} />
                    </Suspense>
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    billingDetailStore: state.billingDetail
}))(TableContainer(ContractBillingDetail));
