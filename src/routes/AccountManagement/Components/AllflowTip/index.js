import I18N from '@/utils/I18N';
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Popover, Table } from 'tntd';

class index extends PureComponent {
    getColums = () => {
        let columns = [
            {
                title: I18N.allflowtip.index.zhuangTai, // 状态
                dataIndex: 'status'
            },
            {
                title: I18N.allflowtip.index.liuLiang, // 流量
                dataIndex: 'flow'
            }
            // {
            // 	title: '是否参与计费', // 是否参与计费
            // 	dataIndex: "bill"
            // }
        ];
        return columns;
    };

    getData = (data, type) => {
        const { page } = this.props;
        let dataSource = [
            { status: I18N.allflowtip.index.shiBai, flow: data.failCount, bill: I18N.allflowtip.index.fou }, // 失败 "否"
            ...(page !== 'invoke' && type !== 'external'
                ? [
                      {
                          status: I18N.allflowtip.index.chengGongBenDiCha,
                          flow: data.successCacheCount,
                          bill: data.chargeMethod ? I18N.allflowtip.index.shi : I18N.allflowtip.index.fou
                      }
                  ]
                : []),
            // { status: '成功（本地查得）', flow: data.successCacheCount, bill: data.chargeMethod ? '是' : '否' }, // 成功（缓存查得） "是"
            {
                status: I18N.allflowtip.index.chengGongShuJuYuan2,
                flow: (
                    <span>
                        {data.successThirdCount}
                        {page === 'invoke' && I18N.allflowtip.index.hanBenDi + (data.successCacheCount || 0) + I18N.allflowtip.index.tiao}
                    </span>
                ),
                bill: data.chargeMethod ? I18N.allflowtip.index.shi : I18N.allflowtip.index.fou
            }, // 成功（三方查得） "是"
            {
                status: I18N.allflowtip.index.chengGongShuJuYuan,
                flow: data.successThirdNotCount,
                bill: data.chargeMethod === 1 ? I18N.allflowtip.index.shi : I18N.allflowtip.index.fou
            } // 成功（三方未查得） "否" : "是"
        ];

        // if (type === 'external') dataSource.splice(1, 1);
        return dataSource;
    };

    render() {
        const { data, type } = this.props;

        const columns = this.getColums();
        const dataSource = this.getData(data, type);

        let content = <Table size="middle" bordered columns={columns} dataSource={dataSource} pagination={false} rowKey="status" />;

        return (
            <Popover content={content} placement="left">
                <a>{data.totalFlow}</a>
            </Popover>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(index);
