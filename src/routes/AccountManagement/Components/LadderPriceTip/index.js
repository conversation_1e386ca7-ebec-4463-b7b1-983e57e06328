import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Popover, Table } from 'tntd';

const style = {
    color: '#126BFB',
    margin: '0 8px'
};

const overlayStyle = {
    maxHeight: '252px',
    overflowY: 'auto'
};

class index extends PureComponent {
    getColumns = (type) => {
        let columns = [
            {
                title: type === 3 ? I18N.ladderpricetip.index.liuLiangQuJianCi : I18N.ladderpricetip.index.riQiQuJian, // "流量区间（次）" : "日期区间"
                dataIndex: 'begin',
                render: (text, record) => {
                    return `${record.begin} ~ ${record.end}`;
                }
            },
            {
                title: I18N.ladderpricetip.index.danJiaYuan, // 单价（元）
                dataIndex: 'price'
            }
        ];
        return columns;
    };

    render() {
        const { data, name, chargeType, globalStore } = this.props;
        const { allFieldList } = globalStore;
        let config = data ? JSON.parse(data) : null;
        let dom = data ? name : '--';

        const yuan = I18N.ladderpricetip.index.yuan;
        const count = I18N.ladderpricetip.index.ci;
        const year = I18N.ladderpricetip.index.nian;
        const month = I18N.ladderpricetip.index.yue;
        const ji = I18N.ladderpricetip.index.ji;

        if (config) {
            // 按次计费
            if (config.type === 1 && chargeType === 'count') {
                dom = `${name} ${config.price}${yuan}/${count}`; // 元/次
            }
            // 按次按字段计费
            if (config.type === 1 && chargeType === 'countFieldPrices') {
                const columns = [
                    {
                        title: I18N.ladderpricetip.index.ziDuanPiPeiCi,
                        dataIndex: 'matchFields',
                        render: (text) => {
                            const code = text?.[0]?.fieldCode;
                            return (
                                <span>
                                    {allFieldList?.find((v) => v?.name === code)?.displayName}
                                    <span style={style}>{I18N.ladderpricetip.index.zhengZe}</span>
                                    {text?.[0]?.matchConfig?.regex}
                                </span>
                            );
                        }
                    },
                    {
                        title: I18N.ladderpricetip.index.danJiaYuan,
                        dataIndex: 'price'
                    }
                ];
                let content = (
                    <Table size="middle" bordered columns={columns} dataSource={config.fieldPrices} pagination={false} rowKey="begin" />
                );
                dom = (
                    <Popover content={content} overlayStyle={overlayStyle} placement="left">
                        <a>{name}</a>
                    </Popover>
                );
            }
            // 2包计费制
            if (config.type === 2) {
                if (chargeType === 'packageYear') dom = `${name} ${config.price}${yuan}/${year}`; // 元/年
                if (chargeType === 'packageMonth') dom = `${name} ${config.price}${yuan}/${month}`; // 元/月
                if (chargeType === 'packageSeason') dom = `${name} ${config.price}${yuan}/${ji}`; // 元/季
            }

            if (config.type === 3 && chargeType === 'hierarchyDimensionsFlow') {
                let arr = config.fieldPrices;
                arr.forEach((v) => {
                    const { matchFields } = v;
                    v.hierarchyDetail.forEach((vv) => {
                        vv.matchFields = matchFields;
                    });
                });
                let newArr = arr.map((v) => v.hierarchyDetail);
                const columns = [
                    {
                        title: I18N.ladderpricetip.index.ziDuanPiPeiJie,
                        dataIndex: 'matchFields',
                        render: (text) => {
                            const code = text?.[0]?.fieldCode;
                            return (
                                <span>
                                    {allFieldList?.find((v) => v?.name === code)?.displayName}
                                    <span style={style}>{I18N.ladderpricetip.index.zhengZe}</span>
                                    {text?.[0]?.matchConfig?.regex}
                                </span>
                            );
                        }
                    },
                    {
                        title: I18N.ladderpricetip.index.liuLiangQuJian,
                        render: (text) => {
                            const { begin, end } = text;
                            return `${begin} ~ ${end}`;
                        }
                    },
                    {
                        title: I18N.ladderpricetip.index.danJiaYuan,
                        dataIndex: 'price'
                    }
                ];
                let content = (
                    <Table size="middle" bordered columns={columns} dataSource={newArr.flat()} pagination={false} rowKey="begin" />
                );
                dom = (
                    <Popover content={content} overlayStyle={overlayStyle} placement="left">
                        <a>{name}</a>
                    </Popover>
                );
            }
            // 3阶梯计费-流量区间, 4阶梯计费-日期区间
            if ((config.type === 3 && chargeType === 'hierarchyFlow') || config.type === 4) {
                const columns = this.getColumns(config.type);
                let content = (
                    <Table size="middle" bordered columns={columns} dataSource={config.hierarchyDetail} pagination={false} rowKey="begin" />
                );
                dom = (
                    <Popover content={content} overlayStyle={overlayStyle} placement="left">
                        <a>{name}</a>
                    </Popover>
                );
            }
        }

        return <Fragment>{config && dom}</Fragment>;
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(index);
