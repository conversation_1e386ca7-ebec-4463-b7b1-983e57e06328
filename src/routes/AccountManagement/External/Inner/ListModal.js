/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 外部计费管理 - 查看弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Modal, Button, Table, Pagination, Tooltip, Icon, Ellipsis } from 'tntd';
import { externalAPI } from '@/services';
import { formatStartTime, formatEndTime, formatMoney } from '@/utils/utils';
import LadderPriceTip from '@/routes/AccountManagement/Components/LadderPriceTip';
import AllflowTip from '@/routes/AccountManagement/Components/AllflowTip';

class ListModal extends PureComponent {
    state = {
        exportLoading: false
    };

    componentDidMount() {
        this.search();
    }

    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'external/setAttrValue',
            payload: {
                dialogShow: {
                    lookModal: false
                },
                lookId: null
            }
        });
    };

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, externalStore } = this.props;
        const { modalSearchParams } = externalStore;
        dispatch({
            type: 'external/getModalList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : modalSearchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 导出
    export = () => {
        const { lookId, modalSearchParams } = this.props.externalStore;
        const { date, serviceCode, providerId, contractId, contractVersion } = modalSearchParams;
        const params = {
            serviceCode,
            providerId,
            startTime: date ? formatStartTime(date[0]) : null,
            endTime: date ? formatEndTime(date[1]) : null,
            contractId,
            contractVersion
        };
        this.setState({ exportLoading: true });
        // 外部计费明细报表
        externalAPI
            .exportExternalDetail(params, I18N.inner.listmodal.shuJuYuanJiFei, 'csv')
            .then(() => {
                this.setState({ exportLoading: false });
            })
            .catch(() => {
                this.setState({ exportLoading: false });
            });
    };

    // 查看跳转
    lookDetail(record) {
        let path = '/handle/dataManagement/threeCallDetail';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    serviceCode: record.thirdServiceCode,
                    date: [moment(record.statisticsDate).valueOf(), moment(record.statisticsDate).valueOf()]
                })
            )}`,
            '_blank'
        );
    }

    render() {
        const { exportLoading } = this.state;
        const { externalStore } = this.props;
        const { modalTableList, modalTotal, modalSearchParams, modalLoading, dialogShow } = externalStore;

        const tipComtent = (
            <Fragment>
                <div>
                    {/* 按照计费方式进行估算，估算方法： */}
                    {I18N.inner.listmodal.anZhaoJiFeiFang}
                </div>
                <div>
                    {/* 1.按次计费：单日估算= 单价 * 参与计费流量 */}
                    {I18N.inner.listmodal.anCiJiFeiDan}
                </div>
                <div>
                    {/* 2.月包、年包、季包：单日估算=每天平均价格* 天数，月包 按30天计算，季包 按90天计算，年包按365天计算 */}
                    {I18N.inner.listmodal.yueBaoNianBaoJi}
                </div>
                <div>
                    {/* 3.阶梯计费：单日估算=阶梯区间的单价* 参与计费流量 */}
                    {I18N.inner.listmodal.jieTiJiFeiDan}
                </div>
            </Fragment>
        );
        const columns = [
            {
                title: I18N.inner.listmodal.shuJuYuanFuWu, // 三方服务接口名称,
                dataIndex: 'thirdServiceName',
                key: 'thirdServiceName',
                width: 200,
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: I18N.inner.listmodal.heZuoFangMingCheng, // 合作方名称
                dataIndex: 'supplierName',
                key: 'supplierName',
                width: 160,
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: I18N.inner.listmodal.banBenHao,
                dataIndex: 'contractVersion',
                width: 100,
                render: (text) => <Ellipsis title={'V' + Number(text) || '- -'} />
            },
            {
                title: I18N.inner.listmodal.riQi, // 日期
                dataIndex: 'statisticsDate',
                key: 'statisticsDate',
                width: 240,
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: I18N.inner.listmodal.jiFeiFangShi, // 计费方式
                dataIndex: 'chargeTypeName',
                key: 'chargeTypeName',
                ellipsis: true,
                width: 300,
                render: (text, record) => {
                    return <LadderPriceTip data={record.chargeConfig} name={text} chargeType={record.chargeType} />;
                }
            },
            {
                title: I18N.inner.listmodal.danRiChanShengZong, // 单日产生总流量
                dataIndex: 'totalFlow',
                key: 'totalFlow',
                width: 140,
                render: (text, record) => {
                    return <AllflowTip data={record} type="external" />;
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 单日成功流量 */}
                        {I18N.inner.listmodal.danRiChengGongLiu}
                        {/* 调用成功的流量数量，参与流量上限的统计和预警 */}
                        <Tooltip title={I18N.inner.listmodal.diaoYongChengGongDe}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'successFlow',
                key: 'successFlow',
                width: 150,
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: I18N.inner.listmodal.danRiCanYuJi, // 单日参与计费流量
                dataIndex: 'chargeFlow',
                key: 'chargeFlow',
                width: 160,
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: (
                    <Fragment>
                        {/* 单日估算 */}
                        {I18N.inner.listmodal.danRiGuSuan}
                        <Tooltip title={tipComtent}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                width: 140,
                align: 'right',
                render: (text) => <Ellipsis title={text || '- -'} />
            },
            {
                title: I18N.inner.listmodal.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 100,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.lookDetail(record)}>
                            {/* 查看 */}
                            {I18N.inner.listmodal.chaKan}
                        </span>
                    );
                    return dom;
                }
            }
        ];

        return (
            <Modal
                title={I18N.inner.listmodal.meiRiJiFeiMing} // "列表"
                width={800}
                maskClosable={false}
                visible={dialogShow.lookModal}
                onCancel={this.handleCancel}
                footer={null}
                className="m-modal-list">
                <div className="page-global-header">
                    <div className="left-info">
                        <div className="left-info-item">
                            <Button type="primary" loading={exportLoading} onClick={this.export}>
                                {/* 导出 */}
                                {I18N.inner.listmodal.daoChu}
                            </Button>
                        </div>
                    </div>
                    {/* <div className="right-info">
						<div className="right-info-item">
							<Button
								type="primary"
								loading={modalLoading}
								onClick={() => this.search()}
							>
								查询
							</Button>
						</div>
					</div> */}
                </div>

                <div className="page-global-body">
                    <div className="page-global-body-main">
                        <Table
                            rowKey={(record) => record.index}
                            className="table-card-body"
                            columns={columns}
                            dataSource={modalTableList}
                            pagination={false}
                            loading={modalLoading}
                            scroll={{ x: 1700 }}
                        />
                        <div className="page-global-body-pagination">
                            <span className="ml20">{I18N.template(I18N.inner.listmodal.gongMODA, { val1: modalTotal })}</span>
                            <Pagination
                                showSizeChanger
                                showQuickJumper
                                current={modalSearchParams.curPage}
                                pageSize={modalSearchParams.pageSize}
                                total={modalTotal}
                                onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                            />
                        </div>
                    </div>
                </div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    externalStore: state.external
}))(ListModal);
