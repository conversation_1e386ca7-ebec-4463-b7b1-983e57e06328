/*
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 业务系统计费
 */

import I18N from '@/utils/I18N';
import React, { PureComponent, Suspense, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { internalAPI } from '@/services';
import { Button, Table, Pagination, Select, DatePicker, message, Tooltip, Icon, Ellipsis, HandleIcon } from 'tntd';
import { formatStartTime, formatEndTime, formatMoney } from '@/utils/utils';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import LadderPriceTip from '@/routes/AccountManagement/Components/LadderPriceTip';
import AllflowTip from '@/routes/AccountManagement/Components/AllflowTip';
import { DateRanges } from '@/constants';

const ListModal = React.lazy(() => import('../Modal/ListModal1'));

const Option = Select.Option;
const { RangePicker } = DatePicker;

class Tab1 extends PureComponent {
    state = {
        exportLoading: false
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0301', 'query')) {
                    this.search();
                }
            }
        }, 100);
    }

    componentDidUpdate(preProps) {
        const appName = preProps.globalStore.currentApp.name;
        const nextAppName = this.props.globalStore.currentApp.name;
        if (appName !== nextAppName) {
            this.search();
        }
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'internal/reset'
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, internalStore } = this.props;
        const { searchParams } = internalStore;
        dispatch({
            type: 'internal/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            const st = e[0].valueOf();
            const et = e[1].valueOf();
            const rangeDay = (et - st) / 1000 / 60 / 60 / 24;
            if (rangeDay > 365) return message.warning(I18N.inner.tab1.xiTongYiCiZui); // 系统一次最多支持查询一年
            val = [st, et];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) {
            val = null;
        }
        obj[field] = val;
        dispatch({
            type: 'internal/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });

        this.search();
    }

    // 导出
    export = () => {
        const { currentApp } = this.props.globalStore;
        const { channelCode, date, serviceCode, billMethod } = this.props.internalStore.searchParams;
        const params = {
            billMethod,
            serviceCode,
            channelCode,
            appNames: currentApp?.name,
            startTime: date ? formatStartTime(date[0]) : null,
            endTime: date ? formatEndTime(date[1]) : null
        };
        this.setState({ exportLoading: true });
        // "业务系统计费报表"
        internalAPI
            .exportChannelList(params, I18N.inner.tab1.yingYongXiTongJi, 'csv')
            .then(() => {
                this.setState({ exportLoading: false });
            })
            .catch(() => {
                this.setState({ exportLoading: false });
            });
    };

    // 查看
    async lookDetail(record) {
        const { dispatch, internalStore } = this.props;
        const { searchParams } = internalStore;
        const { date, billMethod } = searchParams;
        await dispatch({
            type: 'internal/setAttrValue',
            payload: {
                dialogShow: {
                    lookModal: true
                },
                modalSearchParams: {
                    businessCode: record.appName, // 业务系统code
                    serviceCode: record.serviceCode, // 三方服务code
                    billMethod,
                    date, // 时间
                    contractId: record.contractId,
                    contractVersion: record.contractVersion
                }
            }
        });
    }

    getColumns = (billMethod) => {
        const tipComtent = (
            <Fragment>
                <div>{I18N.inner.tab1.canYuJiFeiLiu2}</div>
                <div>{I18N.inner.tab1.chaDeLiuLiangShu}</div>
                <div>{I18N.inner.tab1.chaXunLiuLiangShu}</div>
            </Fragment>
        );

        const tipComtent2 = (
            <Fragment>
                <div>
                    {/* 按照计费方式进行估算，估算方法： */}
                    {I18N.inner.tab1.anZhaoJiFeiFang}
                </div>
                <div>
                    {/* 1.按次计费：估算总价= 单价 * 参与计费流量 */}
                    {I18N.inner.tab1.anCiJiFeiGu}
                </div>
                <div>
                    {/* 2.月包、年包、季包：估算总价=每天平均价格* 天数，月包 按30天计算，季包 按90天计算，年包按365天计算 */}
                    {I18N.inner.tab1.yueBaoNianBaoJi2}
                </div>
                <div>
                    {/* 3.阶梯计费：估算总价=阶梯区间的单价* 参与计费流量 */}
                    {I18N.inner.tab1.jieTiJiFeiGu}
                </div>
            </Fragment>
        );

        let columns = [
            {
                title: I18N.inner.tab1.quDao,
                dataIndex: 'appDisplayName',
                key: 'appDisplayName',
                width: 160,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 10) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.inner.tab1.shuJuYuanFuWu, // "三方服务接口名称",
                dataIndex: 'serviceName',
                key: 'serviceName',
                width: 180,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                    // let dom = text;
                    // if (text && text.length > 10) {
                    //     dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    // }
                    // return dom ? dom : '--';
                }
            },
            {
                title: I18N.inner.tab1.heTongBianHao,
                dataIndex: 'contractCode',
                width: 160,
                render: (text) => {
                    return <Ellipsis title={text || '- -'} />;
                }
            },
            {
                title: I18N.inner.tab1.heTongBanBen,
                dataIndex: 'contractVersion',
                width: 180,
                render: (text) => 'V' + Number(text)
            },
            {
                title: I18N.inner.tab1.jiFeiFangShi, // "计费方式",
                dataIndex: 'chargeTypeName',
                key: 'chargeTypeName',
                width: 260,
                render: (text, record) => {
                    return <LadderPriceTip data={record.chargeConfig} name={text} chargeType={record.chargeType} />;
                }
            },
            {
                title: I18N.inner.tab1.chanShengZongLiuLiang, // 产生总流量,
                dataIndex: 'totalFlow',
                key: 'totalFlow',
                width: 160,
                render: (text, record) => {
                    return <AllflowTip data={record} type="internal" page="invoke" />;
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 参与计费流量 */}
                        {I18N.inner.tab1.canYuJiFeiLiu}
                        <Tooltip title={tipComtent}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'chargeFlow',
                key: 'chargeFlow',
                width: 160,
                render: (text) => {
                    return text || text === 0 ? text : '- -';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 估算总价 */}
                        {I18N.inner.tab1.guSuanZongJia}
                        <Tooltip title={tipComtent2} placement="left">
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                width: 160,
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '- -';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 均摊平均单次成本 */}
                        {I18N.inner.tab1.junTanPingJunDan}
                        {/* 均摊费用估算总价/参与计费流量 */}
                        <Tooltip title={I18N.inner.tab1.junTanFeiYongGu2}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'avgPrice',
                key: 'avgPrice',
                width: 170,
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '- -';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 均摊费用估算总价 */}
                        {I18N.inner.tab1.junTanFeiYongGu}
                        {/* 按照调用量占比，和其他共用合同的调用方/调用方组，共同均摊系统调用所产生的费用。 */}
                        <Tooltip title={I18N.inner.tab1.anZhaoDiaoYongLiang} placement="left">
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'shareCost',
                key: 'shareCost',
                width: 170,
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '- -';
                }
            },
            {
                title: I18N.inner.tab1.caoZuo, // "操作",
                dataIndex: 'operate',
                key: 'operate',
                width: 90,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = checkFunctionHasPermission('TZ0301', 'look') && (
                        <HandleIcon>
                            <HandleIcon.Item title={I18N.inner.tab1.chaKan}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        this.lookDetail(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];
        if (!billMethod) {
            columns.splice(8, 1);
            columns.splice(8, 1);
        }
        return columns;
    };

    disabledDate = (current) => {
        // Can not select days before today and today
        return (current && current > moment().endOf('day')) || current <= moment().add(-365, 'days');
    };

    render() {
        const { exportLoading } = this.state;
        const { internalStore, globalStore, history } = this.props;
        const { menuTreeReady, threeServiceList } = globalStore;
        const { tableList, loading, total, searchParams, dialogShow } = internalStore;
        const { serviceCode, billMethod } = searchParams;

        const columns = this.getColumns(billMethod);

        const tipComtent = (
            <Fragment>
                <div>
                    {/* 1. 按日计算均摊费用（T+1），计算公式是 单日参与计费调用量占比 乘以 单日系统调用三方服务接口产生的总费用。 */}
                    {I18N.inner.tab1.anRiJiSuanJun}
                </div>
                <div>
                    {/* 2.计费类型为查询计费时，参与计费调用量=总流量-失败量；计费类型为查得计费时，参与计费调用量=总流量-失败量-成功（三方未查得）。 */}
                    {I18N.inner.tab1.jiFeiLeiXingWei}
                </div>
                <div>
                    {/* 3.月包、年包、季包会计算平均每天费用进行计算。 */}
                    {I18N.inner.tab1.yueBaoNianBaoJi}
                </div>
            </Fragment>
        );

        return (
            <div className="page-global-body" style={{ padding: '16px 20px' }}>
                {menuTreeReady && checkFunctionHasPermission('TZ0301', 'query') && (
                    <div className="page-global-body-search">
                        {checkFunctionHasPermission('TZ0301', 'export') && (
                            <div className="right-info">
                                <div className="right-info-item">
                                    <Tooltip title={I18N.inner.tab1.daoChu}>
                                        <Button loading={exportLoading} onClick={this.export}>
                                            <Icon type="export" />
                                        </Button>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div className="left-info">
                            <div className="left-info-item">
                                <RangePicker
                                    allowClear={false}
                                    className="middle-calendar-picker"
                                    value={searchParams.date ? [moment(searchParams.date[0]), moment(searchParams.date[1])] : null}
                                    // disabledDate={this.disabledDate}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'date')}
                                    ranges={DateRanges}
                                />
                                {/* 系统一次最多支持查询一年 */}
                                <Tooltip title={I18N.inner.tab1.xiTongYiCiZui}>
                                    <Icon type="question-circle" style={{ fontSize: '16px', marginLeft: '5px' }} />
                                </Tooltip>
                            </div>
                            <div className="left-info-item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    placeholder={I18N.inner.tab1.qingXuanZeShuJu} // 请选择三方服务接口
                                    optionFilterProp="children"
                                    value={serviceCode ? serviceCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceCode')}>
                                    {threeServiceList &&
                                        threeServiceList.map((item, index) => {
                                            return (
                                                <Option value={item.name} key={index}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="left-info-item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    placeholder={I18N.inner.tab1.qingXuanZeJunTan} // 请选择均摊计费方式
                                    optionFilterProp="children"
                                    value={billMethod ? billMethod : undefined}
                                    optionLabelProp="label"
                                    onChange={(e) => this.changeField(e, 'select', 'billMethod')}>
                                    <Option value="day" label={I18N.inner.tab1.anRiJunTan}>
                                        {/* 按日均摊 */}
                                        {I18N.inner.tab1.anRiJunTan}
                                        <Tooltip title={tipComtent} placement="left">
                                            <Icon type="question-circle" style={{ float: 'right', fontSize: '16px', marginTop: '2px' }} />
                                        </Tooltip>
                                    </Option>
                                </Select>
                            </div>
                        </div>
                    </div>
                )}
                {billMethod && (
                    <span className="u-billMethod">
                        <Icon type="exclamation-circle" />
                        {/* 均摊计费方式是系统给出的估算方法，只能保证在统计单位上内外收支平衡。需要根据场景选择适合的估算方法。 */}
                        {I18N.inner.tab1.junTanJiFeiFang}
                    </span>
                )}
                {menuTreeReady && checkFunctionHasPermission('TZ0301', 'query') && (
                    <div className="page-global-body-main">
                        <Table
                            rowKey={(record) => record.index}
                            className="table-card-body"
                            columns={columns}
                            dataSource={tableList}
                            pagination={false}
                            loading={loading}
                            scroll={{ x: billMethod ? 1800 : 1500 }}
                        />
                        <div className="page-global-body-pagination">
                            <span className="ml20">{I18N.template(I18N.inner.tab1.gongTOTA, { val1: total })}</span>
                            <Pagination
                                showSizeChanger
                                showQuickJumper
                                current={searchParams.curPage}
                                pageSize={searchParams.pageSize}
                                total={total}
                                onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                            />
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0301', 'query') && <NoPermission />}
                {dialogShow.lookModal && (
                    <Suspense fallback={null}>
                        <ListModal history={history} />
                    </Suspense>
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    internalStore: state.internal
}))(Tab1);
