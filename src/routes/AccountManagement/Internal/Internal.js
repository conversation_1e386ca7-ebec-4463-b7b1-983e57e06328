/*
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 内部计费管理
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Tabs, TabsContainer } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import Tab1 from './Inner/Tab1';
import Tab2 from './Inner/Tab2';

const TabPane = Tabs.TabPane;
class Internal extends PureComponent {
    // tab切换
    changeTabHandle(key) {
        let { location, history } = this.props;
        let { pathname } = location;
        let search = '?currentTab=' + key;
        history.push(pathname + search);
    }

    render() {
        const currentTab = getUrlKey('currentTab');

        return (
            <Tabs activeKey={currentTab ? currentTab : '1'} onChange={(key) => this.changeTabHandle(key)} type="ladder-card">
                {/* 业务系统计费 */}
                <TabPane tab={I18N.internal.anYingYongJiFei} key="1">
                    <Tab1 history={this.props.history} />
                </TabPane>
                {/* 机构计费 */}
                <TabPane tab={I18N.internal.anJiGouJiFei} key="2">
                    <Tab2 history={this.props.history} />
                </TabPane>
            </Tabs>
        );
    }
}

export default TabsContainer(Internal);
