/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 机构计费 - 查看弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Modal, Button, Table, Pagination, DatePicker, message, Tooltip, Icon } from 'tntd';
import { internalAPI } from '@/services';
import { formatStartTime, formatEndTime, formatMoney } from '@/utils/utils';
import AllflowTip from '@/routes/AccountManagement/Components/AllflowTip';

const { RangePicker } = DatePicker;
class ListModal extends PureComponent {
    state = {
        exportLoading: false
    };

    componentDidMount() {
        this.search();
    }

    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'internal/setAttrValue',
            payload: {
                dialogShow: {
                    lookModal2: false
                },
                modalSearchParams2: {
                    curPage: 1,
                    pageSize: 10,
                    businessType: 2, // 1=业务系统，2=机构
                    businessCode: null, // 机构code
                    serviceCode: null, // 三方服务code
                    date: null, // 时间
                    contractId: null, // 合同id
                    contractVersion: null // 合同版本
                }
            }
        });
    };

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, internalStore } = this.props;
        const { modalSearchParams2 } = internalStore;
        dispatch({
            type: 'internal/getModalList2',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : modalSearchParams2.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 导出
    export = () => {
        const { modalSearchParams2 } = this.props.internalStore;
        const { businessCode, serviceCode, date, contractId, contractVersion } = modalSearchParams2;
        const params = {
            serviceCode,
            businessCode,
            startTime: date ? formatStartTime(date[0]) : null,
            endTime: date ? formatEndTime(date[1]) : null,
            contractId,
            contractVersion
        };
        this.setState({ exportLoading: true });
        // 机构计费明细报表
        internalAPI
            .exportOrganizeListDetail(params, I18N.modal.listmodal2.jiGouJiFeiMing, 'csv')
            .then(() => {
                this.setState({ exportLoading: false });
            })
            .catch(() => {
                this.setState({ exportLoading: false });
            });
    };

    // 查看跳转
    lookDetail(record) {
        let path = '/handle/dataManagement/businessChannel';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    serviceCode: record.thirdServiceCode,
                    organizeCode: record.businessCode,
                    date: [moment(record.statisticsDate).valueOf(), moment(record.statisticsDate).valueOf()]
                })
            )}`,
            '_blank'
        );
    }

    getColumns = (billMethod) => {
        const tipComtent = (
            <Fragment>
                <div>
                    {/* 按照计费方式进行估算，估算方法： */}
                    {I18N.modal.listmodal2.anZhaoJiFeiFang}
                </div>
                <div>
                    {/* 1.按次计费：单日估算= 单价 * 参与计费流量 */}
                    {I18N.modal.listmodal2.anCiJiFeiDan}
                </div>
                <div>
                    {/* 2.月包、年包、季包：单日估算=每天平均价格* 天数，月包 按30天计算，季包 按90天计算，年包按365天计算 */}
                    {I18N.modal.listmodal2.yueBaoNianBaoJi}
                </div>
                <div>
                    {/* 3.阶梯计费：单日估算=阶梯区间的单价* 参与计费流量 */}
                    {I18N.modal.listmodal2.jieTiJiFeiDan}
                </div>
            </Fragment>
        );

        let columns = [
            {
                title: I18N.modal.listmodal2.jiGou, // 机构,
                dataIndex: 'businessName',
                key: 'businessName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.modal.listmodal2.shuJuYuanFuWu, // "三方服务接口名称",
                dataIndex: 'thirdServiceName',
                key: 'thirdServiceName',
                width: 140,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.modal.listmodal2.banBenHao,
                dataIndex: 'contractVersion',
                width: 80,
                render: (text) => 'V' + Number(text)
            },
            {
                title: I18N.modal.listmodal2.riQi, // "日期",
                dataIndex: 'statisticsDate',
                width: 120,
                key: 'statisticsDate'
            },
            {
                title: I18N.modal.listmodal2.danRiChanShengZong, // 单日产生总流量
                dataIndex: 'totalFlow',
                key: 'totalFlow',
                width: 150,
                render: (text, record) => {
                    return <AllflowTip data={record} type="internal" />;
                }
            },
            {
                title: I18N.modal.listmodal2.danRiCanYuJi2, // 单日参与计费流量
                dataIndex: 'chargeFlow',
                key: 'chargeFlow',
                width: 180,
                render: (text) => {
                    return text || text === 0 ? text : '--';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 单日估算 */}
                        {I18N.modal.listmodal2.danRiGuSuan}
                        <Tooltip title={tipComtent}>
                            <Icon type="question-circle" className="ml5" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                width: 180,
                align: 'right',
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '--';
                }
            },
            {
                title: I18N.modal.listmodal2.danRiCanYuJi, // 单日参与计费流量占比
                dataIndex: 'chargeFlowPercentage',
                key: 'chargeFlowPercentage',
                width: 180
            },
            {
                title: I18N.modal.listmodal2.danRiJunTanGu, // 单日均摊估算
                dataIndex: 'shareCost',
                key: 'shareCost',
                width: 160,
                align: 'right',
                render: (text) => {
                    return text || text === 0 ? formatMoney(text) : '--';
                }
            },
            {
                title: I18N.modal.listmodal2.caoZuo, // "操作",
                dataIndex: 'operate',
                key: 'operate',
                width: 100,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.lookDetail(record)}>
                            {/* 查看 */}
                            {I18N.modal.listmodal2.chaKan}
                        </span>
                    );
                    return dom;
                }
            }
        ];
        if (!billMethod) {
            columns.splice(6, 1);
            columns.splice(6, 1);
        }
        return columns;
    };

    render() {
        const { exportLoading } = this.state;
        const { internalStore } = this.props;
        const { modalTableList2, modalTotal2, modalSearchParams2, modalLoading2, dialogShow } = internalStore;
        const { billMethod } = modalSearchParams2;
        const columns = this.getColumns(billMethod);

        return (
            <Modal
                title={I18N.modal.listmodal2.meiRiJiFeiMing} // "列表"
                width={800}
                maskClosable={false}
                visible={dialogShow.lookModal2}
                onCancel={this.handleCancel}
                footer={null}
                className="m-modal-list">
                <div className="page-global-header">
                    <div className="left-info">
                        <div className="left-info-item">
                            <Button type="primary" loading={exportLoading} onClick={this.export}>
                                {/* 导出 */}
                                {I18N.modal.listmodal2.daoChu}
                            </Button>
                        </div>
                    </div>
                    {/* <div className="right-info">
						<div className="right-info-item">
							<Button
								type="primary"
								loading={modalLoading2}
								onClick={() => this.search()}
							>
								查询
							</Button>
						</div>
					</div> */}
                </div>

                <div className="page-global-body">
                    <div className="page-global-body-main">
                        <Table
                            rowKey={(record) => record.index}
                            className="table-card-body"
                            columns={columns}
                            dataSource={modalTableList2}
                            pagination={false}
                            loading={modalLoading2}
                            scroll={{ x: billMethod ? 1700 : 1400 }}
                        />
                        <div className="page-global-body-pagination">
                            <span className="ml20">{I18N.template(I18N.modal.listmodal2.gongMODA, { val1: modalTotal2 })}</span>
                            <Pagination
                                showSizeChanger
                                showQuickJumper
                                current={modalSearchParams2.curPage}
                                pageSize={modalSearchParams2.pageSize}
                                total={modalTotal2}
                                onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                            />
                        </div>
                    </div>
                </div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    internalStore: state.internal
}))(ListModal);
