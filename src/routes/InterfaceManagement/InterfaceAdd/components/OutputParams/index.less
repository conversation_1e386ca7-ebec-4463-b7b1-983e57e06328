.text-flex-right{
    .model-text{
        float: right;
        position: relative;
        height: 28px;
        line-height: 28px;
        .anticon-caret-down{
            position: absolute;
            left: 10px;
            left: -20px;
            top: 7px;
        }
        > span{
            display: inline-block;
            color: #454f64;
            border-radius: 2px;
            // padding: 0 11px;
            width: 100%;
            cursor: not-allowed;
            // 超出部分显示省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
    
           
        }
     
}
}