.sync-interface-add-detail{
    margin: 16px 20px;

    .tnt-current-v3 & {
        margin: 0;
    }

    background: #fff;
    box-shadow: 0 0 6px 0 rgba(215,219,243,.5);
    border-radius: 2px;
    height: calc(100vh - 120px);
    min-width: 1100;
    overflow: scroll;
    border-radius: @border-radius-base;
    .title{
        // display: block;
        margin-left: 96px;
        // border-left: 4px solid #1890ff;
        // padding-left: 10px;
        font-weight: 700;
        // line-height: 14px;
        margin-bottom: 20px;
        margin-top: 30px;
    }

    .ant-form{
        overflow: hidden;
        display: flex;
        flex-wrap: wrap;

        .ant-form-explain{
            text-align: left;
            // position: absolute;
        }
        .ant-row.ant-form-item{
            position: relative;
            // float: left;
            width: 50%;
            text-align: center;
            padding-left: 96px;
            padding-right: 96px;

            .ant-col.ant-form-item-label{
                display: block;
                text-align: left;
            }

            .ant-col.ant-form-item-control-wrapper{
                display: block;
            }

            .input-actions-btn{
                position: absolute;
                top: -8px;
                left: 65px;

                > .ant-btn{
                    margin-left: 10px;
                }
            }

            .search{
                float: right;
                width: 240px;
                line-height: 35px;
            }

        }
        .ant-row.ant-form-item.full{
            width: 100%;
            margin-bottom: 50px;
            .ant-col.ant-form-item-label{
                line-height: 35px;
                margin-bottom: 4px;
            }
        }

        .input-params{
            position: relative;
            margin-bottom: 50px;
            .add-field-btn{
                width: 100%;
                text-align: center;
                position: absolute;
                bottom: -37px;
                color: #126bfb;
                border: 1px solid #EBEEF5;
                border-top: none;
                cursor: pointer;
            }
        }

        .ant-pagination.ant-table-pagination{
            position: absolute;
            bottom: -80px;
            right: 0;
        }

    }
    .submit-btn{
        float: right;
        position: relative;
        right: 95px;
        bottom: 20px;

        > .ant-btn{
            margin-left: 10px;
        }
    }
}

.async-interface-add-detail{
    margin: 16px 20px;
    height: calc(100vh - 120px);
    box-shadow: 0 0 6px 0 rgba(215,219,243,.5);
    border-radius: 2px;
    min-width: 1100px;
    overflow: scroll;

    .page-global-body-main{
        min-width: 1100px;
        background: #fff;
        overflow: scroll;
    }

    .title{
        display: block;
        margin-left: 96px;
        border-left: 4px solid #1890ff;
        padding-left: 10px;
        font-weight: 700;
        line-height: 14px;
        margin-bottom: 20px;
        margin-top: 30px;
    }

    .steps{
        background: #F0F2F6;
        display: flex;
        height: 48px;
        .step{
            flex: 1;
            line-height: 48px;
            padding-left: 40px;
            position: relative;
        }
        .step-active {
            background: #1890ff;
            color: #fff;
            span{
                position: absolute;
                right: 0;
                top: 0;
                display: inline-block;
                width: 30px;
                background: #F0F2F6;
                height: 100%;
                i{
                    display: inline-block;
                    width: 0;
                    height: 0;
                    border-width: 24px;
                    border-style: solid;
                    border-color: transparent transparent transparent #1890ff;
                }
            }
        }
        .last-step span {
            background: #1890ff;
        }
    }

    .hidden{
        position: absolute;
        left: 9999px;
        width: 0px;
        overflow: hidden;
    }

    .ant-form{
        overflow: hidden;

        .ant-form-explain{
            text-align: left;
            position: absolute;
        }

        .ant-row.ant-form-item{
            position: relative;
            float: left;
            min-width: 50%;
            text-align: center;
            padding-left: 96px;
            padding-right: 96px;
            margin-bottom: 20px;

            .ant-col.ant-form-item-label{
                display: block;
                text-align: left;
                line-height: 15px;
            }

            .ant-col.ant-form-item-control-wrapper{
                display: block;
            }

            .input-actions-btn{
                position: absolute;
                top: -8px;
                left: 65px;

                > .ant-btn{
                    margin-left: 10px;
                }
            }

            .search{
                float: right;
                width: 240px;
                line-height: 35px;
            }

        }
        .ant-row.ant-form-item.full{
            width: 100%;
            margin-bottom: 50px;
            .ant-col.ant-form-item-label{
                line-height: 35px;
            }
        }

        .input-params{
            position: relative;
            margin-bottom: 50px;
            .add-field-btn{
                width: 100%;
                text-align: center;
                position: absolute;
                bottom: -37px;
                color: #126bfb;
                border: 1px solid #EBEEF5;
                border-top: none;
                cursor: pointer;
            }
        }

        .ant-pagination.ant-table-pagination{
            position: absolute;
            bottom: -80px;
            right: 0;
        }

    }

    .submit-btn{
        float: right;
        position: relative;
        right: 95px;
        bottom: 20px;

        > .ant-btn{
            margin-left: 10px;
        }
    }

}
