import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Button, TableContainer, Icon, message, Popconfirm, Switch, QueryListScene, HandleIcon, Ellipsis } from 'tntd';

import service from './service';
import { baseAPI } from '@/services';
import './index.less';
// import history from '@/utils/history';
import { getUrlKey, traverseTree } from '@/utils/utils';
import { connect } from 'dva';

import TestServiceModal from './TestServiceModal/index';
import otp from './otp';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const AppManager = (props) => {
    const [loading, setLoading] = useState(false);

    const [testService, setTestService] = useState({});
    const [orgAll, setOrgAll] = useState(null);
    const [appTypeList, setAppTypeList] = useState([]);

    const { testDrawer, testDrawerData, testTitle, testDrawerLoading, errorMsg, name, testDrawerChildData } = testService;

    // eleValue 字段为变量时默认带入初始值 枚举 和 时间类型字段 默认为空
    const vTestDrawerData =
        (testDrawerData &&
            testDrawerData.map((v) => {
                if (!v.eleValue && v.type === 'variable') {
                    if (v.dataType === 4) {
                        v.eleValue = null;
                    } else if (v.dataType === 5) {
                        v.eleValue = null;
                    } else {
                        v.eleValue = ''; //v.serviceParam;
                    }
                }
                return v;
            })) ||
        [];

    let displayName = getUrlKey('displayName');

    const query = (params = {}) => {
        const { current: curPage = 1, pageSize = 10, obj, ...rest } = params;

        return service
            .interfaceList({
                ...rest,
                ...obj,
                curPage,
                pageSize
            })
            .then((res) => {
                let { data, ...rest } = res;
                return {
                    ...rest,
                    data: data.contents || [],
                    total: data.total,
                    pageSize: data.pageSize,
                    current: res.curPage
                };
            });
    };

    const delInterface = (uuid) => {
        setLoading(true);
        service
            .interfaceDelete({ uuid })
            .then((res) => {
                setLoading(false);
                message.success(I18N.interfacemanagement.list.shanChuChengGong);
                actions.search();
            })
            .catch((err) => {
                setLoading(false);
                message.error(err.message);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    // 点击测试
    const testHandle = (record) => {
        const parmas = {
            uuid: record.uuid
        };
        service.getInterfaceDetail(parmas).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                let testDrawerData = res.data.inputConfig ? JSON.parse(res.data.inputConfig) : [];

                setTestService({
                    ...testService,
                    testDrawer: true,
                    testTitle: record.displayName,
                    testDrawerData,
                    serviceInterfaceName: record.name,
                    name: record.name
                });
            } else {
                message.error(res.message || res.message);
            }
        });
    };

    // 关闭测试
    const closeTestDrawer = () => {
        setTestService({
            ...testService,
            testDrawer: false
        });
        setTimeout(() => {
            setTestService({
                testDrawerData: null,
                testTitle: null,
                errorMsg: null,
                uuid: null,
                testDrawerChildData: null
            });
        }, 300);
    };

    // 调取测试接口
    const testSubmit = (data, serviceInterfaceName) => {
        let flag = false;
        data.forEach((item) => {
            if (item.type === 'variable' && item.mustInput && !item.eleValue && item.eleValue !== 0) {
                flag = true;
            }
        });
        //有必填字段未填写
        if (flag) {
            setTestService({ ...testService, errorMsg: I18N.interfacemanagement.list.youBiTianZiDuan });
            return;
        }
        let arr = [];
        data.forEach((item) => {
            arr.push({
                paramName: item.serviceParam,
                paramValue: item.type === 'variable' ? item.eleValue : item.serviceParam
            });
        });
        setTestService({
            ...testService,
            testDrawerLoading: true,
            errorMsg: null
        });
        let params = {
            inputConfig: JSON.stringify(arr),
            serviceInterfaceName
        };
        service
            .apiTest(params)
            .then((res) => {
                setTestService({ ...testService, testDrawerLoading: false });
                if (res && res.success) {
                    setTestService({ ...testService, testDrawerChildData: res.data, errorMsg: null });
                } else {
                    setTestService({ ...testService, errorMsg: res.message || res.message });
                }
            })
            .catch(() => {
                setTestService({ ...testService, testDrawerLoading: false, errorMsg: I18N.interfacemanagement.list.fuWuCuoWu });
            });
    };

    // 关闭测试结果二级抽屉
    const onChildrenDrawerClose = () => {
        setTestService({ ...testService, testDrawerChildData: null });
    };

    useEffect(() => {
        baseAPI.getOrgAll().then((res) => {
            const orgList = traverseTree(res?.data || [], (item) => {
                item.title = item.name;
                item.value = item.code;
                item.key = item.code;
                return item;
            });
            setOrgAll(orgList);
        });
        getInterfaceDict();
    }, []);

    // 获取接口信息
    const getInterfaceDict = () => {
        service.getInterfaceDict().then((res) => {
            if (res.success) {
                setAppTypeList(res.data.appTypeList || []);
            } else {
                message.error(res.message || res.message);
            }
        });
    };

    const columns = [
        {
            title: I18N.interfacemanagement.list.fuWuMingCheng,
            width: 180,
            dataIndex: 'displayName',
            render: (text) => {
                return <Ellipsis title={text || '- -'} widthLimit={140} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.fuWuBiaoZhi,
            width: 120,
            dataIndex: 'name',
            render: (text, row) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.fuWuChangJing,
            width: 120,
            dataIndex: 'appType',
            render: (text, row) => {
                let obj = appTypeList.find((item) => item.name === text) || {};
                text = obj.displayName || '- -';
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.fuWuLeiXing,
            width: 120,
            dataIndex: 'serviceType',
            render: (text) => {
                text =
                    (!!text && String(text)) === '1' ? I18N.interfacemanagement.list.shuJuFuWu : I18N.interfacemanagement.list.shuJuBuChong;
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.zhuangTai,
            width: otp.status,
            dataIndex: 'status',
            render: (value, row) => {
                let { status, uuid } = row;
                let checked = String(status) !== '2';

                return (
                    <Switch
                        checked={checked}
                        checkedChildren={I18N.interfacemanagement.list.shangXian}
                        unCheckedChildren={I18N.interfacemanagement.list.xiaXian}
                        onChange={() => {
                            let statusValue = checked ? 2 : 1;
                            changeStatus(uuid, statusValue);
                        }}
                    />
                );
            }
        },
        {
            title: I18N.interfacemanagement.list.chuangJianShiJian,
            width: 200,
            dataIndex: 'gmtCreate',
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.chuangJianRen,
            width: 120,
            dataIndex: 'creator',
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.xiuGaiShiJian,
            width: 200,
            dataIndex: 'gmtModify',
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.xiuGaiRen,
            width: 120,
            dataIndex: 'operator',
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.interfacemanagement.list.caoZuo,
            width: 150,
            fixed: 'right',
            render: (row) => {
                let { uuid, status, displayName } = row;
                return (
                    <HandleIcon>
                        {String(status) === '2' && (
                            <HandleIcon.Item title={I18N.interfacemanagement.list.xiuGai}>
                                <Icon
                                    type="form"
                                    onClick={() => {
                                        props.history.push(`/handle/interface/management/detail?type=update&uuid=${uuid}`);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        <HandleIcon.Item title={I18N.interfacemanagement.list.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    props.history.push(`/handle/interface/management/detail?type=view&uuid=${uuid}`);
                                }}
                            />
                        </HandleIcon.Item>
                        {String(status) === '2' && (
                            <HandleIcon.Item title={I18N.interfacemanagement.list.shanChu}>
                                <Popconfirm
                                    title={I18N.interfacemanagement.list.queDingShanChu} //
                                    placement="topRight"
                                    onConfirm={() => delInterface(uuid)}>
                                    <Icon type="delete" />
                                </Popconfirm>
                            </HandleIcon.Item>
                        )}

                        <HandleIcon.Item title={I18N.interfacemanagement.list.xiaZai}>
                            <Icon
                                type="download-file"
                                onClick={() => {
                                    service.interfaceDocDownload(
                                        { uuid },
                                        I18N.template(I18N.interfacemanagement.list.dISPL, { val1: displayName })
                                    );
                                }}
                            />
                        </HandleIcon.Item>
                        {String(status) !== '2' && (
                            <HandleIcon.Item title={I18N.interfacemanagement.list.ceShi}>
                                <Icon
                                    type="debug"
                                    onClick={() => {
                                        testHandle(row);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                    </HandleIcon>
                );
            }
        }
    ];

    const onFormChange = (values, changeInfo) => {
        if (['serviceType', 'status'].includes(changeInfo.name)) {
            actions.search(values);
        }
    };

    const changeStatus = (uuid, status) => {
        setLoading(true);
        setTimeout(() => {
            service
                .interfaceStatuChange({ uuid, status })
                .then((res) => {
                    if (res.success) {
                        message.success(I18N.interfacemanagement.list.caoZuoChengGong);
                        actions.search();
                    } else {
                        message.error(res.message || res.message);
                    }
                })
                .finally(() => {
                    setLoading(false);
                });
        }, 2000);
    };

    return (
        <QueryListScene query={query} actions={actions} className="interface-page-list">
            <QueryForm
                onChange={onFormChange}
                initialValues={{
                    displayName
                }}
                extraActions={
                    <Button
                        type="primary"
                        icon="plus"
                        onClick={() => {
                            props.history.push('/handle/interface/management/detail');
                        }}>
                        {I18N.interfacemanagement.list.xinZeng}
                    </Button>
                }>
                <Field
                    type="input"
                    name="displayName"
                    props={{
                        placeholder: I18N.interfacemanagement.list.shuRuFuWuMing,
                        onPressEnter: (e) => {
                            actions.search({ displayName: e.target.value, current: 1 });
                        }
                    }}
                />
                <Field
                    title=""
                    name="status"
                    type="select"
                    props={{
                        placeholder: I18N.interfacemanagement.list.qingXuanZeZhuangTai,
                        options: [
                            { label: I18N.interfacemanagement.list.shangXian, value: '1' },
                            { label: I18N.interfacemanagement.list.xiaXian, value: '2' }
                        ]
                    }}
                />
                <Field
                    title=""
                    name="serviceType"
                    type="select"
                    props={{
                        placeholder: I18N.interfacemanagement.list.qingXuanZeFuWu,
                        options: [
                            { label: I18N.interfacemanagement.list.shuJuFuWu, value: '1' },
                            { label: I18N.interfacemanagement.list.shuJuBuChong, value: '2' }
                        ]
                    }}
                />
            </QueryForm>
            <QueryList
                rowKey="uuid"
                columns={columns}
                scroll={{
                    x: 1450
                }}
                loading={loading}
            />
            <TestServiceModal
                orgAll={orgAll}
                title={testTitle}
                name={name}
                visible={testDrawer}
                data={vTestDrawerData}
                testDrawerChildData={testDrawerChildData}
                loading={testDrawerLoading}
                errorMsg={errorMsg}
                onClose={() => closeTestDrawer()}
                onTest={(data, name) => testSubmit(data, name)}
                onChildrenDrawerClose={() => onChildrenDrawerClose()}
            />
        </QueryListScene>
    );
};

export default connect((state) => ({ ...state }))(TableContainer(AppManager));
