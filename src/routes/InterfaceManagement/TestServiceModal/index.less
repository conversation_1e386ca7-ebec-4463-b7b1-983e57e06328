:global{
    .m-service-testdrawer{
        .title{
            border-left: 8px solid #ffd76d;
            margin-left: -24px;
            font-size: 16px;
            font-weight: 700;
            padding-left: 15px;
        }
        .warp{
            height: ~"calc(100vh - 190px)";
            overflow: auto;
            margin-top: 20px;
            .box{
                margin-top: 15px;
                text-align: center;
                .label{
                    display: inline-block;
                    margin-right: 10px;
                    width: 180px;
                    text-align: right;
                    text-overflow: ellipsis;
					overflow: hidden;
                    white-space: nowrap;
                    vertical-align: middle;
                    margin-top: -2px;
                    b{
                        vertical-align: middle;
                        margin-right: 2px;
                        color: #f00;
                    }
                }
                .content{
                    display: inline-block;
                    width: 230px;
                }
            }
        }
        .test-drawer-footer{
            position: absolute;
            bottom: 0;
            left: 0;
            text-align: center;
            width: 100%;
            border-top: 1px solid #e8e8e8;
            padding: 10px 0;
        }
        .ant-drawer-header{
            padding: 19px 24px !important;
        }
    }
	.test-org-tree-select{
		.ant-select-tree li .ant-select-tree-node-content-wrapper{
			overflow: hidden;
			text-overflow: ellipsis;
		}
        .ant-select-tree li span.ant-select-tree-iconEle, .ant-select-tree li span.ant-select-tree-switcher{
            vertical-align: top;
        }
	}
}
