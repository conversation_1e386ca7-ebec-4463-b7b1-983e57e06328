import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import { PageContainer } from 'tntd';

import SyncInterfaceAdd from './InterfaceAdd/Sync';

import List from './List';
import { getUrlKey } from '@/utils/utils';

export default PageContainer(
    BreadCrumb(() => {
        let type = getUrlKey('type');
        let text = I18N.interfacemanagement.index.tianJia;
        text = (type === 'update' && I18N.interfacemanagement.index.xiuGai) || text;
        text = (type === 'view' && I18N.interfacemanagement.index.chaKan) || text;
        return (
            <Switch>
                <Route
                    name={I18N.template(I18N.interfacemanagement.index.jieKouFuWuXiang, { val1: text })}
                    exact
                    component={SyncInterfaceAdd}
                    path="/handle/interface/management/detail"
                />
                <Route name={I18N.interfacemanagement.index.jieKouFuWuLie} component={List} path="/" />
            </Switch>
        );
    })
);
