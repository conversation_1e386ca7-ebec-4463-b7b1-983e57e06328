import request from '@/utils/request';
import { getUrl } from '@/services/common';

/**
 * 流程模板模块
 * */
// 流程模板列表
const workflowTemplateList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/page', param),
        {
            method: 'GET'
        },
        true
    );
};

// 新增流程模板
const addSubWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflowTemplate/add',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 修改流程模板
const updateSubWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflowTemplate/update',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 删除流程模板
const subWorkFlowDel = async (params) => {
    return request(
        '/bridgeApi/workflowTemplate/delete',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 查看流程模板
const getWorkflowTemplateDetail = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/detail', param),
        {
            method: 'GET'
        },
        true
    );
};
export default {
    workflowTemplateList,
    updateSubWorkFlow,
    addSubWorkFlow,
    subWorkFlowDel,
    getWorkflowTemplateDetail
};
