import I18N from '@/utils/I18N';
import { useState, useEffect, useRef } from 'react';
import { Modal, Button, Form, Input, message } from 'tntd';
import service from '../../service';
import otp from '../../otp';

// import otp from './otp';

const { TextArea } = Input;
const AddModifyModal = (props) => {
    const { form, data, onCancel, search, visible } = props;
    const { getFieldDecorator, setFieldsValue, resetFields } = form;
    const [loading, setLoading] = useState(false);
    const { title = I18N.modal.addmodify.xinZeng, titleType, uuid, graphJson, ...rest } = data || {};
    useEffect(() => {
        if (visible) {
            setFieldsValue(rest || {});
            return () => {
                resetFields();
            };
        }
    }, [visible]);

    const submit = () => {
        form.validateFields((errors, formData) => {
            if (!errors) {
                setLoading(true);
                if (uuid) {
                    formData.uuid = uuid;
                }
                if (graphJson) {
                    formData.graphJson = JSON.stringify(graphJson);
                }
                let serviceType = '';
                switch (titleType) {
                    case 'update':
                        serviceType = 'updateSubWorkFlow';
                        break;
                    case 'add':
                    default:
                        serviceType = 'addSubWorkFlow';
                        break;
                }

                service[serviceType](formData)
                    .then((res) => {
                        if (res?.success) {
                            search && search();
                            message.success(res?.message || I18N.template(I18N.modal.addmodify.tITLE3, { val1: title }));
                            onCancel && onCancel();
                        } else {
                            message.error(res?.message || I18N.template(I18N.modal.addmodify.tITLE2, { val1: title }));
                        }
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.modal.addmodify.quXiao}
        </Button>,
        <Button type="primary" onClick={submit} loading={loading} key="ok">
            {I18N.modal.addmodify.queDing}
        </Button>
    ];
    return (
        <Modal
            zIndex={1003}
            width={otp.modal.width}
            maskClosable={false}
            title={I18N.template(I18N.modal.addmodify.tITLE, { val1: title })}
            visible={visible}
            onCancel={onCancel}
            footer={footerDom}>
            <Form {...otp.modal.formItemLayout}>
                <Form.Item label={I18N.modal.addmodify.liuChengMuBanMing2}>
                    {getFieldDecorator('displayName', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.addmodify.qingTianXieLiuCheng2
                            },
                            {
                                max: 200,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu2
                            },
                            {
                                pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                message: I18N.modal.addmodify.liuChengMuBanMing
                            }
                        ]
                    })(<Input placeholder={I18N.modal.addmodify.jinZhiChiZhongYing} />)}
                </Form.Item>
                <Form.Item label={I18N.modal.addmodify.liuChengMuBanBiao2}>
                    {getFieldDecorator('code', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.addmodify.qingTianXieLiuCheng
                            },
                            {
                                max: 200,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu2
                            },
                            {
                                pattern: /^[a-zA-Z0-9\_]+$/,
                                message: I18N.modal.addmodify.liuChengMuBanBiao
                            }
                        ]
                    })(<Input disabled={uuid && !['copy'].includes(titleType)} placeholder={I18N.modal.addmodify.qingTianXieLiuCheng} />)}
                </Form.Item>
                <Form.Item label={I18N.modal.addmodify.miaoShu}>
                    {getFieldDecorator('description', {
                        rules: [
                            {
                                max: 2000,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu
                            }
                        ]
                    })(<TextArea style={{ marginTop: '6px' }} placeholder={I18N.modal.addmodify.qingTianXieMiaoShu} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(AddModifyModal);
