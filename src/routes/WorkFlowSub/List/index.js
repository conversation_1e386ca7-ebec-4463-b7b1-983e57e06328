import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { TableContainer, Button, message, Popconfirm, QueryListScene, Ellipsis, HandleIcon, Icon } from 'tntd';
import history from '@/utils/history';
import QueryListWrapper from '@/components/QueryListWrapper';
import service from '../service';
import AddModify from './Modal/AddModify';
import otp from '../otp';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const SubList = () => {
    const [addModifyData, setAddModifyData] = useState();

    // 查询
    const query = (params) => {
        const { current, obj, total, pageSize = 10, ...rest } = params;
        const dParams = {
            curPage: current,
            pageSize,
            ...(obj || {}),
            ...rest
        };

        return service.workflowTemplateList(dParams).then(({ data }) => ({
            ...data,
            data: data.contents || [],
            current: data.curPage,
            pageSize: data.pageSize
        }));
    };

    // 删除
    const del = (record) => {
        service.subWorkFlowDel({ uuid: record.uuid }).then((res) => {
            if (res?.success) {
                message.success(res?.message || I18N.list.index.shanChuChengGong);
                actions.search({ current: 1 });
            } else {
                message.error(res?.message || I18N.list.index.shanChuShiBai);
            }
        });
    };

    const columns = [
        {
            title: I18N.list.index.liuChengMuBanMing,
            dataIndex: 'displayName',
            width: 300,
            ellipsis: true,
            render: (displayName, record) => {
                return <Ellipsis key={displayName} title={displayName || '- -'} />;
            }
        },
        {
            title: I18N.list.index.liuChengMuBanBiao,
            dataIndex: 'code',
            width: 300,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.miaoShu,
            dataIndex: 'description',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.chuangJianRen,
            dataIndex: 'creator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return text || '- -';
            }
        },
        {
            title: I18N.list.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return text || '- -';
            }
        },
        {
            title: I18N.list.index.caoZuo,
            width: 150,
            fixed: 'right',
            render: (record) => {
                return (
                    <HandleIcon>
                        <HandleIcon.Item title={I18N.list.index.bianPai}>
                            <Icon
                                type="bianpai"
                                onClick={() => {
                                    history.push(`/handle/workflow/sub/opera/edit?uuid=${record.uuid}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    history.push(`/handle/workflow/sub/opera/view?uuid=${record.uuid}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.xiuGai}>
                            <Icon
                                type="form"
                                onClick={() => {
                                    setAddModifyData({
                                        ...record,
                                        titleType: 'update',
                                        title: I18N.list.index.xiuGai
                                    });
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.shanChu}>
                            <Popconfirm
                                placement="topLeft"
                                title={
                                    // eslint-disable-next-line prettier/prettier
                                 <div>{I18N.list.index.queRenShanChu}<span style={{ display: 'inline-block', maxWidth: 150 }}><Ellipsis title={record?.displayName} widthLimit={150} /></span>{I18N.list.index.ma}</div>
                                }
                                overlayStyle={{
                                    width: 280
                                }}
                                onConfirm={() => {
                                    del(record);
                                }}>
                                <Icon type="delete" />
                            </Popconfirm>
                        </HandleIcon.Item>
                    </HandleIcon>
                );
            }
        }
    ];

    return (
        <QueryListScene query={query} memory actions={actions}>
            <QueryForm
                extraActions={
                    <>
                        <Button
                            onClick={() => {
                                setAddModifyData({
                                    titleType: 'add',
                                    title: I18N.list.index.xinZeng
                                });
                            }}
                            icon="plus"
                            type="primary">
                            {I18N.list.index.xinZeng}
                        </Button>
                    </>
                }>
                <Field
                    type="selectInput"
                    name="obj"
                    props={{
                        placeholder: I18N.list.index.shuRuSouSuoNei,
                        style: {
                            width: otp.list.selectInputWidth1
                        },
                        onChange: (vals) => {
                            if (!vals.displayName && !vals.code) {
                                const allVals = actions.getFormData();
                                const { obj, ...rest } = allVals;
                                actions.setFormData({ ...rest });
                            }
                        },
                        onPressEnter: (e) => {
                            const vals = actions.getFormData();
                            actions.search({ ...vals, current: 1 });
                        },
                        addonBeforeStyle: {
                            width: otp.list.selectInputWidth2
                        },
                        options: [
                            { label: I18N.list.index.liuChengMuBanMing, value: 'displayName' },
                            { label: I18N.list.index.liuChengMuBanBiao, value: 'code' }
                        ]
                    }}
                />
            </QueryForm>
            <QueryList columns={columns} rowKey="uuid" scroll={{ x: 1700 }} />
            <AddModify
                visible={!!addModifyData}
                data={addModifyData}
                search={actions.search}
                onCancel={() => {
                    setAddModifyData();
                }}
            />
        </QueryListScene>
    );
};

export default QueryListWrapper(TableContainer(SubList), actions);
