import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Spin, message, Tag, Ellipsis } from 'tntd';
import InfoSection, { InfoTitle } from '@/components/InfoSection';
import WorkFlowDetail from '@/components/WorkFlowDetail';
import { getUrlKey } from '@/utils/utils';
import service from '../service';

import './index.less';

export default (props) => {
    const { match = {} } = props;
    const { type } = match?.params || {};
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState();
    const uuid = getUrlKey('uuid');

    const getDetail = () => {
        service
            .getWorkflowTemplateDetail({ uuid })
            .then((res) => {
                setData(res?.data || {});
            })
            .finally(() => {
                setLoading(false);
            });
    };

    useEffect(() => {
        setLoading(true);
        getDetail();
    }, []);

    const save = async (graphJson) => {
        return service
            .updateSubWorkFlow({
                uuid,
                displayName: data?.displayName,
                description: data?.description,
                graphJson: JSON.stringify(graphJson)
            })
            .then((res) => {
                if (res.success) {
                    getDetail();
                    message.success(res?.message || I18N.detail.index.bianJiChengGong);
                } else {
                    message.error(res?.message || I18N.detail.index.bianJiShiBai);
                }
            });
    };

    return (
        <div className="page-global-body">
            <Spin spinning={loading}>
                <div className="workflow-base-head">
                    <Ellipsis
                        widthLimit={500}
                        style={{ alignItems: 'center' }}
                        prefix={<Tag color="#5D7092">{I18N.detail.index.liuChengMuBan}</Tag>}
                        title={data?.displayName || I18N.detail.index.liuChengMuBan}
                        key={data?.displayName}
                    />
                </div>
                <InfoSection className="workflow-base-contain">
                    <InfoTitle title={I18N.detail.index.liuChengXiangQing} />
                    <WorkFlowDetail
                        graphData={data?.graphJson || {}}
                        type={type}
                        flowType="child"
                        operateGroup={[
                            {
                                key: 'save',
                                name: I18N.detail.index.baoCun,
                                type: 'primary',
                                click: save,
                                clickType: 'async',
                                permission: true
                            }
                        ]}
                    />
                </InfoSection>
            </Spin>
        </div>
    );
};
