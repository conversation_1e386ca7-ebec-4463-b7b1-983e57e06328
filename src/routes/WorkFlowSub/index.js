import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import { PageContainer } from 'tntd';
import Detail from './Detail';
import List from './List';

export default PageContainer(
    BreadCrumb(() => {
        return (
            <Switch>
                <Route name={I18N.workflowsub.index.liuChengMuBanXiang} exact component={Detail} path="/handle/workflow/sub/opera/:type" />
                <Route name={I18N.workflowsub.index.liuChengMuBan} component={List} path="/" />
            </Switch>
        );
    })
);
