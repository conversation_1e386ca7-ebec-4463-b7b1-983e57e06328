import { createOtp } from '@/utils/I18N';
export default createOtp({
    cn: {
        list: {
            selectInputWidth1: 320,
            selectInputWidth2: 150
        },
        modal: {
            width: 520,
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 6 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 16 }
                }
            }
        },
        columns: {
            action: 210
        }
    },
    en: {
        list: {
            selectInputWidth1: 400,
            selectInputWidth2: 200
        },
        modal: {
            width: 720,
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 8 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 16 }
                }
            }
        },
        columns: {
            action: 100
        }
    }
});
