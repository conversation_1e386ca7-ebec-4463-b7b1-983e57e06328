import request, { PostForm, downloadReport } from '@/utils/request';
import { getUrl, deleteEmptyObjItem } from '@/services/common';
// 工作流列表
const getWorkFlowList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflow/page', param),
        {
            method: 'GET'
        },
        true
    );
};

// 新增工作流
const addWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflow/add',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 修改工作流
const updateWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflow/update',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 查看工作流详情
const getWorkFlowDetail = async (param) => {
    return request(
        getUrl('/bridgeApi/workflow/detail', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取工作流状态列表
const statusDict = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/dict/config', param),
        {
            method: 'GET'
        },
        true
    );
};

// 删除工作流
const workFlowDel = async (params) => {
    return request(
        '/bridgeApi/workflow/delete',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 复制工作流
const copyFlow = async (params) => {
    return request(
        '/bridgeApi/workflow/copy',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 导入工作流
const importWorkFlow = async (params) => {
    return PostForm('/bridgeApi/workflow/import', 'POST', deleteEmptyObjItem(params), null, null, true);
};

// 导出工作流
const exportWorkFlow = async (params) => {
    return downloadReport(
        getUrl('/bridgeApi/workflow/export', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        undefined,
        undefined,
        true
    );
};

// 工作流上线
const publishWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflow/publish',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 工作流下线
const offlineWorkFlow = async (params) => {
    return request(
        '/bridgeApi/workflow/offline',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 查看工作流列表
const versionList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflow/version/page', param),
        {
            method: 'GET'
        },
        true
    );
};

// 查看版本详情
const getVersionDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/workflow/version/detail', params),
        {
            method: 'GET'
        },
        true
    );
};

// 覆盖版本
const changeVersion = async (params) => {
    return request(
        '/bridgeApi/workflow/version/cover',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

export default {
    getWorkFlowList,
    addWorkFlow,
    updateWorkFlow,
    getWorkFlowDetail,
    statusDict,
    workFlowDel,
    copyFlow,
    importWorkFlow,
    exportWorkFlow,
    publishWorkFlow,
    offlineWorkFlow,
    versionList,
    getVersionDetail,
    changeVersion
};
