import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import { PageContainer } from 'tntd';
import Detail from './Detail';
import VersionList from './VersionList';
import List from './List';

export default PageContainer(
    BreadCrumb(() => {
        return (
            <Switch>
                <Route name={I18N.workflow.index.xiangQing} exact component={Detail} path="/handle/workflow/arrange/opera/:type" />
                <Route name={I18N.workflow.index.banBenLiShi} exact component={VersionList} path="/handle/workflow/arrange/versionList" />
                <Route name={I18N.workflow.index.gongZuoLiu} component={List} path="/" />
            </Switch>
        );
    })
);
