import I18N from '@/utils/I18N';
export const graphJson = {
    'flowLineDefinitions': [
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '3cf64890-b66c-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '20b8c7d0b66b11ed8de209f5ceb428c1',
            'targetNodeId': '3a92dd20b66c11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {
                'ratio': 50 //分流比例50%
            },
            'fromPoint': '2',
            'id': '422975d0-b66c-11ed-8de2-09f5ceb428c1',
            'lineType': 'RouteConditionLine', //线类型：智能路由条件
            'name': 'first', //名称
            'sourceNodeId': '3a92dd20b66c11ed8de209f5ceb428c1',
            'targetNodeId': '4007f560b66c11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {
                'ratio': 50 //分流比例50%
            },
            'fromPoint': '2',
            'id': '2f2adf30-b66e-11ed-8de2-09f5ceb428c1',
            'lineType': 'RouteConditionLine',
            'name': 'second',
            'sourceNodeId': '3a92dd20b66c11ed8de209f5ceb428c1',
            'targetNodeId': '2b6cecd0b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '3acee9d0-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '2b6cecd0b66e11ed8de209f5ceb428c1',
            'targetNodeId': '392ef570b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '3c9cf310-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '2b6cecd0b66e11ed8de209f5ceb428c1',
            'targetNodeId': '3a02c030b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '4f5e6ec0-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '392ef570b66e11ed8de209f5ceb428c1',
            'targetNodeId': '4e564d90b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '5090fb50-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '3a02c030b66e11ed8de209f5ceb428c1',
            'targetNodeId': '4e564d90b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '570ed100-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '4e564d90b66e11ed8de209f5ceb428c1',
            'targetNodeId': '55d58db0b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {
                'isDefault': true
            },
            'fromPoint': '2',
            'id': '618976d0-b66e-11ed-8de2-09f5ceb428c1',
            'lineType': 'ExclusiveConditionLine',
            'name': I18N.detail.graphjson.moRenDiaoYong,
            'sourceNodeId': '55d58db0b66e11ed8de209f5ceb428c1',
            'targetNodeId': '60763210b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '6ebc85e0-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '60763210b66e11ed8de209f5ceb428c1',
            'targetNodeId': 'b3cdc190b66d11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {
                'priority': 2,
                'fieldList': [],
                'condition': '{"logicOperator":"&&","type":"context","children":[{"property":"S_S_ENTRYID","op":"include","value":"loan","propertyDataType":"STRING","type":"context","rightValueType":"input","priority":1,"enumTypeValues":[]}]}',
                'ruleUuid': '563b9a95b9b242c2a3ebe8ed39b79904'
            },
            'fromPoint': '2',
            'id': '88a78090-b66e-11ed-8de2-09f5ceb428c1',
            'lineType': 'ExclusiveConditionLine',
            'name': I18N.detail.graphjson.xinDai,
            'sourceNodeId': '55d58db0b66e11ed8de209f5ceb428c1',
            'targetNodeId': '81d681d0b66e11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '9a8504e0-b66e-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '81d681d0b66e11ed8de209f5ceb428c1',
            'targetNodeId': 'b3cdc190b66d11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '1a02f560-b66f-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '4007f560b66c11ed8de209f5ceb428c1',
            'targetNodeId': 'b87f6ec71f8f430eaf259157d328dbce',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '1e128a30-b66f-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': 'b87f6ec71f8f430eaf259157d328dbce',
            'targetNodeId': '1c9d0130b66f11ed8de209f5ceb428c1',
            'toPoint': '0'
        },
        {
            'attributes': {},
            'fromPoint': '2',
            'id': '24ce7a50-b66f-11ed-8de2-09f5ceb428c1',
            'sourceNodeId': '1c9d0130b66f11ed8de209f5ceb428c1',
            'targetNodeId': 'b3cdc190b66d11ed8de209f5ceb428c1',
            'toPoint': '0'
        }
    ],
    'flowNodeDefinitions': [
        {
            'attributes': {},
            'id': '20b8c7d0b66b11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.kaiShi,
            'nodeType': 'StartFlowNode',
            'outgoingFields': [],
            'x': '292',
            'y': '-168'
        },
        {
            'attributes': {
                'type': '1',
                'shuntType': '1'
            },
            'id': '3a92dd20b66c11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.zhiNengLuYou,
            'nodeType': 'RouteServiceNode',
            'outgoingFields': [],
            'x': '265',
            'y': '-67'
        },
        {
            'attributes': {
                'thirdServiceCode': 'test001',
                'thirdServiceName': I18N.detail.graphjson.renXingGeRen,
                'exceptionTerminate': false,
                'featureCodes': [],
                'async': false,
                'originInput': {
                    'fileName': 'C_S_PBID',
                    'type': 'C_N_PBTYPE'
                },
                'originOutput': {
                    'c': 'C_S_RESPONSE'
                }
            },
            'id': '4007f560b66c11ed8de209f5ceb428c1',
            'incomingFields': [
                {
                    'fieldName': 'C_S_PBID',
                    'fieldType': 'field',
                    'mappingName': 'fileName'
                },
                {
                    'fieldName': 'C_N_PBTYPE',
                    'fieldType': 'field',
                    'mappingName': 'type'
                }
            ],
            'name': I18N.detail.graphjson.renXingGeRen,
            'nodeType': 'FeatureServiceNode',
            'outgoingFields': [
                {
                    'fieldName': 'C_S_RESPONSE',
                    'fieldType': 'field',
                    'mappingName': 'c'
                },
                {
                    'fieldName': 'S_S_METRICRESULT',
                    'fieldType': 'field',
                    'mappingName': 'responseCode'
                }
            ],
            'x': '602',
            'y': '244'
        },
        {
            'attributes': {},
            'id': 'b3cdc190b66d11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.jieShu,
            'nodeType': 'EndFlowNode',
            'outgoingFields': [],
            'x': '460',
            'y': '565'
        },
        {
            'attributes': {
                'type': 'start'
            },
            'id': '2b6cecd0b66e11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.bingXingKaiShi,
            'nodeType': 'ParallelGateway',
            'outgoingFields': [],
            'x': '287',
            'y': '36'
        },
        {
            'attributes': {
                'thirdServiceCode': 'test001',
                'thirdServiceName': I18N.detail.graphjson.renXingGeRen,
                'exceptionTerminate': false,
                'featureCodes': [],
                'async': false,
                'originInput': {
                    'fileName': 'C_S_PBID',
                    'type': 'C_N_PBTYPE'
                },
                'originOutput': {
                    'c': 'C_S_RESPONSE'
                }
            },
            'id': '392ef570b66e11ed8de209f5ceb428c1',
            'incomingFields': [
                {
                    'fieldName': 'C_S_PBID',
                    'fieldType': 'field',
                    'mappingName': 'fileName'
                },
                {
                    'fieldName': 'C_N_PBTYPE',
                    'fieldType': 'field',
                    'mappingName': 'type'
                }
            ],
            'name': I18N.detail.graphjson.renXingGeRen,
            'nodeType': 'FeatureServiceNode',
            'outgoingFields': [
                {
                    'fieldName': 'C_S_RESPONSE',
                    'fieldType': 'field',
                    'mappingName': 'c'
                },
                {
                    'fieldName': 'S_S_METRICRESULT',
                    'fieldType': 'field',
                    'mappingName': 'responseCode'
                }
            ],
            'x': '342',
            'y': '160'
        },
        {
            'attributes': {
                'thirdServiceCode': 'china_people_bank_enterprise',
                'thirdServiceName': I18N.detail.graphjson.renXingQiYe,
                'exceptionTerminate': false,
                'featureCodes': [],
                'async': false,
                'originInput': {
                    'fileName': 'C_S_TAXPAYERIDENTIFICATION'
                },
                'originOutput': {
                    'c': 'C_S_RESPONSE'
                }
            },
            'id': '3a02c030b66e11ed8de209f5ceb428c1',
            'incomingFields': [
                {
                    'fieldName': 'C_S_TAXPAYERIDENTIFICATION',
                    'fieldType': 'field',
                    'mappingName': 'fileName'
                }
            ],
            'name': I18N.detail.graphjson.renXingQiYe,
            'nodeType': 'FeatureServiceNode',
            'outgoingFields': [
                {
                    'fieldName': 'C_S_RESPONSE',
                    'fieldType': 'field',
                    'mappingName': 'c'
                },
                {
                    'fieldName': 'C_F_QYPFKFS',
                    'fieldType': 'field',
                    'mappingName': 'responseCode'
                }
            ],
            'x': '80',
            'y': '152'
        },
        {
            'attributes': {
                'type': 'end'
            },
            'id': '4e564d90b66e11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.bingXingJieShu,
            'nodeType': 'ParallelGateway',
            'outgoingFields': [],
            'x': '287',
            'y': '244'
        },
        {
            'attributes': {
                'type': 'start'
            },
            'id': '55d58db0b66e11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.panDuanKaiShi,
            'nodeType': 'ExclusiveGateway',
            'outgoingFields': [],
            'x': '287',
            'y': '360'
        },
        {
            'attributes': {
                'thirdServiceCode': 'td_modelindex',
                'thirdServiceName': I18N.detail.graphjson.tongDunDaiQianShen,
                'exceptionTerminate': false,
                'featureCodes': [],
                'async': false,
                'originInput': {
                    'account_name': 'C_S_ACCOUNTNAME',
                    'account_mobile': 'C_S_PHONENUM',
                    'id_number': 'C_S_IDNUM'
                },
                'originOutput': {
                    'error_info': 'C_S_ERRINFO',
                    'reason_desc': 'C_S_REASONDESC',
                    'indicatrix': 'C_S_INDICATRIX',
                    'reason_code': 'C_S_REASONCODE'
                }
            },
            'id': '60763210b66e11ed8de209f5ceb428c1',
            'incomingFields': [
                {
                    'fieldName': 'C_S_ACCOUNTNAME',
                    'fieldType': 'field',
                    'mappingName': 'account_name'
                },
                {
                    'fieldName': 'C_S_PHONENUM',
                    'fieldType': 'field',
                    'mappingName': 'account_mobile'
                },
                {
                    'fieldName': 'C_S_IDNUM',
                    'fieldType': 'field',
                    'mappingName': 'id_number'
                }
            ],
            'name': I18N.detail.graphjson.tongDunDaiQianShen,
            'nodeType': 'FeatureServiceNode',
            'outgoingFields': [
                {
                    'fieldName': 'C_S_ERRINFO',
                    'fieldType': 'field',
                    'mappingName': 'error_info'
                },
                {
                    'fieldName': 'C_S_REASONDESC',
                    'fieldType': 'field',
                    'mappingName': 'reason_desc'
                },
                {
                    'fieldName': 'C_S_INDICATRIX',
                    'fieldType': 'field',
                    'mappingName': 'indicatrix'
                },
                {
                    'fieldName': 'C_S_REASONCODE',
                    'fieldType': 'field',
                    'mappingName': 'reason_code'
                },
                {
                    'fieldName': 'S_S_CUSTSTS',
                    'fieldType': 'field',
                    'mappingName': 'responseCode'
                }
            ],
            'x': '264',
            'y': '464'
        },
        {
            'attributes': {
                'thirdServiceCode': 'score_card_credit_st',
                'thirdServiceName': I18N.detail.graphjson.xinYongKaKeQun,
                'exceptionTerminate': false,
                'featureCodes': [],
                'async': false,
                'originInput': {
                    'occ_orgn': 'C_S_OCCORGN',
                    'certtype': 'C_S_CERTTYPE',
                    'mobile': 'C_S_MOBILE',
                    'cert_name': 'C_S_CERTNAME',
                    'qry_rsn': 'C_S_QRYRSN',
                    'cert_no': 'C_S_CERTNO'
                },
                'originOutput': {
                    'flag': 'C_S_FLAG',
                    'psce_cc_score': 'C_F_PSCECCSCORE'
                }
            },
            'id': '81d681d0b66e11ed8de209f5ceb428c1',
            'incomingFields': [
                {
                    'fieldName': 'C_S_OCCORGN',
                    'fieldType': 'field',
                    'mappingName': 'occ_orgn'
                },
                {
                    'fieldName': 'C_S_CERTTYPE',
                    'fieldType': 'field',
                    'mappingName': 'certtype'
                },
                {
                    'fieldName': 'C_S_MOBILE',
                    'fieldType': 'field',
                    'mappingName': 'mobile'
                },
                {
                    'fieldName': 'C_S_CERTNAME',
                    'fieldType': 'field',
                    'mappingName': 'cert_name'
                },
                {
                    'fieldName': 'C_S_QRYRSN',
                    'fieldType': 'field',
                    'mappingName': 'qry_rsn'
                },
                {
                    'fieldName': 'C_S_CERTNO',
                    'fieldType': 'field',
                    'mappingName': 'cert_no'
                }
            ],
            'name': I18N.detail.graphjson.xinYongKaKeQun,
            'nodeType': 'FeatureServiceNode',
            'outgoingFields': [
                {
                    'fieldName': 'C_S_FLAG',
                    'fieldType': 'field',
                    'mappingName': 'flag'
                },
                {
                    'fieldName': 'C_F_PSCECCSCORE',
                    'fieldType': 'field',
                    'mappingName': 'psce_cc_score'
                },
                {
                    'fieldName': 'C_F_QYZCZB',
                    'fieldType': 'field',
                    'mappingName': 'responseCode'
                }
            ],
            'x': '421',
            'y': '464'
        },
        {
            'attributes': {},
            //流程模板边定义
            'flowLineDefinitions': [
                {
                    'attributes': {},
                    'fromPoint': '1',
                    'id': '201d149d-baab-41b4-84c5-b52efa475bec',
                    'sourceNodeId': '4a8aa1d77e854d968b5a91d68c0bb9a9',
                    'targetNodeId': 'b0a2cf5925da46959ce9ea11c7f6e245',
                    'toPoint': '3'
                },
                {
                    'attributes': {},
                    'fromPoint': '1',
                    'id': 'fdd421cf-ab1a-43d8-847b-c542ef59dd04',
                    'sourceNodeId': 'b0a2cf5925da46959ce9ea11c7f6e245',
                    'targetNodeId': '6e0e521ec3f74aee8ab1909126bf8ffd',
                    'toPoint': '3'
                }
            ],
            //流程模板节点定义
            'flowNodeDefinitions': [
                {
                    'attributes': {},
                    'id': '4a8aa1d77e854d968b5a91d68c0bb9a9',
                    'incomingFields': [],
                    'name': I18N.detail.graphjson.kaiShi,
                    'nodeType': 'StartFlowNode',
                    'outgoingFields': [],
                    'x': '317',
                    'y': '239'
                },
                {
                    'attributes': {
                        'thirdServiceCode': 'score_card_credit_st',
                        'thirdServiceName': I18N.detail.graphjson.xinYongKaKeQun,
                        'exceptionTerminate': false,
                        'featureCodes': [],
                        'async': false,
                        'originInput': {
                            'occ_orgn': 'C_S_OCCORGN',
                            'certtype': 'C_S_CERTTYPE',
                            'mobile': 'C_S_MOBILE',
                            'cert_name': 'C_S_CERTNAME',
                            'qry_rsn': 'C_S_QRYRSN',
                            'cert_no': 'C_S_CERTNO'
                        },
                        'originOutput': {
                            'flag': 'C_S_FLAG',
                            'psce_cc_score': 'C_F_PSCECCSCORE'
                        }
                    },
                    'id': 'b0a2cf5925da46959ce9ea11c7f6e245',
                    'incomingFields': [
                        {
                            'fieldName': 'C_S_OCCORGN',
                            'fieldType': 'field',
                            'mappingName': 'occ_orgn'
                        },
                        {
                            'fieldName': 'C_S_CERTTYPE',
                            'fieldType': 'field',
                            'mappingName': 'certtype'
                        },
                        {
                            'fieldName': 'C_S_MOBILE',
                            'fieldType': 'field',
                            'mappingName': 'mobile'
                        },
                        {
                            'fieldName': 'C_S_CERTNAME',
                            'fieldType': 'field',
                            'mappingName': 'cert_name'
                        },
                        {
                            'fieldName': 'C_S_QRYRSN',
                            'fieldType': 'field',
                            'mappingName': 'qry_rsn'
                        },
                        {
                            'fieldName': 'C_S_CERTNO',
                            'fieldType': 'field',
                            'mappingName': 'cert_no'
                        }
                    ],
                    'name': I18N.detail.graphjson.moXingYuJing,
                    'nodeType': 'FeatureServiceNode',
                    'outgoingFields': [
                        {
                            'fieldName': 'C_S_FLAG',
                            'fieldType': 'field',
                            'mappingName': 'flag'
                        },
                        {
                            'fieldName': 'C_F_PSCECCSCORE',
                            'fieldType': 'field',
                            'mappingName': 'psce_cc_score'
                        },
                        {
                            'fieldName': 'C_F_QYZCZB',
                            'fieldType': 'field',
                            'mappingName': 'responseCode'
                        }
                    ],
                    'x': '493',
                    'y': '251'
                },
                {
                    'attributes': {},
                    'id': '6e0e521ec3f74aee8ab1909126bf8ffd',
                    'incomingFields': [],
                    'name': I18N.detail.graphjson.jieShu,
                    'nodeType': 'EndFlowNode',
                    'outgoingFields': [],
                    'x': '737',
                    'y': '215'
                }
            ],
            'id': 'b87f6ec71f8f430eaf259157d328dbce',
            'incomingFields': [],
            'name': I18N.detail.graphjson.liuChengMuBan,
            'nodeType': 'SubDecisionFlowNode',
            'outgoingFields': [],
            'x': '602',
            'y': '360'
        },
        {
            'attributes': {
                'serviceCode': 'sbhbc'
            },
            'id': '1c9d0130b66f11ed8de209f5ceb428c1',
            'incomingFields': [],
            'name': I18N.detail.graphjson.suiBianHuaJueCe,
            'nodeType': 'SuspendFlowNode',
            'outgoingFields': [],
            'x': '598',
            'y': '464'
        }
    ]
}
