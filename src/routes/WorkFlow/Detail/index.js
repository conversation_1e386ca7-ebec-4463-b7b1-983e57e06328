import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Tag, Divider, Spin, Row, message, Ellipsis } from 'tntd';
import InfoSection, { InfoTitle } from '@/components/InfoSection';
import WorkFlowDetail from '@/components/WorkFlowDetail';
import Publish from '@/routes/WorkFlow/List/Tab2/Modal/Publish';
import { statusColorMap } from '@/constants/workflow';
import { colorRgb, getUrlKey } from '@/utils/utils';
import service from '../../WorkFlow/service';
import { ReferenceOnlineCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
// import { graphJson } from './graphJson';

import './index.less';

export default (props) => {
    const { match = {} } = props;
    const { type } = match?.params || {};
    const [statusDict, setStatusDict] = useState([]);
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState();
    const [publishData, setPublishData] = useState();
    const uuid = getUrlKey('uuid');
    const areaType = getUrlKey('currentTab') || '2';

    const isPublish = data?.status === 3;
    const getDetail = () => {
        service
            .getWorkFlowDetail({ uuid, areaType })
            .then((res) => {
                // res.data.graphData = JSON.stringify(graphJson);
                setData(res?.data || {});
            })
            .finally(() => {
                setLoading(false);
            });
    };

    useEffect(() => {
        setLoading(true);
        service.statusDict().then((res) => {
            setStatusDict(res?.data?.workflowStatus || {});
        });
        getDetail();
    }, []);

    const save = (graphJson, callback) => {
        return service
            .updateWorkFlow({
                uuid,
                displayName: data?.displayName,
                description: data?.description,
                graphJson: JSON.stringify(graphJson)
            })
            .then((res) => {
                if (res.success) {
                    getDetail();
                    if (callback) {
                        return callback(
                            res?.data || {
                                uuid,
                                displayName: data?.displayName,
                                description: data?.description,
                                graphJson: JSON.stringify(graphJson)
                            }
                        );
                    }
                    message.success(res?.message || I18N.detail.index.bianJiChengGong);
                } else {
                    message.error(res?.message || I18N.detail.index.bianJiShiBai);
                }
            });
    };

    const color = statusColorMap[data?.status]?.color || '#126BFB';
    return (
        <div className="page-global-body">
            <Spin spinning={loading}>
                <div className="workflow-base-head">
                    <Ellipsis
                        widthLimit={500}
                        title={data?.displayName || I18N.detail.index.gongZuoLiuMingCheng}
                        style={{ alignItems: 'center', maxWidth: 300 }}
                        prefix={<Tag color="#5D7092">{I18N.detail.index.gongZuoLiu}</Tag>}
                        suffix={
                            <Row type="flex" align="middle" style={{ flexFlow: 'row' }}>
                                {!!data?.version && (
                                    <>
                                        <Divider type="vertical" style={{ background: '#b2becd' }} />
                                        <span style={{ width: 70 }}>
                                            {I18N.detail.index.banBenV}
                                            {data?.version}
                                        </span>
                                    </>
                                )}
                                <>
                                    <Divider type="vertical" style={{ background: '#b2becd' }} />
                                    <Tag
                                        color={colorRgb(color, 0.1)}
                                        style={{
                                            border: `1px solid ${color}`,
                                            color
                                        }}>
                                        {statusDict?.[data?.status]}
                                    </Tag>
                                </>
                            </Row>
                        }
                        key={data?.displayName}
                    />
                </div>
                <InfoSection className="workflow-base-contain">
                    <InfoTitle title={I18N.detail.index.liuChengXiangQing} />
                    <WorkFlowDetail
                        graphData={data?.graphJson || {}}
                        type={type}
                        operateGroup={[
                            {
                                key: 'save',
                                name: I18N.detail.index.baoCun,
                                click: save,
                                clickType: 'async',
                                permission: true
                            },
                            {
                                key: 'publish',
                                name: I18N.detail.index.faBu,
                                type: 'primary',
                                clickType: 'async',
                                click: (graphJson) => {
                                    return ReferenceOnlineCheck({
                                        rq: () => {
                                            return referenceAPI.onlineValidate({
                                                componentType: 'DATASOURCE_WORKFLOW',
                                                componentIds: data?.code
                                            });
                                        }
                                    }).then(() => {
                                        return save(graphJson, (resData) => {
                                            return setPublishData(resData);
                                        });
                                    });
                                },
                                tip: isPublish && I18N.detail.index.dangQianPeiZhiMei,
                                permission: !isPublish
                            }
                        ]}
                    />
                </InfoSection>
            </Spin>
            <Publish
                visible={!!publishData}
                data={publishData}
                search={() => {
                    getDetail();
                }}
                onCancel={() => {
                    setPublishData();
                }}
            />
        </div>
    );
};
