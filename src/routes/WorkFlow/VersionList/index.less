.version-list-wrap{
    height:calc(100vh - 90px);
    overflow-y: auto;
    .tnt-querylistscene-content {
        .tnt-current-v3 & {
            padding: 0;
        }
    }
    .task-flow-version{
        border: 1px solid #E1E6EE;
        .ant-table-expand-icon-th, .ant-table-row-expand-icon-cell{
            width: 35px;
            min-width: 35px;
            +th{
                padding: 9px 0px !important;
            }
            +td{
                padding-left: 0 !important
            }
        }
        .ant-table-tbody {
            tr {
                td:first-child {
                    .ant-table-row-disabled {
                        opacity: 0.4;
                        cursor: not-allowed;
                    }
                }
                &:last-of-type{
                    td{
                        border-bottom:  0;
                    }
                }
            }
            .ant-table-expanded-row{
                td:first-of-type{
                    display: none;
                }
                td{
                    padding:0 !important
                }
            }
        }
        .version-info{
            margin-left: 12px;
            position: relative;
            width: auto;
            padding: 0 8px;
            font-size: 12px;
            height: 22px;
            line-height: 20px;
            color: #fff;
            text-align: center;
            border: 1px solid #126BFB;
            border-radius: 2px;
            background: #126BFB;
            &::after{
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                top: 5px;
                left: -5px;
                border-top: 4px solid transparent;
                border-bottom: 4px solid transparent;
                border-right: 4px solid #126BFB;
            }
        }

        .version-view--content-wrap{
            background: #FAFAFB;
            box-shadow: inset 0px 0px 8px 0px rgba(209,213,227,1);
            border-radius: 2px;
            padding: 16px;
            .workflow-editor.job-editor{
                height: 450px;
                border: 1px solid #e1e6ee;
            }
        }
    }
}
