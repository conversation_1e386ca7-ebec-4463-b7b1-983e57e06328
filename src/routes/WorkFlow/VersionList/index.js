import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { QueryListScene, Ellipsis, HandleIcon, Icon, Button, Spin, message } from 'tntd';
import QueryListWrapper from '@/components/QueryListWrapper';
import WorkFlowDetail from '../../../components/WorkFlowDetail';
import service from '../service';
import { getUrlKey } from '@/utils/utils';
import './index.less';

const { QueryList, createActions } = QueryListScene;
const actions = createActions();

const VersionList = () => {
    const uuid = getUrlKey('uuid');
    const version = getUrlKey('version');
    const [curVersion, setCurVersion] = useState(version);
    const [switchCurVersion, setSwitchCurVersion] = useState();
    const [versionDetailLoading, setVersionDetailLoading] = useState({});
    const [versionDetail, setVersionDetail] = useState({});
    const colRef = useRef();

    useEffect(() => {
        if (versionDetail?.uuid) {
            colRef.current = setTimeout(() => {
                const tdDom = document.querySelector('[data-row-key=\'' + versionDetail?.uuid + '-extra-row\'] td:last-of-type');
                if (tdDom) {
                    // const colspan = tdDom?.getAttribute('colspan');
                    const colspan = document.querySelectorAll('.task-flow-version .ant-table-thead tr th')?.length;
                    tdDom.setAttribute('colspan', Number(colspan));
                }
            }, 0);
        }
    }, [versionDetail?.uuid]);

    useEffect(() => {
        return () => {
            colRef.current && clearTimeout(colRef.current);
        };
    }, []);

    // 获取版本详情
    const getVersionDetail = (data) => {
        setVersionDetailLoading(true);
        service
            .getVersionDetail({
                uuid: data.uuid
            })
            .then((res) => {
                setVersionDetail(res?.data || {});
            })
            .finally(() => {
                setVersionDetailLoading(false);
            });
    };

    const switchVersionHandle = (record) => {
        setSwitchCurVersion(record);
        service
            .changeVersion({
                uuid: record?.uuid,
                code: record?.code,
                version: record?.version
            })
            .then((res) => {
                if (res?.success) {
                    message.success(I18N.template(I18N.versionlist.index.yiQieHuanDaoBan, { val1: record?.version }));
                    setCurVersion(record?.version);
                    actions.search();
                } else {
                    message.error(res?.message || I18N.versionlist.index.banBenQieHuanShi);
                }
            })
            .finally(() => {
                setSwitchCurVersion(null);
            });
    };
    // 查询
    const query = (params) => {
        const { current, pageSize, ...rest } = params;
        const dParams = {
            curPage: current,
            pageSize: pageSize || 10,
            uuid,
            ...rest
        };

        return service.versionList(dParams).then(({ data }) => ({
            ...data,
            data: data.contents || [],
            current: data.curPage,
            pageSize: data.pageSize
        }));
    };

    const columns = [
        {
            title: I18N.versionlist.index.gongZuoLiuMingCheng,
            dataIndex: 'displayName',
            width: 320,
            ellipsis: true,
            render: (displayName, record) => {
                return (
                    <Ellipsis
                        title={displayName || '- -'}
                        suffix={
                            <span className="version-info">
                                {I18N.versionlist.index.banBenHaoV}
                                {record?.version || ''}
                            </span>
                        }
                    />
                );
            }
        },
        {
            title: I18N.versionlist.index.faBanMiaoShu,
            dataIndex: 'publishDesc',
            width: 160,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.versionlist.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 160,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.versionlist.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 180,
            ellipsis: true,
            render: (text) => {
                return text || '- -';
            }
        },
        {
            title: I18N.versionlist.index.caoZuo,
            dataIndex: 'functionName',
            width: 110,
            render: (text, record) => {
                return (
                    <HandleIcon>
                        {String(record?.version) !== String(curVersion) && (
                            <HandleIcon.Item title={I18N.versionlist.index.fuGaiBianJiQu}>
                                <Icon
                                    type="toggle"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        switchVersionHandle(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                    </HandleIcon>
                );
            }
        }
    ];
    return (
        <div className="version-list-wrap">
            <QueryListScene query={query} memory actions={actions}>
                <QueryList
                    className="task-flow-version"
                    columns={columns}
                    expandRowByClick={true}
                    expandedRowKeys={[versionDetail?.uuid]}
                    expandIcon={(props) => <Icon type={props.expanded ? 'minus-square' : 'plus-square'} />}
                    onExpand={async (expanded, record) => {
                        if (expanded) {
                            getVersionDetail(record);
                        } else {
                            setVersionDetail(null);
                        }
                    }}
                    expandedRowRender={(record) => {
                        return (
                            <div className="version-view--content-wrap">
                                {versionDetailLoading ? (
                                    <Spin className="globalSpin" />
                                ) : (
                                    <WorkFlowDetail
                                        className="version-work-flow-detail"
                                        graphData={versionDetail?.graphJson || {}}
                                        type="view"
                                    />
                                )}
                            </div>
                        );
                    }}
                    rowKey="uuid"
                    scroll={{ x: 1100 }}
                />
            </QueryListScene>
        </div>
    );
};

export default QueryListWrapper(VersionList, actions);
