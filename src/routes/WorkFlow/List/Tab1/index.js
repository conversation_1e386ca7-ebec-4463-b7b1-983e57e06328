import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Tag, Popconfirm, Button, Tooltip, message, QueryListScene, Ellipsis, HandleIcon, Icon } from 'tntd';
// import history from '@/utils/history';
import QueryListWrapper from '@/components/QueryListWrapper';
import service from '../../service';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const Tab1 = (props) => {
    const { areaType, history } = props;
    const [selectedRows, setSelectedRows] = useState([]);

    const [referenceDrawerData, setReferenceDrawerData] = useState(null);

    useEffect(() => {
        if (areaType && areaType === '2') {
            actions.search();
        }
    }, [areaType]);

    // 下线
    const del = ({ uuid, code }) => {
        ReferenceCheck({
            strongMsg: I18N.tab1.index.cunZaiQiangYinYong,
            rq: () =>
                referenceAPI.checkComponentReference({
                    componentType: 'DATASOURCE_WORKFLOW',
                    componentId: code
                })
        }).then(() => {
            service.offlineWorkFlow({ uuid }).then((res) => {
                if (res?.success) {
                    message.success(res?.message || I18N.tab1.index.xiaXianChengGong);
                    actions.search({ current: 1 });
                } else {
                    message.error(res?.message || I18N.tab1.index.xiaXianShiBai);
                }
            });
        });
    };

    // 批量导出
    const batchDown = () => {
        const workflowUuids = selectedRows?.map((v) => v.uuid).join(',');
        service.exportWorkFlow({ workflowUuids }).then((res) => {
            console.log(res);
        });
    };

    // 查询
    const query = (params) => {
        const { current, pageSize = 10, total, obj, ...rest } = params;
        const dParams = {
            areaType,
            pageSize,
            curPage: current,
            ...(obj || {}),
            ...rest
        };

        return service.getWorkFlowList(dParams).then(({ data }) => ({
            ...data,
            data: data.contents || [],
            current: data.curPage,
            pageSize: data.pageSize
        }));
    };

    const columns = [
        {
            title: I18N.tab1.index.gongZuoLiuMingCheng,
            dataIndex: 'displayName',
            width: 300,
            ellipsis: true,
            render: (displayName, record) => {
                return (
                    <Ellipsis key={displayName} prefix={<Tag color="tnt-purple">V{record?.version}</Tag>} title={displayName || '- -'} />
                );
            }
        },
        {
            title: I18N.tab1.index.gongZuoLiuBiaoZhi,
            dataIndex: 'code',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.miaoShu,
            dataIndex: 'description',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.chuangJianRen,
            dataIndex: 'creator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab1.index.caoZuo,
            dataIndex: 'functionName',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <HandleIcon>
                        <HandleIcon.Item title={I18N.tab1.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    history.push(`/handle/workflow/arrange/opera/view?uuid=${record.uuid}&currentTab=${areaType}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab1.index.xiaXian}>
                            <Popconfirm
                                placement="topLeft"
                                title={
                                    // eslint-disable-next-line prettier/prettier
                                <div>{I18N.tab1.index.queRenXiaXian}<span style={{ display: 'inline-block', maxWidth: 150 }}><Ellipsis title={record?.displayName} widthLimit={150} /></span>{I18N.tab1.index.ma}</div>
                                }
                                overlayStyle={{
                                    width: 280
                                }}
                                onConfirm={() => {
                                    del(record);
                                }}>
                                <Icon type="offline" />
                            </Popconfirm>
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab1.index.yinYongGuanXi}>
                            <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
            }
        }
    ];
    return (
        <QueryListScene query={query} memory actions={actions} initSearch={true}>
            <QueryForm
                extraActions={
                    <Tooltip title={I18N.tab1.index.piLiangDaoChu} placement="left">
                        <Button disabled={!selectedRows?.length} onClick={batchDown}>
                            <Icon type="batch-export" />
                        </Button>
                    </Tooltip>
                }>
                <Field
                    type="selectInput"
                    name="obj"
                    props={{
                        placeholder: I18N.tab1.index.shuRuSouSuoNei,
                        onChange: (vals) => {
                            if (!vals.displayName && !vals.code) {
                                const allVals = actions.getFormData();
                                const { obj, ...rest } = allVals;
                                actions.setFormData({ ...rest });
                            }
                        },
                        onPressEnter: (e) => {
                            const vals = actions.getFormData();
                            actions.search({ ...vals, current: 1 });
                        },
                        options: [
                            { label: I18N.tab1.index.gongZuoLiuMingCheng, value: 'displayName' },
                            { label: I18N.tab1.index.gongZuoLiuBiaoZhi, value: 'code' }
                        ]
                    }}
                />
            </QueryForm>
            <QueryList
                columns={columns}
                rowKey="uuid"
                rowSelection={{
                    columnWidth: '35px',
                    selectedRowKeys: selectedRows?.map((v) => v.uuid),
                    onChange: (selectedRowKeys, sRows) => {
                        // 翻页的场景sRows未代入
                        const notMatchUuids = selectedRowKeys.filter((uuid) => {
                            if (!sRows.find((s) => s.uuid === uuid)) {
                                return uuid;
                            }
                        });
                        const sRowsPre = selectedRows.filter((s) => notMatchUuids.includes(s.uuid)) || [];
                        setSelectedRows(sRowsPre.concat(sRows));
                    }
                }}
                scroll={{ x: 1600 }}
            />
            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.code}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_WORKFLOW',
                        componentId: referenceDrawerData.code
                    });
                }}
            />
        </QueryListScene>
    );
};

export default QueryListWrapper(Tab1, actions);
