import { createOtp } from '@/utils/I18N';
export default createOtp({
    cn: {
        labelCol: 5,
        wrapperCol: 18,
        ImportWorkFlow: {
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 7 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 17 }
                }
            }
        },
        publish: {
            formItemLayout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 20 }
            }
        }
    },

    en: {
        labelCol: 9,
        wrapperCol: 14,
        ImportWorkFlow: {
            formItemLayout: {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 9 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 15 }
                }
            }
        },
        publish: {
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 18 }
            }
        }
    }
});
