import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, Button, Form, Upload, Radio, message } from 'tntd';
import service from '../../../service';

import otp from './otp';

const formItemLayout = otp.ImportWorkFlow.formItemLayout;

const ImportWorkFlow = (props) => {
    const { form, onCancel, search, visible } = props;
    const { getFieldDecorator, resetFields } = form;
    const [loading, setLoading] = useState(false);
    const [fileLists, setFileLists] = useState([]);

    useEffect(() => {
        if (visible) {
            return () => {
                resetFields();
                setFileLists([]);
            };
        }
    }, [visible]);

    const submit = () => {
        form.validateFields((errors, formData) => {
            if (!errors) {
                setLoading(true);
                service
                    .importWorkFlow(formData)
                    .then((res) => {
                        if (res?.success) {
                            message.success(res?.message || I18N.modal.importworkflow.daoRuChengGong);
                            search && search();
                            onCancel && onCancel();
                        } else {
                            message.error(res?.message || I18N.modal.importworkflow.daoRuShiBai);
                        }
                    })
                    .finally(() => setLoading(false));
            }
        });
    };

    const normFile = (e) => {
        const { file, fileList } = e;
        const fileName = file.name;
        const fileExt = fileName.replace(/.+\./, '').toLowerCase(); // 后缀名
        // 清除
        if (fileList?.length === 0) {
            return undefined;
        }
        setFileLists([]);
        if (fileExt !== 'wf') {
            // 先判断文件类型
            message.error(I18N.modal.importworkflow.wenJianLeiXingCuo);
            return undefined;
        }
        if (file.size / 1024 / 1024 > 50) {
            // 再判断文件大小
            // 弹出警告
            message.error(I18N.modal.importworkflow.wenJianDaXiaoBu);
            return undefined;
        }
        setFileLists(fileList.slice(-1));
        return file;
    };

    const removeHandle = () => {
        setFileLists([]);
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.modal.importworkflow.quXiao}
        </Button>,
        <Button type="primary" onClick={submit} loading={loading} key="ok">
            {I18N.modal.importworkflow.queDing}
        </Button>
    ];

    return (
        <Modal
            width={960}
            maskClosable={false}
            title={I18N.modal.importworkflow.daoRuGongZuoLiu}
            visible={visible}
            onCancel={onCancel}
            footer={footerDom}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.modal.importworkflow.xuanZeWenJian}>
                    {getFieldDecorator('file', {
                        getValueFromEvent: normFile,
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.importworkflow.qingXuanZeShangChuan
                            }
                        ]
                    })(
                        <Upload beforeUpload={() => false} onRemove={removeHandle} fileList={fileLists} multiple={false}>
                            <Button icon="upload">{I18N.modal.importworkflow.dianJiXuanZeWen}</Button>
                        </Upload>
                    )}
                </Form.Item>
                <Form.Item label={I18N.modal.importworkflow.xiangTongGongZuoLiu}>
                    {getFieldDecorator('importType', {
                        initialValue: 'SKIP',
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.importworkflow.qingXuanZeChuLi
                            }
                        ]
                    })(
                        <Radio.Group>
                            <Radio value="SKIP">{I18N.modal.importworkflow.tiaoGuoXiangTongBiao}</Radio>
                            <Radio value="COVER">{I18N.modal.importworkflow.fuGaiXiangTongBiao}</Radio>
                        </Radio.Group>
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(ImportWorkFlow);
