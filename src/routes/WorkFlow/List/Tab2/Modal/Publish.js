import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, Input, Form, message, Ellipsis } from 'tntd';
import service from '../../../service';
import './Publish.less';
import otp from './otp';

const TextArea = Input.TextArea;

const PublishModal = (props) => {
    const { form, visible, data, onCancel, search } = props;
    const { getFieldDecorator, resetFields } = form;
    const [submitLoading, setSubmitLoading] = useState(false);
    const isBatchPublish = Array.isArray(data) && data?.length;
    const title = isBatchPublish ? I18N.modal.publish.gongZuoLiuPiLiang : I18N.modal.publish.gongZuoLiuShangXian;

    useEffect(() => {
        if (visible) {
            return () => {
                resetFields();
            };
        }
    }, [visible]);

    const onOk = () => {
        form.validateFields(async (errors, formData) => {
            if (!errors) {
                let uuids = [];
                if (isBatchPublish) {
                    uuids = data?.map((v) => v.uuid);
                } else {
                    uuids = [data?.uuid];
                }
                const params = {
                    uuids: JSON.stringify(uuids),
                    ...formData
                };
                setSubmitLoading(true);
                service
                    .publishWorkFlow(params)
                    .then((res) => {
                        if (res?.success) {
                            message.success(res?.message || title + I18N.modal.publish.chengGong);
                            search && search();
                            onCancel && onCancel();
                        } else {
                            message.error(res?.message || title + I18N.modal.publish.shiBai);
                        }
                    })
                    .finally(() => {
                        setSubmitLoading(false);
                    });
            }
        });
    };

    return (
        <Modal title={title} visible={visible} maskClosable={true} confirmLoading={submitLoading} onOk={onOk} onCancel={onCancel}>
            <Form {...otp.publish.formItemLayout}>
                <Form.Item label={I18N.modal.publish.gongZuoLiuMingCheng}>
                    {!!isBatchPublish ? (
                        <ul className="workflow-publish-list">
                            {data?.map((v, i) => (
                                <li key={v?.uuid + i}>
                                    <Ellipsis title={`${Number(i) + 1}.${v?.displayName}`} />
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <Ellipsis title={data?.displayName} />
                    )}
                </Form.Item>
                <Form.Item label={I18N.modal.publish.miaoShu}>
                    {getFieldDecorator('publishDesc', {
                        rules: [
                            { required: true, message: I18N.modal.publish.qingShuRuFaBan },
                            { max2000: true, message: I18N.modal.publish.faBanMiaoShuZui }
                        ]
                    })(<TextArea placeholder={I18N.modal.publish.qingShuRuFaBan} rows={4} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(PublishModal);
