import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, message, Input, Form } from 'tntd';
import { connect } from 'dva';
import serviceApi from '../../service';

const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
    }
};

const OfflineModal = (props) => {
    const { visible, onCancel, form, uuid, dispatch, globalStore } = props;
    const { getFieldDecorator, resetFields } = form;
    const { currentApp } = globalStore || {};
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        return () => {
            resetFields();
        };
    }, [visible]);
    const handleOk = () => {
        form.validateFields(async (errors, data) => {
            if (!errors) {
                setLoading(true);
                serviceApi
                    .applyOffline({
                        ...data,
                        uuid
                    })
                    .then((res) => {
                        if (res && res.success) {
                            onCancel();
                            message.success(res?.message || I18N.modal.offlinemodal.caoZuoChengGong);
                            dispatch({
                                type: 'appService/getRunListData',
                                payload: {
                                    appName: currentApp ? currentApp.name : null
                                }
                            });
                        } else {
                            message.error(res?.message || I18N.modal.offlinemodal.xiaXianShiBai);
                        }
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        });
    };

    return (
        <Modal
            maskClosable={false}
            title={I18N.modal.offlinemodal.yingYongFuWuXia}
            visible={visible}
            confirmLoading={loading}
            onCancel={onCancel}
            onOk={handleOk}
            loading={loading}>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.modal.offlinemodal.yiJian}>
                    {getFieldDecorator('remark', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.offlinemodal.qingShuRuYiJian2
                            },
                            {
                                max: 2000,
                                message: I18N.modal.offlinemodal.qingShuRuYiJian
                            }
                        ]
                    })(
                        <TextArea
                            style={{ resize: 'none', height: '160px', marginTop: '5px' }}
                            placeholder={I18N.modal.offlinemodal.qingShuRuYiJian}
                            rows={4}
                        />
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(
    connect((state) => ({
        globalStore: state.global
    }))(OfflineModal)
);
