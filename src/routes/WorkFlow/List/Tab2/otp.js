import { createOtp } from '@/utils/I18N';
export default createOtp({
    cn: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            }
        },

        action: 220
    },

    en: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            }
        },
        action: 100
    }
});
