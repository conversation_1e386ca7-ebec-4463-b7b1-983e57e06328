import I18N from '@/utils/I18N';
import { Tabs, TabsContainer } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import Tab1 from './Tab1';
import Tab2 from './Tab2';

const { TabPane } = Tabs;

export default TabsContainer((props) => {
    const { location, history } = props;
    const { pathname } = location;
    const currentTab = getUrlKey('currentTab') || '2';

    // tab切换
    const changeTabHandle = (key) => {
        const search = '?currentTab=' + key;
        history.push(pathname + search);
    };

    return (
        <Tabs activeKey={currentTab} onChange={changeTabHandle} animated={false} type="ladder-card">
            <TabPane tab={I18N.list.index.yunXingQu} key="2">
                <Tab1 areaType={currentTab} history={history} />
            </TabPane>
            <TabPane tab={I18N.list.index.bianJiQu} key="1">
                <Tab2 areaType={currentTab} history={history} />
            </TabPane>
        </Tabs>
    );
});
