import I18N from '@/utils/I18N';
import { Modal, Table, Tag, Button, Tooltip } from 'tntd';

function PublishResult({ onCancel, visible, dataSource = [] }) {
    const columns = [
        {
            title: I18N.modal.publishresult.hanShuMingCheng,
            dataIndex: 'functionName',
            ellipsis: true,
            width: 300,
            render: (text) => (
                <Tooltip placement="topLeft" title={text}>
                    {text}
                </Tooltip>
            )
        },
        {
            title: I18N.modal.publishresult.jieGuo,
            dataIndex: 'success',
            width: 150,
            render: (text) => {
                return <Tag color={text ? 'green' : 'red'}>{text ? I18N.modal.publishresult.chengGong : I18N.modal.publishresult.shiBai}</Tag>;
            }
        },
        {
            title: I18N.modal.publishresult.miaoShu,
            dataIndex: 'msg',
            ellipsis: true,
            render: (text) => (
                <Tooltip placement="topLeft" title={text}>
                    {text}
                </Tooltip>
            )
        }
    ];

    return (
        <Modal
            destroyOnClose
            title={I18N.modal.publishresult.tiJiaoJieGuo}
            width={800}
            visible={visible}
            onCancel={onCancel}
            footer={<Button onClick={onCancel}>{I18N.modal.publishresult.queDing}</Button>}>
            <Table rowKey="functionName" columns={columns} dataSource={dataSource} pagination={false} />
        </Modal>
    );
}

export default PublishResult;
