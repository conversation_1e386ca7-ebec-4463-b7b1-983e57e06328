import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, Input, Form, message } from 'tntd';
import PublishResult from '../Modal/PublishResult';
import service from '../service';

const TextArea = Input.TextArea;
const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 }
};
const OnlineModal = (props) => {
    const { setSelectedRowKeys, selectedRowKeys, uuid } = props;
    const [loading, setLoading] = useState(false);
    const [batchResult, setBatchResult] = useState(null);
    const [decisionToolUuids, setDecisionToolUuids] = useState([]); //发布状态数组

    const { visible, onCancel, search, form } = props;
    const { getFieldDecorator, resetFields } = form;
    const modelPublish = () => {
        form.validateFields((errors, data) => {
            if (!errors) {
                // 决策工具批量上线接口
                // 批量上线后取消取消所有的单选框

                setLoading(true);
                service
                    .functionOnline({
                        uuids: uuid || selectedRowKeys.join(','),
                        ...data
                    })
                    .then((res) => {
                        if (res.success) {
                            if (uuid) {
                                //上线
                                if (res?.data[0]?.success) {
                                    message.success(res?.data && res?.data[0]?.msg);
                                } else {
                                    message.error(res?.data && res?.data[0]?.msg);
                                }
                                onCancel();
                            } else {
                                //批量上线
                                setBatchResult(res.data || []);
                            }
                        } else {
                            message.error(res.message || res.msg);
                        }
                    })
                    .finally(() => {
                        setSelectedRowKeys([]);
                        setDecisionToolUuids([]);
                        resetFields();
                        setLoading(false);
                    });
            }
        });
    };
    return (
        <>
            <Modal
                title={uuid ? I18N.modal.onlinemodal.shangXian : I18N.modal.onlinemodal.piLiangShangXian}
                width={600}
                visible={visible}
                maskClosable={false}
                onOk={modelPublish}
                onCancel={onCancel}
                confirmLoading={loading}
                destroyOnClose>
                <Form {...formItemLayout}>
                    <Form.Item label={I18N.modal.onlinemodal.miaoShu}>
                        {getFieldDecorator('remark', {
                            rules: [
                                {
                                    required: true,
                                    message: I18N.modal.onlinemodal.qingShuRuShangXian
                                },
                                {
                                    max: 2000,
                                    message: I18N.modal.onlinemodal.zuiDuoShuRuGe
                                }
                            ]
                        })(<TextArea rows={4} placeholder={I18N.modal.onlinemodal.qingShuRuShangXian} />)}
                    </Form.Item>
                </Form>
            </Modal>
            <PublishResult
                onCancel={() => {
                    setBatchResult(null);
                    onCancel();
                }}
                visible={!!batchResult}
                dataSource={batchResult || []}
            />
        </>
    );
};

export default Form.create()(OnlineModal);
