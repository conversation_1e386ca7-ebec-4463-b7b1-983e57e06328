import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { connect } from 'dva';
import { message, Button, Popconfirm, Tag, QueryListScene, HandleIcon, Ellipsis, Icon, TntdAction, Tooltip } from 'tntd';

import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
import { formatStandardTime, getUrlKey, colorRgb } from '@/utils/utils';
import { queryRouterHandle } from '@/utils/queryRouterHandle';
import TestModal from '@/components/BpmnCalculateDialog/Inner/TestModal';
import QueryListWrapper from '@/components/QueryListWrapper';
import PagePermission from '@/components/PagePermission';

import ImportModal from '@/components/importModal';
import Status from '@/components/TablePage/components/Status';
import PublishResult from '../Modal/PublishResult';
import CopyModal from '../CopyModal';
import OnlineModal from '../Modal/OnlineModal';

import service from '../service';
import { statusMap, auditStatusMap } from '../constants';

const { QueryForm, Field, QueryList, createActions } = QueryListScene;
const actions = createActions();

const Page = (props) => {
    const { history, currentTab } = props;
    const [prevCode, setPrevCode] = useState();
    const [referenceDrawerData, setReferenceDrawerData] = useState(null);
    const [batchResult, setBatchResult] = useState(null);

    const [onlineVisible, setOnlineVisible] = useState(false);
    const [uuid, setUuid] = useState(false);
    const [copyItem, setCopyItem] = useState(undefined);

    const [testDataInfo, setTestDataInfo] = useState({
        visible: false,
        testData: []
    });
    const { visible, testData } = testDataInfo;
    const [code, setCode] = useState('');
    const [type, setType] = useState(1);

    // 导出
    const [importVisible, setImportVisible] = useState(false);

    const formulaCode = getUrlKey('code');
    const status = getUrlKey('status');

    useEffect(() => {
        if (currentTab === '2') {
            actions.search();
        }
    }, [currentTab]);

    useEffect(() => {
        return () => {
            queryRouterHandle.init();
        };
    }, []);

    useEffect(() => {
        if (formulaCode || status) {
            actions.setFormData({
                obj: {
                    functionCode: formulaCode
                },
                status: status || undefined
            });
        }
    }, [formulaCode, status]);

    const query = ({ current = 1, pageSize = 10, ...rest }) => {
        queryRouterHandle.handle();
        let params = {
            curPage: current,
            areaType: 2,
            // appCode: currentApp.name,
            // orgCode: currentOrgCode,
            pageSize,
            ...rest,
            functionName: rest?.obj?.functionName,
            functionCode: rest?.obj?.functionCode
        };
        setPrevCode(code || '');
        if (code !== prevCode && code) {
            params.functionCode = code;
        }
        delete params.obj;
        return service.getFunctionQuery(params).then((res) => {
            if (!res) return;
            if (res.success) {
                let data = res?.data;
                return {
                    pageSize: Number(data?.pageSize),
                    current: Number(data?.curPage),
                    total: Number(data?.total),
                    data: data?.contents || []
                };
            }
            message.error(res.message || res.msg);

            //
        });
    };

    // 删除
    const deleteHandle = (uuid) => {
        service.formulaDelete({ uuid }).then((res) => {
            if (res?.success) {
                message.success(res?.message);
                actions?.search();
            } else {
                message.error(res.message || res.msg);
            }
        });
    };

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const mutiOnline = () => {
        const tableData = actions.getTableDataSource();
        const componentIds = (tableData.filter((d) => selectedRowKeys.includes(d.uuid)) || [])?.reduce((total, cur) => {
            if (cur?.functionVersionDTOList?.[0]?.uuid) {
                total.push(cur?.functionVersionDTOList?.[0]?.uuid);
            }
            return total;
        }, []);
        // const componentIds = (tableData.filter((d) => selectedRowKeys.includes(d.uuid)) || [])?.map((v) => v.functionCode);
        ReferenceOnlineCheck({
            rq: () =>
                referenceAPI.onlineValidate({
                    componentType: 'DATASOURCE_FUNCTION',
                    componentIds: componentIds?.join(',')
                })
        }).then(() => {
            setOnlineVisible(true);
        });
    };

    const testHandle = (record) => {
        const script = record?.viewVersion?.script;
        const type = record?.type;
        setCode(script);
        setType(type);
        service.preInvoke({ script, type }).then((res) => {
            if (res?.success) {
                setTestDataInfo({
                    visible: true,
                    testData: res?.data || []
                });
            } else {
                message.error(res?.message);
            }
        });
    };
    const columns = [
        {
            title: I18N.editorarea.index.hanShuMingCheng,
            dataIndex: 'functionName',
            width: 350,
            ellipsis: true,
            render: (text, record) => {
                const status = record?.viewVersion?.status;
                const version = record?.viewVersion?.version;
                let statusObj = statusMap[status] || {};
                return (
                    <Ellipsis
                        prefix={
                            <>
                                {status && (
                                    <Tag
                                        color={colorRgb(statusObj?.color, 0.1)}
                                        style={{ border: `1px solid ${statusObj?.color}`, color: statusObj?.color }}>
                                        {statusObj.value}
                                    </Tag>
                                )}
                                <Tag color="tnt-purple">V{version}</Tag>
                            </>
                        }
                        title={text}
                    />
                );
                // return (
                //     <div className="function-tag">

                //         <div className="function-text">
                //             <Ellipsis widthLimit={200} title={text} />
                //         </div>
                //     </div>
                // );
            }
        },

        {
            title: I18N.editorarea.index.biaoZhi,
            dataIndex: 'functionCode',
            width: 200
        },
        {
            title: I18N.editorarea.index.zhuangTai,
            width: 200,
            dataIndex: 'status',
            render: (text, record) => {
                let res = auditStatusMap[text].value;
                if (text === 2 || text === 3) {
                    let extra = record.auditInfo === 1 ? I18N.editorarea.index.shangXian2 : I18N.editorarea.index.xiaXian;
                    res = res + extra;
                }
                return (
                    <div>
                        <Status text={res} color={auditStatusMap[text].color} />
                    </div>
                );
            }
        },
        {
            title: I18N.editorarea.index.leiXing,
            width: 110,
            dataIndex: 'type',
            render: (text) => {
                const map = {
                    1: I18N.editorarea.index.gongShi,
                    2: I18N.editorarea.index.jiaoBen
                };
                return <Tag color={text === 1 ? 'geekblue' : 'cyan'}>{map[text]}</Tag>;
            }
        },

        {
            title: I18N.editorarea.index.xiuGaiRen,
            dataIndex: 'updateBy',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.editorarea.index.xiuGaiShiJian,
            dataIndex: 'gmtModified',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record.gmtModified || record.gmtCreate);
            }
        },
        {
            title: I18N.editorarea.index.caoZuo,
            dataIndex: 'operate',
            width: 200,
            operate: true,
            fixed: 'right',
            render: (text, record) => {
                const isDisabled = record.status === 2 || record.status === 3;
                // true为能 false为下线
                const isOnline = record?.viewVersion?.status === 4;
                return (
                    <HandleIcon>
                        {!isOnline &&
                            window.auth('layoutFunctionLibrary', 'Online') &&
                            !isDisabled &&
                            record?.canWriter &&
                            record?.viewVersion?.status !== 1 && (
                                <HandleIcon.Item title={I18N.editorarea.index.shangXian}>
                                    <Icon
                                        type="online"
                                        onClick={() => {
                                            if (isOnline) {
                                                ReferenceCheck({
                                                    strongMsg: I18N.editorarea.index.cunZaiQiangYinYong,
                                                    rq: () =>
                                                        referenceAPI.checkComponentReference({
                                                            componentType: 'DATASOURCE_FUNCTION',
                                                            componentIds: record.functionCode
                                                        })
                                                }).then(() => {
                                                    service.functionOffline({ uuid: record.uuid }).then((res) => {
                                                        if (res?.success) {
                                                            message.success(res?.message);
                                                            actions?.search();
                                                        } else {
                                                            message.error(res.message || res.msg);
                                                        }
                                                    });
                                                });
                                            } else {
                                                ReferenceOnlineCheck({
                                                    rq: () =>
                                                        referenceAPI.onlineValidate({
                                                            componentType: 'DATASOURCE_FUNCTION',
                                                            componentIds: record?.functionVersionDTOList?.[0]?.uuid //record.functionCode
                                                        })
                                                }).then(() => {
                                                    setUuid(record?.uuid);
                                                    setOnlineVisible(true);
                                                });
                                            }
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}

                        <HandleIcon.Item title={I18N.editorarea.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    const verUuid = record?.viewVersion?.uuid;
                                    history.push(`/handle/formula/view/${verUuid}?currentTab=2`);
                                }}
                            />
                        </HandleIcon.Item>

                        {window.auth('layoutFunctionLibrary', 'Update') && !isDisabled && record?.canWriter && (
                            <HandleIcon.Item title={I18N.editorarea.index.xiuGai}>
                                <Icon
                                    type="form"
                                    onClick={() => {
                                        const verUuid = record?.viewVersion?.uuid;
                                        history.push(`/handle/formula/edit/${verUuid}?currentTab=2`);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}
                        {window.auth('layoutFunctionLibrary', 'Update') && (
                            <HandleIcon.Item title={I18N.editorarea.index.fuZhi}>
                                <Icon
                                    type="copy"
                                    onClick={() => {
                                        setCopyItem({ ...record });
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        {window.auth('layoutFunctionLibrary', 'PreInvoke') && record?.viewVersion?.status !== 1 && (
                            <HandleIcon.Item title={I18N.editorarea.index.ceShi}>
                                <Icon type="debug" onClick={() => testHandle(record)} />
                            </HandleIcon.Item>
                        )}
                        <HandleIcon.Item title={I18N.editorarea.index.chaKanBanBen}>
                            <Icon
                                type="history"
                                onClick={() => {
                                    history.push(`/handle/formula/history/${record.uuid}?currentTab=2`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.editorarea.index.yinYongGuanXi}>
                            <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                        </HandleIcon.Item>
                        {window.auth('layoutFunctionLibrary', 'Delete') && Number(record.status) === 1 && record?.canWriter && (
                            <HandleIcon.Item title={I18N.editorarea.index.shanChu}>
                                <Popconfirm
                                    placement="topRight"
                                    title={I18N.editorarea.index.queDingShanChuCi}
                                    onClick={(e) => e.stopPropagation()}
                                    getPopupContainer={(trigger) => trigger.parentNode}
                                    trigger="hover"
                                    overlayStyle={{ width: 180 }}
                                    onConfirm={() => deleteHandle(record.uuid)}>
                                    <Icon type="delete" />
                                </Popconfirm>
                            </HandleIcon.Item>
                        )}
                    </HandleIcon>
                );
            }
        }
    ];

    const ExtralActions = (
        <>
            {/* {window.auth('layoutFunctionLibrary', 'Import') && (
                <Button
                    onClick={() => {
                        setImportVisible(true);
                    }}>
                    {'导入'}
                </Button>
            )} */}
            <TntdAction
                actionProps="disabled"
                hidden={currentTab !== '2'}
                afterClose={() => setSelectedRowKeys([])}
                title={I18N.template(I18N.editorarea.index.sELEC, { val1: selectedRowKeys.length })}>
                {window.auth('layoutFunctionLibrary', 'Online') && (
                    <Tooltip title={I18N.editorarea.index.piLiangShangXian}>
                        <Button
                            check
                            text={I18N.editorarea.index.piLiangShangXian}
                            onClick={mutiOnline}
                            disabled={selectedRowKeys.length === 0}>
                            <Icon type="batch-online" />
                        </Button>
                    </Tooltip>
                )}
            </TntdAction>
            {window.auth('layoutFunctionLibrary', 'Add') && (
                <Button
                    type="primary"
                    icon="plus"
                    onClick={() => {
                        history.push('/handle/formula/detail');
                    }}>
                    {I18N.editorarea.index.xinZeng}
                </Button>
            )}
        </>
    );

    return (
        <PagePermission
            auth={() => {
                return true || window.auth('layoutFunctionLibrary', 'Query');
            }}>
            <QueryListScene memory query={query} actions={actions} className="formula-page" initSearch={formulaCode || status}>
                <QueryForm extraActions={ExtralActions}>
                    <Field
                        title=""
                        name="obj"
                        type="selectInput"
                        props={{
                            placeholder: I18N.editorarea.index.shuRuSouSuoNei,
                            onChange: (vals) => {
                                if (!vals.functionName && !vals.functionCode) {
                                    const allVals = actions.getFormData();
                                    const { obj, ...rest } = allVals;
                                    actions.setFormData({ ...rest });
                                }
                            },
                            onPressEnter: (e) => {
                                const vals = actions.getFormData();
                                actions.search({ ...vals, current: 1 });
                            },
                            options: [
                                { label: I18N.editorarea.index.hanShuMingCheng, value: 'functionName' },
                                { label: I18N.editorarea.index.hanShuBiaoZhi, value: 'functionCode' }
                            ]
                        }}
                    />
                    <Field
                        title=""
                        name="status"
                        type="select"
                        props={{
                            dropdownMatchSelectWidth: false,
                            placeholder: I18N.editorarea.index.qingXuanZeZhuangTai,
                            options: [
                                { label: I18N.editorarea.index.daiTiJiao, value: '1' },
                                // { label: I18N.editorarea.index.daiShenHe, value: '2' },
                                // { label: I18N.editorarea.index.daiFuHe, value: '3' },
                                { label: I18N.editorarea.index.yiFaBu, value: '4' }
                            ],
                            onChange: (status) => {
                                actions.search({ current: 1, status });
                            }
                        }}
                    />
                </QueryForm>

                <QueryList
                    rowKey="uuid"
                    columns={columns}
                    scroll={{ x: 1800 }}
                    rowSelection={{
                        selectedRowKeys,
                        onChange: (selectedRowKeys) => {
                            setSelectedRowKeys(selectedRowKeys);
                        },
                        getCheckboxProps: (record) => ({
                            // 联调
                            disabled: !(
                                !(record.status === 2 || record.status === 3 || record.status === 4) &&
                                record?.canWriter &&
                                record?.viewVersion?.status !== 1
                            )
                        })
                    }}
                />
            </QueryListScene>
            {visible && (
                <TestModal
                    visible={visible}
                    data={testData}
                    code={code}
                    type={type}
                    onCancel={() => {
                        setTestDataInfo({
                            visible: false,
                            testData: []
                        });
                        setCode('');
                        setType(1);
                    }}
                />
            )}

            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.functionName}【${referenceDrawerData?.functionCode}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_FUNCTION',
                        componentId: referenceDrawerData.functionCode
                    });
                }}
            />

            <PublishResult
                onCancel={() => {
                    actions?.search();
                    setSelectedRowKeys([]);
                    setBatchResult(null);
                }}
                visible={!!batchResult}
                dataSource={batchResult || []}
            />

            <CopyModal
                item={copyItem}
                visible={!!copyItem}
                actions={actions}
                close={() => {
                    setCopyItem(undefined);
                }}
            />
            <ImportModal
                type="FUNCTION"
                visible={importVisible}
                onCancel={() => {
                    setImportVisible(false);
                    actions.search({ current: 1 });
                }}
            />
            <OnlineModal
                uuid={uuid}
                selectedRowKeys={selectedRowKeys}
                setSelectedRowKeys={setSelectedRowKeys}
                visible={onlineVisible}
                onCancel={() => {
                    setUuid();
                    setOnlineVisible(false);
                    actions.search();
                }}
                destroyOnClose
            />
        </PagePermission>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(QueryListWrapper(Page, actions));
