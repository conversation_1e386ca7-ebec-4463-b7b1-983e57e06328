import { getUrl, getHeader } from '@/services/common';
import request, { downloadFileHandle } from '@/utils/request';

const getFunctionQuery = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/function/functionQuery', params),
        {
            method: 'GET'
        },
        true
    );
};
const add = (params) => {
    return request(
        '/bridgeApi/layout/function/functionCreate',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const authorization = (params) => {
    return request(
        '/bridgeApi/layout/function/authorization',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const functionOnline = (params) => {
    return request(
        '/bridgeApi/layout/function/functionOnline',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const getAuthorization = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/function/authorizationQuery', params),
        {
            method: 'GET'
        },
        true
    );
};

const functionVersionHistory = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/function/functionVersionHistory', params),
        {
            method: 'GET'
        },
        true
    );
};

const update = async (params) => {
    return request(
        '/bridgeApi/formula/update',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const updateStatus = async (params) => {
    return request(
        '/bridgeApi/formula/updateStatus',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const getById = async (params) => {
    return request(
        getUrl('/bridgeApi/formula/getById', params),
        {
            method: 'GET'
        },
        true
    );
};

const preInvoke = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionParseParams',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const invoke = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionInvoke',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const functionCopy = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionCopy',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const functionOffline = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionOffline',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const functionCover = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionCover',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const versionDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/function/versionDetail', params),
        {
            method: 'GET'
        },
        true
    );
};

const formulaDelete = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionDelete',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const exportFormula = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/formula/exportFormula', params),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

// 获取机构下的app
const getAppByOrgId = async (params) => {
    return request(
        getUrl('/bridgeApi/user/app', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

// 授权
const rulesetAuthorize = (params) => {
    return request(
        '/bridgeApi/ruleset/authorize',
        {
            method: 'POST',
            headers: {
                ...getHeader(),
                'Content-Type': 'application/json'
            },
            body: { ...params }
        },
        true
    );
};

export default {
    getFunctionQuery,
    functionCopy,
    authorization,
    getAuthorization,
    versionDetail,
    functionCover,
    functionOnline,
    functionOffline,
    functionVersionHistory,
    add,
    update,
    updateStatus,
    getById,
    preInvoke,
    invoke,
    formulaDelete,
    exportFormula,
    getAppByOrgId,
    rulesetAuthorize
};
