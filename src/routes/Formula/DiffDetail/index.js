import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { connect } from 'dva';
import { Breadcrumb, message } from 'tntd';

import './index.less';
import service from '../service';

import DetailInfo from '../DetailInfo';

const Page = (props) => {
    const { history, match, globalStore } = props;
    const { params } = match || {};
    const { uuid, curuuid } = params || {};
    const { orgList, orgCodeMap, allOrgListMap } = globalStore;

    const [versionInfo, setVersionInfo] = useState({});

    let typeName = I18N.diffdetail.index.duiBi;

    useEffect(() => {
        document.querySelector('.g-formula-diff-detail').parentNode.style.minWidth = 'auto';
        if (uuid) {
            service.versionDetail({ versionUuids: [uuid, curuuid] }).then((res) => {
                if (res.success) {
                    setVersionInfo(res.data || {});
                } else {
                    message.error(res.message || res.msg);
                }
            });
        }
        return () => {
            document.querySelector('.g-formula-diff-detail').parentNode.style.minWidth = '1024px';
        };
    }, [uuid]);

    return (
        <div className="g-formula-diff-detail">
            <div className="page-global-body">
                <DetailInfo
                    item={versionInfo}
                    history={history}
                    curVersionUuid={versionInfo?.functionVersionDTOList && versionInfo?.functionVersionDTOList[0].uuid}
                    orgCodeMap={orgCodeMap}
                    allOrgListMap={allOrgListMap}
                    orgList={orgList}
                    type={'contrast'}
                />
                <DetailInfo
                    codemirrorId="diff-codemirror-id"
                    item={versionInfo}
                    history={history}
                    curVersionUuid={versionInfo?.functionVersionDTOList && versionInfo?.functionVersionDTOList[1].uuid}
                    orgCodeMap={orgCodeMap}
                    allOrgListMap={allOrgListMap}
                    orgList={orgList}
                    type={'contrast'}
                />
            </div>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(Page);
