import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import { PageContainer } from 'tntd';

import Detail from './Detail';
import ListTab from './ListTab';
import FormulaHistory from './FormulaHistory';
import DiffDetail from './DiffDetail';

const Entrance = () => {
    return (
        <Switch>
            <Route exact name={I18N.detail.index.chaKan} component={Detail} path="/handle/formula/view/:uuid" />
            <Route exact name={I18N.detail.index.xiuGai} component={Detail} path="/handle/formula/edit/:uuid" />
            <Route exact name={I18N.detail.index.xinZeng} component={Detail} path="/handle/formula/detail" />
            <Route exact name={I18N.formulahistory.index.banBenLiShi} component={FormulaHistory} path="/handle/formula/history/:uuid" />
            <Route exact name={I18N.diffdetail.index.duiBi} component={DiffDetail} path="/handle/formula/diff/:uuid/:curuuid" />
            <Route name={I18N.detail.index.hanShuKu} component={ListTab} path="/" />
        </Switch>
    );
};

export default PageContainer(BreadCrumb(Entrance));
