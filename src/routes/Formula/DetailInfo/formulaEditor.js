import I18N from '@/utils/I18N';
import { useRef, useEffect } from 'react';
import { Popover, Icon } from 'tntd';
import { connect } from 'dva';
import FormulaEdit from '@tntd/formula-edit';
// import FormulaEdit from './FormulaEdit';
import './index.less';
const typeKeyWords = ['αINT', 'αSTRING', 'αDOUBLE', 'αBOOLEAN', 'αDATETIME', 'αLONG', 'αENUM', 'αJSON'];

export const enCodeToCnExtraLogic = (enCode) => {
    const regExp = new RegExp(`(${typeKeyWords.join('|')})`, 'g');
    const cnCode = enCode.replace(regExp, () => {
        return '';
    });
    return cnCode;
};

let FormulaEditor = (props) => {
    const { globalStore, placeholder, methodList, disabled, value, onChange, height, defaultValue = '' } = props;
    const { allSystemFieldList } = globalStore;
    console.log('allSystemFieldList', allSystemFieldList);
    const formulaRef = useRef({});

    useEffect(() => {
        props.refInstance.current = formulaRef.current || {};
    }, []);

    // 初始化数据
    useEffect(() => {
        if (!!defaultValue) {
            let initValue = formulaRef.current.EnCodeToCn(value);
            formulaRef.current.codeEditor.setValue(initValue);
        }
    }, [defaultValue]);

    return (
        <div className="formulaEdit-wrap">
            <FormulaEdit
                editorEvent={(obj) => {
                    formulaRef.current = obj;
                }}
                height={height || 600}
                placeholder={placeholder}
                value={value}
                fieldList={allSystemFieldList || []} // @唤起
                methodList={methodList} // #唤起
                readOnly={disabled}
                cnCodeToEnExtraLogic={(item) => {
                    return `α${item.type}`;
                }}
                enCodeToCnExtraLogic={enCodeToCnExtraLogic}
                onChange={(enCode, data, type) => {
                    onChange && onChange(enCode, data, type);
                }}
            />
            {/* <Popover
                placeholder="topLeft"
                title={I18N.detailinfo.formulaeditor.wenXinTiShi}
                content={
                    <code>
                        {I18N.detailinfo.formulaeditor.tIPSHou}<br />
                        {I18N.detailinfo.formulaeditor.liRuShuChuZi}<br />
                        {I18N.detailinfo.formulaeditor.zhiChiDeHanShu}<br />
                        {I18N.detailinfo.formulaeditor.zhiChiDeYunSuan}</code>
                }>
                <Icon className="formulaEdit-ques-icon" type="question-circle" />
            </Popover> */}
        </div>
    );
};

FormulaEditor = connect((state) => ({
    globalStore: state.global
}))(FormulaEditor);

export default FormulaEditor;
