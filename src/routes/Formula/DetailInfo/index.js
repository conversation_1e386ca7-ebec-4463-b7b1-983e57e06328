import I18N from '@/utils/I18N';
import { useEffect, useState, useMemo, useRef } from 'react';
import { message, Input, Form, Select, Button, Radio, Icon, Tooltip, TreeSelect, Tag, Title } from 'tntd';

import './index.less';
import CustomEditor from '@tntx/custom-syntax-editor';
import Codemirror from '@/components/BpmnCalculateDialog/Inner/Codemirror';
import FormulaEditor from './formulaEditor';
import TestModal from '@/components/BpmnCalculateDialog/Inner/TestModal';
import service from './service';
import { quickList, ctrlList, codeKeywords, codePlaceholder } from './constants';

const { TextArea } = Input;

const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 10 }
};

// 需要被保留的列表
let keepList = [
    I18N.detailinfo.index.pingJunZhi,
    I18N.detailinfo.index.zuiDaZhi2,
    I18N.detailinfo.index.zuiXiaoZhi,
    I18N.detailinfo.index.qiuHe,
    I18N.detailinfo.index.xiangShangQuZheng,
    I18N.detailinfo.index.xiangXiaQuZheng,
    // '四舍五入',
    '+',
    '-',
    '*',
    '/',
    I18N.detailinfo.index.pinJieZiFuChuan,
    I18N.detailinfo.index.jieQuZiFuChuan,
    I18N.detailinfo.index.quTouWeiKongGe,
    I18N.detailinfo.index.kongZhiFuZhi
];

let keepQuickList = quickList.filter((i) => keepList.includes(i.name));

let keepCtrlList = ctrlList.filter((i) => keepList.includes(i.name));

const DetailInfo = (props) => {
    const { form, item = {}, type, curVersionUuid, history, codemirrorId = 'c-formula', allOrgListMap } = props;
    const { getFieldDecorator, validateFields } = form;

    const refInstance = useRef();
    const [saveLoading, setSaveLoading] = useState(false);

    const [editorCode, setEditorCode] = useState('');
    const [fieldList, setFieldList] = useState();
    const [defaultScript, setDefaultScript] = useState('');

    const [data, setData] = useState({});

    const [version, setVersion] = useState(1);
    const [formulaType, setFormulaType] = useState(1);

    const [testDataInfo, setTestDataInfo] = useState({
        visible: false,
        testData: []
    });

    const { visible, testData } = testDataInfo;
    const isDisabled = type === 'view' || type === 'contrast';
    const isEdit = type === 'edit';

    useEffect(() => {
        let fieldList = localStorage.cmFieldList ? JSON.parse(localStorage.cmFieldList) : [];
        setFieldList(fieldList);
    }, []);

    useEffect(() => {
        if (item.type && curVersionUuid && fieldList) {
            const curVersion = item.functionVersionDTOList.find((res) => res.uuid === curVersionUuid);
            const script = curVersion?.script;
            setDefaultScript(script);
            setData({
                ...data,
                // type: item.type,
                script
            });

            setFormulaType(item.type);
            setVersion(curVersion?.version);

            if (item.type !== 1) {
                setEditorCode(script);
            }
        }
    }, [item, curVersionUuid, fieldList]);

    const stagingClick = () => {
        validateFields((errors, values) => {
            if (!errors) {
                if (curVersionUuid) {
                    setSaveLoading(true);
                    service
                        .functionUpdate({
                            ...data,
                            type: formulaType,
                            saveType: 1,
                            uuid: item.uuid,
                            functionName: values.functionName,
                            description: values.description,
                            versionUuid: curVersionUuid,
                            script: formulaType === 1 ? data.formulaCode : data.scriptCode
                        })
                        .then((res) => {
                            if (res.success) {
                                message.success(res.message);
                                history.goBack();
                            } else {
                                message.error(res.message || res.msg);
                            }
                            //测试说每次暂存都要返回列表
                            // history.goBack();
                            setSaveLoading(false);
                        })
                        .catch(() => {
                            setSaveLoading(false);
                        });
                } else {
                    setSaveLoading(true);
                    service
                        .functionCreate({
                            ...data,
                            type: formulaType,
                            saveType: 1,
                            ...values,
                            script: formulaType === 1 ? data.formulaCode : data.scriptCode
                        })
                        .then((res) => {
                            if (res.success) {
                                message.success(res.message);
                                history.goBack();
                            } else {
                                message.error(res.message || res.msg);
                            }
                            // history.goBack();
                            setSaveLoading(false);
                        })
                        .catch(() => {
                            setSaveLoading(false);
                        });
                }
            }
        });
    };
    const saveClick = () => {
        if (!data?.script) {
            return message.error(I18N.detailinfo.index.qingShuRuHanShu2);
        }

        validateFields((errors, values) => {
            if (!errors) {
                setSaveLoading(true);
                if (curVersionUuid) {
                    service
                        .functionUpdate({
                            ...data,
                            type: formulaType,
                            saveType: 2,
                            uuid: item.uuid,
                            description: values.description,
                            functionName: values.functionName,
                            versionUuid: curVersionUuid,
                            script: formulaType === 1 ? data.formulaCode : data.scriptCode
                        })
                        .then((res) => {
                            if (res.success) {
                                history.goBack();
                                message.success(res.message);
                            } else {
                                message.error(res.message || res.msg);
                            }

                            setSaveLoading(false);
                        })
                        .catch(() => {
                            setSaveLoading(false);
                        });
                } else {
                    service
                        .functionCreate({
                            ...data,
                            type: formulaType,
                            saveType: 2,
                            ...values,
                            script: formulaType === 1 ? data.formulaCode : data.scriptCode
                        })
                        .then((res) => {
                            if (res.success) {
                                history.goBack();
                                message.success(res.message);
                            } else {
                                message.error(res.message || res.msg);
                            }
                            setSaveLoading(false);
                        })
                        .catch(() => {
                            setSaveLoading(false);
                        });
                }
            }
        });
    };
    const testClick = () => {
        let params = {};
        if (formulaType === 1) {
            params = {
                type: formulaType,
                version,
                script: data.script
            };
        } else {
            params = {
                type: formulaType,
                version,
                script: data.script
            };
        }
        service.preInvoke(params).then((res) => {
            if (res?.success) {
                setTestDataInfo({
                    visible: true,
                    testData: res?.data || []
                });
            } else {
                message.error(res?.message);
            }
        });
    };

    return (
        <div className="detail-info">
            {type !== 'contrast' && (
                <>
                    <Title className="title" bold size="small" title={I18N.detailinfo.index.jiBenXinXi} />
                    <Form className="detail-form">
                        {/* <Form.Item className="form-item" {...formItemLayout} label={'所属机构'}>
                            {getFieldDecorator('orgCode', {
                                initialValue: item.orgCode,
                                rules: [{ required: true, message: '请填写机构' }]
                            })(
                                <TreeSelect
                                    placeholder={'选择机构'}
                                    searchPlaceholder={'机构名称'}
                                    treeNodeFilterProp="title"
                                    showSearch
                                    disabled={isDisabled || isEdit}
                                    treeData={orgList}
                                    treeDefaultExpandAll
                                    onChange={(e) => {
                                        getAppByOrgId(e);
                                        form.setFieldsValue({ appCode: undefined });
                                    }}
                                    style={{ width: '100%', height: 28, lineHeight: 28 }}
                                    dropdownStyle={{ maxHeight: 300, overflow: 'auto', width: 340 }}
                                />
                            )}
                        </Form.Item>
                        <Form.Item className="form-item" {...formItemLayout} label={'所属渠道'}>
                            {getFieldDecorator('appCode', {
                                initialValue: item.appCode,
                                rules: [{ required: true, message: '请选择所属渠道' }]
                            })(
                                <Select
                                    showSearch
                                    disabled={isDisabled || isEdit}
                                    filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                    placeholder={'请选择所属渠道'}>
                                    {appList.map((item) => {
                                        return (
                                            <Option value={item.name} key={item.uuid} item={item}>
                                                {item.displayName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            )}
                        </Form.Item> */}

                        {type && (
                            <Form.Item className="form-item" {...formItemLayout} label={I18N.detailinfo.index.hanShuBiaoZhi}>
                                {getFieldDecorator('functionCode', {
                                    initialValue: item.functionCode,
                                    rules: [{ required: true, message: I18N.detailinfo.index.qingXuanZeHanShu }]
                                })(<Input disabled placeholder={I18N.detailinfo.index.qingXuanZeHanShu} />)}
                            </Form.Item>
                        )}

                        <Form.Item className="form-item" {...formItemLayout} label={I18N.detailinfo.index.hanShuMingCheng}>
                            {getFieldDecorator('functionName', {
                                initialValue: item.functionName,
                                rules: [
                                    { required: true, message: I18N.detailinfo.index.qingShuRuHanShu },
                                    { max: 200, message: I18N.detailinfo.index.changDuBuNengChao2 },
                                    { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9\s]+$/, message: I18N.detailinfo.index.mingChengBiXuYou }
                                ]
                            })(<Input disabled={isDisabled} placeholder={I18N.detailinfo.index.qingShuRuHanShu} />)}
                        </Form.Item>

                        <Form.Item className="form-item" {...formItemLayout} label={I18N.detailinfo.index.miaoShu}>
                            {getFieldDecorator('description', {
                                initialValue: item.description,
                                getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                                rules: [{ max: 2000, message: I18N.detailinfo.index.changDuBuNengChao }]
                            })(
                                <TextArea
                                    disabled={isDisabled}
                                    placeholder={I18N.detailinfo.index.qingShuRuMiaoShu}
                                    maxLength={2000}
                                    style={{ height: '160px' }}
                                />
                            )}
                        </Form.Item>
                        {!isDisabled && (
                            <div className="u-btn">
                                <Button onClick={testClick}>{I18N.detailinfo.index.ceShi}</Button>
                                <Button onClick={stagingClick} loading={saveLoading}>
                                    {I18N.detailinfo.index.zanCun}
                                </Button>
                                <Button type="primary" onClick={saveClick} loading={saveLoading}>
                                    {I18N.detailinfo.index.baoCun}
                                </Button>
                            </div>
                        )}
                    </Form>
                </>
            )}
            <Title className="title" bold size="small" title={I18N.detailinfo.index.jiSuanLuoJi} />
            {(type === 'contrast' || type === 'view') && (
                <Tag color="rgba(148, 95, 185, 0.1)" style={{ border: '1px solid #945FB9', color: '#945FB9', marginLeft: '10px' }}>
                    V{version}
                </Tag>
            )}
            <div className="mb10">
                {I18N.detailinfo.index.bianXieLeiXing}
                <Tooltip title={I18N.detailinfo.index.zhiKeXuanZeYi}>
                    <Icon type="question-circle" />
                </Tooltip>
                ：
                <Radio.Group
                    onChange={(e) => {
                        setData({
                            ...data
                        });
                        setFormulaType(e.target.value);
                        setEditorCode(codePlaceholder);
                    }}
                    value={formulaType}
                    disabled={isDisabled || isEdit}>
                    <Radio value={1}>{I18N.detailinfo.index.gongShi}</Radio>
                    <Radio value={2}>{I18N.detailinfo.index.jiaoBen}</Radio>
                </Radio.Group>
            </div>
            <div style={{ display: formulaType === 1 ? 'block' : 'none' }}>
                <div className="calculate-tips">
                    {I18N.detailinfo.index.tIPSHou}
                    <p>
                        {I18N.detailinfo.index.liRu}
                        <span className="origin">@ {I18N.detailinfo.index.shiJiFangKuanJin}</span> ={' '}
                        <span className="blur">{I18N.detailinfo.index.zuiDaZhi}</span>(
                        <span className="origin">@ {I18N.detailinfo.index.shenQingJinE}</span>,{' '}
                        <span className="origin">@ {I18N.detailinfo.index.fangKuanJinE}</span>);
                        <span className="red">{I18N.detailinfo.index.zhuYiRenYiWan}</span>
                    </p>
                </div>
                <Codemirror tagList={keepQuickList} ctrlList={keepCtrlList} readOnly={isDisabled} formulaEditor={refInstance.current} />
                <FormulaEditor
                    defaultValue={defaultScript}
                    refInstance={refInstance}
                    value={data?.script || ''}
                    methodList={keepCtrlList}
                    height={600}
                    onChange={(enCode) => {
                        setData({
                            ...data,
                            script: enCode,
                            formulaCode: enCode
                        });
                    }}
                />
            </div>

            <div style={{ display: formulaType === 2 ? 'block' : 'none' }}>
                <div className="wrap-custom-editor">
                    <CustomEditor
                        defaultCode={editorCode}
                        readOnly={isDisabled}
                        height={650}
                        keywords={codeKeywords}
                        onChange={(code) => {
                            setData({
                                ...data,
                                script: code,
                                scriptCode: code
                            });
                        }}
                    />
                </div>
            </div>
            {visible && (
                <TestModal
                    visible={visible}
                    data={testData}
                    code={data.script}
                    type={formulaType}
                    onCancel={() => {
                        setTestDataInfo({
                            visible: false,
                            testData: []
                        });
                    }}
                />
            )}
        </div>
    );
};

export default Form.create()(DetailInfo);
