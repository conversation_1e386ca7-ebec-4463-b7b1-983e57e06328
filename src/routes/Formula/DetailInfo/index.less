.detail-info {
    background: #fff;
    padding: 20px;
    position: relative;
    line-height: 1.5;
    border-radius: @border-radius-base;

    .cm-field-li > sup{
        pointer-events: none;
    }

    .title {
        // display: inline-block;
        // font-size: 16px;
        // margin-bottom: 20px;
        // font-weight: bold;
        // border-left: solid 4px #2855f0;
        // // margin-left: -20px;
        // text-indent: 20px;
        // line-height: 1;
        // .tntd-title-text {
        //     >h3 {
        //         font-size: 14px;
        //     }
        // }
    }
    .detail-form {
        position: relative;
        .u-btn {
            position: absolute;
            right: 20px;
            bottom: -60px;

            .ant-btn {
                margin-left: 10px;
            }
        }
    }

    .box {
        margin-bottom: 16px;

        .label {
            float: left;
            width: 80px;
            display: inline-block;
            text-align: right;
            margin-top: 2px;

            b {
                color: #f00;
                vertical-align: middle;
                margin-right: 3px;
            }
        }

        .ant-input {
            width: 500px;
            float: left;
        }

        &:last-child {
            margin-bottom: 20px;
        }
    }
    .calculate-tips {
        font-size: 12px;
        color: #999;
        margin-bottom: 6px;

        .origin {
            color: #ff9800;
        }

        .blur {
            color: #03a9f4;
        }

        .red {
            color: #f00;
        }
    }

    .form-item {
        margin-bottom: 10px;
    }

    .ant-form-item-children {
        display: block;
        overflow: auto;
    }
}
