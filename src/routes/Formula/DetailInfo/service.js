import { getUrl, getHeader } from '@/services/common';
import request, { downloadFileHandle } from '@/utils/request';

// 获取机构下的app
const getAppByOrgId = async (params) => {
    return request(
        getUrl('/bridgeApi/user/app', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const functionUpdate = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionUpdate',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const functionCreate = (params) => {
    return request(
        '/bridgeApi/layout/function/functionCreate',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const preInvoke = async (params) => {
    return request(
        '/bridgeApi/layout/function/functionParseParams',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

export default {
    getAppByOrgId,
    preInvoke,
    functionCreate,
    functionUpdate
};
