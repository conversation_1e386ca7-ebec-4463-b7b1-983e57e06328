import I18N from '@/utils/I18N';
import { useState } from 'react';
import { Tabs, TabsContainer } from 'tntd';

import './index.less';

import { getUrlKey } from '@tntd/utils';
import history from '@/utils/history';
import EditorArea from './EditorArea';
import RunningArea from './RunningArea';

const TabPane = Tabs.TabPane;

const Page = (props) => {
    const { location } = props;
    const currentTab = getUrlKey('currentTab');

    const [key, setKey] = useState('1');

    const changeTab = (key) => {
        const { pathname } = location;
        const search = '?currentTab=' + key;
        setKey(key);
        history.push(pathname + search);
    };

    return (
        <Tabs activeKey={currentTab || key} onChange={changeTab} animated={false} type="ladder-card">
            <TabPane tab={I18N.formula.listtab.yunXingQu} key="1">
                <RunningArea currentTab={currentTab} />
            </TabPane>
            <TabPane tab={I18N.formula.listtab.bianJiQu} key="2">
                <EditorArea currentTab={currentTab} />
            </TabPane>
        </Tabs>
    );
};

export default TabsContainer(Page);
