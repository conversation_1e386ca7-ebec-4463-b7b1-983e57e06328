import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { message, HandleIcon, Table, Tag, Button, Modal, Ellipsis, Icon, TableContainer } from 'tntd';
import { formatStandardTime } from '@/utils/utils';
import Status from '@/components/TablePage/components/Status';
import service from '../service';
import './index.less';
const { confirm } = Modal;
const FormulaHistory = (props) => {
    const { history, match } = props;
    const { params } = match || {};
    const { uuid, type, currentTab } = params || {};

    const [dataSource, setDataSource] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [canWriter, setCanWriter] = useState(false);

    useEffect(() => {
        service.functionVersionHistory({ uuid }).then((res) => {
            if (res.success) {
                const funcData = res?.data || {};
                setCanWriter(funcData?.canWriter);
                const versions = funcData.functionVersionDTOList.map((res) => {
                    return {
                        ...res,
                        functionCode: funcData.functionCode,
                        type: funcData.type,
                        functionName: funcData.functionName
                    };
                });
                setDataSource(versions || []);
            } else {
                message.error(res.message || res.msg);
            }
        });
        return () => {};
    }, []);

    const onCover = (record) => {
        confirm({
            title: I18N.formulahistory.index.fuGaiBianJiQu2,
            content: I18N.formulahistory.index.ninQueDingYaoJiang,
            cancelText: I18N.formulahistory.index.quXiao,
            okText: I18N.formulahistory.index.queDing,
            onOk() {
                const verUuid = record?.uuid;
                service.functionCover({ versionUuid: verUuid }).then((res) => {
                    if (res.success) {
                        message.success(I18N.formulahistory.index.caoZuoChengGong);
                    } else {
                        message.error(res.message || res.msg);
                    }
                });
            }
        });
    };
    const columns = [
        {
            title: I18N.formulahistory.index.hanShuMingCheng,
            dataIndex: 'functionName',
            width: 300,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.formulahistory.index.banBenZhuangTai,
            width: 200,
            dataIndex: 'status',
            render: (text, record) => {
                let auditStatusMap = {
                    1: {
                        value: I18N.formulahistory.index.yiZanCun,
                        color: '#126BFB'
                    },
                    2: {
                        value: I18N.formulahistory.index.yiBaoCun,
                        color: '#126BFB'
                    },
                    3: {
                        value: I18N.formulahistory.index.daoRuDaiTiJiao,
                        color: '#126BFB'
                    },
                    4: {
                        value: I18N.formulahistory.index.yiShangXian,
                        color: '#07C790'
                    },
                    5: {
                        value: I18N.formulahistory.index.yiXiaXian,
                        color: '#5e7092'
                    }
                };
                return (
                    text && (
                        <div>
                            <Status text={auditStatusMap[text].value} color={auditStatusMap[text].color} />
                        </div>
                    )
                );
            }
        },
        {
            title: I18N.formulahistory.index.banBenHao,
            dataIndex: 'version',
            width: 140,
            render: (text, record) => {
                return <Tag color="tnt-purple">V{text}</Tag>;
            }
        },
        {
            title: I18N.formulahistory.index.biaoZhi,
            dataIndex: 'functionCode',
            width: 140
        },
        {
            title: I18N.formulahistory.index.leiXing,
            width: 110,
            dataIndex: 'type',
            render: (text) => {
                const map = {
                    1: I18N.formulahistory.index.gongShi,
                    2: I18N.formulahistory.index.jiaoBen
                };
                return <Tag color={text === 1 ? 'geekblue' : 'cyan'}>{map[text]}</Tag>;
            }
        },
        {
            title: I18N.formulahistory.index.xiuGaiRen,
            dataIndex: 'updateBy',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.formulahistory.index.xiuGaiShiJian,
            dataIndex: 'gmtModified',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record.gmtModified || record.gmtCreate);
            }
        },
        {
            title: I18N.formulahistory.index.caoZuo,
            dataIndex: 'operate',
            width: 130,
            operate: true,
            fixed: 'right',
            render: (text, record) => (
                <HandleIcon>
                    <HandleIcon.Item title={I18N.formulahistory.index.chaKan}>
                        <Icon
                            type="profile"
                            onClick={() => {
                                const verUuid = record?.uuid;
                                history.push(`/handle/formula/detail/view/${verUuid}`);
                            }}
                        />
                    </HandleIcon.Item>

                    {window.auth('layoutFunctionLibrary', 'Cover') && canWriter && (
                        <HandleIcon.Item title={I18N.formulahistory.index.fuGaiBianJiQu}>
                            <Icon
                                type="toggle"
                                onClick={() => {
                                    onCover(record);
                                }}
                            />
                        </HandleIcon.Item>
                    )}
                </HandleIcon>
            )
        }
    ];

    return (
        <div className="g-formula-history tnt-querylistscene-content">
            <div className="formula-compare">
                <Button
                    disabled={selectedRowKeys.length <= 1}
                    icon="compare"
                    type="primary"
                    onClick={() => {
                        history.push(`/handle/formula/diff/${selectedRowKeys[0]}/${selectedRowKeys[1]}`);
                    }}>
                    {I18N.formulahistory.index.duiBi}
                </Button>
            </div>
            <Table
                rowKey="uuid"
                rowSelection={{
                    selectedRowKeys,
                    columnTitle: ' ',
                    hideDefaultSelections: true,
                    onChange: (selectedRowKeys) => {
                        if (selectedRowKeys.length > 2) {
                            return message.warning(I18N.formulahistory.index.zuiDuoTongShiXuan);
                        }
                        setSelectedRowKeys(selectedRowKeys);
                    }
                }}
                columns={columns}
                dataSource={dataSource || []}
                scroll={{ x: 1300 }}
            />
        </div>
    );
};

export default TableContainer(FormulaHistory);
