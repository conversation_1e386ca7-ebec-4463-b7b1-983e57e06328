import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Modal, message, Input, Form, Select, Button } from 'tntd';

import './index.less';
import service from '../service';

const Option = Select.Option;
const { TextArea } = Input;

const CopyModal = (props) => {
    const { visible, close, form, item = {}, actions } = props;

    const { getFieldDecorator, validateFields } = form;

    const { appList = [], uuid, orgCode } = item;
    const [loading, setLoading] = useState(false);
    const onSubmit = () => {
        validateFields(async (errors, values) => {
            if (!errors) {
                setLoading(true);
                service
                    .functionCopy({
                        ...values,
                        uuid
                        // orgCode
                    })
                    .then((res) => {
                        if (res.success) {
                            message.success(res.message);
                            actions.search({ current: 1 });
                            close();
                        } else {
                            message.error(res.message || res.msg);
                        }
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        });
    };

    const formItemLayout = {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
    };

    return (
        <Modal
            className="fun-copy-modal"
            title={I18N.copymodal.index.fuZhi}
            visible={visible}
            width={700}
            onCancel={() => {
                close();
            }}
            footer={[
                <Button key="cancel" onClick={close}>
                    {I18N.copymodal.index.quXiao}
                </Button>,
                <Button key="ok" type="primary" onClick={onSubmit} loading={loading}>
                    {I18N.copymodal.index.queDing}
                </Button>
            ]}
            onOk={onSubmit}
            maskClosable={false}
            destroyOnClose>
            <Form className="catalog-form">
                {/* <Form.Item className="form-item" {...formItemLayout} label={'所属渠道'}>
                    {getFieldDecorator('appCode', {
                        rules: [{ required: true, message: '请选择所属渠道' }]
                    })(
                        <Select
                            showSearch
                            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                            placeholder={'请选择所属渠道'}>
                            {appList.map((item) => {
                                return (
                                    <Option value={item.name} key={item.uuid} item={item}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                        </Select>
                    )}
                </Form.Item> */}
                <Form.Item className="form-item" {...formItemLayout} label={I18N.copymodal.index.hanShuMingCheng}>
                    {getFieldDecorator('functionName', {
                        initialValue: item.functionName + I18N.copymodal.index.fuBen,
                        rules: [
                            { required: true, message: I18N.copymodal.index.qingTianXieHanShu },
                            { pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/, message: I18N.copymodal.index.mingChengBiXuYou },
                            { max: 200, message: I18N.copymodal.index.changDuBuNengChao }
                        ]
                    })(<Input />)}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.copymodal.index.hanShuLeiXing}>
                    {getFieldDecorator('type', {
                        initialValue: item.type,
                        rules: [{ required: true, message: I18N.copymodal.index.qingXuanZeHanShu }]
                    })(
                        <Select disabled placeholder={I18N.copymodal.index.qingXuanZeHanShu}>
                            <Option value={1}>{I18N.copymodal.index.gongShi}</Option>
                            <Option value={2}>{I18N.copymodal.index.jiaoBen}</Option>
                        </Select>
                    )}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.copymodal.index.miaoShu}>
                    {getFieldDecorator('description', {
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [{ max: 2000, message: I18N.copymodal.index.changDuBuNengChao2 }]
                    })(<TextArea placeholder={I18N.copymodal.index.qingShuRuMiaoShu} maxLength={2000} style={{ height: '160px' }} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(CopyModal);
