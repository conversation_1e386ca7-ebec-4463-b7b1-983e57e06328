import { message } from "tntd";
import { supplierWarningAPI } from "../../services";
import cloneDeep from "lodash.clonedeep";
import { compare } from "../../utils/operatorState";

const defaultState = {
	tableList: [],
	total: 0,
	searchParams: {
		curPage: 1,
		pageSize: 10,
		remainingDay: null // 合作剩余天数
	},
	loading: false
}

export default {
	namespace: "supplierWarning",
	state: cloneDeep(defaultState),

	effects: {
		*getList({ payload }, { call, put, select }) {
			if (payload && !payload.noLoading) {
				yield put({
					type: "setAttrValue",
					payload: {
						loading: true
					}
				});
			}
			const supplierWarning = yield select(state => ({ searchParams: state.supplierWarning.searchParams }));
			const { searchParams } = supplierWarning;
			let params = Object.assign({}, searchParams);
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}

			let res = yield call(supplierWarningAPI.getList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: "setAttrValue",
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState)
		}

	}

};
