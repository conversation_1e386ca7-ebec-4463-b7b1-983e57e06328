import { message } from "tntd";
import { externalAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";
import { compare } from "../../utils/operatorState";
import { formatStartTime, formatEndTime } from "@/utils/utils";
import moment from "moment";

const threeMonth = [
	moment().subtract(1, "months").valueOf(),
	moment().valueOf()
];

const defaultState = {
	tableList: [],
	total: 0,
	searchParams: {
		curPage: 1,
		pageSize: 10,
		serviceCode: null, // 三方服务名称
		providerId: null, // 供应商
		date: threeMonth // 时间
	},
	modalTableList: [],
	modalTotal: 0,
	modalSearchParams: {
		curPage: 1,
		pageSize: 10,
		serviceCode: null,
		providerId: null,
		date: null // 时间
	},
	dialogShow: {
		lookModal: false // 查看弹框
	},
	lookId: null, // 点击查看传ID
	loading: false,
	modalLoading: false
};

export default {
	namespace: "external",
	state: cloneDeep(defaultState),

	effects: {
		*getList({ payload }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					loading: true
				}
			});
			const external = yield select(state => ({ searchParams: state.external.searchParams }));
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { searchParams } = external;
			const { currentApp } = global;
			let params = Object.assign({}, searchParams);
			params.appNames = currentApp.name;
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			let res = yield call(externalAPI.getExternalList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: "setAttrValue",
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		},
		*getModalList({ payload }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					modalLoading: true
				}
			});
			const external = yield select(state => ({ modalSearchParams: state.external.modalSearchParams }));
			const { modalSearchParams } = external;
			let params = Object.assign({}, modalSearchParams);
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			const lookIdField = yield select(state => ({ lookId: state.external.lookId }));
			const { lookId } = lookIdField;
			params.exterChargeId = lookId;
			let res = yield call(externalAPI.getModalExternalList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					modalLoading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: "setAttrValue",
				payload: {
					modalTableList: res.data.contents,
					modalTotal: res.data.total,
					modalSearchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState);
		}
	}

};
