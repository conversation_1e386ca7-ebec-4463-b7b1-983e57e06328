import I18N from '@/utils/I18N';
import { isAppListVisible, isOrgAppListUnVisible, isOrgListVisible, messageError, traverseTree } from '../utils/utils';
import Store from 'store';
import { baseAPI, userAPI, dataServiceListAPI, supplierListAPI, systemFieldsAPI } from '../services';
import cloneDeep from 'lodash.clonedeep';
import { topLang } from '../constants/lang';
import { isJSON } from '../utils/isJSON';
import { message } from 'tntd';
import { compare, multipleToNull } from '../utils/operatorState';
import { DATA_TYPE_MAP } from '@/constants';

export default {
    // model 的命名空间，同时也是他在全局 state 上的属性，只能用字符串，不支持通过 . 的方式创建多层命名空间
    namespace: 'global',
    state: {
        // 初始值，优先级低于传给 dva() 的 opts.initialState。
        showApp: false,
        showOrg: false,
        showOrgApp: false,
        currentOrgCode: '',
        orgList: [],
        orgAppList: [],
        orgTreeMap: {},
        allOrgListMap: {},
        appMap: {},
        orgMap: {},
        orgReady: false,
        appList: [],
        messageStatus: false,
        noRepeatSelectArray: [],
        allMap: {},
        allFieldList: [],
        allSystemFieldList: [],
        currentApp: {
            name: null,
            dName: null
        },
        collapsed: false,
        sideMenu: {
            collapsed: false,
            activeGroupCode: false,
            openKeys: [],
            beforeOpenKeys: []
        },
        customMenuList: [],
        customTree: {},
        currentUser: {
            userName: null,
            account: null,
            uuid: null,
            avatar: null,
            token: null
        },
        personalMode: {
            showModal: false,
            lang: 'cn',
            theme: 'day',
            layout: 'default',
            simplified: true
        },
        searchDrawer: {
            showDrawer: false
        },
        allMapReady: false,
        menuTreeReady: false,
        needApp: false,
        threeServiceList: [], // 所有三方数据
        providerList: [], // 供应商列表
        multiUserModal: false // 是否多用户登录弹窗控制变量
    },
    effects: {
        *getAllFieldList({ payload }, { call, put }) {
            let response = yield call(systemFieldsAPI.getListAll);
            if (!response || !response.data) return;
            if (!response.success) return;

            yield put({
                type: 'setAttrValue',
                payload: {
                    allFieldList: response.data
                }
            });
        },
        *changeMessage({ payload }, { call, put }) {
            yield put({
                type: 'changeMessageStatus',
                payload: true
            });
            const response = yield call(messageError, payload);
            yield put({
                type: 'changeMessageStatus',
                payload: response
            });
        },
        // 获取策略配置信息
        *getWorkflowConfig({ payload }, { call, put, select }) {
            let globalStore = yield select((state) => ({ personalMode: state.global.personalMode }));
            let response = yield call(baseAPI.getVariableList, payload);
            if (!response.success) return message.error(response.message);
            const res = response?.data || {};
            let ruleFieldList = res[0]?.data || [];

            // 把系统字段变量存在本地 供codemirror控件去取出来做校验
            const TYPE_MAP = {
                STRING: I18N.models.global.ziFuXing,
                DOUBLE: I18N.models.global.xiaoShuXing,
                BOOLEAN: I18N.models.global.buErXing,
                DATETIME: I18N.models.global.riQiXing,
                ENUM: I18N.models.global.meiJuXing,
                INT: I18N.models.global.zhengShuXing
            };
            let fieldList = [];
            let cmFieldList = [];
            ruleFieldList.forEach((item) => {
                let obj = DATA_TYPE_MAP[Object.keys(DATA_TYPE_MAP).find((key) => DATA_TYPE_MAP[key].type === item.type)];
                // 目前计算公式只支持这5中类型
                if (
                    item.type === 'STRING' ||
                    item.type === 'DOUBLE' ||
                    item.type === 'BOOLEAN' ||
                    item.type === 'DATETIME' ||
                    item.type === 'ENUM'
                ) {
                    fieldList.push({
                        name: item.dName,
                        value: item.name,
                        type: item.type,
                        dataType: obj.dataType
                    });
                    cmFieldList.push({
                        name: `${item.dName}【${TYPE_MAP[item.type]}】`,
                        value: item.name,
                        type: item.type
                    });
                } else if (item.type === 'INT') {
                    fieldList.push({
                        name: item.dName,
                        value: item.name,
                        type: 'LONG',
                        dataType: obj.dataType
                    });
                    cmFieldList.push({
                        name: `${item.dName}【${TYPE_MAP[item.type]}】`,
                        value: item.name,
                        type: 'LONG'
                    });
                }
            });
            let codemirrorFieldList = [];
            for (let i = 0; i < cmFieldList.length; i++) {
                codemirrorFieldList.push(`@${cmFieldList[i].name}`);
            }
            // 排序，把长的放前面
            codemirrorFieldList.sort((a, b) => {
                if (a.length > b.length) {
                    return -1;
                }
                if (a.length < b.length) {
                    return 1;
                }
                return 0;
            });
            localStorage.codemirrorFieldList = codemirrorFieldList;
            localStorage.ruleFieldList = JSON.stringify(fieldList);
            // 把fieldList，ctrlList存本地，供初始化解析用
            localStorage.cmFieldList = JSON.stringify(cmFieldList);
            // 防止字段不存在情况

            // const { lang } = globalStore.personalMode;
            yield put({
                type: 'setMultipleAttrValue',
                payload: {
                    isLoading: false,
                    workflowConfig: {
                        ruleFieldList
                    }
                }
            });
            yield put({
                type: 'setAttrValue',
                payload: {
                    workflowConfigReady: true,
                    allSystemFieldList: fieldList
                }
            });
        },

        *getUserInfo({ payload, actions, dispatch }, { call, put, select }) {
            const { currentUser } = yield select((state) => state.global);
            let response;
            let allOrgListMap = {};
            let arr = [];
            // 运行在lightbox中，则使用 actions去获取userInfo
            const formatData = (orgGroup, apps = []) => {
                let orgTreeMap = {};
                let orgMap = {};
                let appMap = {};

                const orgList = traverseTree([cloneDeep(orgGroup)], (item) => {
                    item.title = item.name;
                    item.value = item.code;
                    item.key = item.code;
                    orgMap[item.code] = item;
                    orgTreeMap[item.uuid] = {
                        ...item,
                        children: undefined
                    };
                    arr.push(item);
                    return item;
                });

                // 机构下有权限的应用
                const appList = [
                    {
                        key: '',
                        name: I18N.models.global.quanBuQuDao
                    }
                ].concat(
                    apps.map((app) => {
                        appMap[app.name] = app.displayName;
                        return {
                            key: app.name,
                            name: app.displayName
                        };
                    })
                );

                let currentApp;

                let allTempObj = {
                    dName: I18N.models.global.quanBuQuDao,
                    name: ''
                };
                arr?.map((item) => {
                    allOrgListMap[item.code] = item;
                });

                if (localStorage.hasOwnProperty('currentApp')) {
                    // 缓存中是否存在currentApp
                    let currentAppObjStr = localStorage.getItem('currentApp');
                    if (currentAppObjStr && isJSON(currentAppObjStr)) {
                        // 存在的currentApp是否是标准JSON
                        let currentAppJson = JSON.parse(currentAppObjStr);
                        if (currentAppJson.name && currentAppJson.dName) {
                            // 判断currentApp是否是标准格式
                            currentApp = currentAppJson;
                        }
                        if (currentAppJson.name && currentAppJson.key) {
                            // 判断currentApp是否是标准格式
                            currentApp = {
                                name: currentAppJson.key,
                                dName: currentAppJson.name
                            };
                        } else {
                            currentApp = allTempObj;
                        }
                    } else {
                        currentApp = allTempObj;
                    }
                } else {
                    currentApp = allTempObj;
                }
                return {
                    orgTreeMap,
                    currentApp,
                    appList,
                    orgList,
                    appMap,
                    orgMap
                };
            };

            if (actions) {
                // 没有用户信息才监听 不然会重复监听
                if (!currentUser?.account) {
                    actions.on(actions.EVENTS_ENUM.USER_INFO_CHANGE, ({ avatar, userName, account, token, orgGroup }) => {
                        dispatch({
                            type: 'global/getAppAndNetWorkByOrgId',
                            actions,
                            payload: {
                                orgTree: orgGroup,
                                orgListSync: true // 接收到消息，同步机构列表
                            }
                        });
                        // 只有Layout中使用的是uuid，其他场景均使用给的code
                        const { orgList, orgTreeMap, appList, currentApp, appMap, orgMap } = formatData(orgGroup, apps);
                        dispatch({
                            type: 'global/setAttrValue',
                            payload: {
                                currentUser: {
                                    avatar,
                                    userName,
                                    account,
                                    token
                                },
                                orgTreeMap,
                                currentApp,
                                appList,
                                appMap,
                                orgMap,
                                orgList
                            }
                        });
                    });
                }
            }
            response = yield call(userAPI.getUserInfo, payload);
            response = response?.data ? response.data : response;
            const { avatar, userName, account, token, orgGroup, apps } = response || {};
            Object.assign(currentUser, { avatar, userName, account, token });

            // 只有Layout中使用的是uuid，其他场景均使用给的code
            const { orgList, orgTreeMap, appList, currentApp, appMap, orgMap } = formatData(orgGroup, apps);

            yield put({
                type: 'setAttrValue',
                payload: {
                    currentUser,
                    orgTreeMap,
                    currentApp,
                    apps,
                    appList,
                    orgList,
                    appMap,
                    orgMap,
                    allOrgListMap
                }
            });
            yield put({
                type: 'getAppAndNetWorkByOrgId',
                actions,
                payload: {
                    orgTree: orgGroup
                }
            });
        },

        // 根据机构获取应用及网点信息
        *getAppAndNetWorkByOrgId({ payload, actions }, { call, put, select }) {
            //  根据默认orgUuid获取应用列表
            let { orgTree, orgSync, curOrgTree } = payload || {};
            let { uuid: orgUuid, name: orgName, code: orgCode } = orgTree || {};

            // 基座触发
            let orgUuids = orgUuid || curOrgTree.uuid;
            let currentOrgCode = orgCode || curOrgTree.code;

            if (localStorage.hasOwnProperty('currentOrg') && orgTree) {
                try {
                    const currentOrg = JSON.parse(localStorage.getItem('currentOrg'));
                    orgUuids = currentOrg.key;
                    currentOrgCode = currentOrg.code;
                } catch (e) {
                    console.log(e);
                }
            }

            // 获取当前机构下的应用
            let appData = (yield call(userAPI.getAppByOrgId, { orgUuid: orgUuids })) || [];
            appData = appData?.data ? appData.data : [];

            // 机构下有权限的应用
            const orgAppList = [
                {
                    key: '',
                    name: I18N.models.global.quanBuQuDao
                }
            ].concat(
                appData?.map((app) => ({
                    key: app.name,
                    name: app.displayName
                }))
            );

            // 如果在微前端中则发送当前机构下的应用
            if (actions) {
                if (orgSync) {
                    // uuid的机构List
                    const orgUuidList = traverseTree([cloneDeep(orgTree)], (item) => {
                        item.title = item.name;
                        item.value = item.uuid;
                        item.key = item.uuid;
                    });
                    actions.setOrgList(orgUuidList?.length ? orgUuidList[0] : {});
                }
                actions.setOrgAppList(orgAppList);
            }

            yield put({
                type: 'setAttrValue',
                payload: {
                    orgAppList,
                    currentOrgCode,
                    orgReady: true
                }
            });
        },

        *getAllMap({ payload }, { call, put }) {
            const response = yield call(baseAPI.getAllMap, payload);
            const response2 = yield call(baseAPI.getIntegrationInfo, payload);
            const response3 = yield call(baseAPI.getIntegrationInfo, payload);
            if (!response) {
                return;
            }
            if (!response.success) {
                message.error(response.message);
                return;
            }
            if (response2?.success) {
                response.data.isIntegrationTG = response2.data || false;
            }
            yield put({
                type: 'initAllMap',
                payload: response
            });
        },

        *getAllAppName({ payload }, { call, put }) {
            const response = yield call(baseAPI.getAllAppName, payload);
            if (!response) {
                return;
            }
            if (!response.success) {
                message.error(response.msg);
                return;
            }
            let list = [
                {
                    name: '',
                    dName: I18N.models.global.quanBuQuDao
                }
            ];
            response.data.forEach((item) => {
                list.push({
                    name: item.name,
                    dName: item.displayName
                });
            });
            yield put({
                type: 'setMultipleAttrValue',
                payload: {
                    appList: list
                }
            });
        },

        *getUserMenuTree({ payload, actions }, { call, put }) {
            let response;

            // if (actions) {
            //     response = {
            //         data: actions.getMenuTreeInfo()
            //     };
            //     actions.on(actions.EVENTS_ENUM.MENU_TREE_INFO_CHANGE, (menuTreeInfo) => {
            //         put({
            //             type: 'initUserMenuTree',
            //             payload: {
            //                 data: menuTreeInfo
            //             }
            //         });
            //     });
            // } else {
            response = yield call(userAPI.getUserMenuTree, payload);
            if (response && !response.success) {
                message.error(response.message);
                return;
            }
            // }

            yield put({
                type: 'initUserMenuTree',
                payload: response
            });
            yield put({
                type: 'setMultipleAttrValue',
                payload: {
                    currentUser: {
                        userName: response?.data?.user?.userName,
                        account: response?.data?.user?.account,
                        uuid: response?.data?.user?.uuid,
                        avatar: response?.data?.user?.avatar
                    }
                }
            });
        },

        // 获取所有三方数据
        *getAllThreeService({}, { call, put }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeServiceList: []
                }
            });
            const res = yield call(dataServiceListAPI.getListAll, {});
            if (!res) return;
            if (res && !res.success) return message.error(res.msg);
            if (!res.data) return;
            let data = [];
            // 过滤掉没有表示的数据
            res.data.contents.forEach((item) => {
                if (item.name) {
                    data.push(item);
                }
            });
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeServiceList: data
                }
            });
        },

        // 获取所有供应商数据
        *getAllProvider({}, { call, put }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    providerList: []
                }
            });
            const res = yield call(supplierListAPI.getListAll, {});
            if (!res) return;
            if (res && !res.success) return message.error(res.msg);
            if (!res.data) return;
            yield put({
                type: 'setAttrValue',
                payload: {
                    providerList: res.data.contents ? res.data.contents : []
                }
            });
        }
    },

    reducers: {
        setAttrValue(state, { payload }) {
            return (function multiple(state, newState) {
                let stateChange = state;
                // 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
                stateChange = compare(stateChange, newState);
                for (let [key, value] of Object.entries(stateChange)) {
                    // 这里严格判断value是否是对象{},不能使用typeof,原因自己查
                    if (
                        Object.prototype.toString.call(value) === '[object Object]' &&
                        newState[key] !== undefined &&
                        newState[key] !== null
                    ) {
                        stateChange[key] = multiple(value, newState[key]);
                    } else {
                        if (newState[key] !== undefined && newState[key] !== null) {
                            stateChange[key] = newState[key];
                        }
                        if (newState[key] === null) {
                            stateChange[key] = multipleToNull(stateChange[key]);
                        }
                    }
                }
                return stateChange;
            })(cloneDeep(state), payload);
        },

        setMultipleAttrValue(state, { payload }) {
            return (function multiple(state, newState) {
                let stateChange = state;
                for (let [key, value] of Object.entries(stateChange)) {
                    // 这里严格判断value是否是对象{},不能使用typeof,原因自己查
                    if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined) {
                        stateChange[key] = multiple(value, newState[key]);
                    } else {
                        if (newState[key] !== undefined) {
                            stateChange[key] = newState[key];
                        }
                    }
                }
                return stateChange;
            })(cloneDeep(state), payload);
        },

        changeLayoutCollapsed(state, { payload }) {
            Store.set('collapsed', !!payload);
            return {
                ...state,
                collapsed: payload
            };
        },

        changeMessageStatus(state, { payload }) {
            return {
                ...state,
                messageStatus: payload
            };
        },

        initAllMap(state, { payload }) {
            let { allMap, currentApp } = state;
            allMap = payload.data || {};

            let defaultAppObj = {
                dName: topLang.common('allApplication'),
                name: ''
            };

            // 这里判断localStorage在缓存中的多种复杂情况
            if (localStorage.hasOwnProperty('currentApp')) {
                // 缓存中是否存在currentApp
                let currentAppObjStr = localStorage.getItem('currentApp');
                if (currentAppObjStr && isJSON(currentAppObjStr)) {
                    // 存在的currentApp是否是标准JSON
                    let currentAppJson = JSON.parse(currentAppObjStr);
                    if (currentAppJson.name && currentAppJson.dName) {
                        // 判断currentApp是否是标准格式
                        currentApp = currentAppJson;
                    } else if (currentAppJson.key) {
                        currentApp = {
                            name: currentAppJson.key,
                            dName: currentAppJson.name
                        };
                    } else {
                        currentApp = defaultAppObj;
                        localStorage.setItem('currentApp', JSON.stringify(currentApp));
                    }
                } else {
                    currentApp = defaultAppObj;
                    localStorage.setItem('currentApp', JSON.stringify(currentApp));
                }
            } else {
                currentApp = defaultAppObj;
                localStorage.setItem('currentApp', JSON.stringify(currentApp));
            }

            return {
                ...state,
                allMap,
                currentApp,
                allMapReady: true
            };
        },

        setCustomMenuList(state, action) {
            return {
                ...state,
                customMenuList: action.payload.customMenuList
            };
        },

        initUserMenuTree(state, { payload }) {
            let { customTree } = state;
            customTree = payload.data || {};
            // window.localStorage.setItem('customTree', JSON.stringify(customTree));

            return {
                ...state,
                customTree,
                menuTreeReady: true
            };
        }
    },

    subscriptions: {
        setup({ dispatch, history }) {
            history.listen((location) => {
                dispatch({
                    type: 'setAttrValue',
                    payload: {
                        showApp: isAppListVisible(location.pathname),
                        showOrg: isOrgListVisible(location.pathname),
                        showOrgApp: !isOrgAppListUnVisible(location.pathname)
                    }
                });
            });
        }
    }
};
