import { message } from "tntd";
import { appServiceListAPI } from "../../services";
import cloneDeep from "lodash.clonedeep";
import { compare } from "../../utils/operatorState";

const defaultState = {
	tableList: [],
	total: 0,
	relationOrgList: [], // 关联机构列表
	searchParams: {
		curPage: 1,
		pageSize: 10,
		displayName: null, // 供应商名称
		serviceName: null, // 三方服务名称对应标识
		status: null // 状态(1上线/2下线)
	},
	dialogShow: {
		addEditModal: false // 新增、编辑、查看modal
	},
	dialogData: {
		addEditModalData: {
			id: null,
			appName: null, // 应用名称
			displayName: null, // 服务名称
			name: null, // 业务系统服务标识
			serviceName: null, // 三方服务接口名称
			channelName: null, // 业务系统标识
			cacheDay: null, // 数据缓存天数
			retry: null, // 重试次数
			timeout: null, // 超时时间
			switchFlag: 2, // 调用异常是否切换 1是，2否
			dataType: null, // 数据类型
			responseType: null, // 返回类型(1原始报文/2解析报文)
			routeConfig: [""] // 路由切换
		}
	},
	loading: false,
	modalType: 1, // 弹框类型 1新增，2修改，3查看
	updateId: null // 点击修改传过去id
};

export default {
	namespace: "appServiceList",
	state: cloneDeep(defaultState),

	effects: {
		*getList({ payload }, { call, put, select }) {
			if (payload && !payload.noLoading) {
				yield put({
					type: "setAttrValue",
					payload: {
						loading: true
					}
				});
			}
			const appServiceList = yield select(state => ({ searchParams: state.appServiceList.searchParams }));
			const { searchParams } = appServiceList;
			let params = Object.assign({}, searchParams);
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { currentApp } = global;
			params.appNames = currentApp.name;
			if (payload) {
				const { curPage, pageSize, sortField, sortRule } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;

				if (sortField) {
					params.sortField = sortField;
				} else {
					delete params.sortField;
				}

				if (sortRule) {
					params.sortRule = sortRule;
				} else {
					delete params.sortRule;
				}
			}

			let res = yield call(appServiceListAPI.getList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: "setAttrValue",
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize,
						// 为了配合下面判断 null 的逻辑，这边必须返回 null。。
						sortField: (payload && payload.sortField) ? payload.sortField : null,
						sortRule: (payload && payload.sortRule) ? payload.sortRule : null
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState);
		}
	}

};
