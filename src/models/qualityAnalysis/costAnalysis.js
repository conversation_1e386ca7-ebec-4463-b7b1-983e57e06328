import I18N from '@/utils/I18N';
import { message } from 'tntd';
import { costAnalysisAPI } from '../../services';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import { compare } from '../../utils/operatorState';
import { formatStartTime, formatEndTime } from '@/utils/utils';

const defaultState = {
	searchParams: {
		date: [formatStartTime(moment().subtract(6, 'days').valueOf()), formatEndTime(moment().valueOf())],
		top: null,
		serviceType: null
	},
	chartData: null,
	loading: false
}

export default {
	namespace: 'costAnalysis',
	state: defaultState,

	effects: {
		*getCostAnalyse({ }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: true
				}
			});
			const costAnalysis = yield select(state => ({ searchParams: state.costAnalysis.searchParams }));
			const { searchParams } = costAnalysis;
			const personalMode = yield select(state => state.global.personalMode);
			const lang = personalMode.lang === 'cn' ? 'cn' : 'en';
			let params = Object.assign({}, searchParams);
			if (params.date) {
				params.startDate = params.date[0];
				params.endDate = params.date[1];
				delete params.date;
			}
			if (params.top === I18N.qualityanalysis.costanalysis.quanBu || params.top === 'All') params.top = null;

			let res = yield call(costAnalysisAPI.getCostAnalyse, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			let xAxisData = [];
			let seriesData = [];
			res.data.forEach(item => {
				xAxisData.push(item.serviceName);
				seriesData.push(item.serviceTotalCost);
			});
			let chartData = {
				xAxisData,
				seriesData,
				yAxisName: lang === 'cn' ? I18N.qualityanalysis.costanalysis.diaoYongZongChengBen : 'Call total cost (yuan)',
				xAxisName: lang === 'cn' ? I18N.qualityanalysis.costanalysis.fuWuMingCheng : 'Service name',
				xAxisRotate: 30
			};
			yield put({
				type: 'setAttrValue',
				payload: {
					chartData: (res.data && res.data.length > 0) ? chartData : null
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState)
		}
	}

};
