import { message } from "tntd";
import { appChannelAPI } from "../../services";
import cloneDeep from "lodash.clonedeep";
import { compare } from "../../utils/operatorState";
import moment from "moment";
import { formatStartTime, formatEndTime } from "@/utils/utils";
import { appChannelLang } from "@/constants/lang";

export default {
	namespace: "appChannel",
	state: {
		loading: false,
		chartData: null,
		searchParams: {
			top: null,
			date: [moment().subtract(6, "days").valueOf(), moment().valueOf()] // 时间 - 默认一周
		},
		loading2: false,
		thirdServiceData: null,
		orginizeData: null,
		isShow: false // 是否展示三方和调用数据
	},

	effects: {
		// 获取大盘数据
		*getChannelInvokeStatistic({ }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					loading: true,
					thirdServiceData: null,
					orginizeData: null,
					isShow: false
				}
			});
			const appChannel = yield select(state => ({ searchParams: state.appChannel.searchParams }));
			const { searchParams } = appChannel;
			let params = Object.assign({}, searchParams);
			if (params.date) {
				params.startDate = formatStartTime(params.date[0]);
				params.endDate = formatEndTime(params.date[1]);
				delete params.date;
			}
			if (params.top === appChannelLang.common("all")) params.top = null; // 全部

			let res = yield call(appChannelAPI.getChannelInvokeStatistic, params);
			yield put({
				type: "setAttrValue",
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			let legendData = [
				{ name: appChannelLang.common("failedCount"), value: [] }, // 失败数量
				{ name: appChannelLang.common("queryCount"), value: [] }, // 查询数量
				{ name: appChannelLang.common("cacheCount"), value: [] } // 缓存数量
			];
			let xAxisData = [];
			let seriesData = [];
			res.data.channelInvokeList &&
				res.data.channelInvokeList.forEach(item => {
					xAxisData.push(item.channelName);
					legendData[0].value.push(item.channelFailCount);
					legendData[1].value.push(item.channelTotalCount);
					legendData[2].value.push(item.channelCachedCount);
				});
			legendData.forEach(item => {
				seriesData.push({
					name: item.name,
					type: "bar",
					barMaxWidth: 40,
					itemStyle: {
						barBorderRadius: 4
					},
					data: item.value,
					barGap: 0
				});
			});
			let chartData = {
				legendData: [appChannelLang.common("failedCount"), appChannelLang.common("queryCount"), appChannelLang.common("cacheCount")], // "失败数量", "查询数量", "缓存数量"
				xAxisData,
				seriesData,
				list: res.data.channelInvokeList
			};
			yield put({
				type: "setAttrValue",
				payload: {
					chartData: (res.data.channelInvokeList && res.data.channelInvokeList.length > 0) ? chartData : null
				}
			});
		},
		// 获取三方服务明细和调用机构明细
		*getChannelDetailByType({ payload }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					loading2: true,
					thirdServiceData: null,
					orginizeData: null,
					isShow: true
				}
			});
			const appChannel = yield select(state => ({ searchParams: state.appChannel.searchParams }));
			const { searchParams } = appChannel;
			let params = Object.assign({}, searchParams);
			delete params.top;
			if (params.date) {
				params.startDate = formatStartTime(params.date[0]);
				params.endDate = formatEndTime(params.date[1]);
				delete params.date;
			}
			params.channelCode = payload.channelCode;
			params.type = payload.type;

			let res = yield call(appChannelAPI.getChannelDetailByType, params);
			yield put({
				type: "setAttrValue",
				payload: {
					loading2: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			let seriesData = [];
			let seriesData2 = [];
			res.data.serviceDetailList &&
				res.data.serviceDetailList.forEach(item => {
					seriesData.push({
						name: item.serviceName,
						value: item.count
					});
				});
			res.data.organizeDetailList &&
				res.data.organizeDetailList.forEach(item => {
					seriesData2.push({
						name: item.organizeName,
						value: item.count
					});
				});
			let thirdServiceData = {
				seriesData
			};
			let orginizeData = {
				seriesData: seriesData2
			};
			yield put({
				type: "setAttrValue",
				payload: {
					thirdServiceData: (res.data.serviceDetailList && res.data.serviceDetailList.length > 0) ? thirdServiceData : null,
					orginizeData: (res.data.organizeDetailList && res.data.organizeDetailList.length > 0) ? orginizeData : null
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}

};
