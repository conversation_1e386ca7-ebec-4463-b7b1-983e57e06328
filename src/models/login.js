import { routerRedux } from 'dva/router';
import { userAPI } from '../services';
import { message } from 'tntd';
import { rsaPwd } from '@/utils/user';

export default {
    namespace: 'login',

    state: {
        status: undefined
    },

    effects: {
        // 模拟登录
        *mockLogin({ payload }, { call, put }) {
            const { account, password } = payload;
            const params = { account, password: rsaPwd(password) };
            let { tempRandom, authSuccess, authMessage } = ['', false, ''];
            // 获取加盐随机数
            const authResult = yield call(userAPI.auth, params);

            tempRandom = authResult.data;
            authSuccess = authResult.success;
            authMessage = authResult.message;

            if (tempRandom) {
                const res = yield call(userAPI.userLogin, { ...params, tempRandom });
                if (res.success) {
                    const csrfToken = res.data.csrfToken;
                    sessionStorage.setItem('_csrf_', csrfToken);
                    localStorage.setItem('_sync_qjt_csrf_', csrfToken); // 新的csrf同步到其他页面
                    localStorage.setItem('developmentLoginData', JSON.stringify(params));

                    location.reload();
                } else {
                    message.error(res.message);
                    return Promise.reject(res.message);
                }
                return;
            }
            message.error(authMessage);
            return Promise.reject(authMessage);
        },

        *accountSubmit({ payload }, { call, put }) {
            yield put({
                type: 'changeSubmitting',
                payload: true
            });
            const response = yield call(userAPI.signIn, payload);
            yield put({
                type: 'changeLoginStatus',
                payload: response
            });
            yield put({
                type: 'changeSubmitting',
                payload: false
            });
        },

        *signOut({}, { call }) {
            if (process.env.SYS_ENV === 'development') {
                return;
            }
            const response = yield call(userAPI.signOut);
            if (response) {
                sessionStorage.setItem('_csrf_', '');
                // sessionStorage.clear();
                // localStorage.clear();
                sessionStorage.removeItem('_sync_qjt_csrf_');
                localStorage.removeItem('_sync_qjt_csrf_');
            }
        },
        *logout({}, { call, put }) {
            if (process.env.SYS_ENV === 'development') {
                return;
            }
            const response = yield call(userAPI.signOut);
            if (response) {
                yield put({
                    type: 'goLogin',
                    payload: {
                        homePage: true
                    }
                });
            }
        },
        *goLogin({ payload }, {}) {
            if (process.env.SYS_ENV === 'development') {
                return;
            }
            sessionStorage.setItem('_csrf_', '');
            // sessionStorage.clear();
            // localStorage.clear();
            sessionStorage.removeItem('_sync_qjt_csrf_');
            localStorage.removeItem('_sync_qjt_csrf_');
            const { homePage } = payload || {};
            const { origin, pathname, search } = window.location || {};
            const callbackUrl = origin + pathname + encodeURIComponent(search);
            if (pathname !== '/user/login') {
                // homePage为true为主动退出默认回到首页
                if (homePage) {
                    window.location.href = '/user/login';
                } else {
                    window.location.href = '/user/login?callbackUrl=' + callbackUrl;
                }
            }
        }
    },

    reducers: {
        changeLoginStatus(state, { payload }) {
            console.log(payload);
            return {
                ...state,
                status: payload.success ? 'ok' : 'error',
                type: payload.type,
                info: payload.data
            };
        },
        changeSubmitting(state, { payload }) {
            return {
                ...state,
                status: payload,
                submitting: payload
            };
        }
    }
};
