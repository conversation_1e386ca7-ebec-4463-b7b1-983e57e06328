import { message } from "tntd";
import cloneDeep from 'lodash.clonedeep';
import { etlAPI } from '@/services';
import { compare } from '../../utils/operatorState';

const defaultState = {
	tableList: [],
	total: 0,
	searchParams: {
		curPage: 1,
		pageSize: 10,
		displayName: null,
		type: null,
		status: null
	},
	dialogShow: {
		addEditModal: false // 新增、编辑、查看modal
	},
	dialogData: {
		addEditModalData: {
			uuid: null,
			displayName: null, // ETL处理器名称
			name: null, // 标识
			appName: null, // 应用
			script: null,
			defaultScript: null,
			type: '2', // 2前置处理器/3后置处理器
			comment: null, // 备注
			commonUuids: null,
			commonCode: [] // 公共代码
		}
	},
	loading: false,
	modalType: 1, // 弹框类型 1新增，2修改，3查看
	updateId: null // 点击修改传过去id
};

export default {
	namespace: 'etl',
	state: cloneDeep(defaultState),

	effects: {
		*getList({ payload }, { call, put, select }) {
			if (payload && !payload.noLoading) {
				yield put({
					type: 'setAttrValue',
					payload: {
						loading: true
					}
				});
			}
			const etl = yield select(state => ({ searchParams: state.etl.searchParams }));
			const { searchParams } = etl;
			let params = Object.assign({}, searchParams);
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { currentApp } = global;
			params.appNames = currentApp.name;

			let res = yield call(etlAPI.getList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState);
		}
	}
};
