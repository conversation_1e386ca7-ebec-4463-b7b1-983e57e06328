import I18N from '@/utils/I18N';
import { getLanguage } from '../app';

let uidCounter = 0;
const UID_PROPERTY = 'atreus_uid_' + ((Math.random() * 1e9) >>> 0);
const globalLocales = Object.create(null);
const es6Flag = '__esModule';

/**
 * 简单获取uid
 * @param {Class|Object} obj
 */
function getUID(obj) {
	return obj[UID_PROPERTY] || (obj[UID_PROPERTY] = ++uidCounter);
}

function forOwn(obj, callback) {
	if (obj) {
		Object.keys(obj).forEach((k) => {
			callback(obj[k], k);
		});
	}
}

function isNil(arg) {
	return arg === null || typeof arg === 'undefined';
}

/**
 * 遍历模块集合
 * @param {Function} requireContext
 * @param {Function} callback
 */
function eachModule(requireContext, callback) {
	requireContext.keys().forEach(key => {
		const mod = requireContext(key);
		callback(mod[es6Flag] ? (mod.default || mod) : mod, key);
	});
}

/**
 * 处理装饰器参数
 * @param {String|Object} key
 * @param {Object|undefined} val
 * @param {Function} callback
 */
function handleOptions(key, val, callback) {
	if (typeof key === 'object') {
		forOwn(key, callback);
	} else if (typeof key === 'string' && typeof val === 'object') {
		callback(val, key);
	} else if (typeof key === 'function') {
		eachModule(key, (model, k) => {
			callback(model, k.replace(/\.\/(.+)\.(properties|js||json)/, '$1'));
		});
	} else {
		throw new TypeError(I18N.common.locale.canShuCuoWu);
	}
}

/**
 * 挂载本地化配置
 * 一个入参表示挂载某一个文件夹下面的所有的配置
 * 两个入参表示挂载自定义挂载
 *
 * es7挂载方法:
 *
 * @locale(require.context(‘文件路径’, true, /\.\/.+properties/))
 * @locale(require.context(‘文件路径’, true, /\.\/.+js/))
 * @locale(require.context(‘文件路径’, true, /\.\/.+json/))
 *
 * @locale({
 *   en: require(‘文件路径’),
 *   cn: require(‘文件路径’)
 * })
 *
 * @locale('en', require(‘文件路径’))
 * @locale('en', require(‘文件路径’))
 *
 * 使用方法
 *
 * { this.resource(key) }
 *
 * @param {Function|String|Object} language
 * @param {Object|undefined} locale
 */
export default function(language, locale) {
	return function(targetClass) {
		const classUID = getUID(targetClass);

		// 初始化locale缓存
		handleOptions(language, locale, (val, key) => {
			key += '_' + classUID;
			let arr;

			// 一个组件可能绑定多个本地化文件
			if (!(arr = globalLocales[key])) {
				arr = globalLocales[key] = [];
			} else if (arr.indexOf(val) > -1) { // 去重复
				return;
			}
			arr.push(val);
		});

		// 挂载$locale方法
		if (!targetClass.prototype.resource) {
			targetClass.prototype.resource = function(key, lang) {
				if (!lang) {
					lang = getLanguage();
				}

				// 从上一次的缓存中获取
				const cache = this._$localCache || (this._$localCache = Object.create(null));
				let val = cache[`${lang}_${key}`];

				// 匹配本地化配置
				if (!val) {
					const localeSet = globalLocales[`${lang}_${classUID}`] || [];
					localeSet.some(l => !!(val = l[key]));
				}
				return val || '';
			};
		}

		return targetClass;
	};
}

const BY_INDEX = /\{\s*(\d+)\s*\}/g;
const BY_KEY = /\{\s*(\w+)\s*\}/g;
if (!String.prototype.tformat) {
	/**
	 * 格式化字符串
	 *
	 * '当前第{0}页, 共{1}条记录'.tformat(1, 5) ==> 当前第1页, 共5条记录
	 * '共{number}条记录'.tformat({ number: 5 }) ==> 共5条记录
	 *
	 * @param {Object| ...any} 传入一个对象，或者多个非对象数据
	 */
	// eslint-disable-next-line no-extend-native
	String.prototype.tformat = function(arg) {
		const len = arguments.length;
		let val = this;
		if (!isNil(arg) && len) {
			let regexp = BY_KEY;
			if (typeof arg !== 'object') {
				arg = arguments;
				regexp = BY_INDEX;
			}
			val = val.replace(regexp, function(str, index) {
				const newVal = arg[index];
				return isNil(newVal) ? str : newVal;
			});
		}
		return val;
	};
}

const BY_EXPRESSION = /\{([^\{\}]+)\}/g;
const tmplCache = Object.create(null);
function compile(str, obj) {
	let preexec = '';
	forOwn(obj, (v, k) => {
		preexec += `var ${k}=${typeof v === 'string' ? ('"' + v + '"') : v};`;
	});
	str = str.replace(BY_EXPRESSION, function(s, exp) {
		return `"+""+(${exp})+""+"`;
	});
	return new Function(`${preexec}return "${str}";`);
}
if (!String.prototype.eformat) {
	/**
	 * 格式化字符串
	 *
	 * 'A total of { page } record{ page > 1? "s" : "" }'.eformat({ page: 5 }) ==> A total of 5 records
	 *
	 * @param {Object| ...any} 传入一个对象，或者多个非对象数据
	 */
	// eslint-disable-next-line no-extend-native
	String.prototype.eformat = function(obj) {
		let val = this;
		if (obj && typeof obj === 'object') {
			let fn = tmplCache[val];
			if (!fn) {
				fn = compile(val, obj);
				tmplCache[val] = fn;
			}
			val = fn();
		}
		return val;
	};
}

