body, html {
	width: 100%;
	height: 100%;
}

body {
	height: 100%;
	overflow-y: hidden;
	background-color: #f0f2f5;
	text-rendering: optimizeLegibility;
	text-rendering: initial;
	-webkit-font-smoothing: initial;
	font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

:global(#root) {
	height: 100vh;
	position: relative;
}

:global {
	.globalSpin {
		width: 100%;
		margin: 40px auto !important;
		text-align: center;
		display: inherit;
	}
 }
 #root{
	.ant-layout-content {
        min-width: 1024px;
		> .ant-spin-spinning{
			width: 22px;
			padding-top: 2px;
		}
    }
 }
 .ued-framework-layout {
	& > .ant-layout .ant-layout-content {
		height: calc(~'100vh - 60px');
		overflow: auto;
	}

	.logo {
		width: unset;
		height: unset;
		position: relative;
		top: -2px;
	}
}
.isEmptyLayout{
	.page-global-body{
		height:inherit !important;
	}
	.ant-layout-content > div:nth-child(2):not(.page-global-header){
		height:inherit !important;
	}
}

.m-tooltip-alert{
	max-width: none !important;
	.ant-tooltip-arrow::before{
		background-color: #2196F3;
	}
	.ant-tooltip-inner{
		padding: 0;
		.ant-alert-with-description{
			padding: 6px 8px 10px 40px;
			.ant-alert-icon{
				font-size: 16px;
				top: 9px;
				left: 14px;
			}
			.ant-alert-message{
				color: rgba(0, 0, 0, 0.65);
				font-size: 14px;
				line-height: 22px;
			}
		}
	}
}

.cucoxF > .ant-layout-content > div:nth-child(2){
	width: 100%;
}

.tnt-qls-title{
	z-index: 3 !important;
}
.ant-pagination-options{
	.ant-select-dropdown-menu{
		text-align: left;
	}
}

.ace-editor {
	border: 1px solid #ccc;
	.ace_print-margin {
		visibility: hidden !important;
	}
}


.lang-en {
	.price-config-item{
		.price-item{
			.u-label{
				width: 120px !important;
			}
		}
		.field-item{
			.u-label{
				width: 120px !important;
			}
			> div{
				>div:nth-child(1){
					width: 200px !important;
				}
				// >div:nth-child(2){
				// 	width: 100px !important;
				// }
			}
		}

	}
	.statistics-method {
		.attr-select{
			> span{
				width: 120px !important;
			}
			.required::before{
				right: 112px !important;
			}
		}
	}
		.basic-info-add-edit-modal {
			width: 960px !important;
			.modal-box {
				.u-label{
					width: 145px !important;
				}
			}
		}
		.sync-interface-add-detail {
			.ant-form{
				.input-actions-btn{
					left: 170px !important;
				}
			}
		}

		.g-batch-call-add {
			.page-global-body-main{
				.wraps {
					.wrap {
						.box {
							.label {
								width: 136px;
							}
						}
					}
				}
			}
		}
}
