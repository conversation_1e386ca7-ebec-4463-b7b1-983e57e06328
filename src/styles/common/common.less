// 系统用的比较多的样式
:global{
	.ant-layout {
		background-color: #f1f2f5;
	}

    // 表格操作按钮样式
    .u-operate{
        display: inline-block;
        // width: 100%;
        color: #126BFB;
        cursor: pointer;
        margin-right: 8px;
        transition: all 0.5s;
        &:last-child{
            margin-right: 0;
        }
        &:hover{
            color: #1084ef;
        }
        i{
            font-size: 16px;
        }
    }
    .org-pop-opera-btn{
        .u-operate{
            display: block;
        }
    }
    .not-allow{
		color: #ccc !important;
		cursor: not-allowed !important;
	}
    // 表格限制长度。。。
    .u-ellipsis{
        display: inline-block;
        max-width: 100px;
        text-overflow: ellipsis;
		overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
    }
    // 更多样式
    .u-more-operate{
		width: 110px !important;
		li{
			span{
                text-align: center;
                display: block;
			}
        }
        .ant-dropdown-menu-item{
            line-height: 32px !important;
        }
	}
    // 弹框布局样式
    .modal-box{
        margin-bottom: 15px;
        &:last-child{
            margin-bottom: 0;
        }
        span{
            display: inline-block;
        }
        .u-label, .u-label2{
            width: 120px;
            text-align: right;
            padding-right: 10px;
            vertical-align: middle;
            margin-top: -3px;
            b{
                color: #f00;
                vertical-align: middle;
                margin-right: 3px;
            }
        }
        .u-label-table{
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .u-input, .u-select, .u-text{
            width: 250px;
        }
        .u-textarea{
            width: 620px;
        }
        .u-question{
            font-size: 20px;
            margin-left: 10px;
            vertical-align: middle;
            cursor: pointer;
        }
    }

    // 弹框列表样式
    .m-modal-list{
        .page-global-header{
            border: none;
            padding: 0;
            margin-top: 10px;
        }
        .ant-modal-body{
            padding: 0 24px;
        }
        .page-global-body{
            height: auto !important;
            padding: 0;
            .page-global-body-main{
                padding-bottom: 40px;
            }
        }
    }
}
