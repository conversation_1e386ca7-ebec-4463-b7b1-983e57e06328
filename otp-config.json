{"otpDir": "./.octopus", "proType": "react", "srcLang": "zh-CN", "distLangs": ["en-US"], "googleApiKey": "", "baiduApiKey": {"appId": "", "appKey": ""}, "baiduLangMap": {"en-US": "en"}, "translateOptions": {"concurrentLimit": 10, "requestOptions": {}}, "fileSuffix": [".ts", ".js", ".vue", ".jsx", ".tsx"], "defaultTranslateKeyApi": "<PERSON><PERSON><PERSON>", "importI18N": "import I18N from '@/utils/I18N';", "include": ["./src"], "exclude": ["./src/constants/lang", "./src/common/otp", "./src/components/WorkFlowEditor/otp"], "regPath": "locale", "resourcePath": ["./src/constants/locale"], "reservedKey": ["template", "case"]}