"use strict";

const { EOL } = require("os");

const R_KEY_VAL = /^\s*([\w.-]+)\s*=\s*(.*)?\s*$/;
const R_COMMENT = /\s+#\s*.*/g;

class Properties {

	constructor(options) {
		this.cache = {};
		this.options = options || {};
		this.readline = this.readline.bind(this);
	}

	load(str) {
		if (str) {
			const lines = str.split(EOL);
			lines.forEach(this.readline);
		}
		return this;
	}

	getProperty(name) {
		return this.cache[name];
	}

	readline(line) {
		const { cache } = this;
		const keyValue = line.match(R_KEY_VAL);
		if (keyValue) {
			const [, key, value] = keyValue;
			cache[key] = (value || "").replace(R_COMMENT, "");
		}
	}

}

module.exports = Properties;
