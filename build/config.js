const path = require('path');
const PORT = 8000;
const MOCK = true; // 控制这里的是否开启mock
const actionMap = {
    'build:v2': 'v2',
    'build:v3': 'v3',
    start: 'v3',
    mock: 'v3'
};
const version = actionMap[process.env.npm_lifecycle_event] || 'v3';
const sourcePrefix = 'tianzuo-resource/' + version;

module.exports = {
    projectCode: 'tianzuo',
    staticPath: sourcePrefix,
    staticSourcePath: sourcePrefix + '/static',
    version,
    dev: {
        hot: true,
        mock: MOCK,
        port: PORT,
        proxyTable: {
            '/bridgeApi': {
                target: 'http://10.58.16.247:8000/', // 开发环境
                // target: "http://10.57.17.154:8105", // 测试环境
                changeOrigin: true,
                pathRewrite: {
                    // '^/bridgeApi': ''
                }
            },
            '/handleApi': {
                target: 'http://10.58.16.247:8000/', // 开发环境
                // target: "http://10.57.17.154:8105", // 测试环境
                changeOrigin: true,
                pathRewrite: {
                    // "^/handleApi": "/api"
                }
            },
            '/noahApi': {
                target: 'http://10.58.16.247:8000/', // 开发环境
                // target: "http://10.57.17.154:8105", // 测试环境
                changeOrigin: true,
                pathRewrite: {
                    // "^/handleApi": "/api"
                }
            },
            '/captainApi': {
                target: 'http://10.58.16.247:8000/', // 开发环境
                // target: "http://10.58.12.237:8084/", // 测试环境
                changeOrigin: true,
                pathRewrite: {
                    // "^/captainApi": "/api"
                }
            },
            '/mockApi': {
                target: 'https://sinan.tongdun.me/mock/23176/api', // Mock
                changeOrigin: true,
                pathRewrite: {
                    '^/mockApi': ''
                }
            }
        },
        autoOpenBrowser: true,
        devtool: 'eval-source-map',
        // 环境变量在这里控制，可查看package.json中原来脚本中的环境变量定义进行指定
        env: {
            MOCK,
            PORT,
            POC: false,
            SYS_ENV: 'development',
            BABEL_ENV: 'development',
            PATH: '/' + sourcePrefix,
            version
        }
    },
    build: {
        assetsRoot: path.resolve(__dirname, '../dist'),
        devtool: 'source-map',
        env: {
            PORT,
            POC: false,
            SYS_ENV: 'production',
            BABEL_ENV: 'production',
            PATH: '/' + sourcePrefix,
            version
            // SYS_ENV: 'development',
            // BABEL_ENV: 'development'
        }
    }
};
