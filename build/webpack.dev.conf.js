const path = require('path');
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const baseWebpackConfig = require('./webpack.base.conf');
const config = require('./config');

const { staticPath } = config;

module.exports = merge(baseWebpackConfig, {
    mode: 'development',
    cache: true,
    output: {
        chunkFilename: staticPath + '/js/[name].js',
        library: config.projectCode,
        libraryTarget: 'umd',
        chunkLoadingGlobal: `webpackJsonp_${config.projectCode}`
    },
    devtool: config.dev.devtool,
    module: baseWebpackConfig.module('development' === 'development'),
    plugins: [
        new webpack.DefinePlugin({
            'process.env': JSON.stringify(config.dev.env)
        }),
        new webpack.HotModuleReplacementPlugin(),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: path.resolve(__dirname, '../src/index.ejs'), // 配置html模板的地址
            inject: true,
            scriptLoading: 'blocking',
            chunksSortMode: 'none',
            pathPrefix: staticPath
        })
    ],
    optimization: {
        minimize: false,
        moduleIds: 'named'
    }
});
