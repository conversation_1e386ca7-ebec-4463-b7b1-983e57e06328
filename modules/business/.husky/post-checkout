#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "husky post-checkout"

currentDirectory=$(pwd)
parent_directory="src/modules"

if [ $3 -eq 1 ]; then
    if  [[ `git submodule status` ]]; then
        echo "存在Git子模块"
        echo "submodule update --init --remote"
        git submodule update --init --remote
    else
        echo "不存在Git子模块"
        rm -rf ".git/modules/$parent_directory"
        rm -rf "$currentDirectory/$parent_directory"
    fi
fi

echo "post-checkout hook end"
