{"presets": [["@babel/preset-env", {"corejs": {"version": 3}, "useBuiltIns": "usage", "targets": {"ie": 9, "chrome": 52, "safari": 10, "opera": 32, "firefox": 30}}], "@babel/preset-react"], "env": {"development": {"plugins": []}, "production": {"plugins": [["transform-remove-console", {"exclude": ["error", "warn", "info"]}]]}}, "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es"}], ["import", {"libraryName": "tntd", "libraryDirectory": "es"}, "tntd"], ["@babel/plugin-proposal-optional-chaining", {"loose": true}], ["@babel/plugin-transform-modules-commonjs", {"allowTopLevelThis": true, "strictMode": false}], ["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-proposal-private-property-in-object", {"loose": true}], ["@babel/plugin-proposal-private-methods", {"loose": true}], ["@babel/plugin-transform-runtime", {"corejs": 3, "helpers": true, "regenerator": true, "useESModules": false}], ["@babel/plugin-transform-object-assign"]]}