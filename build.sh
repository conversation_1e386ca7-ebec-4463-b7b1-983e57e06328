#!/bin/bash
# 激活node环境（必须写）
source ~/.nvm/nvm.sh
# 默认使用node8.2.1, 预装了高版本的8.11可以直接切换
nvm use 16
# npm install
echo "开始安装 npm install"
npm install --registry=https://registry.npmmirror.com/

# 获取本次提交的代码，只取src目录下的文件
check_files=$(git diff --name-only HEAD~ HEAD --diff-filter=ACM | grep -E 'src' | grep -E '\.js$|\.jsx$|\.ts$|\.tsx$')

echo "\033[43;39m 本次校验文件为: $check_files \033[0m"

# 对本次提交的代码打包前进行eslint校验，避免绕过husky将代码提交到master
if [ "$check_files" ];
then
	npx eslint $check_files --quiet
	check_result=$(npx eslint $check_files --quiet)
fi

echo "\033[43;39m 本次校验结果为: $check_result \033[0m"

# 根据校验结果做相应处理
if [ "$check_result" ];
then
	echo "\033[31m eslint校验不通过 \033[0m"
	exit
fi

echo "开始构建 npm run build"
npm run build
# 编译代码（非必须，根据实际项目是否需要编译而定）
mkdir -p target
tar czf target/$APPNAME-dist.tar.gz dist Dockerfile nginx.conf ok.htm
