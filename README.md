## 同盾私有云外部数据服务管理平台
> 技术栈：react + dva 状态管理使用redux，组件库选用antd

### Style

> styles -> components：组件样式(组件包括开发的组件以及`antd`

**Note**

组件样式文件夹中，每个组件为单独一个文件，`index.less`文件为入口文件。

在项目中使用上传功能的时候接口不要写在service中，直接页面中完成请求 -----> 上传文件(适合无专门的文件服务器时候)

### Store

1. 在`models`中创建`store`文件
2. 在页面中使用`connect`将`models`与页面相连
3. 在`nav.js`的 `dynamicWrapper`方法中引入`models`

### 分支说明
天策信贷4.2.0-对应天座：release/1.0.0-atreus

天策信贷4.1.0-对应天座：release/1.0.0-security

天策交易-对应天座：release/1.0.0-spartan

天策交易-对应天座：release/1.0.0-spartan

天策交易商户版-对应天座：release/1.0.0-spartan-newlayout

信贷汽融版-对应天座：release/1.0.0-simple
