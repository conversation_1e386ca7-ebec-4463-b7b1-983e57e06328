require('app-module-path/register');

const path = require('path');
const Koa = require('koa');
const bodyParser = require('koa-bodyparser');
const proxy = require('koa2-proxy-middleware');
const staticServer = require('koa-static-cache');
const pkg = require('./package.json');
// const config = require('./config');
// const router = require('./router');
// const healthCheck = require('./middlewares/health-check');
// const tplRender = require('./middlewares/tpl-render');

const app = new Koa();
const port = 8090;
const env = process.env.NODE_ENV || 'development';
const isDev = env === 'development';
// global.M = require('./models')(config[env].mysql);
app.name = pkg.name;

// app.use(healthCheck());

// console.log(path.join(__dirname, '../demo'));

// 需要开启 静态资源
app.use(
    staticServer(path.join(__dirname, './'), {
        maxage: 3600 * 24 * 30,
        gzip: true
    })
);

// view
// app.use(tplRender(isDev));

const options = {
    targets: {
        '/bridgeApi/(.*)': {
            target: 'http://************:8000/',
            changeOrigin: true,
            pathRewrite: {
                '^/bridgeApi': '/bridgeApi'
            }
        },
        '/indexApi/(.*)': {
            target: 'http://************:8000/',
            changeOrigin: true,
            pathRewrite: {
                '^/indexApi': '/indexApi'
            }
        }
    }
};

app.use(proxy(options));
// app.use(sessionRedis());
// app.use(formatSession());
app.use(
    bodyParser({
        enableTypes: ['json', 'form', 'text']
    })
);

// 路由配置
// app.use(router.routes(), router.allowedMethods());

// 启动服务
app.listen(port, () => {
    console.log(`server started at localhost:${port}`);
});
