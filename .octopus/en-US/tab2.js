export default {
  index: {
    shuRuXuanZeZhuang: 'Input selection status',
    gongZuoLiuBiaoZhi: 'Workflow ID',
    gongZuoLiuMingCheng: 'Workflow name',
    shuRuSouSuoNei: 'Input search content',
    dao<PERSON>u: 'Import',
    pi<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON>: 'Batch Online',
    xin<PERSON><PERSON>: 'Add',
    yinYongGuanXi: 'Reference relation',
    banBenLiShi: 'Version history',
    fuZhi: 'Copy',
    shan<PERSON><PERSON>: 'Delete',
    ma: ']?',
    queRenShan<PERSON>hu: 'Confirm deletion of [',
    shang<PERSON><PERSON>: 'Go online',
    xiuGai: 'Modify',
    cha<PERSON><PERSON>: 'View',
    bian<PERSON>ai: 'Configure',
    cao<PERSON>uo: 'Action',
    xiuGaiShiJian: 'Modification time',
    xiuGaiRen: 'Modifier',
    chuang<PERSON>ian<PERSON>hi<PERSON><PERSON>: 'Creation time',
    chuangJianRen: 'Creator',
    miao<PERSON><PERSON>: 'Description',
    shan<PERSON>hu<PERSON>hi<PERSON>ai: 'Failure to delete',
    shan<PERSON>hu<PERSON>hengGong: 'Deleted successfully',
    cunZaiQiangYinYong:
      'Strong references (referenced by online, enabled, etc. related state components) exist, prohibiting operation',
  },
};
