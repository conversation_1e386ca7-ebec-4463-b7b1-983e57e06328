export default {
  gongTOTA: 'Totally {val1} Record(s)',
  xin<PERSON><PERSON>: 'Add',
  chaXun: 'Query',
  xia<PERSON><PERSON>: 'Go offline',
  shang<PERSON><PERSON>: 'Go online',
  qingXuanZeFuWu: 'Select service status',
  qingXuanZeShuJu: 'Select the data source service interface name',
  qingShuRuFuWu: 'Enter the service name',
  jiGou: 'Organization',
  xuanZeJiGouBing: 'Select organization and download',
  xiaZai: 'Download',
  shan<PERSON>hu: 'Delete',
  wuQuanXianCaoZuo: 'No permission operation',
  queDingShanChuCi: 'Confirm to delete this data?',
  cha<PERSON><PERSON>: 'View',
  xiuGai: 'Modify',
  caoZuo: 'Action',
  xiuGaiRen: 'Modifier',
  xiuGaiShiJian: 'Modification time',
  chuang<PERSON>ian<PERSON>en: 'Creator',
  chuangJianShiJian: 'Creation time',
  zhuangTai: 'Status',
  shuJuLeiXing: 'Data type',
  shuJuYuanFuWu: 'Data source service interface name',
  zhong<PERSON>hi<PERSON>i<PERSON><PERSON>: 'Reset secret key',
  fu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Copy successfully',
  fu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Copy secret key',
  miYao: 'Secret key',
  quDao: 'Channel',
  fuWuBiaoZhi: 'Service ID',
  fuWuMingCheng: 'Service name',
  zhongZhiMiYaoShi: 'Failed to reset the key',
  zhongZhiMiYaoCheng: 'Reset key successful',
  dISPL: '{val1} - interface document',
  caoZuoChengGong: 'Operation successful',
};
