export default {
  daoRuChengGong: 'Imported successfully',
  tiaoJiLu: 'Totally {val1} Record(s)',
  gong: 'Total',
  xin<PERSON><PERSON>: 'Add',
  daoRu: 'Import',
  cha<PERSON>un: 'Query',
  zan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'No permission',
  xia<PERSON><PERSON>: 'Go offline',
  shang<PERSON><PERSON>: 'Go online',
  qing<PERSON>uanZe<PERSON>ie<PERSON>: 'Select status',
  yiBu<PERSON>ie<PERSON><PERSON>: 'Asynchronous interface',
  tongBu<PERSON>ie<PERSON>ou: 'Synchronization interface',
  qingXuanZeJieKou2: 'Select type',
  qingXuanZeShuJu: 'Select dataType',
  qingXuanZeShuJu2: 'Select Interface',
  qingXuanZeHeZuo: 'Select a partner',
  geng<PERSON>uo: 'More',
  cha<PERSON><PERSON>: 'View',
  xiuGai: 'Modify',
  yinYongGuanXi: 'Reference relation',
  shanChu: 'Delete',
  daoChu: 'Export',
  mOCKPei: 'Mock',
  fu<PERSON>hi: 'Copy',
  ce<PERSON>hi: 'Test',
  cao<PERSON><PERSON>: 'Action',
  xiuGai<PERSON>en: 'Modifier',
  xiu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Modification time',
  chuang<PERSON><PERSON><PERSON><PERSON>: 'Creator',
  chuangJianShiJian: 'Creation time',
  tEXTTian: '{val1} days',
  kongZhiDiaoYongGuo:
    'Control whether to save the result data during the call process, such as saving, and set the saving cycle',
  huanCunYouXiaoQi: 'Cache validity period',
  yiXiaXian: 'Offline',
  yiShangXian: 'Online',
  yiShanChu: 'Deleted',
  zhuangTai: 'Status',
  jieKouLeiXing: 'Interface type',
  heZuoFangMingCheng: 'Partner name',
  shuJuLeiXing: 'Data type',
  shuJuYuanFuWu: 'Data source service interface id',
  shuJuYuanFuWu2: 'Data source service interface name',
  daoChuShiBai: 'Export failed!',
  cunZaiQiangYinYong:
    'Strong references (referenced by online, enabled, and other related state components) exist, prohibiting the operation of the',
  queDingShanChuCi: 'Confirm to delete this data?',
  caoZuoChengGong: 'Operation successful',
  qingShuRuFuWu: 'Enter interface id',
  shouQuan: 'Auth',
};
