export default {
  gongTOTA: 'Totally {val1} Record(s)',
  daoChu: 'Export',
  qingXuanZeShuJu: 'Select the interface name',
  qingXuanZeHeZuo: 'Select a partner',
  xiTongYiCiZui: 'The system can query for up to one year at a time',
  cha<PERSON>an: 'View',
  wuQuanXianCaoZuo: 'No permission operation',
  caoZuo: 'Action',
  jiaSheShuJuYuan:
    'Assume that the data source service interface sets the upper limit of total flow to n, and the surplus flow=n - successful flow; if it is not set, "- -" will be displayed',
  shengYuLiuLiang: 'Surplus flow',
  canYuJiFeiLiu: 'Participating billing flow',
  diaoYongChengGongDe:
    'Number of flow successfully called, participating in statistics and alerts of flow upper limit',
  chengGongLiuLiang: 'Successful traffic',
  chanShengZongLiuLiang: 'Total generated traffic',
  guSuanZongJia: 'Estimated total price',
  guSuanZongJiaCan: 'Estimated total price/participating billing traffic',
  pingJunJiFeiDan: 'Average billing cost per trip',
  guSuanZongJiaChan: 'Estimated total price/total traffic generated',
  pingJunDanJia: 'Average unit price',
  jiFeiFangShi: 'Billing method',
  caiGouJieShuRi: 'Purchase end date',
  caiGouKaiShiRi: 'Purchase start date',
  heTongBanBen: 'Contract version',
  heTongBianHao: 'Contract number',
  heZuoFangMingCheng: 'Partner name',
  shuJuYuanFuWu: 'Data source service interface name',
  jieTiJiFeiGu:
    '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
  yueBaoNianBaoJi:
    '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
  anCiJiFeiGu:
    '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
  anZhaoJiFeiFang: 'Estimate according to the billing method. estimate method:',
  chaXunLiuLiangShu:
    '3. Query Traffic: The data source service interface configuration selects query billing, regardless of whether or not the query is available',
  chaDeLiuLiangShu:
    '2. Checked traffic: The data source service interface configuration selects checked billing and requires successful checking',
  canYuJiFeiLiu2:
    '1. Participate in billing traffic = Checked traffic + Query traffic',
  shuJuYuanJiFei: 'Data source billing report',
};
