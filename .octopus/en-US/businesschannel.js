export default {
  gongTOTA: 'Totally {val1} Record(s)',
  jiG<PERSON>: 'Organization',
  qingShuRuD<PERSON>o<PERSON><PERSON>: 'Enter the caller service name',
  xiTongYiCiZui: 'The system can query for up to 90 days at a time',
  diao<PERSON>ongFang<PERSON>iao<PERSON>ong: 'Caller call details',
  chaKanMingXi: 'View Details',
  caoZuo: 'Action',
  jieXiBaoWen: 'Parse message',
  yuanShiBaoWen: 'Original message',
  jieGuoLeiXing: 'Result type',
  haoShiMS: 'Time consumption (ms)',
  qingQiuShiJian: 'Request time',
  chaXunJieGuo: 'Query result ',
  shiBai: 'Failed',
  fanHuiJieGuo: 'Return results',
  shiFouBenDi: 'Local or not',
  shuJuLeiXing: 'Data type',
  shuJuLeiXingLai: 'Data type source',
  shuJuYuanFuWu: 'Data source service interface name',
  quDao: 'Channel',
  fuWuMingCheng: 'Service name',
  diaoYongWeiYiBiao: 'Call unique ID code',
  guanBi: 'Close',
  chuCan: 'Output parameters:',
  ruCan: 'Input parameters:',
  mingXiXinXi: 'Details',
  xiTongYiCiZui2:
    'The system can query for up to 90 days at a time, and has automatically intercepted nearly 90 days of the selected range',
};
