export default {
  constants: {
    jiaoBenZhiXingQi:
      '/**\n* Script executor\n* Please implement the business logic you need within the run method of this class\n*\n* @param inputMap Input parameter collection\n* @return Output parameter collection\n*/\nimport com.alibaba.fastjson.*;\nimport org.apache.commons.lang3.*;\nimport java.text.SimpleDateFormat;\n\npublic class GroovyEtlHandlerCallerImpl implements GroovyEtlHandlerCaller {\n\n   public Map < String, String > run(Map < String, String > inputMap) {\n\t   Map < String, String > outputMap = new HashMap < String, String > ();\n\t   // TODO Translation: Please provide the business logic that needs to be implemented here, and put the resulting value into the outputMap。\n\n\t   return outputMap;\n   }\n}\n',
    panDingZiDuanShi:
      'Determines if field 1 is not null and is not an empty string',
    panDingZiDuanShi2:
      'Determines whether field 1 is a null value or an empty string',
    kongZhiFuZhiHan:
      'Null assignment function. If field 1 is null, then return the value of 2, otherwise return the value of field 1. Example: null (null, 1), then return the result 1; null (3, 9), then return the result 3',
    liangGeShiJianXiang:
      'two time subtraction, return time difference, time difference unit can be years, months, days, example: # time subtraction (arg1, arg2, arg3), arg1 and arg2 must be the time type of parameter, arg3 value must be: Y, M, D, arg3 parameter meaning is to retu',
    leiXingZhuanHuanHan:
      'Type conversion function, the string type parameter will be converted to a date type, the string format of the input parameter must be: yyyy-MM-dd HH:mm:ss, example: "2019-02-01 13:02:08"',
    leiXingZhuanHuanHan2:
      'Type conversion function to convert a character type to a boolean type, the value of the input parameter must be: "true" or "false", otherwise the system will throw an exception',
    leiXingZhuanHuanHan3:
      'Type conversion function, will be a character type or integer type parameter will be converted to a decimal type, if the incoming string is non-numeric system will throw an exception',
    leiXingZhuanHuanHan4:
      'Type conversion function, will be character type or decimal type parameter conversion to integer, if the input parameter is a non-numeric character system will throw an exception, if the input parameter is a decimal type system will round off the decimal ',
    leiXingZhuanHuanHan5:
      'Type conversion function to convert integer, decimal, boolean, date, enumerated fields to strings. If the input parameter is Boolean, the output parameter is "true" or "false"; if the input parameter is date, the return example is: "2019-02-01 13:02:08"; ',
    huoQuXiTongDang:
      'Get the current date of the system, does not contain hours, minutes, seconds, format example: 2019-02-01 00:00:00, the return type of this function is a date type, hours, minutes, seconds with "00" complement',
    huoQuXiTongDang2:
      'Get the current time of the system, format example: "2019-02-01 13:02:08", this function return type is date type',
    quDiaoZiFuChuan:
      'Removes extra spaces from both ends of the string. No matter how many spaces are at the ends, they will be removed',
    yongYuTiQuZi:
      'Used to extract characters in a string between two specified subscripts. If start and stop are equal, then the method returns an empty string (i.e., a string of length 0). If start is greater than stop, then the method swaps the two arguments before extra',
    shuRuYiGeZi:
      'Input a string, the return result is the length of the current string. If you enter: 3310293", the result is 7',
    shiYongLiangGeHuo:
      'Using two or more parameters, converts them to their string representation, juxtaposes them and returns a single string. For example, input 3329, 2132; the output is 332921323',
    biJiaoZiFuChuan:
      'Compare string 1 with string 2 to see if they are not equal, case sensitive',
    jiaoYanZiDuanN:
      'Checks that field n does not contain any of the contents of fields 1 through n-1',
    biaoZhunChaShiFang:
      'The standard deviation is the arithmetic square root of the variance. If you enter: # standard deviation (13, 15, 17), the result is 2√6/3',
    geGeShuJuFen:
      'The average of the sum of the squares of the differences of the individual data respectively with their means. If you enter: # variance (13, 15, 17), the result is 8/3',
    suiJiXuanQuDa:
      'Randomly select a pseudo-random double value greater than or equal to 0.0 and less than 1.0',
    tiaoJianHanShuYong:
      'Conditional function. Usage: "If age > 40, then result = 1, otherwise result = 2."',
    tiaoJianHanShuYong2:
      'Conditional function. Usage is: "If age > 40, then Result = 1',
    ruGuoQiTaRu:
      'If (){},Other if (){},Otherwise {}" conditional function, fill in the judgment condition in (), fill in the execution action in {}.\n        Usage 1: If the age is less than 18 years old, then assign the value "minor" to the "user" field.\n        Example: ',
    huoYongYuTiaoJian:
      'or. Used in conditional functions, the usage is: if age > 60 or annual income > 100,000, then result = 1',
    qieYongYuTiaoJian:
      'and. Used in conditional functions as follows: if age > 60 and sex = female, then result = 1',
    chu: 'Division',
    jian: 'Subtraction',
    zaiQuXiaoShuJin:
      "When approximating decimals, if the highest digit of the mantissa is 4 or less than 4, remove the mantissa. If the highest digit of the mantissa is 5 or greater than 5, round off the mantissa and enter '1' in its first digit. For example, if you enter: # ",
    buGuanSiSheWu:
      'Regardless of the rounding rules, only the integer portion of the given value is selected as the output result. For example, if you enter: # round down (8.9), the output will be 8',
    quBiDangQianShu:
      'Takes the nearest integer greater than the current value and outputs it as the result, e.g., if you enter: # Round up (7.2), the result will be 8',
    quJueDuiZhiRu:
      'Takes an absolute value. If you enter: # absolute value (-9), the result will be 9',
    biJiaoZiFuChuan2:
      'Compare string 1 with string 2 to see if they are exactly equal, case sensitive',
    jiaoYanZiDuanN2:
      'Checks whether field n contains the contents of field 1, or field 2...or field n-1',
    cheng: 'multiply',
    jia: 'Add',
    xiaoYuDengYu: 'Less than or equal to',
    daYuDengYu: 'Greater than or equal to',
    buDengYu: 'Not equal',
    huoQuDangQianRi: 'Gets the year of the current date format string',
    huoQuDangQianRi2: 'Gets the month of the current date format string',
    huoQuDangQianRi3: 'Gets the day of the current date format string',
    kuoHao: 'Braces',
    xiaoYu: 'Less than ',
    daYu: 'Greater than ',
    dengYu: 'Equal',
    hanShuXingShiWei:
      'function in the form of # power function (the first digital type, the second digital type), the second digital type if a decimal will automatically be converted to an integer, the final result to maintain the decimal 6 rounded, if the result is more than ',
    qiuDuoGeShuZhi:
      'Sums multiple values. If you enter: # Sum (1, 3, 2); the result is 6',
    biJiaoDuoGeShu:
      'Compare the size of multiple values and select the smallest value as the result output. For example, if you enter: # smallest value (13, 117, 29), the result will be 13',
    biJiaoDuoGeShu2:
      'Compare the size of multiple values and select the largest value as the result output. For example, if you enter: #Maximum value (13, 117, 29), the result will be 117',
    qiuDuoGeShuZhi2:
      'Finds the average of multiple numeric or value-like fields. Note: The last digit indicates the precision of the result',
    ruGuoNaMeFou: 'If ()\n#Then {}\n#Otherwise {}',
    ruGuoQiTaRu2: 'If (){}\n#Other if (){}\n#Otherwise{}',
    ruGuoNaMe: 'If()\n#Then {}',
    huo: 'Or',
    qie: 'And',
    panFeiKong: 'Nonempty',
    panKong: 'Null',
    kongZhiFuZhi: 'Null assignment',
    shiJianXiangJian: 'Subtracting time',
    zhuanHuanWeiShiJian: 'Convert to date',
    zhuanHuanWeiBuEr: 'Convert to boolean',
    zhuanHuanWeiXiaoShu: 'Convert to decimal',
    zhuanHuanWeiZhengShu: 'Convert to integer',
    zhuanHuanWeiZiFu: 'Convert to string',
    huoQuDangQianRi4: 'Get current date',
    huoQuDangQianShi: 'Get current time',
    fouZe: 'Otherwise',
    qiTaRuGuo: 'Other if',
    naMe: 'If',
    ruGuo: 'If',
    quTouWeiKongGe: 'Remove header and footer spaces',
    jieQuZiFuChuan: 'Intercepted string',
    ziFuChuanChangDu: 'String length',
    pinJieZiFuChuan: 'Splice the string',
    buXiangDeng: 'Not equal',
    buBaoHan: 'Exclude',
    biaoZhunCha: 'Standard deviation',
    fangCha: 'Variance',
    suiJiShu: 'Random number',
    ruGuoNaMeFou2: 'If, then, else',
    ruGuoQiTaRu3: 'If, other if, otherwise',
    ruGuoNaMe2: 'If, then',
    siSheWuRu: 'Round up to the nearest integer',
    xiangXiaQuZheng: 'Round down',
    xiangShangQuZheng: 'Round up',
    jueDuiZhi: 'Absolute value',
    xiangDeng: 'Equal',
    baoHan: 'Include',
    huoQuNian: 'Get year',
    huoQuYue: 'Get months',
    huoQuTian: 'Get days',
    miHanShu: 'Power functions',
    qiuHe: 'Summation',
    zuiXiaoZhi: 'Minimum value',
    zuiDaZhi: 'Maximum value',
    pingJunZhi: 'Average',
  },
  formulaeditor: {
    zhiChiDeYunSuan: 'Supported operators: +, -, x, /, =, ()',
    zhiChiDeHanShu:
      'Supported functions: average, maximum, minimum, absolute value',
    liRuShuChuZi:
      'Example: [Output Field]@Actual Lending Amount = #Maximum (@Application Amount, @Lending Amount)',
    tIPSHou:
      'Tips: @ can be followed by variables, # can be followed by functions, constants and operators can be output directly from the keyboard',
    wenXinTiShi: 'Warm tips',
  },
  index: {
    zhuYiRenYiWan:
      '(Note: any complete formula must be followed by a semicolon)',
    fangKuanJinE: 'Loan amount',
    shenQingJinE: 'Application amount',
    zuiDaZhi: '#Maximum',
    shiJiFangKuanJin: 'Actual loan amount',
    liRu: 'Example:',
    tIPSHou:
      'Tips: @ can be followed by variables, # can be followed by functions, constants and operators can be output directly from the keyboard',
    jiaoBen: 'Script',
    gongShi: 'Formula',
    zhiKeXuanZeYi:
      'Only one can be selected. Note: The configuration contents under the original method will be cleared after switching',
    bianXieLeiXing: 'Type of authoring',
    jiSuanLuoJi: 'Computational logic',
    baoCun: 'Save',
    zanCun: 'Stage',
    ceShi: 'Test',
    qingShuRuMiaoShu: 'Enter a description in 2000 words or less',
    changDuBuNengChao: 'Length cannot exceed 2000',
    miaoShu: 'Description',
    qingShuRuHanShu: 'Enter a function name',
    mingChengBiXuYou:
      'The name must be composed of Chinese and English, numbers, and underscores',
    changDuBuNengChao2: 'Length must not exceed 200',
    hanShuMingCheng: 'Function name',
    qingXuanZeHanShu: 'Select the function type',
    hanShuBiaoZhi: 'Function ID',
    jiBenXinXi: 'Basic Information',
    qingShuRuHanShu2: 'Enter the function formula!',
    kongZhiFuZhi: 'Null assignment',
    quTouWeiKongGe: 'Remove header and footer spaces',
    jieQuZiFuChuan: 'Intercepted string',
    pinJieZiFuChuan: 'Splice the string',
    xiangXiaQuZheng: 'Round down',
    xiangShangQuZheng: 'Round up',
    qiuHe: 'Summation',
    zuiXiaoZhi: 'Minimum value',
    zuiDaZhi2: 'Maximum value',
    pingJunZhi: 'Average',
  },
};
