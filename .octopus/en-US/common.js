export default {
  test: 'Test',
  locale: {
    canShuCuoWu: 'Parameter error',
  },
  nav: {
    zhiLiangFenXi: 'Quality analysis',
    jianKongYuJing: 'Monitoring and warning',
    diaoYongChaXun: 'Call query',
    diaoYongFangGuanLi: 'Caller management',
    jiFeiGuanLi: 'Billing management',
    shuJuYuanGuanLi: 'Data source management',
    shuJuDaPan: 'Data dashboard',
    shouYe: 'Home page',
    shiXiaoFenXiDui: 'Time efficiency analysis (comparison)',
    chengBenFenXiDui: 'Cost analysis (comparison)',
    shuJuYuanQiXian: 'Data source duration warning',
    shuJuYuanZongLiu: 'Data source total traffic warning',
    shuJuYuanYiChang: 'Data source anomaly warning',
    shouGongChaXun: 'Manual query',
    diaoYongFangDiaoYong: 'Caller call details',
    shuJuYuanDiaoYong: 'Data source call details',
    diaoYongFangFuWu: 'Caller service group details',
    diaoYongFangFuWu2: 'Caller service group details',
    diaoYongFangFuWu3: 'Caller service management',
    heTongJiFei: 'Contract billing',
    shuJuYuanJiFei: 'Data source billing',
    diaoYongFangJiFei: 'Caller billing',
    eTLChuLi: 'Etl processor management',
    mOCKPei: 'Mock configuration',
    sanFangFuWuJie: 'External service interface management-added',
    shuJuYuanFuWu: 'Data source service interface management',
    heTongGuanLi: 'Contract management',
    heZuoFangGuanLi: 'Partner management',
    xiTongZiDuanGuan: 'System field management',
    shuJuGaiLan: 'Data overview',
    shuJuYuanDaPan: 'Data source dashboard',
    diaoYongFangDaPan: 'Caller dashboard',
  },
};
