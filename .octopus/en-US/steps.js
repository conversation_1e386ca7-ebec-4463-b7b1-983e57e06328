export default {
  one: {
    zhu<PERSON><PERSON><PERSON><PERSON>anTongGuo:
      'Note: The content of the check passed does not support the page view, you can download the detailed list of check to view',
    tiaoJiaoYanWeiTong: 'Pieces not passed',
    tiao: 'Pieces',
    gong<PERSON><PERSON><PERSON><PERSON><PERSON>: 'Total pieces',
    gong: 'Total',
    jiao<PERSON>an<PERSON>uanBuTong: 'All calibrations passed',
    xiaZaiXiangXiXiao: 'Download the detailed checklist',
    cURNA: '{val1}_Checklist',
    geZuJian<PERSON><PERSON>o<PERSON><PERSON>: 'Components failing check',
    you: 'Yes',
    jiaoYanWeiTongGuo: 'Failed',
    quanBuJiaoYanTong: 'All components passed',
    qingShangChuanCS:
      'Upload the csv format file/do not modify the template name and format',
    dianJiShangChuanHuo: 'Click upload or drag the file here to upload',
    shangChuanWenJian: 'Upload files',
    qingShuRuYongHu: 'Enter the user password',
    dengLuMiMa: 'Login password',
    qingShuRuDengLu: 'Enter the login user name',
    dengLuYongHuMing: 'Login user name',
    qingShuRuYuanShu:
      'Enter the full path of the source data (excluding the file name)',
    yuanShuJuWenJian: 'Source data file',
    qingShuRuFuWu:
      'Enter the server address (for example, ftp://ip:port or sftp://ip:port)',
    fuWuQiDiZhi: 'Server address',
    zu: '(Group)',
    qingXuanZeDiaoYong: 'Select a caller/caller group',
    diaoYongFangDiaoYong: 'Caller/caller group',
    benDiWenJian: 'Local file',
    fTPSF: 'Ftp/sftp file',
    cunZaiBiTianXiang: 'There are required fields not filled in',
  },
  three: {
    kuaiSuTiaoZhuan: 'Quick Jump',
    daoRuJieGuo: 'Import results',
    tiao: 'Pieces',
    gong: 'Total',
    weiTongGuo: 'Not Passed',
    daoRu: 'Import',
    huiGun: 'Rollback',
    geZuJianShiLi: 'Component instances',
    you: 'Yes',
    daoRuCaoZuoWei: 'Import operation failed',
    huiGunCaoZuoWei: 'Rollback operation was unsuccessful',
    daoRuCaoZuoQuan: 'All import operations succeeded',
    huiGunCaoZuoQuan: 'Rollback operation was successful',
    xiaZaiXiangXiDao: 'Download detailed import results',
    xiaZaiXiangXiHui: 'Download detailed rollback results',
    cURNA: '{val1}_Import Result List',
    cURNA2: 'List of {val1}_Rollback results',
    shiBai: 'Fail',
    chengGong: 'Success',
    qingShuRuYongHu: 'Enter the user password',
    dengLuMiMa: 'Login password',
    qingShuRuDengLu: 'Enter the login user name',
    dengLuYongHuMing: 'Login user name',
    qingShuRuJieGuo:
      'Enter the full path of the result data (excluding the file name)',
    jieGuoShuJuWen: 'Result data file',
    qingShuRuFuWu:
      'Enter the server address (for example, ftp://ip:port or sftp://ip:port)',
    fuWuQiDiZhi: 'Server address',
    benDiWenJian: 'Local file',
    fTPSF: 'Ftp/sftp file',
    cunZaiBiTianXiang: 'There are required fields not filled in',
  },
  two: {
    daoRuFangShi: 'Import method:',
    xiangTong: 'Same',
    zhuXiangTongZuJian:
      'Note: The same component override import, do not override the original component name',
    zhuRuoZiDongChuang:
      'Note: If the name is the same in the case of automatic creation and import, it will be imported by default after adding the time to the name',
    zhuQueShiZuJian:
      'Note: The system automatically creates new components by default for missing components, no need to manually select the import mode',
    dangQianWuXuShou:
      'Currently, there is no need to manually select the import mode',
    ru: 'For example: 0 0/30 * * *?',
    zhiNengXuanZeFen: 'Only time after 10 minutes can be selected',
    miao: 'Second',
    fen: 'Minute',
    shi: 'Hour',
    qingXuanZe: 'Select',
    cRONBiao: 'Cron expression configuration error, please check!',
    cunZaiBiTianXiang: 'There are required fields not filled in',
    cRONBiao2: 'Cron expression ',
    zhiDingMouYiShi: 'Specify a time',
    meiYueGuDingShi: 'Fixed time every month',
    meiZhouGuDingShi: 'Fixed time every week',
    meiTianGuDingShi: 'Fixed time every day',
    xingQiRi: 'Sunday',
    xingQiLiu: 'Saturday',
    xingQiWu: 'Friday',
    xingQiSi: 'Thursday',
    xingQiSan: 'Wednesday',
    xingQiEr: 'Tuesday',
    xingQiYi: 'Monday',
  },
  zero: {
    wenJianZhongRuoYin:
      'If the file references components that are not enabled or live, the referenced components will not be imported together',
    dianJiXuanZeWen: 'Click to select a file',
    qingXuanZeShangChuan: 'Select a file to upload',
    shangChuanWenJian: 'Upload file',
    fou: 'No',
    shi: 'Yes',
    qingTianXieJiGou: 'Fill in the organization',
    yiLaiZuJianDao: 'Dependent component import',
    genJuYuanWenJian: 'Import according to source file organization channel',
    genJuZhiDingJi: 'Import according to specified organization channel',
    qingXuanZeDaoRu: 'Select the import mode',
    daoRuMoShi: 'Import Mode',
    wenJianDaXiaoBu: 'The file size cannot exceed 50M, please re-upload',
    wenJianLeiXingCuo: 'The file type is wrong, please re-upload',
  },
};
