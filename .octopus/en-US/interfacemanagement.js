export default {
  list: {
    shuJuBuChong: 'Data supplement',
    shuJuFuWu: 'Data service',
    qingXuanZeFuWu: 'Select service type',
    xia<PERSON>ian: 'Go offline',
    shang<PERSON>ian: 'Go online',
    qingXuanZeZhuangTai: 'Select status',
    shuRuFuWuMing: 'Enter a service name',
    xin<PERSON><PERSON>: 'Add',
    caoZuoChengGong: 'Operation successful',
    ceShi: 'Test',
    xiaZai: 'Download',
    dISPL: '{val1} Docking document',
    shanChu: 'Delete',
    queDingShanChu: 'Delete?',
    cha<PERSON>an: 'View',
    xiuGai: 'Modify',
    caoZuo: 'Action',
    xiuGaiRen: 'Modifier',
    xiuGaiShiJian: 'Modification time',
    chuangJianRen: 'Creator',
    chuangJianShi<PERSON>ian: 'Creation time',
    zhuangTai: 'Status',
    fuWuLeiXing: 'Service type',
    fuWuChangJing: 'Service scene',
    fuWuBiaoZ<PERSON>: 'Service ID',
    fuWuMingCheng: 'Service name',
    fuWuCuoWu: 'Service Error',
    youBiTianZiDuan: 'A required field was not filled in',
    shan<PERSON>huChengGong: 'Deleted successfully',
  },
  index: {
    jieKouFuWuLie: 'Interface Service List',
    jieKouFuWuXiang: 'Interface Service Details {val1}',
    chaKan: 'View',
    xiuGai: 'Modify',
    tianJia: 'Add',
  },
};
