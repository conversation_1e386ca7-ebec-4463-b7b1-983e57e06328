export default {
  gongTOTA: 'Totally {val1} Record(s)',
  daoChu: 'Export',
  qingXuanZeHeTong: 'Select the contract name',
  qingXuanZeHeZuo: 'Select a partner',
  xiTongYiCiZui: 'The system can query for up to one year at a time',
  chaKan: 'View',
  wuQuanXianCaoZuo: 'No permission operation',
  caoZuo: 'Action',
  canYuJiFeiLiu: 'Participating billing flow',
  diaoYongChengGongDe:
    'Number of flow successfully called, participating in statistics and alerts of flow upper limit',
  chengGongLiuLiang: 'Successful flow',
  chanShengZongLiuLiang: 'Total generated flow',
  guSuanZongJia: 'Estimated total price',
  guSuanZongJiaCan: 'Estimated total price/participating billing flow',
  pingJunJiFeiDan: 'Average billable cost per visit',
  guSuanZongJiaChan: 'Estimated total price/generated total flow',
  pingJunDanJia: 'Average unit price',
  jiFeiFangShi: 'Billing method',
  caiGouJieShuRi: 'Purchase end date',
  caiGouKaiShiRi: 'Purchase start date',
  heTongBanBen: 'Contract version',
  heTongBianHao: 'Contract number',
  heTongMingCheng: 'Contract name',
  heZuoFangMingCheng: 'Partner name',
  jieTiJiFeiGu:
    '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
  yueBaoNianBaoJi:
    '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
  anCiJiFeiGu:
    '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
  anZhaoJiFeiFang: 'Estimate according to the billing method. estimate method:',
  chaXunLiuLiangShu:
    '3. Query Traffic: The data source service interface is configured to select query billing, regardless of whether it is checked or not',
  chaDeLiuLiangShu:
    '2. Access to the traffic: the data source service interface is configured to select access to the billing, requiring successful access',
  canYuJiFeiLiu2:
    '1. Participation in billing traffic = Checked Traffic + Query Traffic',
  heTongJiFeiBao: 'Contract billing report',
};
