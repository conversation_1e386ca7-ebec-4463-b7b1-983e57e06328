export default {
  index: {
    piLiangDiaoYongRen: 'Batch call task management',
    xiaZaiMuBan: 'Download template',
    jianYiDanGeWen:
      'It is recommended that the data of a single file be less than 100000',
    xinJianPiLiangRen:
      'Before creating a batch task, download the template to prepare the source data',
    xin<PERSON><PERSON>: 'Add',
    ren<PERSON>uZhuangTai: 'Task status',
    diaoYongFangDiao<PERSON>ong: 'Caller/caller group',
    ting<PERSON><PERSON>: 'Stop',
    zan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'No permission',
    jiangTingZhiWeiKai:
      'The task details that have not been started will be stopped,the task status will change to completed,are you sure you want to stop the task?',
    tingZhiQueRen: 'Stop confirmation',
    mingXi: 'Details',
    cao<PERSON><PERSON>: 'Action',
    chuang<PERSON>ian<PERSON>hi<PERSON>ian: 'Creation time',
    chuang<PERSON>ian<PERSON><PERSON>: 'Creator',
    wan<PERSON>heng: 'Complete',
    yun<PERSON><PERSON><PERSON><PERSON>: 'Running',
    weiKaiShi: 'Not started',
    jieGuoWenJianDi: 'Result file address',
    yuanWenJianDiZ<PERSON>: 'Source file address',
    renWu<PERSON>ei<PERSON>ing: 'Task type',
    diao<PERSON>ongFangZu: 'Caller (group)',
    qu<PERSON><PERSON>: 'Channel',
    zu: 'Group',
  },
};
