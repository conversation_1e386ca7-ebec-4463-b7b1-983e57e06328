export default {
  layout: {
    tian<PERSON>uo: 'Freyr',
  },
  router: {
    hanShuKu: 'Function Library',
    liuChengMoBan: 'Process template',
    gongZuoLiu: 'Workflow',
    piLiangDiaoYongRen: 'Batch call task management',
    ren<PERSON>u<PERSON><PERSON><PERSON><PERSON>: 'Add task',
    shuJuYuanQiXian: 'Data source duration warning',
    shuJuYuanZongLiu: 'Data source total traffic warning',
    shuJuYuanYiChang: 'Data source anomaly warning',
    shouGongChaXun: 'Manual query',
    diaoYongFangDiaoYong: 'Caller call details',
    shuJuYuanDiaoYong: 'Data source call details',
    diaoYongFangFuWu: 'Caller service group details',
    diaoYongFangFuWu2: 'Caller service group details',
    diaoYongFangFuWu3: 'Caller service management',
    heTongJiFei: 'Contract billing',
    shuJuYuanJiFei: 'Data source billing',
    diaoYongFangJiFei: 'Caller billing',
    fuWuJieKouGuan: 'Service Interface Management',
    eTLChuLi: 'Etl processor management',
    shuJuYuanFuWu: 'Data source service interface management',
    mOCKPei: 'Mock configuration',
    sanFangFuWuJie: 'External service interface management-added',
    heZuoFangGuanLi: 'Partner management',
    heTongGuanLi: 'Contract management',
    zhiShiGaiLan: 'Knowledge overview',
    shuJuYuanDaPan: 'Data source dashboard',
    diaoYongFangDaPan: 'Caller dashboard',
  },
};
