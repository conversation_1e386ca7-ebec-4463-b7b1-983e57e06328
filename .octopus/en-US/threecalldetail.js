export default {
  gongTOTA: 'Totally {val1} Record(s)',
  qingXuanZeShuJu: 'select data source service interface name',
  qingXuanZeShuJu2: 'Select data type',
  xiTongYiCiZui: 'The system can query for up to 90 days at a time',
  shuJu<PERSON>uanDiao<PERSON>ong: 'Data source call details',
  chaKanMingXi: 'View Details',
  caoZuo: 'Action',
  haoShiMS: 'Time consumption (ms)',
  shiFouBenDi: 'Local or not',
  qingQiuShiJian: 'Request time',
  chaXunJieGuo: 'Query result ',
  heTongMingCheng: 'Contract name',
  heZuoFangMingCheng: 'Partner name',
  shiBai: 'Failed',
  fanHuiJieGuo: 'Return results',
  shuJuLeiXing: 'Data type',
  shuJuLeiXingLai: 'Data type source',
  shuJuYuanFuWu: 'Data source service interface',
  yeWuWeiYiLiu: 'Business unique serial number',
  diaoYongWeiYiBiao: 'Call unique ID code',
  weiChaDaoXiangGuan: 'No detailed information was found',
  guanBi: 'Close',
  chuCan: 'Output parameters:',
  ruCan: 'Input parameters:',
  mingXiXinXi: 'Details',
  xiTongYiCiZui2:
    'The system can query for up to 90 days at a time, and has automatically intercepted nearly 90 days of the selected range',
};
