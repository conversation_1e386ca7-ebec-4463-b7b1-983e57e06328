export default {
  index: {
    mingChengZuiChangBu: 'Name up to 200 characters',
    mingChengZhiNengShu:
      'Support Chinese, English, numbers, spaces, underscores, -, .',
    diHASE:
      'There is an incomplete conditional configuration in article {val1}',
    diHASE2:
      'There is an incomplete condition in {val2} of condition group {val1}',
    qingSheZhiZhiXing: 'Set the execution condition',
    qingSheZhiZhiXing2: 'Set the fulfillment condition for execution',
    zhiXingTiaoJian: 'Execution conditions',
    qingShuRuYouXian: 'Enter a priority',
    youXianJi: 'Priority',
    yiGePanDingTiao:
      'Only one default branch can be set in a judgment condition, and the current branch is executed when all other branching conditions are not met',
    moRenFenZhi: 'Default branch',
    qingShuRuPanDing: 'Input the name of the judgment condition',
    panDingTiaoJianMing: 'Judgment name',
    panDuan: 'Judgment',
    queDing: 'OK',
    quXiao: 'Cancel',
    qingTianJiaZhiXing: 'Add conditions for implementation',
  },
};
