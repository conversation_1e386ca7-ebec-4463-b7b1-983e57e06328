export default {
  constant: {
    ceLueBiaoZhiYi:
      'If the strategy ID are the same, the strategy is considered "same strategy"',
    guiZeJiUU: 'Rule sets with the same uuid are considered "same rule set"',
    liXianZhiBiaoBiao:
      'If the Offline indicators have the same uuid, it will be considered as "same indicator"',
    shiShiZhiBiaoBiao:
      'If the Realtime indicators have the same uuid, it will be considered as "same realtime indicator"',
    jiGouDuiJieFu: 'Organization Docking Service',
    zhiDingJiGouQu:
      'If the same ID exists under the specified organization channel, it will be considered as "same decision tool"',
    hanShuKu: 'Function library',
    hanShuKuBiaoZhi:
      'If the function library ID are the same, it will be considered as "same function library"',
    mingDanBiaoZhiYi:
      'If the list ID are the same, it will be considered as "same list"',
    moXingBiaoZhiYi:
      'If the model ID is the same, it is considered "same model"',
    sanFangShuJuBiao: 'Same Decision tool" if all three data ID are the same',
    dongTaiZiDuanBiao:
      'If the dynamic field are identified the same way, it is considered "same dynamic field"',
    xiTongZiDuanBiao:
      'If the system field are the same, they are considered "same system field"',
    pingFenKa: 'Scorecard',
    jueCeGongJuBiao: 'Same decision tool" if the decision tool is the same',
    liuChengMuBan: 'Process template',
    jueCeGongJu: 'Decision tool',
    guiZeJi: 'Rule set',
    ceLue: 'Strategy',
    chuangJianBanBen: 'Create version',
    tiaoGuo: 'Skip',
    fuGai: 'Overwrite',
    shouQuanGuiZeJi:
      'Authorization (Rule sets, Indicators, List sets, Decision tools)',
    ziDongXinJian: 'Automatic new creation',
    guiZe: 'Rule',
    liXianZhiBiao: 'Offline indicator',
    shiShiZhiBiao: 'Realtime indicator',
    mingDan: 'List',
    moXing: 'Model',
    sanFang: 'External service',
    dongTaiZiDuan: 'Dynamic Field',
    xiTongZiDuan: 'System Field',
    ziDian: 'Dictionary',
    yongHuCaoZuoQuan:
      'User operating privileges need to be greater than the current import file of the relevant components of the permissions (organization and channel permissions) to support the import, which is authorized to use the component can be imported related to the r',
    zuJianQueShi: 'Missing components',
    dangQianDaoRuHuan:
      'If the current import environment lacks the necessary environment information in the import file, including channels, organizations, business types, risk types, event types, risk decision results and other information, the import is not supported',
    xiangTongZuJianDao:
      'The import of the same component needs to verify whether the relevant basic configuration information is consistent, if not, the import is not supported',
    yongHuQuanXianXiao: 'User privilege check',
    huanJingXinXiQue: 'Missing environment information check',
    xiangTongZuJianPei: 'Check configuration of the same components',
  },
  index: {
    diSiBuDaoRu: 'Step 4: Import result',
    diSanBuDaoRu: 'Step 3: Import mode selection',
    diErBuXiTong: 'Step 2: System check',
    diYiBuDaoRu: 'Step 1: Import file',
    xiaYiBu: 'Next step',
    guanBi: 'Close',
    huiGun: 'Rollback',
    caoZuoChengGong: 'Operation successful',
    shangYiBu: 'Previous step',
    quXiao: 'Cancel',
    meiGeZuJianYou: 'Each component has and can only select one import method',
    mODAL: '{val1} Import Wizard',
    daoRuPiCiDao: '(Import Batch) > Import Results View',
    daoRuPiCiHui: '(Import Batch) > Rollback Result View',
    daoRuPiCiXiao: '(Import Batch) > Check Results View',
    fuGaiXiangTongBiao: 'Overwrite the same id and continue importing',
    tiaoGuoXiangTongBiao: 'Skip the same id and continue importing',
    chaXunDaoXiangTong: 'If the same id is found, none will be imported',
    chaXunDaoXiangTong2:
      'The processing mode of the same etl identity is queried:',
    chaXunDaoXiangTong3:
      'The processing mode of the same system field ID is queried:',
    chaXunDaoXiangTong4:
      'The processing mode of the same contract ID is queried:',
    chaXunDaoXiangTong5:
      'The processing mode of the same partner ID is queried:',
    chaXunDaoXiangTong6: 'The processing mode of the same etl is queried:',
    chaXunDaoXiangTong7:
      'The processing mode of the same data source ID is queried:',
    dianJiShangChuan: 'Click upload',
    xuanZeWenJian: 'Select file:',
    daoRuShuJuYuan: 'Import data source service',
    qingXianShangChuanWen: 'Upload the file first',
    yiCiZuiDuoShang: 'Upload up to one file at a time!',
    daoRuChengGong: 'Imported successfully',
    qingXuanZeWenJian: 'Select file',
    daoRuETL: 'Import etl processor',
  },
};
