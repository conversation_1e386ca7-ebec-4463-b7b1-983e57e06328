export default {
  index: {
    shuJuYuanFuWuB:
      'Code Only supports the input combination of letters, numbers and underscores',
    shuJuYuanFuWuM:
      'Name Only Support Chinese, English, numbers, spaces, underscores, -, .',
    qingShuRuMiaoShu: 'Enter a description in 2000 words or less',
    changDuBuNengChao: 'Length cannot exceed 200',
    changDuBuNengChao2: 'Length cannot exceed 2000',
    changDuBuNengChao1: 'Length cannot exceed 2000',
    miaoShu: 'Description',
    jiaoBen: 'Script',
    gongShi: 'Formula',
    qingXuanZeHanShu: 'Select the function type',
    hanShuLeiXing: 'Function type',
    mingChengBiXuYou:
      'Support Chinese, English, numbers, spaces, underscores, -, .',
    qingTianXieHanShu: 'Fill in the function name',
    fuBen: '_Copy',
    hanShuMingCheng: 'Function name',
    queDing: 'OK',
    quXiao: 'Cancel',
    fuZhi: 'Copy',
    shuJu<PERSON>uanFuWu: 'Data source service interface id',
    shuJuYuanFuWu2: 'Data source service interface name',
    cunZaiBiTianXiang: 'There are required fields not filled in',
    qingShuRuShuJu: 'Please Input',
    qingShuRuShuJu2: 'Please Input',
  },
};
