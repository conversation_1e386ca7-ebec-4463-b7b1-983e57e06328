export default {
  xiTongZiDongGen:
    'Translation: The system automatically generates a list of third-party service interfaces that match the input parameters for selection and configuration. Multiple interfaces can be specified, and their switching priority can be set. There are two situatio',
  fou: 'No',
  shi: 'Yes',
  qingXuanZe: 'Select',
  yiChangQieHuan: 'Abnormal switching',
  qingXuanZeFanHui: 'Select the return type',
  fanHuiLeiXing: 'Return type',
  moRenZeYuShu: 'The default is the same as the data source interface timeout',
  shuJuChaoShiShi: 'Data timeout (ms)',
  moRenZeYuShu2:
    'The default is the same as the number of retries for the data source interface',
  shuJuZhongShiCi: 'Number of data retries',
  shuJuYuanJieKou: 'Data source interface',
  shuJuLeiXing: 'Data type',
  xiuGaiFuWuJie: 'Modify service interface',
  xinZengFuWuJie: 'Add service interface',
  diaoYongFangFuWu: 'Caller service group',
  cunZaiBiTianXiang: 'There are required fields not filled in',
  yiChangQieHuanBi: 'Abnormal switching required',
  fanHuiLeiXingBi: 'Return type required',
  shuJuYuanJieKou2: 'Data source interface required',
  shuJuLeiXingBi: 'Data type is required',
  inputlist: {
    fuWuJieKouLie: 'Service interface list',
    suoXuFuWuJie: 'List of required service interfaces',
    shiFouBiTian: 'Required',
    ziDuanLeiXing: 'Field type',
    xiTongZiDuan: 'System Field',
  },
  serviceapi: {
    xinZeng: 'Add',
    qingXianXuanZeQu: 'Select a channel first',
    geGuanBi: 'Closed',
    geKaiQi: 'Enabled,',
    geJieKou: 'Interfaces,',
    gong: 'Total',
    shanChu: 'Delete',
    xiuGai: 'Modify',
    caoZuo: 'Action',
    guan: 'Off',
    kai: 'On',
    zhuangTai: 'Status',
    fou: 'No',
    shi: 'Yes',
    chaDeJieKou: 'Queried interface',
    panDingZhi: 'Value',
    buDengYu: 'Not equal',
    dengYu: 'Equal',
    piPeiFangShi: 'Method',
    piPeiZiDuan: 'Field',
    zhongDuanTiaoJianPei: 'Interrupt condition configuration',
    yiChangQieHuan: 'Abnormal switching',
    fanHuiLeiXing: 'Return type',
    moRen: 'Default',
    chaoShiShiJian: 'Timeout',
    zhongShiCiShu: 'Number of retries',
    shuJuYuanFuWu: 'Data source service interface',
    shuJuLeiXing: 'Data type',
  },
  index: {
    queRenTiJiao: 'Confirm submission',
    quXiao: 'Cancel',
    diaoYongFuWuZu: 'Call service group input parameter list',
    chuanXingMoShi: 'Serial mode',
    bingXingMoShi: 'Parallel mode',
    fuWuJieKouLie: 'Service interface list',
    xuanZeQuDao: 'Select channel',
    quDao: 'Channel',
    yongYuDiaoYongFang: 'Used to identify when invokes',
    fuWuZuBiaoZhi: 'Service group ID',
    shuRuFuWuZu: 'Input service group name',
    fuWuZuMingCheng: 'Service group name',
    jiBenXinXi: 'Basic Information',
    bREAD: '{val1} caller service group',
    fanHui: 'Return',
    zhongDuanTiaoJianPei:
      'Incomplete input of interrupt condition configuration field',
    fuWuJieKouLie2: 'Add at least one service interface list',
    quDaoBuNengWei: 'Channel cannot be empty',
    fuWuBiaoZhiGe:
      'Service indentifier format can only be letters, numbers and underscores',
    fuWuZuBiaoZhi2: 'Service group ID up to 200 characters long',
    fuWuZuBiaoZhi3: 'Service group id cannot be empty',
    fuWuZuMingCheng2: 'Service group name up to 200 characters long',
    fuWuZuMingCheng3: 'Service group name cannot be empty',
    chaKan: 'View',
    xiuGai: 'Modify',
    xinZeng: 'Add',
    jianYiDanGeWen:
      'It is recommended that the data of a single file be less than 100000',
    jinXingShuJuZhun: 'Perform data preparation)',
    xiaZaiMuBan: 'Download template',
    ruoHaiWeiZhunBei: '(If the Source Data Is Not Ready, Please First',
    tiJiao: 'Submit',
    xiaYiBu: 'Next step',
    shangYiBu: 'Previous step',
    diSanBuQingXuan: 'Step 3: Select the result data storage method',
    diErBuQingXuan: 'Step 2: Select the task running method',
    diYiBuQingXuan: 'Step 1: Select the source data file',
    xinZengPiLiangTiao: 'Add batch call task',
    shuJuYuanFuWu: 'Data source service interface information',
    diSanBuTianXie: 'Step 3: service query interface information',
    diErBuTianXie: 'Step 2: service request interface information',
    diErBuTianXie2: 'Step 2: service interface',
    diYiBuTianXie: 'Step 1: basic information',
  },
  flowlimit: {
    shuRuZhi: 'Input value',
    jieShu: 'End',
    qiShi: 'Start',
    qingXuanZe: 'Select',
  },
  proxyinput: {
    qingShuRuWangLuo: 'Enter the network proxy (format: ip: port)',
    shi: 'Yes',
    fou: 'No',
    qingXuanZeShiFou: 'Select whether to use a proxy',
  },
  serviceinput: {
    xinZengZiDuan: 'New field',
    caoZuo: 'Action',
    sheZhiDiYiBu:
      'Set the query criteria when the query method in step 1 is "local database first"',
    chaXunTiaoJian: 'Query conditions',
    biTian: 'Required',
    buErXing: 'Boolean',
    riQiXing: 'Date type',
    xiaoShuXing: 'Decimal',
    zhengXing: 'Integer',
    ziFuXing: 'String',
    zhiLeiXing: 'Value type',
    dingZhi: 'Constant value',
    bianLiang: 'Variable',
    canShuLeiXing: 'Parameter type',
    qingShuRuDingZhi: 'Enter the constant value',
    hePingTaiTiGong:
      'And the platform provide system field for mapping. if there are no parameters with the same meaning, you can add them and then select them',
    xiTongZiDuan: 'System Field',
    baXiTongZiDuan:
      'Assign the system field to the service input parameter, indicating that the interface caller requests the input parameter field to be mapped to the service input parameter through the system field',
    yingShe: 'Mapping',
    canShuBiaoZhi: 'Parameter id',
  },
  serviceoutput: {
    xinZengZiDuan: 'New field',
    caoZuo: 'Action',
    youXiangHaoAZ:
      'Mailbox number: ^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\. [a-zA-Z0-9_-]+)+$',
    shouJiHaoD: 'Phone number: ^1[3456789]\\d',
    shenFenZhengHaoD: 'ID number: ^[1-9]\\d',
    shiLi: 'Example:',
    shuRuZhengZeDui:
      'Enter a regularity to format the field. This setting is used in the Interface Analysis Report to see the quality of the results returned by the field',
    geShiJiaoYan: 'Format checking',
    quZhi: 'Value',
    feiKong: 'Non-null',
    ruGuoJieKouWei:
      'If the interface is "Checked Billing", configure at least one "Queried Field" in the outgoing parameter field',
    chaDeZiDuan: 'Queried field',
    dingZhi: 'Constant value',
    bianLiang: 'Variable',
    canShuLeiXing: 'Parameter type',
    qingShuRuDingZhi: 'Enter the constant value',
    canShuBiaoZhi: 'Parameter id',
    baFuWuChuCan:
      'And the platform provide system field for mapping. if there are no parameters with the same meaning, you can add them and then select them',
    yingShe: 'Mapping',
    hePingTaiTiGong:
      'And the platform provide system field for mapping. if there are no parameters with the same meaning, you can add them and then select them',
    xiTongZiDuan: 'System field',
  },
  cachetime: {
    chiJiuHuaKu: 'Persistence library',
    huanCun: 'Cache',
    tian: 'Day',
    guan: 'Off',
    kai: 'On',
  },
};
