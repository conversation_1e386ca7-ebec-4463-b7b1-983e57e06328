export default {
  defaultdataconvert: {
    peiZhiBuNengWei: 'Configuration cannot be empty',
    peiZhiBuHeFa: 'The configuration is not legal for the following reasons:',
    liuChengMuBanWei: '[Process Template] node name not set',
    liuChengMuBan: 'Process Template',
    dian<PERSON><PERSON><PERSON>uJingWei: '[<PERSON>ail <PERSON>ert] does not set the email content',
    dian<PERSON>ou<PERSON>uJingWei2: '[Email Alert] does not set the email address',
    dian<PERSON>ou<PERSON>uJingWei3:
      '[Email Alert] has not been configured with an email title',
    dianYouYuJing: 'Email Alert',
    jiXuBuChongWei:
      '[Continue to Supplement] Supplementary services for decision-making not set up',
    ji<PERSON>u<PERSON>u<PERSON>hong: 'Continue to Supplement',
    fenLiuBiLiLei: ']Diversion ratio cumulative not equal to 100',
    fenLiuBiLiWei: ']Diversion ratio is not filled in',
    tiaoJianPeiZhiWei: ']Condition configuration is not filled in',
    zhiShaoPeiZhiTiao: ']At least 2 output lines are configured',
    zhiNengLuYouWei: '[Intelligent Routing] Node name not configured',
    zhiNengLu<PERSON>ou: 'Intelligent Routing',
    aPIJieKou: '[API Interface] Not set Execution data',
    aPIJieKou2: 'API Interface',
    zhiNengPeiZhiYi: '] Only one output line can be configured',
    xian: 'Line',
    shuRu: 'Input',
    shuChu: 'Output',
    tiao: 'Pieces',
    zuiDuoPeiZhi: ']Configure up to',
    kaiShi: 'Start',
    jieShu: 'End',
    bingXing: '[Parallel ',
    zhiShaoPeiZhiTiao2: '] Configure at least 2',
    bingXing2: 'Parallel',
    xuYaoQieZhiNeng: ']Required and only 1 default branch can be configured',
    panDuanKaiShiTiao:
      '[Start of Judgment] Conditional configuration is not filled in',
    panDuan: 'Judgment',
    hanShuWeiSheZhi: '[Function] Execution data is not configured',
    jiaoBenHanShu: 'Script Function',
    jieShuQueShaoShu: '[End] Input stream missing',
    kaiShiKaiShiJie: '[Start] There can be only one start node',
    kaiShiQueShaoShu: 'Start] Output stream is missing',
    queShaoShuChuLiu: ' Start] Output stream is missing',
    queShaoShuRuLiu: ']Missing Input Streams',
    queShaoShuRuShu: ']Missing Input And Output Streams',
  },
  index: {
    yunXingShiBai: 'Failed to run',
    yunXingZhong: 'Running',
    yunXingWanCheng: 'Running Completed',
    zhiNengLuYou: 'Intelligent Routing',
    jieXiShuJuCuo: 'Parsing error,',
    zhiNengYouYiGe: 'Can only have one output',
    buNengSheZhiShu: 'Cannot set output stream',
    buNengSheZhiShu2: 'Cannot set input stream',
  },
};
