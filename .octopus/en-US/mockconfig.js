export default {
  index: {
    gongTOTA: 'Totally {val1} Record(s)',
    bao<PERSON>un: 'Save',
    daoRu: 'Import',
    xin<PERSON><PERSON>: 'Add',
    souSuo: 'Search',
    qingShuRu: 'Enter',
    mOCKNei: 'Mock internal results',
    mOCKWai: 'Mock external results',
    fou: 'No',
    shi: 'Yes',
    shiFouKaiQiM: 'Whether to enable the mock',
    mOCKPei: 'Mock',
    fanHui: 'Return',
    mOCKWai2:
      'Mock external result: mainly used when debugging the interface parsing process, it refers to the return result of the actual request of the data source service interface corresponding to the mock (the call processing process is executed, the return conten',
    mOCKNei2:
      'Mock internal result: mainly used for business system debugging, it refers to the results returned by the mock system itself (without executing the processing process, you can specify the output parameter content by matching the input parameter fields)',
    shan<PERSON>hu: 'Delete',
    queRenShanChuCi: 'Confirm to delete this data?',
    xiuGai: 'Modify',
    caoZuo: 'Action',
    chuCanBaoWen: 'Output parameter message',
    ruCanPiPeiZi: 'Input parameter matching field',
    baoCunChengGong: 'Saved successfully',
  },
};
