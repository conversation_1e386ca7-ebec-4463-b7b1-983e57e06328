export default {
  index: {
    jiGou: 'Organization:',
    xuanZeJiGouBing: 'Select organization and download',
    xin<PERSON><PERSON>: 'Add',
    chaXun: 'Query',
    shang<PERSON><PERSON>: 'Go online',
    xia<PERSON>ian: 'Go offline',
    qingXuanZeFuWu: 'Select service group status',
    qingXuanZeShuJu: 'Select the data source service interface name',
    qingShuRuFuWu: 'Enter the service group name',
    xiaZai: 'Download',
    shan<PERSON>hu: 'Delete',
    queRenShanChu: 'Confirm to delete?',
    cha<PERSON><PERSON>: 'View',
    wuQuanXianCaoZuo: 'No permission operation',
    xiuGai: 'Modify',
    caoZuo: 'Action',
    xiuGaiRen: 'Modifier',
    xiuGai<PERSON>hi<PERSON><PERSON>: 'Modification time',
    chuangJianRen: 'Creator',
    ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Creation time',
    zhuangTai: 'Status',
    shuJuYuanJieKou: 'Data source interface id',
    shuJuYuanJieKou2: 'Data source interface name',
    jieKouShangXianGe: 'Number of interfaces online/total number of interfaces',
    zhongZhi<PERSON>i<PERSON><PERSON>: 'Reset secret key',
    fuZhiChengGong: 'Copy successfully',
    fuZhiMiYao: 'Copy secret key',
    miYao: 'Secret key',
    quDao: 'Channel',
    fuWuZuBiaoZhi: 'Service group ID',
    fuWuZuMingCheng: 'Service group name',
    zhongZhiMiYaoShi: 'Failed to reset key',
    zhongZhiMiYaoCheng: 'Reset key success',
    gROUP: '{val1} - interface document',
  },
};
