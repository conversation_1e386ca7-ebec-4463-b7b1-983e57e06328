export default {
  sync: {
    fanHui: 'Return',
    tiJiao: 'Submit',
    shuJu<PERSON>unZai<PERSON>ong: 'Null values exist',
    qingXuanZeDiaoYong: 'Select the calling method',
    diaoYongFangShi: 'Call method',
    qingXuanZeFuWu: 'Select a service scenario',
    fuWuChangJing: 'Service scenario',
    qingXuanZeShuJu: 'Select data service',
    shuJuFuWu: 'Data service',
    qingXuanZeGongZuo: 'Select workflow',
    gongZuoLiu: 'Workflow',
    shuJuBuChong: 'Data supplement',
    qingXuanZeFuWu2: 'Select service type',
    fuWuLeiXing: 'Service type',
    qingShuRuFuWu: 'Enter the service ID',
    fuWuBiaoZhiZhi:
      'The service ID only supports the combination of English, numbers, and underscores',
    changDuBuNengChao: 'Length cannot exceed 200',
    fuWuBiaoZhi: 'Service ID',
    qingShuRuFuWu2: 'Enter the service name',
    fuWuBiaoZhiZhi2:
      'Service ID only support Chinese, English, numbers, spaces, underscores, -, .',
    fuWuMingCheng: 'Service name',
    fuWuJieKouGuan: 'Service interface management',
    qingShuRuZiDuan: 'Input field name or ID',
    fuWuChuCan: 'Service out parameter',
    qingKongRuCanZi: 'Clear Incoming Fields',
    qingKongLieBiao: 'Clear the list',
    fuWuRuCan: 'Service input parameter',
    caoZuoChengGong: 'Operation successful',
    fuWuChuCanZhong: 'Null value in service input parameter',
    fuWuRuCanZhong: 'Null value in service input parameter',
    fengKongWeiYiBiao: 'Risk control unique ID',
    yunXingMoShi: 'Operation mode',
    jiGouBiaoZhi: 'Organization ID',
    yeWuLiuShuiHao: 'Business flow number',
  },
};
