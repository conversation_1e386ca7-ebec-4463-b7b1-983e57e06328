export default {
  index: {
    wenJianLieBiao: 'File list',
    gongGongDaiMaKuai: 'Common code block',
    houZhiETL: 'Post etl processor',
    qianZhiETL: 'Front etl processor',
    neiZhiChuLiQi: 'Built-in processor',
    chuLiQiLeiXing: 'Processor type',
    eTLChuLi: 'Etl processor name',
    cunZaiQiangYinYong:
      'Strong references (referenced by online, enabled, and other related state components) exist, prohibiting the operation of the',
    piLiangShangXian: 'Batch Online',
    daoRu: 'Import',
    xinZeng: 'Add',
    queDingPiLiangShang: 'Are you sure you want to batch up your selections?',
    yinYongGuanXi: 'Reference relation',
    liShiBanBen: 'Version history',
    zanWuQuanXian: 'No permission',
    cha<PERSON>an: 'View',
    shanChu: 'Delete',
    queDingShanChuCi: 'Confirm to delete this data?',
    xiu<PERSON><PERSON>: 'Modify',
    shang<PERSON><PERSON>: 'Go online',
    cao<PERSON><PERSON>: 'Action',
    xiuGaiRen: 'Modifier',
    xiu<PERSON>ai<PERSON><PERSON><PERSON><PERSON>: 'Modification time',
    chuang<PERSON>ian<PERSON><PERSON>: 'Creator',
    chuang<PERSON>ian<PERSON><PERSON><PERSON><PERSON>: 'Creation time',
    beiZhu: 'Remark',
    zhuangTai: 'Status',
    leiXing: 'Type',
    eTLChuLi2: 'Etl processor id',
    eTLChuLi3: 'Etl processor name',
    daoRuShiBaiQing: 'Import failed! please upload the file again',
    daoRuShiBai: 'Import failed',
    daoRuChengGong: 'Imported successfully',
    shiBieDaoYiXia: 'The following duplicate etl processor id is identified',
    daoRuChengGong2: 'Import successfully!',
    queDingYaoShangXian: 'Sure you want to go online?',
    caoZuoChengGong: 'Operation successful',
    daiTiJiao: 'Pending submission',
    yiShangXian: 'Online',
    sELEC: 'Totally {val1} Record(s)',
  },
};
