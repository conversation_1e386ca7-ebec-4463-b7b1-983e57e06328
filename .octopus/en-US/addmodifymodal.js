export default {
  index: {
    xinZeng: 'Add',
    fuWuChuCan: 'Service out parameter',
    fuWuRuCan: 'Service input parameter',
    shuChuChuLiQi: 'Output processor template',
    shuRuChuLiQi: 'Input processor template',
    zuZhuangFuWuFan:
      'Assemble the returned results of the service, such as return result parsing, processing, etc',
    qingXuanZeET: 'Select etl processor',
    houZhiETL: 'Post etl processor',
    qianZhiETL:
      'The front etl processor is used to prepare data before service request, such as login check, input parameter processing, encryption and decryption, etc',
    qianZhiETL2: 'Front etl processor',
    qingXuanZeFanHui: 'Select the return method',
    fanHuiFangShi: 'Return method',
    qingXuanZeDiaoYong: 'Select the calling method',
    diaoYongFangShi: 'Call method',
    qingTianXieUR: 'Enter the url address',
    uRLDiZhi: 'Url address',
    qingXuanZeJieKou: 'Select the interface type',
    jieKouLeiXingXie: 'Interface type (protocol)',
    moRenMS: 'Default 1000ms',
    shuJuChaoShiShi: 'Data timeout (ms)',
    moRenCi: 'Default 3 times',
    shuJuZhongShiCi: 'Number of data retries',
    moRenTian: 'Default 30 days',
    shuJuHuanCunQi: 'Data cache period (days)',
    shuJuChuLi: 'Data processing',
    qingTianXieHeTong: 'Enter the contract number',
    heTongBianHao: 'Contract number',
    qingShuRu: 'Enter',
    liuLiangShangXianTiao: 'Upper limit of flow (piece)',
    jiaGeYuan: 'Price',
    qingXuanZeJiFei: 'Select the billing type',
    jiFeiLeiXing: 'Billing type',
    qingXuanZeJiFei2: 'Select the billing method',
    jiFeiFangShi: 'Billing method',
    caiGouJieShuRi: 'Purchase end date',
    caiGouKaiShiRi: 'Purchase start date',
    jiFeiGuanLi: 'Billing management',
    di: 'Low',
    zhong: 'Medium',
    gao: 'High',
    qingXuanZeChengBen: 'Select cost level',
    chengBenDengJi: 'Cost grade',
    qingXuanZeZhiXin: 'Select the confidence level',
    zhiXinDu: 'Confidence level',
    qingTianXieShuJu: 'Enter data source service interface id',
    shuJuYuanFuWu: 'Data source service interface id',
    qingTianXieShuJu2: 'Enter name of the data source service interface',
    shuJuYuanFuWu2: 'Data source service interface name',
    qingXuanZeShuJu: 'Select a data type',
    shuJuLeiXing: 'Data type',
    qingXuanZeHeZuo: 'Select a partner',
    heZuoFangMingCheng: 'Partner name',
    shuJuYuanFuWu3: 'Data source service interface information',
    fuWuJieKou: 'Service interface',
    baoCun: 'Save',
    shangYiBu: 'Previous step',
    xiaYiBu: 'Next step',
    caoZuo: 'Action',
    quZhi: 'Value',
    feiKong: 'Non-null',
    chaDeZiDuan: 'Queried field',
    dingZhi: 'Constant value',
    bianLiang: 'Variable',
    canShuLeiXing: 'Parameter type',
    canShuBiaoZhi: 'Parameter id',
    canShuMingCheng: 'Parameter name',
    leiXing: 'Type',
    fou: 'No',
    shi: 'Yes',
    biTian: 'Required',
    riQiXing: 'Date',
    buErXing: 'Boolean',
    xiaoShuXing: 'Decimal',
    zhengXing: 'Integer',
    ziFuXing: 'String',
    zhiLeiXing: 'Value type',
    xiuGai: 'Modify',
    shuJuFuWuXin: 'Constant value',
    cunZaiWeiTianBi:
      'There are mandatory items not filled in, please check and submit!',
    jieShuRiQiBu: 'The end date cannot be earlier than the start date',
    kaiShiRiQiBu: 'The start date cannot be later than the end date',
    fuWuChuCanYou: 'Service output parameter has unfilled field',
    fuWuRuCanYou: 'Service input parameter has unfilled field',
    fuWuRuCanBu:
      'The service input parameter cannot have the same parameter id',
    fuWuChuCanBu:
      'The service output parameter cannot have the same parameter id',
  },
};
