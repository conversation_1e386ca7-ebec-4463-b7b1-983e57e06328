export default {
  inmaptable: {
    qingXuanZeRuCan: 'Select an input parameter field name',
    ruCanZiDuanMing: 'Input parameter field name',
    bianLiang: 'Variable',
    ding<PERSON><PERSON>: 'Constant value',
    canShuLeiXing: 'Parameter type',
    aPIJieKou: 'API Interface Input ID',
    aPIJieKou2: 'API Interface Input Name',
    shuRu: 'Input',
    ruCanZiDuan: 'Input Field',
    ruCanZiDuanDi:
      'Input field, page {val1}, field not fully configured in line {val2} exists',
  },
  outmaptable: {
    qingXuanZeChuCan: 'Select the outgoing field name',
    chuCanZiDuanMing: 'Output field name',
    aPIJieKou: 'API output parameter ID',
    aPIJieKou2: 'API output parameter name',
    shuChu: 'Output',
    shuChuZiDuan: 'Output Field',
    chuCanZiDuanDi:
      'Output field, page {val1}, line {val2} has a field that is not fully configured',
  },
  index: {
    qingXuanZeAP: 'Select an API interface name',
    aPIJieKou: 'API Interface Name',
    aPIJieKou2: 'API Interface',
    guanBi: 'Close',
    queDing: 'OK',
    quXiao: 'Cancel',
  },
};
