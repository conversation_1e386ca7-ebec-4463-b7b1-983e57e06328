export default {
  step1: {
    shuJuYuanFuWuM:
      'Only Support Chinese, English, numbers, spaces, underscores, -, .',
    shuJuYuanFuWuB:
      'Only supports the input combination of letters, numbers and underscores',
    xiaYiBu: 'Next step',
    jieKouDiaoYongJi: 'Interface call mechanism',
    shuJuYuanFuWu: 'Data source service interface information',
    dangQianHeTongGuan:
      'The billing system field associated with the current contract conflicts with the parameter entry field, which has been overwritten. the overwritten parameter entry field ID is {val1}',
    liuLiangShangXianKong:
      'Flow upper limit control is not filled in completely',
    keYiPeiZhiDuo:
      'Multiple control strategies can be configured, and any one of the strategies that reaches the threshold will be triggered',
    liuLiangShangXianKong2: 'Flow upper limit control',
    huanCunKaiGuan: 'Cache Switch',
    ruGuoBuPei<PERSON>hi: 'If not configured, the background defaults to 20,000ms',
    shuJuChaoShiShi: 'Data timeout (ms)',
    moRenCi: 'Default 3 times',
    shuJuZhongShiCi: 'Number of data retries',
    tongGuoJieKouTiao:
      'Controls the query mode through the interface call entry field invokePolicy: 0 cached first , 1 direct query interface',
    chuanCanYouXian: 'Parameter transfer first',
    zhiJieFaQiJie: 'Directly initiate interface query',
    zhiJieJieKou: 'Direct interface',
    xianChaXunBenDi:
      'Query local data first, then launch interface query if not satisfied',
    benDiHuanCunYou: 'Local cache first',
    chaXunFangShi: 'Query method',
    qingXuanZeJiFei: 'Select the billing type',
    jiFeiLeiXing: 'Billing type',
    di: 'Low',
    zhong: 'Medium',
    gao: 'High',
    qingXuanZeChengBen: 'Select cost level',
    chengBenDengJi: 'Cost grade',
    qingXuanZeZhiXin: 'Select the confidence level',
    zhiXinDu: 'Confidence level',
    chaKanXiangQing: 'View Details',
    qingXuanZeHeTong: 'Select a contract',
    heTong: 'Contract',
    qingXuanZeHeZuo: 'Select a partner',
    heZuoFangMingCheng: 'Partner name',
    qingXuanZeShuJu: 'Select a data type',
    shuJuLeiXing: 'Data type',
    qingTianXieShuJu: 'Enter name of the data source service interface',
    shuJuYuanFuWu2: 'Service interface id',
    shuJuYuanFuWu3: 'Service interface name',
  },
  step2: {
    fanHui: 'Return',
    xiaYiBu: 'Next step',
    baoCun: 'Save',
    shangYiBu: 'Previous step',
    fuWuChuCan: 'Service out parameter',
    fuWuRuCan: 'Service input parameter',
    fuWuJieKou: 'Service interface',
    caoZuoChengGong: 'Operation successful',
    caoZuoChengGong2: 'Operation successful，',
    fuWuRuCanZhong:
      'In the service input parameter, please select at least one field and configure "query condition" as the data service query parameter',
    fuWuRuCanYou: 'Service input parameter has unfilled field',
    dangQianJiFeiFang:
      'The current billing method is queried billing, and at least one field of the output parameter is configured as queried field',
    fuWuChuCanBu: 'The service output must not be null',
    fuWuChuCanBu2:
      'The service output cannot have the same parameter ID {val1}',
    fuWuChuCanYou: 'Service output parameter has unfilled field',
    fuWuRuCanBu: 'The service entry must not be null',
    fuWuRuCanBu2: 'The service entry cannot have the same parameter ID {val1}',
    fuWuRuCanBu3: 'The service entry cannot have the same system fields {val1}',
    qingTianXieMiMa: 'Fill in the password',
    miMa: 'Password',
    qingTianXieYongHu: 'Fill in your username',
    yongHuMing: 'Username',
    shuChuChuLiQi: 'Output processor template',
    shuRuChuLiQi: 'Input processor template',
    qingXuanZeZhiBiao: 'Select the indicator set',
    zhiBiaoJi: 'Indicator set',
    qingXuanZeBaoWen: 'Select message',
    baoWen: 'Message',
    qingXuanZeET: 'Select etl processor',
    houZhiETL: 'Post etl processor',
    shi: 'Yes',
    fou: 'No',
    qingXuanZe: 'Select',
    qingXuanZeShiFou: 'Select whether paging',
    shiFouFenYeJie: 'Pagination interface',
    qianZhiETL: 'Front etl processor',
    daiLiXinXiGe: 'Proxy information format is not correct (format: IP:PORT)',
    qingTianXieWanZheng: 'Fill in the complete information',
    shiFouShiYongDai: 'Whether to use proxy',
    qingXuanZeDiaoYong: 'Select the calling method',
    diaoYongFangShi: 'Call method',
    qingTianXieUR: 'Enter the url address',
    uRLGeShi: 'Incorrect URL format (e.g. https://www.tongdun.cn/)',
    sOCKE: 'Incorrect socket format (e.g. 127.0.0.1:8080)',
    uRLDiZhi: 'Url address',
    qingXuanZeJieKou: 'Select the interface type',
    jieKouLeiXingXie: 'Interface type (protocol)',
    pOSTG:
      'Incorrect postgreSQL format (e.g.: *******************************************)',
    oRACL:
      'incorrect oracle format (e.g. *************************************)',
    jDBCGe:
      'jdbc format is incorrect (e.g.: **************************************)',
  },
  step3: {
    tiJiao: 'Submit',
    fanHui: 'Return',
    shangYiBu: 'Previous step',
    fuWuChuCan: 'Service out parameter',
    fuWuRuCan: 'Service input parameter',
    fuWuJieKou: 'Service interface',
    caoZuoChengGong: 'Operation successful',
    dangQianJiFeiFang:
      'The current billing method is queried billing, and at least one field of the output parameter is configured as queried field',
    fuWuChuCanBu:
      'Service output parameter must not have the same parameter ID {val1}',
    fuWuChuCanYou: 'Service output parameter has unfilled field',
    fuWuRuCanBu: 'The service input parameter cannot be empty',
    qingZhiShaoXuanZe:
      'Select at least one field as query condition for the caller to use when configuring the query',
    fuWuRuCanBu2: 'The service entry cannot have the same parameter ID {val1}',
    fuWuRuCanYou: 'Service input parameter has unfilled field',
    shuChuChuLiQi: 'Output processor template',
    shuRuChuLiQi: 'Input processor template',
    qingXuanZeZhiBiao: 'Select the indicator set',
    zhiBiaoJi: 'Indicator set',
    qingXuanZeBaoWen: 'Select message',
    baoWen: 'Message',
    moRenCi: 'Default 10 times',
    diErBuQingQiu:
      'Maximum number of polls for the request interface in step 2 and the query interface in step 3',
    zuiDaLunXunCi: 'Maximum number of polls',
    moRenMS: 'Default 1000ms',
    diErBuQingQiu2:
      'Automatic polling time for the request interface in step 2 and the query interface in step 3. after the request result is returned in step 2, execute the query interface in step 3',
    lunXunShiJianJian: 'Polling interval (ms)',
    qingXuanZeET: 'Select etl processor',
    houZhiETL: 'Post etl processor',
    qianZhiETL: 'Front etl processor',
    qingTianXieWanZheng: 'Fill in the blanks',
    shiFouShiYongDai: 'Whether to use proxy',
    qingXuanZeDiaoYong: 'Select the calling method',
    diaoYongFangShi: 'Call method',
    qingTianXieUR: 'Enter the url address',
    uRLGeShi: 'Incorrect URL format (e.g. https://www.tongdun.cn/)',
    sOCKE: 'Incorrect socket format (e.g. 127.0.0.1:8080)',
    uRLDiZhi: 'Url address',
    qingXuanZeJieKou: 'Select the interface type',
    jieKouLeiXingXie: 'Interface type (protocol)',
  },
};
