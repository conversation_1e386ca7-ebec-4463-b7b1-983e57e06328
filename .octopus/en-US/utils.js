export default {
  request: {
    wang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Gateway timed out',
    fuWuBuKeYong:
      'The service is unavailable, and the server is temporarily overloaded or maintained',
    wangGuanCuoWu: 'Gateway error',
    fuWuQiFaSheng: 'An error occurred on the server. please check the server',
    dangChuangJianYiGe: 'Check error occurred while creating an object',
    qingQiuDeZiYuan:
      'The requested resource is permanently deleted and will not be obtained again',
    qingQiuDeGeShi: 'The requested format is not available',
    faChuDeQingQiu:
      'The request was made for a non-existent record, and the server did not operate',
    yongHuDeDaoShou: 'The user is authorized, but access is prohibited',
    yongHuZanWuQuan: 'The user has no permission, please log in again',
    faChuDeQingQiu2:
      'There was an error in the request sent. the server did not create or modify data',
    shan<PERSON>huShuJuCheng: 'Data deleted successfully',
    yiGeQingQiuYi:
      'A request has entered the background queue (asynchronous task)',
    xinJian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Successfully created or modified data',
    fuWuQiChengGong: 'The server successfully returned the requested data',
  },
  routedisplayconfigparser: {
    yiShanChu: 'Deleted',
    liXian: 'Offline',
    zaiXian: 'Online',
  },
  sortablecolumntitlerenderer: {
    shengXu: 'Ascending order',
    moRenPaiXu: 'Default sorting',
    jiangXu: 'Descending order',
  },
  jiu: 'Nine',
  ba: 'Eight',
  qi: 'Seven',
  liu: 'Six',
  wu: 'Five',
  si: 'Four',
  san: 'Three',
  er: 'Two',
  yi: 'One',
  ling: 'Zero',
  lingYuanZheng: 'Zero',
  yuan: 'Yuan',
  zheng: 'Whole',
  qian: 'Thousand',
  bai: 'Hundred',
  shi: 'Ten',
  yi2: 'Billion',
  wan: 'Ten thousand',
  jiu2: 'Nine',
  ba2: 'Eight',
  qi2: 'Seven',
  lu: 'Land',
  wu2: 'Five',
  si2: 'Four',
  san2: 'Three',
  er2: 'Two',
  yi3: 'One',
  fen: 'Minute',
  jiao: 'Jiao',
};
