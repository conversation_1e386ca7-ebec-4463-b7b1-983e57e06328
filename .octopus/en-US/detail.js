export default {
  index: {
    hanShu<PERSON>u: 'Function library',
    xiuGai: 'Modify',
    cha<PERSON><PERSON>: 'View',
    xin<PERSON><PERSON>: 'Add',
    dang<PERSON><PERSON><PERSON>ei<PERSON>hi<PERSON>ei:
      'No changes to the current configuration, no need to republish',
    faBu: 'Publish',
    bao<PERSON><PERSON>: 'Save',
    liuChengXiangQing: 'Process Details',
    banBenV: 'Version: ',
    gongZuoLiu: 'Workflow',
    gongZuoLiuMingCheng: 'Workflow name',
    bianJiShiBai: 'Edit Failure',
    bianJiChengGong: 'Edit Success',
    liuChengMuBan: 'Process templates',
  },
  graphjson: {
    suiBianHuaJueCe: 'Random flower decision supplement',
    liuChengMuBan: 'Process templates',
    jieShu: 'End',
    moXingYuJing: 'Model Alert',
    xinYongKaKeQun: 'Credit card segmentation (standard version)',
    kaiShi: 'Start ',
    tongDunDaiQianShen: 'Tongdun-360 Pre-Loan Audit Customized Data Service',
    panDuanKaiShi: 'Start Judgment',
    bingXingJieShu: 'End of parallelism',
    renXingQiYe: 'People-Corporate',
    renXingGeRen: "People's Banks - Individual",
    bingXingKaiShi: 'Start Parallelism',
    zhiNengLuYou: 'Intelligent routing',
    xinDai: 'Credit',
    moRenDiaoYong: 'Default call',
  },
};
