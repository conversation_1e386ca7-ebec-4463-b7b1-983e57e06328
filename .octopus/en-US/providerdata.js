export default {
  index: {
    yiChangZhuangTaiMa: 'Abnormal status code distribution',
    diaoYongLaiYuanLei: 'Call source type distribution',
    shuJuYuanFuWu: 'Data source service interface call ranking',
    quanBu: 'All',
    daYu: 'Greater than ',
    xiaoYu: 'Less than ',
    pingJunHaoShi: 'Average time consumption',
    diaoYongZhuangTai: 'Call status',
    diaoYongZhuangTaiHe: 'Call status and time consumption statistics',
    shiBaiPingJunHao: 'Average failure time',
    chengGongPingJunHao: 'Average success time',
    chengGongShuJuYuan: 'Number of successful (data source queried)',
    chaXunShuJuYuan:
      'The rate at which the query data source service interface returns a status check',
    chaDeLu: 'Queried rate',
    shiBaiLiang: 'Number of failures',
    shiBaiLu: 'Failure rate',
    chengGongLiang: 'Number of success',
    zongChengGongLuBao: 'Total success rate, including queried and not queried',
    chengGongLu: 'Success rate',
    diaoYongZongLiangCi: 'Total calls/times',
    shuJuYuanFuWu2: 'Interface name',
    shuJuLeiXing: 'Data type',
    shouDongTongJi: 'Manual statistics',
    wu: 'None',
    shangYiCiTongJi: 'Last statistical time',
    tongJiJieGuoYou:
      'The statistical results have a delay of about 5~10 minutes',
    diaoYongTongJi: 'Call statistics',
    shuJuLeiXingFen: 'Data type distribution',
    heZuoFangFenBu: 'Partner distribution',
    shengXiaoZhong: 'Active',
    ge: ' ',
    huanBiZuoTian: 'Day-on-day',
    shuJuYuanFuWu3: 'Total number of data source service interfaces',
    shiShiGaiLan: 'Real time overview',
    shuJuYuanDaPan: 'Data source dashboard',
  },
};
