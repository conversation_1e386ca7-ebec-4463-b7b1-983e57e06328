export default {
  index: {
    xinZeng: '新增',
    fuWuChuCan: '服务出参',
    fuWuRuCan: '服务入参',
    shuChuChuLiQi: '输出处理器模板',
    shuRuChuLiQi: '输入处理器模板',
    zuZhuangFuWuFan: '组装服务返回后的结果，如返回结果解析、加工等',
    qingXuanZeET: '请选择ETL处理器',
    houZhiETL: '后置ETL处理器',
    qianZhiETL:
      '前置ETL处理器用于准备服务请求前的数据，如登录校验、入参处理、加解密等',
    qianZhiETL2: '前置ETL处理器',
    qingXuanZeFanHui: '请选择返回方式',
    fanHuiFangShi: '返回方式',
    qingXuanZeDiaoYong: '请选择调用方式',
    diaoYongFangShi: '调用方式',
    qingTianXieUR: '请填写url地址',
    uRLDiZhi: 'url地址',
    qingXuanZeJieKou: '请选择接口类型',
    jieKouLeiXingXie: '接口类型(协议)',
    moRenMS: '默认1000ms',
    shuJuChaoShiShi: '数据超时时间(ms)',
    moRenCi: '默认3次',
    shuJuZhongShiCi: '数据重试次数',
    moRenTian: '默认30天',
    shuJuHuanCunQi: '数据缓存期(天)',
    shuJuChuLi: '数据处理',
    qingTianXieHeTong: '请填写合同编号',
    heTongBianHao: '合同编号',
    qingShuRu: '请输入',
    liuLiangShangXianTiao: '流量上限(条)',
    jiaGeYuan: '价格(元)',
    qingXuanZeJiFei: '请选择计费类型',
    jiFeiLeiXing: '计费类型',
    qingXuanZeJiFei2: '请选择计费方式',
    jiFeiFangShi: '计费方式',
    caiGouJieShuRi: '采购结束日期',
    caiGouKaiShiRi: '采购开始日期',
    jiFeiGuanLi: '计费管理',
    di: '低',
    zhong: '中',
    gao: '高',
    qingXuanZeChengBen: '请选择成本等级',
    chengBenDengJi: '成本等级',
    qingXuanZeZhiXin: '请选择置信度',
    zhiXinDu: '置信度',
    qingTianXieShuJu: '请填写数据源服务接口标识',
    shuJuYuanFuWu: '数据源服务接口标识',
    qingTianXieShuJu2: '请填写数据源服务接口名称',
    shuJuYuanFuWu2: '数据源服务接口名称',
    qingXuanZeShuJu: '请选择数据类型',
    shuJuLeiXing: '数据类型',
    qingXuanZeHeZuo: '请选择合作方',
    heZuoFangMingCheng: '合作方名称',
    shuJuYuanFuWu3: '数据源服务接口信息',
    fuWuJieKou: '服务接口',
    baoCun: '保存',
    shangYiBu: '上一步',
    xiaYiBu: '下一步',
    caoZuo: '操作',
    quZhi: '取值',
    feiKong: '非空',
    chaDeZiDuan: '查得字段',
    dingZhi: '定值',
    bianLiang: '变量',
    canShuLeiXing: '参数类型',
    canShuBiaoZhi: '参数标识',
    canShuMingCheng: '参数名称',
    leiXing: '类型',
    fou: '否',
    shi: '是',
    biTian: '必填',
    riQiXing: '日期型',
    buErXing: '布尔型',
    xiaoShuXing: '小数型',
    zhengXing: '整型',
    ziFuXing: '字符型',
    zhiLeiXing: '值类型',
    xiuGai: '修改',
    shuJuFuWuXin: '数据服务信息',
    cunZaiWeiTianBi: '存在未填必填项,请检查后提交!',
    jieShuRiQiBu: '结束日期不能小于开始日期',
    kaiShiRiQiBu: '开始日期不能大于结束日期',
    fuWuChuCanYou: '服务出参有未填字段',
    fuWuRuCanYou: '服务入参有未填字段',
    fuWuRuCanBu: '服务入参不能存在相同参数标识',
    fuWuChuCanBu: '服务出参不能存在相同参数标识',
  },
};
