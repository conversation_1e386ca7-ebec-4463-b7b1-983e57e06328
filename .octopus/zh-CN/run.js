export default {
  index: {
    gongGongDaiMaKuai: '公共代码块',
    houZhiETL: '后置ETL处理器',
    qianZhiETL: '前置ETL处理器',
    neiZhiChuLiQi: '内置处理器',
    eTLChuLi: 'ETL处理器类型',
    eTLChuLi2: 'ETL处理器名称(支持模糊查询)',
    daoChu: '导出',
    yinYongGuanXi: '引用关系',
    liShiBanBen: '历史版本',
    zanWuQuanXian: '暂无权限',
    chaKan: '查看',
    xiaXian: '下线',
    caoZuo: '操作',
    xiuGaiRen: '修改人',
    xiuGaiShiJian: '修改时间',
    chuangJianRen: '创建人',
    chuangJianShiJian: '创建时间',
    beiZhu: '备注',
    zhuangTai: '状态',
    leiXing: '类型',
    eTLChuLi3: 'ETL处理器标识',
    eTLChuLi4: 'ETL处理器名称',
    cunZaiQiangYinYong:
      '存在强引用（被上线、启用等相关状态组件引用）关系，禁止操作',
    queRenXiaXianGai: '确认下线该处理器吗？',
    eTLChuLi5: 'ETL处理器导出文件',
    daiTiJiao: '待提交',
    yiShangXian: '已上线',
  },
};
