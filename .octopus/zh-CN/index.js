import common from './common';
import utils from './utils';
import workflowsub from './workflowsub';
import workflow from './workflow';
import versionlist from './versionlist';
import list from './list';
import tab2 from './tab2';
import tab1 from './tab1';
import supplierlist from './supplierlist';
import etl from './etl';
import run from './run';
import filedshow from './filedshow';
import diff from './diff';
import historyversionlist from './historyversionlist';
import edit from './edit';
import mockconfig from './mockconfig';
import addmodifymodal from './addmodifymodal';
import formlist from './formlist';
import dataservicelist from './dataservicelist';
import contractlist from './contractlist';
import threedataremainwarn from './threedataremainwarn';
import threedatamonitor from './threedatamonitor';
import supplierwarning from './supplierwarning';
import testservicemodal from './testservicemodal';
import interfacemanagement from './interfacemanagement';
import outputparams from './outputparams';
import inputparams from './inputparams';
import interfaceadd from './interfaceadd';
import runningarea from './runningarea';
import formula from './formula';
import formulahistory from './formulahistory';
import editorarea from './editorarea';
import diffdetail from './diffdetail';
import detailinfo from './detailinfo';
import detail from './detail';
import copymodal from './copymodal';
import threecalldetail from './threecalldetail';
import pagequery from './pagequery';
import businesschannel from './businesschannel';
import toptable from './toptable';
import rangedata from './rangedata';
import codepiechart from './codepiechart';
import providerdata from './providerdata';
import piechart3 from './piechart3';
import overviewknowledge from './overviewknowledge';
import consumerdata from './consumerdata';
import piechart2 from './piechart2';
import linebarchart3 from './linebarchart3';
import linebarchart2 from './linebarchart2';
import linebarchart from './linebarchart';
import piechart from './piechart';
import header from './header';
import tasklist from './tasklist';
import templatemodal from './templatemodal';
import servicegroup from './servicegroup';
import addmodify from './addmodify';
import appservicelist from './appservicelist';
import modal from './modal';
import internal from './internal';
import external from './external';
import inner from './inner';
import contractbillingdetail from './contractbillingdetail';
import ladderpricetip from './ladderpricetip';
import allflowtip from './allflowtip';
import qualityanalysis from './qualityanalysis';
import models from './models';
import constants from './constants';
import steps from './steps';
import importmodal from './importmodal';
import workfloweditor from './workfloweditor';
import content from './content';
import workflowdetail from './workflowdetail';
import suspendflownode from './suspendflownode';
import routeservicenode from './routeservicenode';
import parallelgateway from './parallelgateway';
import routeservicenodeline from './routeservicenodeline';
import exclusiveline from './exclusiveline';
import exclusivecondition from './exclusivecondition';
import onegroup from './onegroup';
import onecondition from './onecondition';
import functionservicenode from './functionservicenode';
import featureservicenode from './featureservicenode';
import exclusivegateway from './exclusivegateway';
import emailflownode from './emailflownode';
import childflownode from './childflownode';
import tdtag from './tdtag';
import jsontip from './jsontip';
import simplelist from './simplelist';
import searchdrawer from './searchdrawer';
import threecolumns from './threecolumns';
import fourcolumns from './fourcolumns';
import pretablepage from './pretablepage';
import pagination from './pagination';
import nopermission from './nopermission';
import nooperate from './nooperate';
import nodata from './nodata';
import multiuser from './multiuser';
import messagebox from './messagebox';
import indicatorscascader from './indicatorscascader';
import cascadertag from './cascadertag';
import dragmodaltitle from './dragmodaltitle';
import detailmodal from './detailmodal';
import testmodal from './testmodal';
import codemirror1 from './codemirror1';
import codemirror from './codemirror';
import src from './src';

export default Object.assign({}, {
  common,
  src,
  codemirror,
  codemirror1,
  testmodal,
  detailmodal,
  dragmodaltitle,
  cascadertag,
  indicatorscascader,
  messagebox,
  multiuser,
  nodata,
  nooperate,
  nopermission,
  pagination,
  pretablepage,
  fourcolumns,
  threecolumns,
  searchdrawer,
  simplelist,
  jsontip,
  tdtag,
  childflownode,
  emailflownode,
  exclusivegateway,
  featureservicenode,
  functionservicenode,
  onecondition,
  onegroup,
  exclusivecondition,
  exclusiveline,
  routeservicenodeline,
  parallelgateway,
  routeservicenode,
  suspendflownode,
  workflowdetail,
  content,
  workfloweditor,
  importmodal,
  steps,
  constants,
  models,
  qualityanalysis,
  allflowtip,
  ladderpricetip,
  contractbillingdetail,
  inner,
  external,
  internal,
  modal,
  appservicelist,
  addmodify,
  servicegroup,
  templatemodal,
  tasklist,
  header,
  piechart,
  linebarchart,
  linebarchart2,
  linebarchart3,
  piechart2,
  consumerdata,
  overviewknowledge,
  piechart3,
  providerdata,
  codepiechart,
  rangedata,
  toptable,
  businesschannel,
  pagequery,
  threecalldetail,
  copymodal,
  detail,
  detailinfo,
  diffdetail,
  editorarea,
  formulahistory,
  formula,
  runningarea,
  interfaceadd,
  inputparams,
  outputparams,
  interfacemanagement,
  testservicemodal,
  supplierwarning,
  threedatamonitor,
  threedataremainwarn,
  contractlist,
  dataservicelist,
  formlist,
  addmodifymodal,
  mockconfig,
  edit,
  historyversionlist,
  diff,
  filedshow,
  run,
  etl,
  supplierlist,
  tab1,
  tab2,
  list,
  versionlist,
  workflow,
  workflowsub,
  utils,
});