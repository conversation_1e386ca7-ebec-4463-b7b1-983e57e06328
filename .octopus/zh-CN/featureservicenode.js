export default {
  inmaptable: {
    qingXuanZeRuCan: '请选择入参字段名',
    ruCanZiDuanMing: '入参字段名',
    bianLiang: '变量',
    dingZhi: '定值',
    canShuLeiXing: '参数类型',
    aPIJieKou: 'API接口入参标识',
    aPIJieKou2: 'API接口入参名',
    shuRu: '输入',
    ruCanZiDuan: '入参字段',
    ruCanZiDuanDi: '入参字段，第{val1}页，第{val2}行中存在字段未配置完整',
  },
  outmaptable: {
    qingXuanZeChuCan: '请选择出参字段名',
    chuCanZiDuanMing: '出参字段名',
    aPIJieKou: 'API接口出参标识',
    aPIJieKou2: 'API接口出参名',
    shuChu: '输出',
    shuChuZiDuan: '输出字段',
    chuCanZiDuanDi: '出参字段，第{val1}页，第{val2}行中存在字段未配置完整',
  },
  index: {
    qingXuanZeAP: '请选择API接口名称',
    aPIJieKou: 'API接口名称',
    aPIJieKou2: 'API接口',
    guanBi: '关闭',
    queDing: '确定',
    quXiao: '取消',
  },
};
