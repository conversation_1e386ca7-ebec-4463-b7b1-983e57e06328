export default {
  constants: {
    jiaoBenZhiXingQi:
      '/**\n* 脚本执行器\n* 请在此类的run方法中补您你需要实现的业务逻辑\n*\n* @param inputMap 入参集合\n* @return 出参集合\n*/\nimport com.alibaba.fastjson.*;\nimport org.apache.commons.lang3.*;\nimport java.text.SimpleDateFormat;\n\npublic class GroovyEtlHandlerCallerImpl implements GroovyEtlHandlerCaller {\n\n   public Map < String, String > run(Map < String, String > inputMap) {\n\t   Map < String, String > outputMap = new HashMap < String, String > ();\n\t   // TODO 请在此处补充需要实现的业务逻辑，结果值put入outputMap中。\n\n\t   return outputMap;\n   }\n}\n',
    panDingZiDuanShi: '判定字段1是否不为空值而且不是空字符串。',
    panDingZiDuanShi2: '判定字段1是否为空值或者空字符串。',
    kongZhiFuZhiHan:
      '空值赋值函数。若字段1为空，则返回2的值，否则返回字段1的值。例：空值（null，1），则返回结果1；空值（3，9），则返回结果为3。',
    liangGeShiJianXiang:
      '两个时间相减，返回时间差值，时间差值单位可以是年、月、日，示例：#时间相减(arg1,arg2,arg3)，arg1和arg2必须是时间类型的参数，arg3取值必须是：Y、M、D，arg3参数的含义是返回时间差的单位，Y表示是年，M表示是月，D表示是日，不足1日、1月或者1年的系统将舍去不足部分，例如：#时间相减(#转换为时间("2019-01-02 10:00:00"), #转换为时间("2019-01-04 08:00:00"), "D")  返回值是1',
    leiXingZhuanHuanHan:
      '类型转换函数，将字符串类型参数转换为日期类型，入参的字符串格式必须是：yyyy-MM-dd HH:mm:ss，示例：“2019-02-01 13:02:08”。',
    leiXingZhuanHuanHan2:
      '类型转换函数，将字符类型转换为布尔类型，入参的值必须是：“true”或者 “false”，否则系统将抛出异常。',
    leiXingZhuanHuanHan3:
      '类型转换函数，将字符类型或者整型参数转换为小数型，如果传入的字符串为非数字系统将抛出异常。',
    leiXingZhuanHuanHan4:
      '类型转换函数，将字符类型或小数类型参数转换为整型，如果入参是非数字字符系统将抛出异常，如果入参是小数类型系统将舍去小数部分取整数返回。',
    leiXingZhuanHuanHan5:
      '类型转换函数，将整数、小数、布尔、日期、枚举型字段转换为字符串。若入参是布尔，出参为“true”或 “false”；若入数是日期，返回示例为："2019-02-01 13:02:08"；若入參是枚举，出參为枚举值内容的字符串。',
    huoQuXiTongDang:
      '获取系统当前的日期，不包含小时、分钟、秒，格式示例：2019-02-01 00:00:00，此函数返回类型是日期型，时、分、秒用“00”补齐。',
    huoQuXiTongDang2:
      '获取系统当前的时间，格式示例："2019-02-01 13:02:08"，此函数返回类型是日期型。',
    quDiaoZiFuChuan:
      '去掉字符串两端的多余的空格。无论两端的空格有多少个都会去掉',
    yongYuTiQuZi:
      '用于提取字符串中介于两个指定下标之间的字符。如果参数 start 与 stop 相等，那么该方法返回的就是一个空串（即长度为 0 的字符串）。如果 start 比 stop 大，那么该方法在提取子串之前会先交换这两个参数。',
    shuRuYiGeZi:
      '输入一个字符串，返回结果为当前字符串的长度。如输入：3310293“，则结果为7',
    shiYongLiangGeHuo:
      '使用两个或两个以上参数，将其转换为其字符串表示，对其进行并置并返回单个字符串。如输入3329，2132；则输出结果为332921323',
    biJiaoZiFuChuan: '比较字符串1与字符串2是否不相等，区分大小写',
    jiaoYanZiDuanN: '校验字段n中是否不包含字段1到字段n-1中任意一个内容。',
    biaoZhunChaShiFang:
      '标准差是方差的算术平方根。如输入：#标准差（13，15，17），则结果为2√6/3',
    geGeShuJuFen:
      '各个数据分别与其平均数之差的平方的和的平均数。如输入：#方差（13，15，17），则结果为8/3',
    suiJiXuanQuDa: '随机选取大于等于 0.0且小于 1.0 的伪随机 double 值',
    tiaoJianHanShuYong:
      '条件函数。用法为：“如果 年龄>40岁，  那么 结果=1，否则 结果=2”',
    tiaoJianHanShuYong2: '条件函数。用法为：“如果 年龄>40岁，  那么 结果=1',
    ruGuoQiTaRu:
      '如果(){},其他如果(){},否则{}”条件函数，在()内填写判断条件，在{}内填写执行动作。\n        用法1：如果年龄小于18岁，则给“用户”字段赋值“未成年”。\n        示例：如果(@年龄 < 18) {@用户= "未成年";}\n        用法2：如果年龄小于18岁，则给“用户”字段赋值“未成年”；如果年龄大于等于18岁，小于60岁，则给用户字段赋值成年人。\n        示例：如果(@年龄 < 18) {@用户= "未成年";}   其他如果(@年龄< 60 ){@用户= "成年人";}\n        用法3：如果年龄小于18岁，则给“用户”字段赋值“未成年”；如果年龄大于等于18岁，小于60岁，则给用户字段赋值成年人；其他情况给字段赋值为老年人。\n        示例：如果(@年龄 < 18) {@用户= "未成年";}   其他如果(@年龄< 60 ){@用户= "成年人";}  否则{@用户= "老年人";}',
    huoYongYuTiaoJian:
      '或。用于条件函数中，用法为：如果 年龄>60岁 或 年收入>10万，那么结果=1',
    qieYongYuTiaoJian:
      '且。用于条件函数中，用法为：如果 年龄>60岁 且 性别=女，那么结果=1',
    chu: '除',
    jian: '减',
    zaiQuXiaoShuJin:
      "在取小数近似数的时候，如果尾数的最高位数字是4或者比4小，就把尾数去掉。如果尾数的最高位数是5或者比5大，就把尾数舍去并且在它的前一位进'1'。如输入：#四舍五入（5.26），则输出结果为5.3；输入5.32，则输出为5.3",
    buGuanSiSheWu:
      '不管四舍五入的规则，只选取所给值的整数部分作为输出结果。如输入：#向下取整（8.9），则输出结果为8。',
    quBiDangQianShu:
      '取比当前数值大的最近一位整数作为结果输出，如输入：#向上取整（7.2），则结果为8。',
    quJueDuiZhiRu: '取绝对值。如输入：#绝对值（-9），则结果为9。',
    biJiaoZiFuChuan2: '比较字符串1与字符串2是否完全相等，区分大小写',
    jiaoYanZiDuanN2: '校验字段n中是否包含字段1，或者字段2…或者字段n-1的内容。',
    cheng: '乘',
    jia: '加',
    xiaoYuDengYu: '小于等于',
    daYuDengYu: '大于等于',
    buDengYu: '不等于',
    huoQuDangQianRi: '获取当前日期格式字符串的年。',
    huoQuDangQianRi2: '获取当前日期格式字符串的月。',
    huoQuDangQianRi3: '获取当前日期格式字符串的天。',
    kuoHao: '括号',
    xiaoYu: '小于',
    daYu: '大于',
    dengYu: '等于',
    hanShuXingShiWei:
      '函数形式为#幂函数(第一个数字类型,第二个数字类型)，第二个数字类型如果是小数会自动转化成整数，最后结果保持小数6位四舍五入，如果结果超过（10个9==9999999999）那么就会输出10个9',
    qiuDuoGeShuZhi: '求多个数值的总和。如输入：#求和（1，3，2）；则结果为6。',
    biJiaoDuoGeShu:
      '比较多个数值的大小，并选取最小的数值作为结果输出。如输入：#最小值（13，117，29），则结果为13。',
    biJiaoDuoGeShu2:
      '比较多个数值的大小，并选取最大的数值作为结果输出。如输入：#最大值（13，117，29），则结果为117。',
    qiuDuoGeShuZhi2:
      '求多个数值或数值类字段的平均值。注意：最后一位表示计算结果的精确度。',
    ruGuoNaMeFou: '如果()\n#那么{}\n#否则{}',
    ruGuoQiTaRu2: '如果(){}\n#其他如果(){}\n#否则{}',
    ruGuoNaMe: '如果()\n#那么{}',
    huo: '或',
    qie: '且',
    panFeiKong: '判非空',
    panKong: '判空',
    kongZhiFuZhi: '空值赋值',
    shiJianXiangJian: '时间相减',
    zhuanHuanWeiShiJian: '转换为时间',
    zhuanHuanWeiBuEr: '转换为布尔',
    zhuanHuanWeiXiaoShu: '转换为小数',
    zhuanHuanWeiZhengShu: '转换为整数',
    zhuanHuanWeiZiFu: '转换为字符串',
    huoQuDangQianRi4: '获取当前日期',
    huoQuDangQianShi: '获取当前时间',
    fouZe: '否则',
    qiTaRuGuo: '其他如果',
    naMe: '那么',
    ruGuo: '如果',
    quTouWeiKongGe: '去头尾空格',
    jieQuZiFuChuan: '截取字符串',
    ziFuChuanChangDu: '字符串长度',
    pinJieZiFuChuan: '拼接字符串',
    buXiangDeng: '不相等',
    buBaoHan: '不包含',
    biaoZhunCha: '标准差',
    fangCha: '方差',
    suiJiShu: '随机数',
    ruGuoNaMeFou2: '如果，那么，否则',
    ruGuoQiTaRu3: '如果，其他如果，否则',
    ruGuoNaMe2: '如果，那么',
    siSheWuRu: '四舍五入',
    xiangXiaQuZheng: '向下取整',
    xiangShangQuZheng: '向上取整',
    jueDuiZhi: '绝对值',
    xiangDeng: '相等',
    baoHan: '包含',
    huoQuNian: '获取年',
    huoQuYue: '获取月',
    huoQuTian: '获取天',
    miHanShu: '幂函数',
    qiuHe: '求和',
    zuiXiaoZhi: '最小值',
    zuiDaZhi: '最大值',
    pingJunZhi: '平均值',
  },
  formulaeditor: {
    zhiChiDeYunSuan: '支持的运算符：+、-、x、/、=、（）',
    zhiChiDeHanShu: '支持的函数：平均值、最大值、最小值、绝对值',
    liRuShuChuZi:
      '例如：[输出字段]@实际放款金额 = #最大值（@申请金额，@放款金额）',
    tIPSHou: 'Tips：@后可添加变量，#后可添加函数，常量和运算符可直接键盘输出。',
    wenXinTiShi: '温馨提示',
  },
  index: {
    zhuYiRenYiWan: '（注意：任意完整公式后均需加分号）',
    fangKuanJinE: '放款金额',
    shenQingJinE: '申请金额',
    zuiDaZhi: '#最大值',
    shiJiFangKuanJin: '实际放款金额',
    liRu: '例如：',
    tIPSHou: 'Tips：@后可添加变量，#后可添加函数，常量和运算符可直接键盘输出。',
    jiaoBen: '脚本',
    gongShi: '公式',
    zhiKeXuanZeYi: '只可选择一种。注意：切换后原方式下的配置内容将清空。',
    bianXieLeiXing: '编写类型',
    jiSuanLuoJi: '计算逻辑',
    baoCun: '保存',
    zanCun: '暂存',
    ceShi: '测试',
    qingShuRuMiaoShu: '请输入描述，2000字以内',
    changDuBuNengChao: '长度不能超过2000',
    miaoShu: '描述',
    qingShuRuHanShu: '请输入函数名称',
    mingChengBiXuYou: '名称必须由中英文、数字、下划线组成',
    changDuBuNengChao2: '长度不能超过200',
    hanShuMingCheng: '函数名称',
    qingXuanZeHanShu: '请选择函数类型',
    hanShuBiaoZhi: '函数标识',
    jiBenXinXi: '基本信息',
    qingShuRuHanShu2: '请输入函数公式！',
    kongZhiFuZhi: '空值赋值',
    quTouWeiKongGe: '去头尾空格',
    jieQuZiFuChuan: '截取字符串',
    pinJieZiFuChuan: '拼接字符串',
    xiangXiaQuZheng: '向下取整',
    xiangShangQuZheng: '向上取整',
    qiuHe: '求和',
    zuiXiaoZhi: '最小值',
    zuiDaZhi2: '最大值',
    pingJunZhi: '平均值',
  },
};
