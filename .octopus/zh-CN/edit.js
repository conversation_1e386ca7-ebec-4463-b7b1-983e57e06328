export default {
  index: {
    wenJianLieBiao: '文件列表',
    gongGongDaiMaKuai: '公共代码块',
    houZhiETL: '后置ETL处理器',
    qianZhiETL: '前置ETL处理器',
    neiZhiChuLiQi: '内置处理器',
    chuLiQiLeiXing: '处理器类型',
    eTLChuLi: 'ETL处理器名称(支持模糊查询)',
    cunZaiQiangYinYong:
      '存在强引用（被上线、启用等相关状态组件引用）关系，禁止操作',
    piLiangShangXian: '批量上线',
    daoRu: '导入',
    xinZeng: '新增',
    queDingPiLiangShang: '确定批量上线选中项吗?',
    yinYongGuanXi: '引用关系',
    liShiBanBen: '历史版本',
    zanWuQuanXian: '暂无权限',
    chaKan: '查看',
    shan<PERSON>hu: '删除',
    queDingShanChuCi: '确定删除此数据?',
    xiuGai: '修改',
    shangXian: '上线',
    caoZuo: '操作',
    xiuGaiRen: '修改人',
    xiuGaiShiJian: '修改时间',
    chuangJianRen: '创建人',
    chuangJianShiJian: '创建时间',
    beiZhu: '备注',
    zhuangTai: '状态',
    leiXing: '类型',
    eTLChuLi2: 'ETL处理器标识',
    eTLChuLi3: 'ETL处理器名称',
    daoRuShiBaiQing: '导入失败！请重新上传文件',
    daoRuShiBai: '导入失败',
    daoRuChengGong: '导入成功',
    shiBieDaoYiXia: '识别到以下重复的ETL处理器标识',
    daoRuChengGong2: '导入成功！',
    queDingYaoShangXian: '确定要上线吗？',
    caoZuoChengGong: '操作成功',
    daiTiJiao: '待提交',
    yiShangXian: '已上线',
    sELEC: '总共 {val1} 条',
  },
};
