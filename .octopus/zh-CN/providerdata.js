export default {
  index: {
    yiChangZhuangTaiMa: '异常状态码分布',
    diaoYongLaiYuanLei: '调用来源类型分布',
    shuJuYuanFuWu: '数据源服务接口调用排行',
    quanBu: '全部',
    daYu: '大于',
    xiao<PERSON>u: '小于',
    pingJunHaoShi: '平均耗时',
    diaoYongZhuangTai: '调用状态',
    diaoYongZhuangTaiHe: '调用状态和耗时统计',
    shiBaiPingJunHao: '失败平均耗时',
    chengGongPingJunHao: '成功平均耗时',
    chengGongShuJuYuan: '成功(数据源查得)量',
    chaXunShuJuYuan: '查询数据源服务接口返回结果为查得状态的比率',
    chaDeLu: '查得率',
    shiBaiLiang: '失败量',
    shiBaiLu: '失败率',
    chengGongLiang: '成功量',
    zongChengGongLuBao: '总成功率，包含查得和未查得',
    chengGongLu: '成功率',
    diaoYongZongLiangCi: '调用总量/次',
    shuJuYuanFuWu2: '数据源服务接口名称',
    shuJuLeiXing: '数据类型',
    shouDongTongJi: '手动统计',
    wu: '无',
    shangYiCiTongJi: '上一次统计时间',
    tongJiJieGuoYou: '统计结果有5~10分钟左右的延迟',
    diaoYongTongJi: '调用统计',
    shuJuLeiXingFen: '数据类型分布',
    heZuoFangFenBu: '合作方分布',
    shengXiaoZhong: '生效中',
    ge: '个',
    huanBiZuoTian: '环比昨天',
    shuJuYuanFuWu3: '数据源服务接口总数',
    shiShiGaiLan: '实时概览',
    shuJuYuanDaPan: '数据源大盘',
  },
};
