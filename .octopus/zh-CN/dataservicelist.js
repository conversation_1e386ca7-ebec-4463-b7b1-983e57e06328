export default {
  daoRuChengGong: '导入成功',
  tiaoJiLu: '共 {val1} 条记录',
  gong: '共',
  xinZeng: '新增',
  daoRu: '导入',
  chaXun: '查询',
  zanWuQuanXian: '暂无权限',
  xiaXian: '下线',
  shang<PERSON>ian: '上线',
  qingXuanZeJieKou: '请选择接口状态',
  yiBuJieKou: '异步接口',
  tongBuJieKou: '同步接口',
  qingXuanZeJieKou2: '请选择接口类型',
  qingXuanZeShuJu: '请选择数据类型',
  qingXuanZeShuJu2: '请选择数据源服务接口名称',
  qingXuanZeHeZuo: '请选择合作方名称',
  gengDuo: '更多',
  chaKan: '查看',
  xiuGai: '修改',
  yinYongGuanXi: '引用关系',
  shan<PERSON>hu: '删除',
  daoChu: '导出',
  mOCKPei: 'Mock 配置',
  fuZhi: '复制',
  ceShi: '测试',
  caoZuo: '操作',
  xiuGaiRen: '修改人',
  xiuGaiShiJian: '修改时间',
  chuangJianRen: '创建人',
  chuangJianShiJian: '创建时间',
  tEXTTian: '{val1}天',
  kongZhiDiaoYongGuo:
    '控制调用过程中的结果数据是否保存，如保存，设置保存的周期',
  huanCunYouXiaoQi: '缓存有效期',
  yiXiaXian: '已下线',
  yiShangXian: '已上线',
  yiShanChu: '已删除',
  zhuangTai: '状态',
  jieKouLeiXing: '接口类型',
  heZuoFangMingCheng: '合作方名称',
  shuJuLeiXing: '数据类型',
  shuJuYuanFuWu: '数据源服务接口标识',
  shuJuYuanFuWu2: '数据源服务接口名称',
  daoChuShiBai: '导出失败！',
  cunZaiQiangYinYong:
    '存在强引用（被上线、启用等相关状态组件引用）关系，禁止操作',
  queDingShanChuCi: '确定删除此数据？',
  caoZuoChengGong: '操作成功',
  qingShuRuFuWu: '请输入服务接口标识',
  shouQuan: '授权',
};
