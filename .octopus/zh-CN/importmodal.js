export default {
  constant: {
    ceLueBiaoZhiYi: '策略标识一致，则认为”相同策略“',
    guiZeJiUU: '规则集uuid一致，则认为”相同规则集”',
    liXianZhiBiaoBiao: '离线指标标识一致，则认为”相同离线指标“',
    shiShiZhiBiaoBiao: '实时指标标识一致，则认为”相同实时指标“',
    jiGouDuiJieFu: '机构对接服务',
    zhiDingJiGouQu: '指定机构渠道下存在相同标识，则认为“相同决策工具”',
    hanShuKu: '函数库',
    hanShuKuBiaoZhi: '函数库标识一致，则认为”相同函数库“',
    mingDanBiaoZhiYi: '名单标识一致，则认为”相同名单“',
    moXingBiaoZhiYi: '模型标识一致，则认为”相同模型“',
    sanFangShuJuBiao: '三方数据标识一致，则认为”相同决策工具“',
    dongTaiZiDuanBiao: '动态字段标识一致，则认为”相同动态字段“',
    xiTongZiDuanBiao: '系统字段标识一致，则认为”相同系统字段“',
    pingFenKa: '评分卡',
    jueCeGongJuBiao: '决策工具标识一致，则认为”相同决策工具“',
    liuChengMuBan: '流程模板',
    jueCeGongJu: '决策工具',
    guiZeJi: '规则集',
    ceLue: '策略',
    chuangJianBanBen: '创建版本',
    tiaoGuo: '跳过',
    fuGai: '覆盖',
    shouQuanGuiZeJi: '授权（规则集、指标、名单集、决策工具）',
    ziDongXinJian: '自动新建',
    guiZe: '规则',
    liXianZhiBiao: '离线指标',
    shiShiZhiBiao: '实时指标',
    mingDan: '名单',
    moXing: '模型',
    sanFang: '三方',
    dongTaiZiDuan: '动态字段',
    xiTongZiDuan: '系统字段',
    ziDian: '字典',
    yongHuCaoZuoQuan:
      '用户操作权限需要大于当前导入文件中相关组件的权限（机构和渠道权限），才支持导入，其中被授权使用的组件可导入相关引用关系',
    zuJianQueShi: '组件缺失',
    dangQianDaoRuHuan:
      '当前导入环境中缺少导入文件中必要的环境信息，包含渠道、机构、业务类型、风险类型、事件类型、风险决策结果等信息，则不支持导入',
    xiangTongZuJianDao:
      '相同组件导入需校验相关基础配置信息是否一致，若不一致，则不支持导入',
    yongHuQuanXianXiao: '用户权限校验',
    huanJingXinXiQue: '环境信息缺失校验',
    xiangTongZuJianPei: '相同组件配置校验',
  },
  index: {
    diSiBuDaoRu: '第四步：导入结果',
    diSanBuDaoRu: '第三步：导入模式选择',
    diErBuXiTong: '第二步：系统校验',
    diYiBuDaoRu: '第一步：导入文件',
    xiaYiBu: '下一步',
    guanBi: '关闭',
    huiGun: '回滚',
    caoZuoChengGong: '操作成功',
    shangYiBu: '上一步',
    quXiao: '取消',
    meiGeZuJianYou: '每个组件有且只能选择一个导入方式',
    mODAL: '{val1}导入向导',
    daoRuPiCiDao: '（导入批次）> 导入结果查看',
    daoRuPiCiHui: '（导入批次）> 回滚结果查看',
    daoRuPiCiXiao: '（导入批次）> 校验结果查看',
    fuGaiXiangTongBiao: '覆盖相同标识，继续导入',
    tiaoGuoXiangTongBiao: '跳过相同标识，继续导入',
    chaXunDaoXiangTong: '查询到相同标识，则全部不导入',
    chaXunDaoXiangTong2: '查询到相同ETL标识的处理方式：',
    chaXunDaoXiangTong3: '查询到相同系统字段标识的处理方式：',
    chaXunDaoXiangTong4: '查询到相同合同标识的处理方式：',
    chaXunDaoXiangTong5: '查询到相同合作方标识的处理方式：',
    chaXunDaoXiangTong6: '查询到相同ETL的处理方式：',
    chaXunDaoXiangTong7: '查询到相同数据源标识的处理方式：',
    dianJiShangChuan: '点击上传',
    xuanZeWenJian: '选择文件：',
    daoRuShuJuYuan: '导入数据源服务',
    qingXianShangChuanWen: '请先上传文件',
    yiCiZuiDuoShang: '一次最多上传一个文件！',
    daoRuChengGong: '导入成功',
    qingXuanZeWenJian: '请选择文件',
    daoRuETL: '导入ETL处理器',
  },
};
