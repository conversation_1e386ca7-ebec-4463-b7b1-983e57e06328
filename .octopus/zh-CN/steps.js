export default {
  one: {
    zhuJiaoYanTongGuo:
      '注：校验通过内容不支持页面查看，可通过下载详细校验清单进行查看',
    tiaoJiaoYanWeiTong: '条校验未通过',
    tiao: '条',
    gong<PERSON>iao<PERSON><PERSON>: '共校验',
    gong: '共',
    jiaoYanQuanBuTong: '校验全部通过',
    xiaZaiXiangXiXiao: '下载详细校验清单',
    cURNA: '{val1}_校验清单',
    geZuJianJiaoYan: '个组件校验未通过',
    you: '有',
    jiaoYanWeiTongGuo: '校验未通过',
    quanBuJiaoYanTong: '全部校验通过',
    qingShangChuanCS: '请上传xlsx格式文件 / 请勿修改模板名称和格式',
    dianJiShangChuanHuo: '点击上传或将文件拖拽到这里上传',
    shangChuanWenJian: '上传文件',
    qingShuRuYongHu: '请输入用户密码',
    dengLuMiMa: '登录密码',
    qingShuRuDengLu: '请输入登录用户名',
    dengLuYongHuMing: '登录用户名',
    qingShuRuYuanShu: '请输入源数据完整路径(不包含文件名)',
    yuanShuJuWenJian: '源数据文件',
    qingShuRuFuWu: '请输入服务器地址(如ftp://ip:port或者sftp://ip:port)',
    fuWuQiDiZhi: '服务器地址',
    zu: '(组)',
    qingXuanZeDiaoYong: '请选择调用方/调用方组',
    diaoYongFangDiaoYong: '调用方/调用方组',
    benDiWenJian: '本地文件',
    fTPSF: 'FTP/SFTP文件',
    cunZaiBiTianXiang: '存在必填项未填',
  },
  three: {
    kuaiSuTiaoZhuan: '快速跳转',
    daoRuJieGuo: '导入结果',
    tiao: '条',
    gong: '共',
    weiTongGuo: '未通过',
    daoRu: '导入',
    huiGun: '回滚',
    geZuJianShiLi: '个组件实例',
    you: '有',
    daoRuCaoZuoWei: '导入操作未成功',
    huiGunCaoZuoWei: '回滚操作未成功',
    daoRuCaoZuoQuan: '导入操作全部成功',
    huiGunCaoZuoQuan: '回滚操作全部成功',
    xiaZaiXiangXiDao: '下载详细导入结果',
    xiaZaiXiangXiHui: '下载详细回滚结果',
    cURNA: '{val1}_导入结果清单',
    cURNA2: '{val1}_回滚结果清单',
    shiBai: '失败',
    chengGong: '成功',
    qingShuRuYongHu: '请输入用户密码',
    dengLuMiMa: '登录密码',
    qingShuRuDengLu: '请输入登录用户名',
    dengLuYongHuMing: '登录用户名',
    qingShuRuJieGuo: '请输入结果数据完整路径(不包含文件名)',
    jieGuoShuJuWen: '结果数据文件',
    qingShuRuFuWu: '请输入服务器地址(如ftp://ip:port或者sftp://ip:port)',
    fuWuQiDiZhi: '服务器地址',
    benDiWenJian: '本地文件',
    fTPSF: 'FTP/SFTP文件',
    cunZaiBiTianXiang: '存在必填项未填',
  },
  two: {
    daoRuFangShi: '导入方式：',
    xiangTong: '相同',
    zhuXiangTongZuJian: '注：相同组件覆盖导入，不覆盖原组件名称',
    zhuRuoZiDongChuang:
      '注：若自动创建导入时遇名称相同，则默认在名称后加时间后导入',
    zhuQueShiZuJian: '注：缺失组件系统默认自动新建，无需手动选择导入模式',
    dangQianWuXuShou: '当前无需手工选择导入模式',
    ru: '如：0 0/30 * * * ?',
    zhiNengXuanZeFen: '只能选择10分钟之后的时间',
    miao: '秒',
    fen: '分',
    shi: '时',
    qingXuanZe: '请选择',
    cRONBiao: 'cron表达式配置错误，请检查！',
    cunZaiBiTianXiang: '存在必填项未填',
    cRONBiao2: 'cron表达式',
    zhiDingMouYiShi: '指定某一时间',
    meiYueGuDingShi: '每月固定时间',
    meiZhouGuDingShi: '每周固定时间',
    meiTianGuDingShi: '每天固定时间',
    xingQiRi: '星期日',
    xingQiLiu: '星期六',
    xingQiWu: '星期五',
    xingQiSi: '星期四',
    xingQiSan: '星期三',
    xingQiEr: '星期二',
    xingQiYi: '星期一',
  },
  zero: {
    wenJianZhongRuoYin:
      '文件中若引用未启用或未上线组件，则相关引用组件将不被一起导入',
    dianJiXuanZeWen: '点击选择文件',
    qingXuanZeShangChuan: '请选择上传文件',
    shangChuanWenJian: '上传文件',
    fou: '否',
    shi: '是',
    qingTianXieJiGou: '请填写机构',
    yiLaiZuJianDao: '依赖组件导入',
    genJuYuanWenJian: '根据源文件机构渠道导入',
    genJuZhiDingJi: '根据指定机构渠道导入',
    qingXuanZeDaoRu: '请选择导入模式',
    daoRuMoShi: '导入模式',
    wenJianDaXiaoBu: '文件大小不能超过50M，请重新上传',
    wenJianLeiXingCuo: '文件类型错误，请重新上传',
  },
};
