export default {
  index: {
    gongTOTA: '共 {val1} 条记录',
    baoCun: '保存',
    daoRu: '导入',
    xin<PERSON>eng: '新增',
    souSuo: '搜索',
    qingShuRu: '请输入',
    mOCKNei: 'Mock内部结果',
    mOCKWai: 'Mock外部结果',
    fou: '否',
    shi: '是',
    shiFouKaiQiM: '是否开启mock',
    mOCKPei: 'Mock 配置',
    fanHui: '返回',
    mOCKWai2:
      'Mock外部结果：主要是调试接口解析过程时使用，指mock对应的数据源服务接口真实请求的返回结果(执行调用处理过程，可以配置返回内容，支持xml、html、json格式)。',
    mOCKNei2:
      'Mock内部结果：主要是业务系统调试使用，指mock系统本身返回结果(执行调用处理过程，可以通过入参字段匹配，指定输出参数内容)。',
    shanChu: '删除',
    queRenShanChuCi: '确认删除此数据?',
    xiuGai: '修改',
    caoZuo: '操作',
    chuCanBaoWen: '出参报文',
    ruCanPiPeiZi: '入参匹配字段',
    baoCunChengGong: '保存成功',
  },
};
