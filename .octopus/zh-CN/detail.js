export default {
  index: {
    hanShuKu: '函数库',
    xiuGai: '修改',
    chaKan: '查看',
    xin<PERSON><PERSON>: '新增',
    dangQianPeiZhiMei: '当前配置没有改动，无需重新发布',
    faBu: '发布',
    baoCun: '保存',
    liuChengXiangQing: '流程详情',
    banBenV: '版本：V',
    gongZuoLiu: '工作流',
    gongZuoLiuMingCheng: '工作流名称',
    bianJiShiBai: '编辑失败',
    bianJiChengGong: '编辑成功',
    liuChengMuBan: '流程模板',
  },
  graphjson: {
    suiBianHuaJueCe: '随便花决策补充',
    liuChengMuBan: '流程模板',
    jieShu: '结束',
    moXingYuJing: '模型预警',
    xinYongKaKeQun: '信用卡客群分（标准版）',
    kaiShi: '开始',
    tongDunDaiQianShen: '同盾-360贷前审核自定义数据服务 ',
    panDuanKaiShi: '判断开始',
    bingXingJieShu: '并行结束',
    renXingQiYe: '人行-企业',
    renXingGeRen: '人行-个人',
    bingXingKaiShi: '并行开始',
    zhiNengLuYou: '智能路由',
    xinDai: '信贷',
    moRenDiaoYong: '默认调用',
  },
};
