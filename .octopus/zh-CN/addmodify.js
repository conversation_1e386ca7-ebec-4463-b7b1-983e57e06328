export default {
  xiTongZiDongGen:
    '系统自动根据输出入参匹配符合条件的三方服务接口列表供选择配置，可指定多个，设置切换优先级。以下两种情况触发异常切换：1.已达到配置重试的次数后，仍然失败；2.当前接口已达到设置的流量上限控制。',
  fou: '否',
  shi: '是',
  qingXuanZe: '请选择',
  yiChangQieHuan: '异常切换',
  qingXuanZeFanHui: '请选择返回类型',
  fanHuiLeiXing: '返回类型',
  moRenZeYuShu: '默认则与数据源接口超时时间一致',
  shuJuChaoShiShi: '数据超时时间(ms)',
  moRenZeYuShu2: '默认则与数据源接口重试次数一致',
  shuJuZhongShiCi: '数据重试次数',
  shuJuYuanJieKou: '数据源接口',
  shuJuLeiXing: '数据类型',
  xiuGaiFuWuJie: '修改服务接口',
  xinZengFuWuJie: '新增服务接口',
  diaoYongFangFuWu: '调用方服务组',
  cunZaiBiTianXiang: '存在必填项未填',
  yiChangQieHuanBi: '异常切换必填',
  fanHuiLeiXingBi: '返回类型必填',
  shuJuYuanJieKou2: '数据源接口必填',
  shuJuLeiXingBi: '数据类型必填',
  inputlist: {
    fuWuJieKouLie: '服务接口列表',
    suoXuFuWuJie: '所需服务接口列表',
    shiFouBiTian: '是否必填',
    ziDuanLeiXing: '字段类型',
    xiTongZiDuan: '系统字段',
  },
  serviceapi: {
    xinZeng: '新增',
    qingXianXuanZeQu: '请先选择渠道',
    geGuanBi: ' 个关闭。',
    geKaiQi: ' 个开启，',
    geJieKou: ' 个接口，',
    gong: '共 ',
    shanChu: '删除',
    xiuGai: '修改',
    caoZuo: '操作',
    guan: '关',
    kai: '开',
    zhuangTai: '状态',
    fou: '否',
    shi: '是',
    chaDeJieKou: '查得接口',
    panDingZhi: '判定值',
    buDengYu: '不等于',
    dengYu: '等于',
    piPeiFangShi: '匹配方式',
    piPeiZiDuan: '匹配字段',
    zhongDuanTiaoJianPei: '中断条件配置',
    yiChangQieHuan: '异常切换',
    fanHuiLeiXing: '返回类型',
    moRen: '默认',
    chaoShiShiJian: '超时时间',
    zhongShiCiShu: '重试次数',
    shuJuYuanFuWu: '数据源服务接口',
    shuJuLeiXing: '数据类型',
  },
  index: {
    queRenTiJiao: '确认提交',
    quXiao: '取消',
    diaoYongFuWuZu: '调用服务组入参列表',
    chuanXingMoShi: '串行模式',
    bingXingMoShi: '并行模式',
    fuWuJieKouLie: '服务接口列表',
    xuanZeQuDao: '选择渠道',
    quDao: '渠道',
    yongYuDiaoYongFang: '用于调用方调用服务时的识别',
    fuWuZuBiaoZhi: '服务组标识',
    shuRuFuWuZu: '输入服务组名称',
    fuWuZuMingCheng: '服务组名称',
    jiBenXinXi: '基本信息',
    bREAD: '{val1}调用方服务组',
    fanHui: '返回',
    zhongDuanTiaoJianPei: '中断条件配置字段输入不完整',
    fuWuJieKouLie2: '服务接口列表至少添加1个',
    quDaoBuNengWei: '渠道不能为空',
    fuWuBiaoZhiGe: '服务标识格式为只能为字母、数字和下划线',
    fuWuZuBiaoZhi2: '服务组标识最多200个字符长度',
    fuWuZuBiaoZhi3: '服务组标识不能为空',
    fuWuZuMingCheng2: '服务组名称最多200个字符长度',
    fuWuZuMingCheng3: '服务组名称不能为空',
    chaKan: '查看',
    xiuGai: '修改',
    xinZeng: '新增',
    jianYiDanGeWen: '建议单个文件数据在10万条以内。',
    jinXingShuJuZhun: '进行数据准备)',
    xiaZaiMuBan: '下载模板',
    ruoHaiWeiZhunBei: '(若还未准备好源数据，请先',
    tiJiao: '提交',
    xiaYiBu: '下一步',
    shangYiBu: '上一步',
    diSanBuQingXuan: '第三步：请选择结果数据存储方式',
    diErBuQingXuan: '第二步：请选择任务运行方式',
    diYiBuQingXuan: '第一步：请选择源数据文件',
    xinZengPiLiangTiao: '新增批量调用任务',
    shuJuYuanFuWu: '数据源服务接口信息',
    diSanBuTianXie: '第三步：填写服务查询接口信息',
    diErBuTianXie: '第二步：填写服务请求接口信息',
    diErBuTianXie2: '第二步：填写服务接口',
    diYiBuTianXie: '第一步：填写基本信息',
  },
  flowlimit: {
    shuRuZhi: '输入值',
    jieShu: '结束',
    qiShi: '起始',
    qingXuanZe: '请选择',
  },
  proxyinput: {
    qingShuRuWangLuo: '请输入网络代理信息(格式：IP:PORT)',
    shi: '是',
    fou: '否',
    qingXuanZeShiFou: '请选择是否使用代理',
  },
  serviceinput: {
    xinZengZiDuan: '新增字段',
    caoZuo: '操作',
    sheZhiDiYiBu: '设置第一步中查询方式为“本地库优先”时的查询条件',
    chaXunTiaoJian: '查询条件',
    biTian: '必填',
    buErXing: '布尔型',
    riQiXing: '日期型',
    xiaoShuXing: '小数型',
    zhengXing: '整型',
    ziFuXing: '字符型',
    zhiLeiXing: '值类型',
    dingZhi: '定值',
    bianLiang: '变量',
    canShuLeiXing: '参数类型',
    qingShuRuDingZhi: '请输入定值',
    hePingTaiTiGong:
      '和平台提供系统字段做映射，如没有相同含义的参数，可添加后再选择',
    xiTongZiDuan: '系统字段',
    baXiTongZiDuan:
      '把系统字段赋值给服务入参，表示接口调用方请求入参字段通过系统字段应映射为服务入参',
    yingShe: '映射',
    canShuBiaoZhi: '参数标识',
  },
  serviceoutput: {
    xinZengZiDuan: '新增字段',
    caoZuo: '操作',
    youXiangHaoAZ:
      '邮箱号：^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$',
    shouJiHaoD: '手机号：^1[3456789]\\d',
    shenFenZhengHaoD: '身份证号：^[1-9]\\d',
    shiLi: '示例：',
    shuRuZhengZeDui:
      '输入正则，对该字段做格式校验。该设置用于“接口分析报告”中，查看字段返回结果的质量',
    geShiJiaoYan: '格式校验',
    quZhi: '取值',
    feiKong: '非空',
    ruGuoJieKouWei: '如果接口为“查得计费”，则出参字段中至少配置一个“查得字段”',
    chaDeZiDuan: '查得字段',
    dingZhi: '定值',
    bianLiang: '变量',
    canShuLeiXing: '参数类型',
    qingShuRuDingZhi: '请输入定值',
    canShuBiaoZhi: '参数标识',
    baFuWuChuCan:
      '把服务出参赋值给系统字段，表示用系统字段接收服务出参，返回给接口调用方',
    yingShe: '映射',
    hePingTaiTiGong:
      '和平台提供系统字段做映射，如没有相同含义的参数，可添加后再选择',
    xiTongZiDuan: '系统字段',
  },
  cachetime: {
    chiJiuHuaKu: '持久化库',
    huanCun: '缓存',
    tian: '天',
    guan: '关',
    kai: '开',
  },
};
