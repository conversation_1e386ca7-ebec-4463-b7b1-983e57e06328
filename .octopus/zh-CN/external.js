export default {
  gongTOTA: '共 {val1} 条记录',
  daoChu: '导出',
  qingXuanZeShuJu: '请选择数据源服务接口名称',
  qingXuanZeHeZuo: '请选择合作方',
  xiTongYiCiZui: '系统一次最多支持查询一年',
  chaKan: '查看',
  wuQuanXianCaoZuo: '无权限操作',
  caoZuo: '操作',
  jiaSheShuJuYuan:
    '假设数据源服务接口设置总流量上限为N，剩余流量=N-成功流量；若没有设置，则显示“-—”',
  shengYuLiuLiang: '剩余流量',
  canYuJiFeiLiu: '参与计费流量',
  diaoYongChengGongDe: '调用成功的流量数量，参与流量上限的统计和预警',
  chengGongLiuLiang: '成功流量',
  chanShengZongLiuLiang: '产生总流量',
  guSuanZongJia: '估算总价',
  guSuanZongJiaCan: '估算总价/参与计费流量',
  pingJunJiFeiDan: '平均计费单次成本',
  guSuanZongJiaChan: '估算总价/产生总流量',
  pingJunDanJia: '平均单价',
  jiFeiFangShi: '计费方式',
  caiGouJieShuRi: '采购结束日期',
  caiGouKaiShiRi: '采购开始日期',
  heTongBanBen: '合同版本',
  heTongBianHao: '合同编号',
  heZuoFangMingCheng: '合作方名称',
  shuJuYuanFuWu: '数据源服务接口名称',
  jieTiJiFeiGu: '3.阶梯计费：估算总价=阶梯区间的单价 * 参与计费流量',
  yueBaoNianBaoJi:
    '2.月包、年包、季包：估算总价=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
  anCiJiFeiGu: '1.按次计费：估算总价= 单价 * 参与计费流量',
  anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
  chaXunLiuLiangShu:
    '3.查询流量：数据源服务接口配置选查询计费，不考虑是否查得。',
  chaDeLiuLiangShu: '2.查得流量：数据源服务接口配置选查得计费，要求成功查得。',
  canYuJiFeiLiu2: '1.参与计费流量 = 查得流量 + 查询流量。',
  shuJuYuanJiFei: '数据源计费报表',
};
