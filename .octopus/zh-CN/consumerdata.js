export default {
  index: {
    yiChangZhuangTaiMa: '异常状态码分布',
    diaoYongFangFuWu: '调用方服务调用排行',
    quanBu: '全部',
    daYu: '大于',
    xiao<PERSON>u: '小于',
    ping<PERSON>un<PERSON>ao<PERSON>hi: '平均耗时',
    diaoYongZhuangTai: '调用状态',
    xianShiQuanBu: '显示全部',
    xianShiDiaoYongLiang: '显示调用量',
    anJiGouTongJi: '按机构统计',
    anYingYongTongJi: '按应用统计',
    diaoYongZhuangTaiHe: '调用状态和耗时统计',
    shiBaiPingJunHao: '失败平均耗时',
    chengGongPingJunHao: '成功平均耗时',
    chengGongBenDiCha: '成功(本地查得)量',
    chaXunBenDiKu: '查询本地库查得的比率',
    benDiChaDeLu: '本地查得率',
    shiBaiLiang: '失败量',
    shiBaiLu: '失败率',
    chengGongLiang: '成功量',
    zongChengGongLuBao: '总成功率，包含本地和数据源',
    chengGongLu: '成功率',
    diaoYongZongLiangCi: '调用总量/次',
    shuJuYuanFuWu: '数据源服务接口名称',
    shouDongTongJi: '手动统计',
    wu: '无',
    shangYiCiTongJi: '上一次统计时间',
    tongJiJieGuoYou: '统计结果有5~10分钟左右的延迟',
    diaoYongTongJi: '调用统计',
    yingYongFenBu: '应用分布',
    shuJuYuanFuWu2: '数据源服务接口分布',
    shengXiaoZhong: '生效中',
    ge: '个',
    huanBiZuoTian: '环比昨天',
    diaoYongFangFuWu2: '调用方服务总数',
    shiShiGaiLan: '实时概览',
    diaoYongFangDaPan: '调用方大盘',
  },
};
