export default {
  layout: {
    tian<PERSON><PERSON>: '天座',
  },
  router: {
    hanShuKu: '函数库',
    liuChengMoBan: '流程模版',
    gongZuoLiu: '工作流',
    piLiangDiaoYongRen: '批量调用任务管理',
    ren<PERSON>u<PERSON>inZeng: '任务新增',
    shuJuYuanQiXian: '数据源期限预警',
    shuJuYuanZongLiu: '数据源总流量预警',
    shuJuYuanYiChang: '数据源异常预警',
    shouGongChaXun: '手工查询',
    diaoYongFangDiaoYong: '调用方调用明细',
    shuJuYuanDiaoYong: '数据源调用明细',
    diaoYongFangFuWu: '调用方服务组管理',
    diaoYongFangFuWu2: '调用方服务组详情',
    diaoYongFangFuWu3: '调用方服务管理',
    heTongJiFei: '合同计费',
    shuJuYuanJiFei: '数据源计费',
    diaoYongFangJiFei: '调用方计费',
    fuWuJieKouGuan: '服务接口管理',
    eTLChuLi: 'ETL处理器管理',
    shuJuYuanFuWu: '数据源服务接口管理',
    mOCKPei: 'mock配置',
    sanFangFuWuJie: '三方服务接口管理-新增',
    heZuoFangGuanLi: '合作方管理',
    heTongGuanLi: '合同管理',
    zhiShiGaiLan: '知识概览',
    shuJuYuanDaPan: '数据源大盘',
    diaoYongFangDaPan: '调用方大盘',
  },
};
