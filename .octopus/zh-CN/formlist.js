export default {
  step1: {
    shuJuYuanFuWuM: '只支持中文, 英文, 数字, 空格, 下划线, -, .',
    shuJuYuanFuWuB: '只支持字母、数字、下划线的输入组合',
    xiaYiBu: '下一步',
    jieKouDiaoYongJi: '接口调用机制',
    shuJuYuanFuWu: '数据源服务接口信息',
    dangQianHeTongGuan:
      '当前合同关联的计费系统字段和入参字段存在冲突，已进行覆盖，被覆盖的入参字段标识为【{val1}】',
    liuLiangShangXianKong: '流量上限控制未填写完整',
    keYiPeiZhiDuo:
      '可以配置多种控制策略，各个策略之间任意一个达到阈值都会被触发',
    liuLiangShangXianKong2: '流量上限控制',
    huanCunKaiGuan: '缓存开关',
    ruGuoBuPeiZhi: '如果不配置，后台默认20000ms',
    shuJuChaoShiShi: '数据超时时间(ms)',
    moRenCi: '默认3次',
    shuJuZhongShiCi: '数据重试次数',
    tongGuoJieKouTiao:
      '通过接口调用入参字段invokePolicy控制查询方式：0 缓存优先 , 1 直查接口',
    chuanCanYouXian: '传参优先',
    zhiJieFaQiJie: '直接发起接口查询',
    zhiJieJieKou: '直接接口',
    xianChaXunBenDi: '先查询本地数据，不满足再发起接口查询',
    benDiHuanCunYou: '本地缓存优先',
    chaXunFangShi: '查询方式',
    qingXuanZeJiFei: '请选择计费类型',
    jiFeiLeiXing: '计费类型',
    di: '低',
    zhong: '中',
    gao: '高',
    qingXuanZeChengBen: '请选择成本等级',
    chengBenDengJi: '成本等级',
    qingXuanZeZhiXin: '请选择置信度',
    zhiXinDu: '置信度',
    chaKanXiangQing: '查看详情',
    qingXuanZeHeTong: '请选择合同',
    heTong: '合同',
    qingXuanZeHeZuo: '请选择合作方',
    heZuoFangMingCheng: '合作方名称',
    qingXuanZeShuJu: '请选择数据类型',
    shuJuLeiXing: '数据类型',
    qingTianXieShuJu: '请填写数据源服务接口名称',
    shuJuYuanFuWu2: '数据源服务接口标识',
    shuJuYuanFuWu3: '数据源服务接口名称',
  },
  step2: {
    fanHui: '返回',
    xiaYiBu: '下一步',
    baoCun: '保存',
    shangYiBu: '上一步',
    fuWuChuCan: '服务出参',
    fuWuRuCan: '服务入参',
    fuWuJieKou: '服务接口',
    caoZuoChengGong: '操作成功',
    caoZuoChengGong2: '操作成功，',
    fuWuRuCanZhong:
      '服务入参中，请至少选择一个字段，配置“查询条件”，作为数据服务查询参数',
    fuWuRuCanYou: '服务入参有未填字段',
    dangQianJiFeiFang:
      '当前计费方式为查得计费，输出参数至少配置一个字段为查得字段',
    fuWuChuCanBu: '服务出参不能为空',
    fuWuChuCanBu2: '服务出参不能存在相同参数标识{val1}',
    fuWuChuCanYou: '服务出参有未填字段',
    fuWuRuCanBu: '服务入参不能为空',
    fuWuRuCanBu2: '服务入参不能存在相同参数标识{val1}',
    fuWuRuCanBu3: '服务入参不能存在相同系统字段{val1}',
    qingTianXieMiMa: '请填写密码',
    miMa: '密码',
    qingTianXieYongHu: '请填写用户名',
    yongHuMing: '用户名',
    shuChuChuLiQi: '输出处理器模板',
    shuRuChuLiQi: '输入处理器模板',
    qingXuanZeZhiBiao: '请选择指标集',
    zhiBiaoJi: '指标集',
    qingXuanZeBaoWen: '请选择报文',
    baoWen: '报文',
    qingXuanZeET: '请选择ETL处理器',
    houZhiETL: '后置ETL处理器',
    shi: '是',
    fou: '否',
    qingXuanZe: '请选择',
    qingXuanZeShiFou: '请选择是否分页',
    shiFouFenYeJie: '是否分页接口',
    qianZhiETL: '前置ETL处理器',
    daiLiXinXiGe: '代理信息格式不对(格式：IP:PORT)',
    qingTianXieWanZheng: '请填写完整',
    shiFouShiYongDai: '是否使用代理',
    qingXuanZeDiaoYong: '请选择调用方式',
    diaoYongFangShi: '调用方式',
    qingTianXieUR: '请填写url地址',
    uRLGeShi: 'URL格式不正确(例如: https://www.tongdun.cn/)',
    sOCKE: 'socket格式不正确(例如: 127.0.0.1:8080)',
    uRLDiZhi: 'url地址',
    qingXuanZeJieKou: '请选择接口类型',
    jieKouLeiXingXie: '接口类型(协议)',
    pOSTG:
      'postgreSQL格式不正确(例如: *******************************************)',
    oRACL: 'oracle格式不正确(例如: *************************************)',
    jDBCGe: 'jdbc格式不正确(例如: **************************************)',
  },
  step3: {
    tiJiao: '提交',
    fanHui: '返回',
    shangYiBu: '上一步',
    fuWuChuCan: '服务出参',
    fuWuRuCan: '服务入参',
    fuWuJieKou: '服务接口',
    caoZuoChengGong: '操作成功',
    dangQianJiFeiFang:
      '当前计费方式为查得计费，输出参数至少配置一个字段为查得字段',
    fuWuChuCanBu: '服务出参不能存在相同参数标识{val1}',
    fuWuChuCanYou: '服务出参有未填字段',
    fuWuRuCanBu: '服务入参不能为空',
    fuWuChuCanBu: '服务出参不能为空',
    qingZhiShaoXuanZe:
      '请至少选择一个作为查询条件的字段，给调用方配置查询时使用。',
    fuWuRuCanBu2: '服务入参不能存在相同参数标识{val1}',
    fuWuRuCanYou: '服务入参有未填字段',
    shuChuChuLiQi: '输出处理器模板',
    shuRuChuLiQi: '输入处理器模板',
    qingXuanZeZhiBiao: '请选择指标集',
    zhiBiaoJi: '指标集',
    qingXuanZeBaoWen: '请选择报文',
    baoWen: '报文',
    moRenCi: '默认10次',
    diErBuQingQiu: '第二步请求接口与第三步查询接口的最大轮询次数',
    zuiDaLunXunCi: '最大轮询次数',
    moRenMS: '默认1000ms',
    diErBuQingQiu2:
      '第二步请求接口与第三步查询接口的自动轮询时间。第二步返回请求结果后，执行第三步的查询接口',
    lunXunShiJianJian: '轮询时间间隔(ms)',
    qingXuanZeET: '请选择ETL处理器',
    houZhiETL: '后置ETL处理器',
    qianZhiETL: '前置ETL处理器',
    qingTianXieWanZheng: '请填写完整',
    shiFouShiYongDai: '是否使用代理',
    qingXuanZeDiaoYong: '请选择调用方式',
    diaoYongFangShi: '调用方式',
    qingTianXieUR: '请填写url地址',
    uRLGeShi: 'URL格式不正确(例如: https://www.tongdun.cn/)',
    sOCKE: 'socket格式不正确(例如: 127.0.0.1:8080)',
    uRLDiZhi: 'url地址',
    qingXuanZeJieKou: '请选择接口类型',
    jieKouLeiXingXie: '接口类型(协议)',
  },
};
