export default {
  listmodal1: {
    gongMODA: '共 {val1} 条记录',
    daoChu: '导出',
    meiRiJiFeiMing: '每日计费明细',
    chaKan: '查看',
    caoZuo: '操作',
    danRiJunTanGu: '单日均摊估算',
    danRiCanYuJi: '单日参与计费流量占比',
    danRiGuSuan: '单日估算',
    danRiCanYuJi2: '单日参与计费流量',
    danRiChanShengZong: '单日产生总流量',
    riQi: '日期',
    banBenHao: '版本号',
    shuJuYuanFuWu: '数据源服务接口名称',
    quDao: '渠道',
    jieTiJiFeiDan: '3.阶梯计费：单日估算=阶梯区间的单价 * 参与计费流量',
    yueBaoNianBaoJi:
      '2.月包、年包、季包：单日估算=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
    anCiJiFeiDan: '1.按次计费：单日估算= 单价 * 参与计费流量',
    anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
    yingYongXiTongJi: '应用系统计费明细报表',
  },
  listmodal2: {
    gongMODA: '共 {val1} 条记录',
    daoChu: '导出',
    meiRiJiFeiMing: '每日计费明细',
    chaKan: '查看',
    caoZuo: '操作',
    danRiJunTanGu: '单日均摊估算',
    danRiCanYuJi: '单日参与计费流量占比',
    danRiGuSuan: '单日估算',
    danRiCanYuJi2: '单日参与计费流量',
    danRiChanShengZong: '单日产生总流量',
    riQi: '日期',
    banBenHao: '版本号',
    shuJuYuanFuWu: '数据源服务接口名称',
    jiGou: '机构',
    jieTiJiFeiDan: '3.阶梯计费：单日估算=阶梯区间的单价 * 参与计费流量',
    yueBaoNianBaoJi:
      '2.月包、年包、季包：单日估算=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
    anCiJiFeiDan: '1.按次计费：单日估算= 单价 * 参与计费流量',
    anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
    jiGouJiFeiMing: '机构计费明细报表',
  },
  onlinemodal: {
    qingShuRuShangXian: '请输入上线原因',
    zuiDuoShuRuGe: '最多输入2000个字符',
    miaoShu: '描述',
    piLiangShangXian: '批量上线',
    shangXian: '上线',
  },
  publishresult: {
    queDing: '确定',
    tiJiaoJieGuo: '提交结果',
    miaoShu: '描述',
    shiBai: '失败',
    chengGong: '成功',
    jieGuo: '结果',
    hanShuMingCheng: '函数名称',
  },
  addmodify: {
    qingTianXieMiaoShu: '请填写描述',
    zuiDuoGeZiFu: '最多2000个字符',
    miaoShu: '描述',
    qingTianXieGongZuo: '请填写工作流标识',
    gongZuoLiuBiaoZhi: '工作流标识只支持字母、数字、下划线的输入组合',
    zuiDuoGeZiFu2: '最多200个字符',
    gongZuoLiuBiaoZhi2: '工作流标识',
    jinZhiChiZhongYing: '仅支持中英文、数字、下划线',
    gongZuoLiuMingCheng: '工作流名称只支持中文、英文、数字、空格、下划线、-、.',
    qingTianXieGongZuo2: '请填写工作流名称',
    gongZuoLiuMingCheng2: '工作流名称',
    tITLE: '{val1} 流程模板',
    queDing: '确定',
    quXiao: '取消',
    tITLE2: '{val1}流程模板失败',
    tITLE3: '{val1}流程模板成功',
    xinZeng: '新增',
    qingTianXieLiuCheng: '请填写流程模板标识',
    liuChengMuBanBiao: '流程模板标识只支持字母、数字、下划线的输入组合',
    liuChengMuBanBiao2: '流程模板标识',
    liuChengMuBanMing: '流程模板名称只支持中文、英文、数字、空格、下划线、-、.',
    qingTianXieLiuCheng2: '请填写流程模板名称',
    liuChengMuBanMing2: '流程模板名称',
  },
  importworkflow: {
    fuGaiXiangTongBiao: '覆盖相同标识，继续导入',
    tiaoGuoXiangTongBiao: '跳过相同标识，继续导入',
    qingXuanZeChuLi: '请选择处理方式',
    xiangTongGongZuoLiu: '相同标识处理方式',
    dianJiXuanZeWen: '点击选择文件',
    qingXuanZeShangChuan: '请选择上传文件',
    xuanZeWenJian: '选择文件',
    daoRuGongZuoLiu: '导入工作流',
    queDing: '确定',
    quXiao: '取消',
    wenJianDaXiaoBu: '文件大小不能超过50M，请重新上传',
    wenJianLeiXingCuo: '文件类型错误，请重新上传',
    daoRuShiBai: '导入失败',
    daoRuChengGong: '导入成功',
  },
  offlinemodal: {
    qingShuRuYiJian: '请输入意见，2000字以内',
    qingShuRuYiJian2: '请输入意见',
    yiJian: '意见',
    yingYongFuWuXia: '应用服务下线',
    xiaXianShiBai: '下线失败',
    caoZuoChengGong: '操作成功',
  },
  publish: {
    qingShuRuFaBan: '请输入发版描述',
    faBanMiaoShuZui: '发版描述最长不超过2000个字符',
    miaoShu: '描述',
    gongZuoLiuMingCheng: '工作流名称',
    shiBai: '失败',
    chengGong: '成功',
    gongZuoLiuShangXian: '工作流上线',
    gongZuoLiuPiLiang: '工作流批量上线',
  },
};
